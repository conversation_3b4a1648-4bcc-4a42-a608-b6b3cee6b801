{"Version": 1, "WorkspaceRootPath": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|d:\\augment-projects\\expired ubi-cpv\\expired ubi-cpv\\ubi.cpv.api\\controllers\\leadscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|solutionrelative:ubi.cpv.api\\controllers\\leadscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|d:\\augment-projects\\expired ubi-cpv\\expired ubi-cpv\\ubi.cpv.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|solutionrelative:ubi.cpv.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|d:\\augment-projects\\expired ubi-cpv\\expired ubi-cpv\\ubi.cpv.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|solutionrelative:ubi.cpv.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Augment-projects\\Compliant Management\\ComplaintManagementAPI\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|d:\\augment-projects\\expired ubi-cpv\\expired ubi-cpv\\ubi.cpv.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{9527C8FF-0756-C3A9-DA44-7CF36550EB4C}|UBI.CPV.API\\UBI.CPV.API.csproj|solutionrelative:ubi.cpv.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Bookmark", "Name": "ST:11:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:12:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:14:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:15:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.Production.json", "DocumentMoniker": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\appsettings.Production.json", "RelativeDocumentMoniker": "UBI.CPV.API\\appsettings.Production.json", "ToolTip": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\appsettings.Production.json", "RelativeToolTip": "UBI.CPV.API\\appsettings.Production.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-29T13:15:42.332Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\appsettings.Development.json", "RelativeDocumentMoniker": "UBI.CPV.API\\appsettings.Development.json", "ToolTip": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\appsettings.Development.json", "RelativeToolTip": "UBI.CPV.API\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-28T13:50:50.172Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "D:\\Augment-projects\\Compliant Management\\ComplaintManagementAPI\\appsettings.json", "RelativeDocumentMoniker": "..\\..\\Compliant Management\\ComplaintManagementAPI\\appsettings.json", "ToolTip": "D:\\Augment-projects\\Compliant Management\\ComplaintManagementAPI\\appsettings.json", "RelativeToolTip": "..\\..\\Compliant Management\\ComplaintManagementAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAB7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-28T13:42:06.998Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "LeadsController.cs", "DocumentMoniker": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\Controllers\\LeadsController.cs", "RelativeDocumentMoniker": "UBI.CPV.API\\Controllers\\LeadsController.cs", "ToolTip": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\Controllers\\LeadsController.cs", "RelativeToolTip": "UBI.CPV.API\\Controllers\\LeadsController.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABcAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:28:05.298Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AuthController.cs", "DocumentMoniker": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "UBI.CPV.API\\Controllers\\AuthController.cs", "ToolTip": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\Controllers\\AuthController.cs", "RelativeToolTip": "UBI.CPV.API\\Controllers\\AuthController.cs", "ViewState": "AgIAACYAAAAAAAAAAAD4vzIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:00:13.519Z"}]}]}]}