﻿++Solution 'Expired UBI-CPV' ‎ (1 of 1 project)
i:{00000000-0000-0000-0000-000000000000}:Expired UBI-CPV.sln
++UBI.CPV.API
i:{00000000-0000-0000-0000-000000000000}:UBI.CPV.API
++Connected Services 
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>572
++Dependencies
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>574
++Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>583
++Frameworks
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>603
++Packages
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>606
++AutoMapper (12.0.1)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>621
++AutoMapper.Extensions.Microsoft.DependencyInjection (12.0.1)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>609
++BCrypt.Net-Next (4.0.3)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>618
++FluentValidation (11.8.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>617
++FluentValidation.AspNetCore (11.3.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>614
++Microsoft.AspNetCore.Authentication.JwtBearer (8.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>622
++Microsoft.AspNetCore.Cors (2.2.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>615
++Microsoft.EntityFrameworkCore (8.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>607
++Microsoft.EntityFrameworkCore.Design (8.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>610
++Microsoft.EntityFrameworkCore.Sqlite (8.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>616
++Microsoft.EntityFrameworkCore.SqlServer (8.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>608
++Microsoft.EntityFrameworkCore.Tools (8.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>623
++Serilog.AspNetCore (7.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>613
++Serilog.Sinks.Console (4.1.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>619
++Serilog.Sinks.File (5.0.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>620
++Swashbuckle.AspNetCore (6.5.0)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>612
++System.IdentityModel.Tokens.Jwt (7.0.3)
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>611
++Properties
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\properties\
++wwwroot
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\wwwroot\
++Controllers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\controllers\
++AuthController.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\controllers\authcontroller.cs
++DocumentsController.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\controllers\documentscontroller.cs
++LeadsController.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\controllers\leadscontroller.cs
++VerificationController.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\controllers\verificationcontroller.cs
++Data
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\data\
++logs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\logs\
++Models
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\
++appsettings.json
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\appsettings.json
++Program.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\program.cs
++ubi_cpv.db
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\ubi_cpv.db
++No service dependencies discovered
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>573
++Microsoft.AspNetCore.Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.3\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.3\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\8.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.11\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.11\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>604
++Microsoft.NETCore.App
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:>605
++launchSettings.json
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\properties\launchsettings.json
++uploads
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\wwwroot\uploads\
++ApplicationDbContext.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\data\applicationdbcontext.cs
++ubi-cpv-20250526.txt
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\logs\ubi-cpv-20250526.txt
++ubi-cpv-20250527.txt
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\logs\ubi-cpv-20250527.txt
++ubi-cpv-20250527_001.txt
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\logs\ubi-cpv-20250527_001.txt
++DTOs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\dtos\
++Entities
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\entities\
++appsettings.Development.json
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\appsettings.development.json
++appsettings.Production.json
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\appsettings.production.json
++AuthDTOs.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\dtos\authdtos.cs
++DocumentDTOs.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\dtos\documentdtos.cs
++LeadDTOs.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\dtos\leaddtos.cs
++VerificationDTOs.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\dtos\verificationdtos.cs
++Document.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\entities\document.cs
++Lead.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\entities\lead.cs
++User.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\entities\user.cs
++VerificationData.cs
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\models\entities\verificationdata.cs
++obj
++Debug
++project.assets.json
++project.nuget.cache
++UBI.CPV.API.csproj.nuget.dgspec.json
++UBI.CPV.API.csproj.nuget.g.props
++UBI.CPV.API.csproj.nuget.g.targets
++net8.0
++EndpointInfo
++ref
++refint
++staticwebassets
++.NETCoreApp,Version=v8.0.AssemblyAttributes.cs
++ApiEndpoints.json
++apphost
++apphost.exe
++staticwebassets.build.endpoints.json
++staticwebassets.build.json
++staticwebassets.development.json
++staticwebassets.references.upToDateCheck.txt
++staticwebassets.removed.txt
++UBI.CPV..51CA0FDF.Up2Date
++UBI.CPV.API.AssemblyInfo.cs
++UBI.CPV.API.AssemblyInfoInputs.cache
++UBI.CPV.API.assets.cache
++UBI.CPV.API.csproj.AssemblyReference.cache
++UBI.CPV.API.csproj.CoreCompileInputs.cache
++UBI.CPV.API.csproj.FileListAbsolute.txt
++UBI.CPV.API.dll
++UBI.CPV.API.GeneratedMSBuildEditorConfig.editorconfig
++UBI.CPV.API.genruntimeconfig.cache
++UBI.CPV.API.GlobalUsings.g.cs
++UBI.CPV.API.MvcApplicationPartsAssemblyInfo.cache
++UBI.CPV.API.MvcApplicationPartsAssemblyInfo.cs
++UBI.CPV.API.pdb
++UBI.CPV.API.json
++UBI.CPV.API.OpenApiFiles.cache
++msbuild.build.UBI.CPV.API.props
++msbuild.buildMultiTargeting.UBI.CPV.API.props
++msbuild.buildTransitive.UBI.CPV.API.props
++bin
++cs
++de
++es
++fr
++it
++ja
++ko
++pl
++pt-BR
++ru
++runtimes
++tr
++zh-Hans
++zh-Hant
++AutoMapper.dll
++AutoMapper.Extensions.Microsoft.DependencyInjection.dll
++Azure.Core.dll
++Azure.Identity.dll
++BCrypt.Net-Next.dll
++FluentValidation.AspNetCore.dll
++FluentValidation.DependencyInjectionExtensions.dll
++FluentValidation.dll
++Humanizer.dll
++Microsoft.AspNetCore.Authentication.JwtBearer.dll
++Microsoft.Bcl.AsyncInterfaces.dll
++Microsoft.CodeAnalysis.CSharp.dll
++Microsoft.CodeAnalysis.CSharp.Workspaces.dll
++Microsoft.CodeAnalysis.dll
++Microsoft.CodeAnalysis.Workspaces.dll
++Microsoft.Data.SqlClient.dll
++Microsoft.Data.Sqlite.dll
++Microsoft.EntityFrameworkCore.Abstractions.dll
++Microsoft.EntityFrameworkCore.Design.dll
++Microsoft.EntityFrameworkCore.dll
++Microsoft.EntityFrameworkCore.Relational.dll
++Microsoft.EntityFrameworkCore.Sqlite.dll
++Microsoft.EntityFrameworkCore.SqlServer.dll
++Microsoft.Extensions.DependencyModel.dll
++Microsoft.Identity.Client.dll
++Microsoft.Identity.Client.Extensions.Msal.dll
++Microsoft.IdentityModel.Abstractions.dll
++Microsoft.IdentityModel.JsonWebTokens.dll
++Microsoft.IdentityModel.Logging.dll
++Microsoft.IdentityModel.Protocols.dll
++Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
++Microsoft.IdentityModel.Tokens.dll
++Microsoft.OpenApi.dll
++Microsoft.SqlServer.Server.dll
++Microsoft.Win32.SystemEvents.dll
++Mono.TextTemplating.dll
++Serilog.AspNetCore.dll
++Serilog.dll
++Serilog.Extensions.Hosting.dll
++Serilog.Extensions.Logging.dll
++Serilog.Formatting.Compact.dll
++Serilog.Settings.Configuration.dll
++Serilog.Sinks.Console.dll
++Serilog.Sinks.Debug.dll
++Serilog.Sinks.File.dll
++SQLitePCLRaw.batteries_v2.dll
++SQLitePCLRaw.core.dll
++SQLitePCLRaw.provider.e_sqlite3.dll
++Swashbuckle.AspNetCore.Swagger.dll
++Swashbuckle.AspNetCore.SwaggerGen.dll
++Swashbuckle.AspNetCore.SwaggerUI.dll
++System.CodeDom.dll
++System.Composition.AttributedModel.dll
++System.Composition.Convention.dll
++System.Composition.Hosting.dll
++System.Composition.Runtime.dll
++System.Composition.TypedParts.dll
++System.Configuration.ConfigurationManager.dll
++System.Drawing.Common.dll
++System.IdentityModel.Tokens.Jwt.dll
++System.Memory.Data.dll
++System.Runtime.Caching.dll
++System.Security.Cryptography.ProtectedData.dll
++System.Security.Permissions.dll
++System.Windows.Extensions.dll
++UBI.CPV.API.deps.json
++UBI.CPV.API.exe
++UBI.CPV.API.runtimeconfig.json
++UBI.CPV.API.staticwebassets.endpoints.json
++UBI.CPV.API.staticwebassets.runtime.json
++Microsoft.CodeAnalysis.CSharp.resources.dll
++Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
++Microsoft.CodeAnalysis.resources.dll
++Microsoft.CodeAnalysis.Workspaces.resources.dll
++browser-wasm
++linux-arm
++linux-arm64
++linux-armel
++linux-mips64
++linux-musl-arm
++linux-musl-arm64
++linux-musl-x64
++linux-ppc64le
++linux-s390x
++linux-x64
++linux-x86
++maccatalyst-arm64
++maccatalyst-x64
++osx-arm64
++osx-x64
++unix
++win
++win-arm
++win-arm64
++win-x64
++win-x86
++nativeassets
++native
++lib
++libe_sqlite3.so
++libe_sqlite3.dylib
++net6.0
++e_sqlite3.dll
++Microsoft.Data.SqlClient.SNI.dll
++e_sqlite3.a
++Imports
++Sdk.props
++Sdk.targets
++Sdk.Server.props
++Sdk.Server.targets
++Sdk.props (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk\Sdk)
++Sdk.props (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk.Razor\Sdk)
++Sdk.props (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk.Web.ProjectSystem\Sdk)
++Sdk.props (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk.Publish\Sdk)
++Microsoft.NET.Sdk.Web.BeforeCommon.targets
++Sdk.targets (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk\Sdk)
++Sdk.targets (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk.Razor\Sdk)
++Sdk.targets (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk.Web.ProjectSystem\Sdk)
++Sdk.targets (C:\Program Files\dotnet\sdk\9.0.101\Sdks\Microsoft.NET.Sdk.Publish\Sdk)
++Microsoft.Common.props
++Microsoft.NET.Sdk.props
++Sdk.Razor.CurrentVersion.props
++Microsoft.NET.Sdk.Web.ProjectSystem.props
++Microsoft.NET.Sdk.Publish.props
++Microsoft.NET.Sdk.BeforeCommon.targets
++Microsoft.CSharp.targets
++Microsoft.NET.Sdk.targets
++Microsoft.NET.ApiCompat.targets
++NuGet.Build.Tasks.Pack.targets
++Microsoft.NET.Build.Containers.props
++Microsoft.NET.Build.Containers.targets
++Sdk.Razor.CurrentVersion.targets
++Microsoft.NET.Sdk.Web.ProjectSystem.targets
++Microsoft.NET.Sdk.Publish.targets
++UseArtifactsOutputPath.props
++Microsoft.NuGet.ImportBefore.props
++NuGet.props
++Microsoft.NET.Sdk.DefaultItems.props
++Microsoft.NET.Sdk.ImportWorkloads.props
++Microsoft.NET.SupportedTargetFrameworks.props
++Microsoft.NET.SupportedPlatforms.props
++Microsoft.NET.WindowsSdkSupportedTargetPlatforms.props
++Microsoft.NET.Sdk.SourceLink.props
++Microsoft.NET.Sdk.CSharp.props
++Microsoft.NET.PackTool.props
++Microsoft.NET.PackProjectTool.props
++Microsoft.NET.Sdk.WindowsDesktop.props
++Microsoft.NET.Windows.props
++Sdk.StaticWebAssets.CurrentVersion.props
++Microsoft.TypeScript.Default.props
++Microsoft.NET.Sdk.Web.DefaultItems.props
++Microsoft.NET.DefaultAssemblyInfo.targets
++Microsoft.NET.Sdk.ImportPublishProfile.targets
++Microsoft.NET.TargetFrameworkInference.targets
++Microsoft.NET.DefaultOutputPaths.targets
++Microsoft.NET.Sdk.Razor.BeforeCommon.targets
++Microsoft.NET.Sdk.ImportWorkloads.targets
++Microsoft.NET.RuntimeIdentifierInference.targets
++Microsoft.NET.EolTargetFrameworks.targets
++Microsoft.NET.NuGetOfflineCache.targets
++Microsoft.Managed.Before.targets
++Microsoft.CSharp.CurrentVersion.targets
++Microsoft.Managed.After.targets
++Microsoft.NET.Sdk.Common.targets
++Microsoft.PackageDependencyResolution.targets
++Microsoft.NET.Sdk.DefaultItems.targets
++Microsoft.NET.Sdk.FrameworkReferenceResolution.targets
++Microsoft.NET.Sdk.Shared.targets
++Microsoft.NET.Sdk.SourceLink.targets
++Microsoft.NET.DisableStandardFrameworkResolution.targets
++Microsoft.NET.DesignerSupport.targets
++Microsoft.NET.GenerateAssemblyInfo.targets
++Microsoft.NET.GenerateGlobalUsings.targets
++Microsoft.NET.GenerateSupportedRuntime.targets
++Microsoft.NET.ComposeStore.targets
++Microsoft.NET.CrossGen.targets
++Microsoft.NET.ObsoleteReferences.targets
++Microsoft.NET.Publish.targets
++Microsoft.NET.PackTool.targets
++Microsoft.NET.PackProjectTool.targets
++Microsoft.NET.PreserveCompilationContext.targets
++Microsoft.NET.ConflictResolution.targets
++Microsoft.NET.Sdk.CSharp.targets
++Microsoft.NET.Sdk.Analyzers.targets
++Microsoft.NET.ApiCompat.Common.targets
++Microsoft.NET.ApiCompat.ValidatePackage.targets
++Sdk.StaticWebAssets.CurrentVersion.targets
++Microsoft.NET.Sdk.Razor.Configuration.targets
++Microsoft.NET.Sdk.Razor.CodeGeneration.targets
++Microsoft.NET.Sdk.Razor.SourceGenerators.targets
++Microsoft.NET.Sdk.Razor.GenerateAssemblyInfo.targets
++Microsoft.NET.Sdk.Razor.MvcApplicationPartsDiscovery.targets
++Microsoft.NET.Sdk.Razor.DesignTime.targets
++Microsoft.TypeScript.targets
++Microsoft.TypeScript.DotNetCore.targets
++Microsoft.Web.Designtime.targets
++Default.pubxml
++Microsoft.NET.Sdk.Publish.ComputeFiles.targets
++Microsoft.NET.Sdk.Publish.FilterFiles.targets
++Microsoft.NET.Sdk.Publish.CopyFiles.targets
++Microsoft.NET.Sdk.Publish.TransformFiles.targets
++Microsoft.NET.Sdk.Publish.FileSystem.targets
++Microsoft.NET.Sdk.DotNetCLITool.targets
++Microsoft.Extensions.ApiDescription.Server.props
++Swashbuckle.AspNetCore.props
++Microsoft.EntityFrameworkCore.props
++Microsoft.EntityFrameworkCore.Design.props
++Microsoft.NuGet.props
++Microsoft.NETCoreSdk.BundledVersions.props
++Microsoft.NETCoreSdk.BundledMSBuildInformation.props
++Microsoft.Build.Tasks.Git.props
++Microsoft.SourceLink.Common.props
++Microsoft.SourceLink.GitHub.props
++Microsoft.SourceLink.GitLab.props
++Microsoft.SourceLink.AzureRepos.Git.props
++Microsoft.SourceLink.Bitbucket.Git.props
++Microsoft.NET.Sdk.WindowsDesktop.WindowsForms.props
++Microsoft.NET.Sdk.WindowsDesktop.WPF.props
++Sdk.StaticWebAssets.StaticAssets.ProjectSystem.props
++Microsoft.NET.Sdk.StaticWebAssets.ContentTypeMappings.props
++Microsoft.NET.Sdk.StaticWebAssets.FingerprintingPatterns.props
++Microsoft.NET.DefaultArtifactsPath.props
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.android\35.0.7)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.ios\18.1.9163)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.maccatalyst\18.1.9163)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.macos\15.1.9163)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.maui\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.tvos\18.1.9163)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.current\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.current\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net6\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net7\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net8\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net6\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net7\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net8\9.0.0)
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\8.0.100\microsoft.net.sdk.aspire\8.2.2)
++Microsoft.CSharp.Core.targets
++Microsoft.CSharp.DesignTime.targets
++Microsoft.Common.targets
++Microsoft.ServiceModel.targets
++Microsoft.NET.Sdk.DefaultItems.Shared.targets
++Microsoft.Build.Tasks.Git.targets
++Microsoft.SourceLink.Common.targets
++Microsoft.SourceLink.GitHub.targets
++Microsoft.SourceLink.GitLab.targets
++Microsoft.SourceLink.AzureRepos.Git.targets
++Microsoft.SourceLink.Bitbucket.Git.targets
++Microsoft.NETCoreSdk.BundledCliTools.props
++Microsoft.NET.DefaultPackageConflictOverrides.targets
++Microsoft.CodeAnalysis.NetAnalyzers.props
++Microsoft.CodeAnalysis.NetAnalyzers.targets
++Microsoft.CodeAnalysis.CSharp.CodeStyle.targets
++Microsoft.NET.Sdk.StaticWebAssets.SingleTargeting.targets
++System.Windows.Forms.Analyzers.props
++Microsoft.Managed.Core.targets
++Microsoft.Managed.DesignTime.targets
++Microsoft.Common.CurrentVersion.targets
++InitializeSourceControlInformation.targets
++Microsoft.NET.Sdk.StaticWebAssets.targets
++Microsoft.Managed.Core.CurrentVersions.targets
++Microsoft.NET.props
++Microsoft.CodeAnalysis.targets
++Microsoft.Xaml.targets
++Microsoft.WorkflowBuildExtensions.targets
++Microsoft.TeamTest.targets
++NuGet.targets
++Containers.Tools.targets
++Maui.Upgrade.targets
++Microsoft.Docker.ImportAfter.targets
++Microsoft.NET.Build.Extensions.targets
++Microsoft.NuGet.ImportAfter.targets
++Microsoft.VisualStudio.Azure.Fabric.ServiceProject.ImportAfter.targets
++Microsoft.Web.ImportAfter.targets
++Microsoft.WebTools.Aspire.ImportAfter.targets
++System.Text.Json.targets
++Microsoft.Extensions.ApiDescription.Server.targets
++SQLitePCLRaw.lib.e_sqlite3.targets
++Microsoft.Extensions.Options.targets
++Microsoft.Extensions.Logging.Abstractions.targets
++Microsoft.NET.Sdk.StaticWebAssets.Publish.targets
++Microsoft.NET.Sdk.StaticWebAssets.References.targets
++Microsoft.NET.Sdk.StaticWebAssets.Design.targets
++Microsoft.NET.Sdk.StaticWebAssets.EmbeddedAssets.targets
++Microsoft.NET.Sdk.StaticWebAssets.Pack.targets
++Microsoft.NET.Sdk.StaticWebAssets.ScopedCss.targets
++Microsoft.NET.Sdk.StaticWebAssets.JSModules.targets
++Microsoft.NET.Sdk.StaticWebAssets.Compression.targets
++Microsoft.Docker.targets
++Microsoft.VisualStudio.Azure.Fabric.ServiceProject.targets
++Microsoft.Web.IISSupport.targets
++Microsoft.WebTools.Aspire.targets
++ubi-cpv-20250528.txt
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\logs\ubi-cpv-20250528.txt
++ubi-cpv-20250528_001.txt
i:{9527c8ff-0756-c3a9-da44-7cf36550eb4c}:d:\augment-projects\expired ubi-cpv\expired ubi-cpv\ubi.cpv.api\logs\ubi-cpv-20250528_001.txt
