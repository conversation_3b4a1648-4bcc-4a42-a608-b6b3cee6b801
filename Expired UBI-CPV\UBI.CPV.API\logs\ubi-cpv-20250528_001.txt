2025-05-28 10:44:23.917 +05:30 [ERR] An error occurred while creating the database or seeding data
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 156
2025-05-28 10:44:24.175 +05:30 [INF] UBI-CPV API starting up...
2025-05-28 10:44:24.481 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-28 10:44:25.712 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-28 10:44:25.724 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-28 10:44:25.868 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-28 10:44:25.884 +05:30 [INF] Hosting environment: Development
2025-05-28 10:44:25.896 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-05-28 10:44:26.930 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null
2025-05-28 10:44:28.340 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 1418.2493ms
2025-05-28 10:44:28.401 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404
2025-05-28 10:45:41.876 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/Auth/login - null null
2025-05-28 10:45:42.041 +05:30 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-05-28 10:45:42.058 +05:30 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-05-28 10:45:42.074 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/Auth/login - 405 0 null 198.1208ms
2025-05-28 10:46:33.632 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/Auth/me - null null
2025-05-28 10:46:33.859 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-28 10:46:33.895 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-28 10:46:33.925 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/Auth/me - 401 0 null 292.7561ms
2025-05-28 10:46:45.167 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/Auth/GetCurrentUser - null null
2025-05-28 10:46:45.309 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/Auth/GetCurrentUser - 404 0 null 141.4089ms
2025-05-28 10:46:45.364 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/Auth/GetCurrentUser, Response status code: 404
2025-05-28 10:46:53.697 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/Auth/me - null null
2025-05-28 10:46:53.869 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-28 10:46:53.899 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-28 10:46:53.919 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/Auth/me - 401 0 null 222.4221ms
2025-05-28 10:53:58.408 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 10:53:58.462 +05:30 [INF] CORS policy execution failed.
2025-05-28 10:53:58.492 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 10:53:58.513 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 105.1596ms
2025-05-28 10:54:45.327 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 10:54:45.368 +05:30 [INF] CORS policy execution failed.
2025-05-28 10:54:45.383 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 10:54:45.408 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 81.6635ms
2025-05-28 10:54:49.460 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 10:54:49.490 +05:30 [INF] CORS policy execution failed.
2025-05-28 10:54:49.514 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 10:54:49.551 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 91.4122ms
2025-05-28 10:54:53.093 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 10:54:53.121 +05:30 [INF] CORS policy execution failed.
2025-05-28 10:54:53.134 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 10:54:53.146 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 53.3936ms
2025-05-28 10:55:51.738 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 10:55:51.776 +05:30 [INF] CORS policy execution failed.
2025-05-28 10:55:51.786 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 10:55:51.800 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 62.3109ms
2025-05-28 10:58:00.325 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads - null null
2025-05-28 10:58:00.596 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-28 10:58:00.624 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-28 10:58:00.651 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads - 401 0 null 326.6768ms
2025-05-28 11:05:34.881 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-28 11:05:34.940 +05:30 [INF] CORS policy execution failed.
2025-05-28 11:05:34.976 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 11:05:34.999 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 118.0633ms
2025-05-28 11:05:39.593 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 11:05:39.619 +05:30 [INF] CORS policy execution failed.
2025-05-28 11:05:39.632 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 11:05:39.646 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 53.6172ms
2025-05-28 11:05:46.616 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-28 11:05:46.663 +05:30 [INF] CORS policy execution failed.
2025-05-28 11:05:46.692 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 11:05:46.724 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 107.5218ms
2025-05-28 11:05:52.477 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 11:05:52.508 +05:30 [INF] CORS policy execution failed.
2025-05-28 11:05:52.540 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 11:05:52.574 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 96.5502ms
2025-05-28 11:06:29.908 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null
2025-05-28 11:06:29.968 +05:30 [INF] CORS policy execution failed.
2025-05-28 11:06:29.995 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 11:06:30.011 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 103.43ms
2025-05-28 11:06:32.691 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-05-28 11:06:32.725 +05:30 [INF] CORS policy execution failed.
2025-05-28 11:06:32.753 +05:30 [INF] Request origin null does not have permission to access the resource.
2025-05-28 11:06:32.771 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 79.8043ms
[2025-05-28 12:25:15.670 +05:30 ERR] An error occurred while creating the database or seeding data {}
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 156
[2025-05-28 12:25:15.891 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-28 12:25:16.111 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-28 12:25:16.735 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:25:16.751 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:25:16.773 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:25:16.786 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-28 12:25:16.799 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
