[2025-05-30 12:29:33.715 +05:30 INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-30 12:29:34.039 +05:30 INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-30 12:29:34.095 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-30 12:29:34.141 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-30 12:29:34.293 +05:30 INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId]; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-30 12:29:34.391 +05:30 INF] Applying migration '20250529130711_InitialCreate'. {"EventId":{"Id":20402,"Name":"Microsoft.EntityFrameworkCore.Migrations.MigrationApplying"},"SourceContext":"Microsoft.EntityFrameworkCore.Migrations"}
[2025-05-30 12:29:34.949 +05:30 ERR] Failed executing DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
); {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-30 12:29:35.218 +05:30 ERR] An error occurred while creating the database or seeding data {}
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:e8c74101-8930-4b04-8ccb-c05d8cde8689
Error Number:2714,State:6,Class:16
[2025-05-30 12:29:35.288 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-30 12:29:35.549 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-30 12:29:36.842 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-30 12:29:36.868 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-30 12:29:37.107 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-30 12:29:37.116 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-30 12:29:37.127 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-30 12:29:39.335 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5KR:********","RequestPath":"/","ConnectionId":"0HNCV8U55I5KR"}
[2025-05-30 12:29:40.416 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 1104.8842ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5KR:********","RequestPath":"/","ConnectionId":"0HNCV8U55I5KR"}
[2025-05-30 12:29:40.483 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5KR:********","RequestPath":"/","ConnectionId":"0HNCV8U55I5KR"}
[2025-05-30 12:29:46.572 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5KT:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KT"}
[2025-05-30 12:29:46.650 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5KT:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KT"}
[2025-05-30 12:29:46.697 +05:30 INF] Request origin http://localhost:3001 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5KT:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KT"}
[2025-05-30 12:29:46.753 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 180.9653ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5KT:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KT"}
[2025-05-30 12:29:54.583 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5KV:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KV"}
[2025-05-30 12:29:54.622 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5KV:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KV"}
[2025-05-30 12:29:54.643 +05:30 INF] Request origin http://localhost:3001 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5KV:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KV"}
[2025-05-30 12:29:54.669 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 85.7809ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5KV:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5KV"}
[2025-05-30 12:30:16.918 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:30:16.946 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:30:16.961 +05:30 INF] Request origin http://localhost:3001 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:30:16.977 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 58.9951ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:30:20.442 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:30:20.516 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:30:20.549 +05:30 INF] Request origin http://localhost:3001 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:30:20.581 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 139.1879ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L1:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L1"}
[2025-05-30 12:36:23.056 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:23.109 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:23.189 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 132.6341ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:23.257 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 55 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:23.309 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:23.361 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:23.478 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"5de0bc40-8c6e-4a7f-83db-992841134ec9","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:28.598 +05:30 INF] Executed DbCommand (179ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"5de0bc40-8c6e-4a7f-83db-992841134ec9","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.328 +05:30 INF] Executed DbCommand (77ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"5de0bc40-8c6e-4a7f-83db-992841134ec9","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.446 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"5de0bc40-8c6e-4a7f-83db-992841134ec9","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.523 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 6970.2404ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.550 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.582 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 200 null application/json; charset=utf-8 7324.8709ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/auth/login","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.743 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.842 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:30.835 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L7:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L7"}
[2025-05-30 12:36:30.867 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:30.951 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:30.960 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:30.970 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L7:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L7"}
[2025-05-30 12:36:30.984 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:31.094 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 351.8506ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:31.096 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 261.3897ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:31.102 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 266.2086ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L7:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L7"}
[2025-05-30 12:36:31.107 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 240.371ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:31.262 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:31.251 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:31.354 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:31.359 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:31.563 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:31.563 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:31.643 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:31.652 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:32.080 +05:30 WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results. {"EventId":{"Id":10103,"Name":"Microsoft.EntityFrameworkCore.Query.FirstWithoutOrderByAndFilterWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:32.161 +05:30 INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:32.229 +05:30 INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:32.291 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:32.338 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 620.4413ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:32.374 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:32.406 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 1143.1091ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L8:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L8"}
[2025-05-30 12:36:32.422 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.495 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.519 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.541 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.633 +05:30 INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.691 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.708 +05:30 INF] Executed DbCommand (105ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:32.751 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 167.1229ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.768 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:32.820 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.873 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 1144.3254ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:32.915 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 493.712ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:32.920 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:32.983 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 1731.6807ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L3:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L3"}
[2025-05-30 12:36:32.993 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.063 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.086 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.108 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.154 +05:30 INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.229 +05:30 INF] Executed DbCommand (34ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.278 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.325 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 183.5851ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.364 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:33.401 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 407.9285ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5L9:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5L9"}
[2025-05-30 12:36:40.594 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:40.594 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:40.653 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:40.659 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:40.691 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 97.1969ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:40.693 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 99.2964ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:40.776 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:40.813 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:40.828 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:40.854 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:41.107 +05:30 WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'. {"EventId":{"Id":20504,"Name":"Microsoft.EntityFrameworkCore.Query.MultipleCollectionIncludeWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:41.314 +05:30 INF] Executed DbCommand (90ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:41.543 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:41.655 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 775.9684ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:41.684 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:41.703 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 926.872ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LC:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LC"}
[2025-05-30 12:36:41.716 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:41.764 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:41.784 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:41.806 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:41.918 +05:30 INF] Executed DbCommand (75ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:41.996 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:42.049 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 210.7924ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:42.097 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 12:36:42.130 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 413.0793ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LD:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LD"}
[2025-05-30 14:56:02.584 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:02.584 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:02.584 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LI:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LI"}
[2025-05-30 14:56:02.609 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:02.674 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:02.674 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:02.677 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LI:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LI"}
[2025-05-30 14:56:02.681 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:02.735 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 151.5034ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:02.735 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 150.9832ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:02.738 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 160.7312ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LI:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LI"}
[2025-05-30 14:56:02.739 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 129.8469ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:02.844 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:02.834 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:02.913 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:02.915 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:02.960 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:02.968 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:02.987 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:02.987 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.131 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.131 +05:30 INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:03.209 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.246 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 215.4986ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.269 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.288 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 453.4713ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.301 +05:30 INF] Executed DbCommand (78ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:03.333 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.340 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:03.376 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.379 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 351.2166ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:03.410 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.412 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:03.435 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.437 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 593.3304ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LK:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LK"}
[2025-05-30 14:56:03.455 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.478 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.514 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.518 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.545 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.548 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 76.2425ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.580 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.583 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.620 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.621 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 288.4073ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LJ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LJ"}
[2025-05-30 14:56:03.664 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.696 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.718 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 102.6193ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.735 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 14:56:03.751 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 296.4693ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LL:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LL"}
[2025-05-30 15:42:04.095 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:04.095 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:04.095 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.095 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.230 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:04.231 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:04.235 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.247 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.326 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 276.4464ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.326 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 276.3725ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:04.326 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 232.8645ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:04.329 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 279.0313ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.422 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.493 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.514 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.551 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.560 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 138.8922ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.581 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.588 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.614 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.616 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.667 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.695 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 78.965ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.745 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:04.812 +05:30 INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.900 +05:30 INF] Executed DbCommand (53ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.927 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.964 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 306.8735ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:04.996 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:05.021 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 527.9091ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:05.043 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.077 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.094 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.110 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.141 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.194 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.228 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.252 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 119.1136ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.264 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:05.278 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 235.5091ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.057 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.057 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:06.058 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.119 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.128 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:06.135 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.201 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 143.4396ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.204 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 147.1322ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:06.212 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 154.3238ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.318 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.343 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.390 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.395 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.429 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.433 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.454 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.456 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 61.6761ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.504 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.508 +05:30 INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.546 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.565 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 75.7323ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.587 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.608 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 290.47ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:06.620 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.696 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.711 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.733 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.768 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.814 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.839 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 79.4308ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.859 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:06.878 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 257.8399ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:16.660 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:16.660 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:16.662 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:16.662 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:16.785 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:16.792 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:16.798 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:16.802 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:16.882 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 222.2478ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:16.885 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 224.7283ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:16.889 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 227.5313ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:16.893 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 231.462ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:17.024 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.039 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.078 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.082 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.104 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.106 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.125 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.128 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.171 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.236 +05:30 INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.243 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.289 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 124.1184ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.305 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.317 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.320 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 296.1434ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.343 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.380 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.397 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.430 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 253.0323ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.431 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.454 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.456 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.484 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 445.1142ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:17.498 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.501 +05:30 INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.572 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.578 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.622 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.626 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 137.3329ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.659 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.662 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.694 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 351.2929ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:17.701 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.745 +05:30 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.767 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.785 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 92.9764ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.800 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:17.813 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 315.448ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:35.639 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:35.639 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:35.641 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:35.641 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:35.730 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:35.734 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:35.740 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:35.745 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:35.791 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 152.0547ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:35.793 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 154.0829ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:35.797 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 157.5816ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:35.799 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 158.3345ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:35.873 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:35.887 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:35.943 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:35.952 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:35.981 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:35.983 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.009 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:36.011 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.069 +05:30 INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:36.084 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.154 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:36.163 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.222 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:36.224 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 153.3594ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.266 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 210.2853ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:36.273 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.324 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:36.326 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 438.8689ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.335 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.378 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 505.0086ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:36.385 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.390 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.444 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.446 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.475 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.477 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.506 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.517 +05:30 INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.578 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.589 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.645 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 136.0561ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.651 +05:30 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.679 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.683 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.711 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 375.7244ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:36.715 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 144.2758ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.756 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:36.771 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 386.2808ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:39.843 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:39.844 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:39.909 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:39.915 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:39.948 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:39.952 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:39.983 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:39.985 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.054 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.081 +05:30 INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.162 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.165 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.203 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.208 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 144.9504ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.234 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 188.5369ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.237 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.274 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.279 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 435.0021ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.312 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 468.5112ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000A","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.321 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.368 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.380 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.425 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.427 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.447 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.448 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.477 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.483 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.530 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.532 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.563 +05:30 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.565 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 84.9919ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.595 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.597 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.629 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 103.7731ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.631 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 310.2984ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:40.659 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:40.680 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 311.3375ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.115 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.116 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.116 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:43.117 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.183 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.191 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.195 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:43.200 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.239 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 123.8338ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.241 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 125.4582ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.244 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 127.3993ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:42:43.245 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 128.2057ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.319 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.325 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.365 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.370 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.392 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.393 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.413 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.414 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.446 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.450 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.490 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.492 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.528 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.530 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 81.9373ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.555 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 112.1258ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.557 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.582 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.586 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 260.8824ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.598 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.629 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 310.0936ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:42:43.646 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.653 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.710 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.720 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.747 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.748 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.781 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.782 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.826 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.837 +05:30 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.883 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 106.0066ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.896 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.939 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.946 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:43.974 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 376.1615ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:43.977 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 144.8544ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:44.009 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:44.020 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 366.8836ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.168 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.169 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000C","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.233 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.241 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:0000000C","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.279 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 111.9272ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.284 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 114.3596ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000C","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.357 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.393 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.415 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.438 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.597 +05:30 INF] Executed DbCommand (116ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.673 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.700 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 232.6515ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.719 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.734 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 376.9667ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:42:52.744 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.776 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.792 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.809 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.841 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.870 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.892 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 58.9323ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.913 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:42:52.951 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 206.5975ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000D","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:26.898 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:26.899 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000D","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:26.923 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:26.944 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:27.075 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.082 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:0000000D","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.093 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:27.100 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:27.177 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 279.4519ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.204 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 304.7559ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000D","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.290 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 367.4795ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:27.293 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 348.425ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:27.303 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.312 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000E","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.471 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.478 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:0000000E","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.515 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.519 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 207.1421ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000E","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.568 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.583 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000E","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.665 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000F","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.687 +05:30 INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.730 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:0000000F","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.762 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 96.9711ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000F","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.783 +05:30 INF] Executed DbCommand (45ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.788 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:0000000F","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.830 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.865 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 227.6336ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.890 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.913 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 610.1557ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:27.937 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.977 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:27.998 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:28.024 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:28.078 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:28.141 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:28.164 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:28.183 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 112.3487ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:28.199 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:28.219 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 282.0735ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:34.792 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:34.792 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:34.824 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:34.828 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:34.850 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 58.1172ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:34.853 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 61.3783ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:34.885 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:34.908 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:34.921 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:34.937 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:35.024 +05:30 INF] Executed DbCommand (66ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:35.048 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:35.062 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 105.7544ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:35.075 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:35.087 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 201.927ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:35.103 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.120 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.134 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.147 +05:30 INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.176 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.205 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d14cf429-7543-4496-afa4-c76282a19f92","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.230 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 60.9517ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.248 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:35.265 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 162.2632ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/6","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.346 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:37.346 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:00000013","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.347 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:37.347 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.412 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:37.416 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:00000013","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.422 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:37.427 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.470 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 123.927ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:37.471 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 125.1247ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:00000013","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.474 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 126.6428ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000A","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.472 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 125.611ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:0000000F","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:37.604 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.618 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.691 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.696 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.721 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.722 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.751 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.755 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.826 +05:30 INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.826 +05:30 INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.885 +05:30 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.888 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.918 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.921 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 118.7318ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.949 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 131.7258ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.952 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.974 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:37.976 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 372.0896ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:37.989 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.004 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 386.1143ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000B","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:38.028 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.048 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.082 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.084 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.110 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.113 +05:30 INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.156 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.166 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.214 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.218 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"531826aa-0111-4619-973e-c3728ef7fd93","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.250 +05:30 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.253 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 91.8243ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.283 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.286 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.314 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 103.0462ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.317 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 328.6984ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/leads/dashboard-stats","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:38.346 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:38.369 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 341.0282ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:40.500 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:40.512 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:40.513 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:40.513 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:00000013","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:40.661 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:40.678 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:40.688 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:40.700 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:00000013","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:40.776 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 275.8872ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LQ:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LQ"}
[2025-05-30 15:43:40.779 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 267.5882ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LT:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LT"}
[2025-05-30 15:43:40.782 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 268.773ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000C","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:40.784 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 270.6677ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:00000013","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:40.915 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:40.927 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:40.979 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:40.987 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:41.035 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.041 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 113.4527ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:41.115 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.131 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:41.192 +05:30 INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.194 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:41.262 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.272 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:41.328 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.329 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 135.5723ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:41.359 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 176.4905ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.364 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LR:********","RequestPath":"/api/users","ConnectionId":"0HNCV8U55I5LR"}
[2025-05-30 15:43:41.388 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.406 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 491.4969ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000D","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.446 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.487 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.517 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.530 +05:30 INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.552 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.581 +05:30 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.611 +05:30 INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"339ccbbd-8ec3-4ce5-8e55-7c2f04d1c704","ActionName":"UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.630 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 81.8469ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.646 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:41.661 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 215.8756ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000E","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.066 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6/assign - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000F","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.090 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:0000000F","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.102 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6/assign - 204 null null 35.7528ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:0000000F","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.123 +05:30 INF] Request starting HTTP/1.1 PUT https://localhost:59358/api/leads/6/assign - application/json 44 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.142 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.160 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.193 +05:30 INF] Route matched with {action = "AssignLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AssignLead(Int32, UBI.CPV.API.Models.DTOs.AssignLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.290 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate]
FROM [Leads] AS [l]
WHERE [l].[LeadId] = @__p_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.331 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[UserId] = @__p_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.352 +05:30 INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.377 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API) in 168.5916ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.392 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 15:43:52.404 +05:30 INF] Request finished HTTP/1.1 PUT https://localhost:59358/api/leads/6/assign - 400 null application/json; charset=utf-8 281.1383ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LS:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LS"}
[2025-05-30 16:51:49.758 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6/assign - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:49.919 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:49.978 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6/assign - 204 null null 250.718ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:50.047 +05:30 INF] Request starting HTTP/1.1 PUT https://localhost:59358/api/leads/6/assign - application/json 44 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:50.083 +05:30 INF] CORS policy execution successful. {"EventId":{"Id":4,"Name":"PolicySuccess"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:50.266 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:50.323 +05:30 INF] Route matched with {action = "AssignLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] AssignLead(Int32, UBI.CPV.API.Models.DTOs.AssignLeadDto) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:51.045 +05:30 INF] Executed DbCommand (137ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate]
FROM [Leads] AS [l]
WHERE [l].[LeadId] = @__p_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:51.166 +05:30 INF] Executed DbCommand (17ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[UserId] = @__p_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:51.227 +05:30 INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"eec852f2-cd6f-4d5c-9397-37e06dad5f96","ActionName":"UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:51.266 +05:30 INF] Executed action UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API) in 889.5992ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:51.301 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.AssignLead (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 16:51:51.331 +05:30 INF] Request finished HTTP/1.1 PUT https://localhost:59358/api/leads/6/assign - 400 null application/json; charset=utf-8 1285.717ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5LV:********","RequestPath":"/api/leads/6/assign","ConnectionId":"0HNCV8U55I5LV"}
[2025-05-30 17:18:16.729 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
[2025-05-30 17:18:16.801 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
[2025-05-30 17:18:16.837 +05:30 INF] Request origin null does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
[2025-05-30 17:18:16.871 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads - 204 null null 146.1324ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
[2025-05-30 17:18:19.388 +05:30 INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
[2025-05-30 17:18:19.440 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
[2025-05-30 17:18:19.468 +05:30 INF] Request origin null does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
[2025-05-30 17:18:19.503 +05:30 INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 115.0672ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCV8U55I5M1:********","RequestPath":"/api/leads","ConnectionId":"0HNCV8U55I5M1"}
