2025-06-04 14:45:04.798 +05:30 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-04 14:45:05.041 +05:30 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-04 14:45:05.066 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-04 14:45:05.088 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-04 14:45:05.197 +05:30 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-04 14:45:05.243 +05:30 [INF] Applying migration '20250529130711_InitialCreate'.
2025-06-04 14:45:05.648 +05:30 [ERR] Failed executing DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
);
2025-06-04 14:45:05.896 +05:30 [ERR] An error occurred while creating the database or seeding data
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:d3b1188a-39f4-4cbb-9bd6-f940153209e4
Error Number:2714,State:6,Class:16
2025-06-04 14:45:05.948 +05:30 [INF] UBI-CPV API starting up...
2025-06-04 14:45:06.124 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 14:45:07.158 +05:30 [INF] Now listening on: https://localhost:59358
2025-06-04 14:45:07.174 +05:30 [INF] Now listening on: http://localhost:59359
2025-06-04 14:45:07.291 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 14:45:07.305 +05:30 [INF] Hosting environment: Development
2025-06-04 14:45:07.321 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-06-04 14:45:39.502 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/Auth/me - null null
2025-06-04 14:45:40.711 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 14:45:40.742 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 14:45:40.871 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/Auth/me - 401 0 null 1387.7688ms
2025-06-04 14:45:59.891 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - null null
2025-06-04 14:45:59.945 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:45:59.987 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/auth/login - 204 null null 96.7223ms
2025-06-04 14:46:00.067 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/auth/login - application/json 55
2025-06-04 14:46:00.096 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:00.116 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-06-04 14:46:00.194 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API).
2025-06-04 14:46:01.408 +05:30 [INF] Executed DbCommand (355ms) [Parameters=[@__request_Username_0='?' (Size = 50), @__request_Role_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[CreatedBy], [u].[CreatedDate], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Username] = @__request_Username_0 AND [u].[Role] = @__request_Role_1 AND [u].[IsActive] = CAST(1 AS bit)
2025-06-04 14:46:02.892 +05:30 [INF] Executed DbCommand (110ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Boolean), @p3='?' (Size = 500), @p4='?' (Size = 500), @p5='?' (DbType = Int32), @p7='?' (DbType = Int32), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserSessions] ([CreatedAt], [ExpiresAt], [IsActive], [RefreshToken], [Token], [UserId])
OUTPUT INSERTED.[SessionId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5);
UPDATE [Users] SET [LastLoginDate] = @p6
OUTPUT 1
WHERE [UserId] = @p7;
2025-06-04 14:46:03.013 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'.
2025-06-04 14:46:03.131 +05:30 [INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 2864.9083ms
2025-06-04 14:46:03.161 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)'
2025-06-04 14:46:03.184 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/auth/login - 200 null application/json; charset=utf-8 3117.0699ms
2025-06-04 14:46:03.291 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:03.376 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:03.376 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:03.376 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:03.424 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:03.429 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:03.435 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:03.442 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:03.491 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 203.7188ms
2025-06-04 14:46:03.493 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 117.11ms
2025-06-04 14:46:03.496 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 119.7862ms
2025-06-04 14:46:03.500 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 123.1337ms
2025-06-04 14:46:03.591 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:03.597 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:03.656 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:03.661 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:03.808 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:03.808 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:03.832 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:03.833 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:04.046 +05:30 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-06-04 14:46:04.114 +05:30 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 14:46:04.214 +05:30 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 14:46:04.242 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 14:46:04.261 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 400.0387ms
2025-06-04 14:46:04.285 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:04.299 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 701.9209ms
2025-06-04 14:46:04.313 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:04.381 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:04.396 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:04.410 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:04.455 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 14:46:04.475 +05:30 [INF] Executed DbCommand (107ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 14:46:04.503 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 14:46:04.510 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 14:46:04.538 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 107.2715ms
2025-06-04 14:46:04.561 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 697.7519ms
2025-06-04 14:46:04.581 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:04.583 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:04.618 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 305.6237ms
2025-06-04 14:46:04.621 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 1030.6283ms
2025-06-04 14:46:04.680 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:04.717 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:04.737 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:04.757 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:04.792 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 14:46:04.845 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 14:46:04.881 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 14:46:04.912 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 130.7576ms
2025-06-04 14:46:04.948 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:04.982 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 306.2223ms
2025-06-04 14:46:09.726 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-06-04 14:46:09.726 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-06-04 14:46:09.789 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:09.796 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:09.826 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 99.9477ms
2025-06-04 14:46:09.828 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 102.0748ms
2025-06-04 14:46:09.875 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-06-04 14:46:09.952 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:09.968 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 14:46:09.993 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:10.150 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 14:46:10.276 +05:30 [INF] Executed DbCommand (74ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-06-04 14:46:10.466 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-06-04 14:46:10.541 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 528.4932ms
2025-06-04 14:46:10.559 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 14:46:10.577 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 702.2705ms
2025-06-04 14:46:10.607 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-06-04 14:46:10.628 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:10.660 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 14:46:10.686 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:10.809 +05:30 [INF] Executed DbCommand (73ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-06-04 14:46:10.837 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-06-04 14:46:10.864 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 130.4148ms
2025-06-04 14:46:10.898 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 14:46:10.941 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 334.2297ms
2025-06-04 14:46:34.988 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-06-04 14:46:34.988 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 14:46:35.025 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 14:46:35.025 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-06-04 14:46:35.105 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.112 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.116 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.121 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.172 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 184.2236ms
2025-06-04 14:46:35.174 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 186.1438ms
2025-06-04 14:46:35.176 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 151.4721ms
2025-06-04 14:46:35.178 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 204 null null 153.2262ms
2025-06-04 14:46:35.273 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:46:35.278 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-06-04 14:46:35.310 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.315 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.348 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 74.8693ms
2025-06-04 14:46:35.356 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:35.395 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:46:35.396 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:35.469 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:46:35.499 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 14:46:35.542 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.563 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 93.4373ms
2025-06-04 14:46:35.580 +05:30 [INF] Executed DbCommand (31ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 14:46:35.607 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:46:35.611 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 14:46:35.657 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 178.514ms
2025-06-04 14:46:35.694 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:35.719 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 440.8614ms
2025-06-04 14:46:35.759 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - null null
2025-06-04 14:46:35.794 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:35.809 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:35.822 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:35.858 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 14:46:35.924 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 14:46:35.947 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 14:46:35.963 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 114.3253ms
2025-06-04 14:46:35.982 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:35.998 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=20 - 200 null application/json; charset=utf-8 239.0178ms
2025-06-04 14:46:49.042 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:49.059 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:49.066 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:49.072 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:49.124 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.130 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.136 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.141 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.193 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 151.3694ms
2025-06-04 14:46:49.194 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 135.7662ms
2025-06-04 14:46:49.196 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 130.4023ms
2025-06-04 14:46:49.198 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 125.9933ms
2025-06-04 14:46:49.278 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:49.272 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:49.326 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.331 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.357 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:49.359 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:49.381 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:49.382 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:49.442 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 14:46:49.447 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 14:46:49.492 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 14:46:49.510 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 93.0137ms
2025-06-04 14:46:49.526 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:49.541 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 263.3836ms
2025-06-04 14:46:49.543 +05:30 [INF] Executed DbCommand (57ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 14:46:49.548 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 14:46:49.599 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 14:46:49.606 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.638 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 212.9062ms
2025-06-04 14:46:49.640 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:49.664 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:49.666 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:49.699 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 427.0572ms
2025-06-04 14:46:49.709 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 14:46:49.730 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 14:46:49.767 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:46:49.772 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 14:46:49.800 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:49.802 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 100.1353ms
2025-06-04 14:46:49.826 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 14:46:49.828 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 14:46:49.859 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 311.163ms
2025-06-04 14:46:49.868 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 14:46:49.929 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 14:46:49.955 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 14:46:49.977 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 121.1659ms
2025-06-04 14:46:49.998 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 14:46:50.014 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 305.307ms
2025-06-04 14:55:35.151 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/swagger/index.html - null null
2025-06-04 14:55:35.537 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/swagger/index.html - 404 0 null 386.5799ms
2025-06-04 14:55:35.579 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/swagger/index.html, Response status code: 404
2025-06-04 14:55:40.565 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 14:55:40.565 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 14:55:40.631 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:55:40.639 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:55:40.675 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 109.8115ms
2025-06-04 14:55:40.676 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 111.0496ms
2025-06-04 14:55:40.715 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:55:40.737 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:55:40.754 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 38.8518ms
2025-06-04 14:55:40.791 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:55:40.817 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:55:40.849 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:55:40.866 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 49.4615ms
2025-06-04 14:55:40.890 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:55:43.234 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:55:43.258 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:55:43.291 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 57.4621ms
2025-06-04 14:55:43.342 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:55:43.387 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:55:43.422 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:55:43.440 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 54.3719ms
2025-06-04 14:55:43.470 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:56:39.308 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 14:56:39.381 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:56:39.399 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 91.3106ms
2025-06-04 14:56:39.427 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:56:39.448 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:56:39.458 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 31.8221ms
2025-06-04 14:56:39.491 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:56:43.951 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:56:43.985 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:56:44.012 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 61.0881ms
2025-06-04 14:56:44.055 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 14:56:44.075 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 14:56:44.100 +05:30 [INF] CORS policy execution successful.
2025-06-04 14:56:44.123 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 48.6387ms
2025-06-04 14:56:44.177 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:01:26.348 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:01:26.386 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:01:26.402 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 54.064ms
2025-06-04 15:01:26.432 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 15:01:26.468 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:01:26.485 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 52.7737ms
2025-06-04 15:01:26.509 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:06:08.290 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:06:08.359 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:06:08.395 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:06:08.402 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:06:08.428 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 138.5391ms
2025-06-04 15:06:08.431 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 72.3973ms
2025-06-04 15:06:08.480 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 15:06:08.505 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:06:08.524 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 43.8507ms
2025-06-04 15:06:08.553 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:06:08.568 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 15:06:08.587 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:06:08.602 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 33.9867ms
2025-06-04 15:06:08.618 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:08:24.813 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:08:24.821 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:08:24.872 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:24.882 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:24.914 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 100.4473ms
2025-06-04 15:08:24.917 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 95.8489ms
2025-06-04 15:08:24.990 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 15:08:25.038 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:25.065 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 75.0965ms
2025-06-04 15:08:25.128 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:08:25.156 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 15:08:25.178 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:25.196 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 39.3635ms
2025-06-04 15:08:25.219 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:08:27.744 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:08:27.744 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:08:27.844 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:08:27.854 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:08:27.924 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:27.928 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:27.933 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:27.938 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:27.986 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 241.7587ms
2025-06-04 15:08:27.988 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 243.4262ms
2025-06-04 15:08:27.990 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 146.2102ms
2025-06-04 15:08:27.993 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 138.3963ms
2025-06-04 15:08:28.066 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:08:28.059 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:08:28.125 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:28.165 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:28.211 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:08:28.211 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:08:28.249 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:08:28.249 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:08:28.545 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 15:08:28.545 +05:30 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 15:08:28.591 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 15:08:28.623 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 324.4638ms
2025-06-04 15:08:28.643 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:08:28.659 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 600.5158ms
2025-06-04 15:08:28.666 +05:30 [INF] Executed DbCommand (54ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 15:08:28.701 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:08:28.709 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 15:08:28.747 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:28.752 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 459.3293ms
2025-06-04 15:08:28.778 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:08:28.779 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:08:28.801 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:08:28.803 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 736.6797ms
2025-06-04 15:08:28.867 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:08:28.869 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 15:08:28.920 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:28.929 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 15:08:28.972 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:08:28.975 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 123.2972ms
2025-06-04 15:08:29.010 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:08:29.013 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:08:29.046 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 345.6887ms
2025-06-04 15:08:29.065 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 15:08:29.134 +05:30 [INF] Executed DbCommand (30ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 15:08:29.172 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 15:08:29.199 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 155.9709ms
2025-06-04 15:08:29.215 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:08:29.230 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 363.9588ms
2025-06-04 15:08:33.635 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:08:33.635 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:08:33.688 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:33.695 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:33.717 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 81.7324ms
2025-06-04 15:08:33.719 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 83.884ms
2025-06-04 15:08:33.765 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 15:08:33.788 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:33.800 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 35.4231ms
2025-06-04 15:08:33.823 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:08:33.840 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/users - null null
2025-06-04 15:08:33.867 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:08:33.884 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/users - 404 0 null 43.782ms
2025-06-04 15:08:33.904 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:10:46.211 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users - null null
2025-06-04 15:10:46.244 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:10:46.276 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users - 204 null null 64.2663ms
2025-06-04 15:10:46.314 +05:30 [INF] Request starting HTTP/1.1 POST https://localhost:59358/api/users - application/json 139
2025-06-04 15:10:46.344 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:10:46.367 +05:30 [INF] Request finished HTTP/1.1 POST https://localhost:59358/api/users - 404 0 null 52.6593ms
2025-06-04 15:10:46.404 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:59358/api/users, Response status code: 404
2025-06-04 15:11:47.075 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/users/1/toggle-status - null null
2025-06-04 15:11:47.116 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:47.127 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/users/1/toggle-status - 204 null null 52.319ms
2025-06-04 15:11:47.163 +05:30 [INF] Request starting HTTP/1.1 PUT https://localhost:59358/api/users/1/toggle-status - null 0
2025-06-04 15:11:47.184 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:47.198 +05:30 [INF] Request finished HTTP/1.1 PUT https://localhost:59358/api/users/1/toggle-status - 404 0 null 35.5147ms
2025-06-04 15:11:47.230 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: PUT https://localhost:59358/api/users/1/toggle-status, Response status code: 404
2025-06-04 15:11:51.474 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:11:51.577 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:11:51.577 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:11:51.577 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:11:51.641 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:51.646 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:51.649 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:51.653 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:51.693 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 231.7095ms
2025-06-04 15:11:51.695 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 118.8683ms
2025-06-04 15:11:51.698 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 121.1217ms
2025-06-04 15:11:51.701 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 123.7396ms
2025-06-04 15:11:51.766 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:11:51.777 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:11:51.836 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:51.843 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:51.868 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:11:51.870 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:11:51.891 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:11:51.893 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:11:51.936 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 15:11:51.941 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 15:11:51.967 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 15:11:51.981 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 50.5961ms
2025-06-04 15:11:51.987 +05:30 [INF] Executed DbCommand (22ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 15:11:52.006 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:11:52.012 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 15:11:52.036 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 258.6326ms
2025-06-04 15:11:52.039 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 113.3283ms
2025-06-04 15:11:52.065 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:11:52.067 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:11:52.089 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:52.091 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 324.8156ms
2025-06-04 15:11:52.099 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:11:52.123 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:11:52.133 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:52.155 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:11:52.156 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:11:52.186 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:11:52.192 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 15:11:52.226 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 15:11:52.233 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 15:11:52.270 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 85.9885ms
2025-06-04 15:11:52.272 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 15:11:52.308 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:11:52.317 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 15:11:52.364 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 298.8486ms
2025-06-04 15:11:52.370 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 146.615ms
2025-06-04 15:11:52.439 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:11:52.480 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 381.5514ms
2025-06-04 15:11:59.684 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-06-04 15:11:59.684 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - null null
2025-06-04 15:11:59.735 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:59.743 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:59.784 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 100.7705ms
2025-06-04 15:11:59.787 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/6 - 204 null null 103.7685ms
2025-06-04 15:11:59.835 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-06-04 15:11:59.866 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:11:59.884 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 15:11:59.901 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:12:00.039 +05:30 [INF] Executed DbCommand (101ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-06-04 15:12:00.094 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-06-04 15:12:00.123 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 196.8081ms
2025-06-04 15:12:00.143 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 15:12:00.157 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 322.2038ms
2025-06-04 15:12:00.168 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/6 - null null
2025-06-04 15:12:00.220 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:00.254 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 15:12:00.274 +05:30 [INF] Route matched with {action = "GetLead", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LeadDto]] GetLead(Int32) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:12:00.302 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[ApprovedDate], [t].[AssignedDate], [t].[AssignedTo], [t].[CreatedBy], [t].[CreatedDate], [t].[CustomerName], [t].[LoanType], [t].[MobileNumber], [t].[RejectedDate], [t].[RejectionReason], [t].[ReviewComments], [t].[ReviewedBy], [t].[ReviewedDate], [t].[StartedDate], [t].[Status], [t].[SubmittedDate], [t].[UserId], [t].[CreatedBy0], [t].[CreatedDate0], [t].[Email], [t].[FirstName], [t].[IsActive], [t].[LastLoginDate], [t].[LastName], [t].[PasswordHash], [t].[PhoneNumber], [t].[Role], [t].[Salt], [t].[Username], [t].[UserId0], [t].[CreatedBy1], [t].[CreatedDate1], [t].[Email0], [t].[FirstName0], [t].[IsActive0], [t].[LastLoginDate0], [t].[LastName0], [t].[PasswordHash0], [t].[PhoneNumber0], [t].[Role0], [t].[Salt0], [t].[Username0], [t].[UserId1], [t].[CreatedBy2], [t].[CreatedDate2], [t].[Email1], [t].[FirstName1], [t].[IsActive1], [t].[LastLoginDate1], [t].[LastName1], [t].[PasswordHash1], [t].[PhoneNumber1], [t].[Role1], [t].[Salt1], [t].[Username1], [t].[VerificationId], [l0].[AddressId], [l0].[Address], [l0].[AddressType], [l0].[CreatedDate], [l0].[District], [l0].[Landmark], [l0].[LeadId], [l0].[Pincode], [l0].[State], [t0].[HistoryId], [t0].[Comments], [t0].[LeadId], [t0].[Status], [t0].[Timestamp], [t0].[UpdatedBy], [t0].[UserId], [t0].[CreatedBy], [t0].[CreatedDate], [t0].[Email], [t0].[FirstName], [t0].[IsActive], [t0].[LastLoginDate], [t0].[LastName], [t0].[PasswordHash], [t0].[PhoneNumber], [t0].[Role], [t0].[Salt], [t0].[Username], [t1].[DocumentId], [t1].[DocumentTypeId], [t1].[FileName], [t1].[FilePath], [t1].[FileSize], [t1].[IsActive], [t1].[LeadId], [t1].[MimeType], [t1].[OriginalFileName], [t1].[UploadedBy], [t1].[UploadedDate], [t1].[DocumentTypeId0], [t1].[Description], [t1].[IsActive0], [t1].[TypeName], [t1].[UserId], [t1].[CreatedBy], [t1].[CreatedDate], [t1].[Email], [t1].[FirstName], [t1].[IsActive1], [t1].[LastLoginDate], [t1].[LastName], [t1].[PasswordHash], [t1].[PhoneNumber], [t1].[Role], [t1].[Salt], [t1].[Username], [t2].[ImageId], [t2].[CreatedBy], [t2].[CreatedDate], [t2].[CropData], [t2].[FileName], [t2].[FilePath], [t2].[FileSize], [t2].[LeadId], [t2].[MimeType], [t2].[OriginalFileName], [t2].[PageNumber], [t2].[UserId], [t2].[CreatedBy0], [t2].[CreatedDate0], [t2].[Email], [t2].[FirstName], [t2].[IsActive], [t2].[LastLoginDate], [t2].[LastName], [t2].[PasswordHash], [t2].[PhoneNumber], [t2].[Role], [t2].[Salt], [t2].[Username], [t].[AdditionalNotes], [t].[AddressConfirmed], [t].[AgentContact], [t].[AgentName], [t].[BusinessNature], [t].[CompanyType], [t].[EmployeesCount], [t].[EstablishmentYear], [t].[GrossSalary], [t].[Landmark], [t].[LeadId0], [t].[NetSalary], [t].[OfficeAddress], [t].[OfficeDistrict], [t].[OfficePincode], [t].[OfficeState], [t].[PersonMet], [t].[ProofType], [t].[Relationship], [t].[VerificationDate]
FROM (
    SELECT TOP(1) [l].[LeadId], [l].[ApprovedDate], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[RejectedDate], [l].[RejectionReason], [l].[ReviewComments], [l].[ReviewedBy], [l].[ReviewedDate], [l].[StartedDate], [l].[Status], [l].[SubmittedDate], [u].[UserId], [u].[CreatedBy] AS [CreatedBy0], [u].[CreatedDate] AS [CreatedDate0], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[LastLoginDate], [u].[LastName], [u].[PasswordHash], [u].[PhoneNumber], [u].[Role], [u].[Salt], [u].[Username], [u0].[UserId] AS [UserId0], [u0].[CreatedBy] AS [CreatedBy1], [u0].[CreatedDate] AS [CreatedDate1], [u0].[Email] AS [Email0], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[Role] AS [Role0], [u0].[Salt] AS [Salt0], [u0].[Username] AS [Username0], [u1].[UserId] AS [UserId1], [u1].[CreatedBy] AS [CreatedBy2], [u1].[CreatedDate] AS [CreatedDate2], [u1].[Email] AS [Email1], [u1].[FirstName] AS [FirstName1], [u1].[IsActive] AS [IsActive1], [u1].[LastLoginDate] AS [LastLoginDate1], [u1].[LastName] AS [LastName1], [u1].[PasswordHash] AS [PasswordHash1], [u1].[PhoneNumber] AS [PhoneNumber1], [u1].[Role] AS [Role1], [u1].[Salt] AS [Salt1], [u1].[Username] AS [Username1], [v].[VerificationId], [v].[AdditionalNotes], [v].[AddressConfirmed], [v].[AgentContact], [v].[AgentName], [v].[BusinessNature], [v].[CompanyType], [v].[EmployeesCount], [v].[EstablishmentYear], [v].[GrossSalary], [v].[Landmark], [v].[LeadId] AS [LeadId0], [v].[NetSalary], [v].[OfficeAddress], [v].[OfficeDistrict], [v].[OfficePincode], [v].[OfficeState], [v].[PersonMet], [v].[ProofType], [v].[Relationship], [v].[VerificationDate]
    FROM [Leads] AS [l]
    INNER JOIN [Users] AS [u] ON [l].[CreatedBy] = [u].[UserId]
    LEFT JOIN [Users] AS [u0] ON [l].[AssignedTo] = [u0].[UserId]
    LEFT JOIN [Users] AS [u1] ON [l].[ReviewedBy] = [u1].[UserId]
    LEFT JOIN [VerificationData] AS [v] ON [l].[LeadId] = [v].[LeadId]
    WHERE [l].[LeadId] = @__id_0
) AS [t]
LEFT JOIN [LeadAddresses] AS [l0] ON [t].[LeadId] = [l0].[LeadId]
LEFT JOIN (
    SELECT [l1].[HistoryId], [l1].[Comments], [l1].[LeadId], [l1].[Status], [l1].[Timestamp], [l1].[UpdatedBy], [u2].[UserId], [u2].[CreatedBy], [u2].[CreatedDate], [u2].[Email], [u2].[FirstName], [u2].[IsActive], [u2].[LastLoginDate], [u2].[LastName], [u2].[PasswordHash], [u2].[PhoneNumber], [u2].[Role], [u2].[Salt], [u2].[Username]
    FROM [LeadStatusHistory] AS [l1]
    INNER JOIN [Users] AS [u2] ON [l1].[UpdatedBy] = [u2].[UserId]
) AS [t0] ON [t].[LeadId] = [t0].[LeadId]
LEFT JOIN (
    SELECT [l2].[DocumentId], [l2].[DocumentTypeId], [l2].[FileName], [l2].[FilePath], [l2].[FileSize], [l2].[IsActive], [l2].[LeadId], [l2].[MimeType], [l2].[OriginalFileName], [l2].[UploadedBy], [l2].[UploadedDate], [d].[DocumentTypeId] AS [DocumentTypeId0], [d].[Description], [d].[IsActive] AS [IsActive0], [d].[TypeName], [u3].[UserId], [u3].[CreatedBy], [u3].[CreatedDate], [u3].[Email], [u3].[FirstName], [u3].[IsActive] AS [IsActive1], [u3].[LastLoginDate], [u3].[LastName], [u3].[PasswordHash], [u3].[PhoneNumber], [u3].[Role], [u3].[Salt], [u3].[Username]
    FROM [LeadDocuments] AS [l2]
    INNER JOIN [DocumentTypes] AS [d] ON [l2].[DocumentTypeId] = [d].[DocumentTypeId]
    INNER JOIN [Users] AS [u3] ON [l2].[UploadedBy] = [u3].[UserId]
) AS [t1] ON [t].[LeadId] = [t1].[LeadId]
LEFT JOIN (
    SELECT [c].[ImageId], [c].[CreatedBy], [c].[CreatedDate], [c].[CropData], [c].[FileName], [c].[FilePath], [c].[FileSize], [c].[LeadId], [c].[MimeType], [c].[OriginalFileName], [c].[PageNumber], [u4].[UserId], [u4].[CreatedBy] AS [CreatedBy0], [u4].[CreatedDate] AS [CreatedDate0], [u4].[Email], [u4].[FirstName], [u4].[IsActive], [u4].[LastLoginDate], [u4].[LastName], [u4].[PasswordHash], [u4].[PhoneNumber], [u4].[Role], [u4].[Salt], [u4].[Username]
    FROM [CroppedImages] AS [c]
    INNER JOIN [Users] AS [u4] ON [c].[CreatedBy] = [u4].[UserId]
) AS [t2] ON [t].[LeadId] = [t2].[LeadId]
ORDER BY [t].[LeadId], [t].[UserId], [t].[UserId0], [t].[UserId1], [t].[VerificationId], [l0].[AddressId], [t0].[HistoryId], [t0].[UserId], [t1].[DocumentId], [t1].[DocumentTypeId0], [t1].[UserId], [t2].[ImageId]
2025-06-04 15:12:00.352 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LeadDto'.
2025-06-04 15:12:00.374 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API) in 77.5129ms
2025-06-04 15:12:00.390 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLead (UBI.CPV.API)'
2025-06-04 15:12:00.404 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/6 - 200 null application/json; charset=utf-8 235.6113ms
2025-06-04 15:12:08.134 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:12:08.134 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:12:08.134 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:12:08.134 +05:30 [INF] Request starting HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:12:08.216 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.220 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.225 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.230 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.267 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 133.3498ms
2025-06-04 15:12:08.269 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 135.4845ms
2025-06-04 15:12:08.270 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads/dashboard-stats - 204 null null 136.1446ms
2025-06-04 15:12:08.268 +05:30 [INF] Request finished HTTP/1.1 OPTIONS https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 204 null null 134.4233ms
2025-06-04 15:12:08.327 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:12:08.339 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:12:08.367 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.370 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.390 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:12:08.390 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:12:08.411 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:12:08.414 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:12:08.449 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 15:12:08.453 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 15:12:08.483 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 15:12:08.489 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 15:12:08.514 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 69.3303ms
2025-06-04 15:12:08.518 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 15:12:08.542 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:12:08.544 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 95.2169ms
2025-06-04 15:12:08.566 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 238.5883ms
2025-06-04 15:12:08.567 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:12:08.571 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - null null
2025-06-04 15:12:08.605 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 266.4369ms
2025-06-04 15:12:08.608 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.649 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - null null
2025-06-04 15:12:08.651 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:12:08.675 +05:30 [INF] CORS policy execution successful.
2025-06-04 15:12:08.677 +05:30 [INF] Route matched with {action = "GetDashboardStats", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.DashboardStatsDto]] GetDashboardStats() on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:12:08.705 +05:30 [INF] Executing endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:12:08.712 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) COUNT(*) AS [TotalLeads], COUNT(CASE
    WHEN [t].[Status] = N'new' THEN 1
END) AS [NewLeads], COUNT(CASE
    WHEN [t].[Status] = N'assigned' THEN 1
END) AS [AssignedLeads], COUNT(CASE
    WHEN [t].[Status] = N'in-progress' THEN 1
END) AS [InProgressLeads], COUNT(CASE
    WHEN [t].[Status] = N'pending-review' THEN 1
END) AS [PendingReviewLeads], COUNT(CASE
    WHEN [t].[Status] = N'approved' THEN 1
END) AS [ApprovedLeads], COUNT(CASE
    WHEN [t].[Status] = N'rejected' THEN 1
END) AS [RejectedLeads]
FROM (
    SELECT [l].[Status], 1 AS [Key]
    FROM [Leads] AS [l]
) AS [t]
GROUP BY [t].[Key]
2025-06-04 15:12:08.740 +05:30 [INF] Route matched with {action = "GetLeads", controller = "Leads"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.PagedResultDto`1[UBI.CPV.API.Models.DTOs.LeadListDto]]] GetLeads(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller UBI.CPV.API.Controllers.LeadsController (UBI.CPV.API).
2025-06-04 15:12:08.746 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.DashboardStatsDto'.
2025-06-04 15:12:08.782 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API) in 74.3083ms
2025-06-04 15:12:08.782 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Leads] AS [l]
2025-06-04 15:12:08.814 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetDashboardStats (UBI.CPV.API)'
2025-06-04 15:12:08.823 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[LeadId], [t].[CustomerName], [t].[MobileNumber], [t].[LoanType], [t].[Status], [t].[CreatedDate], [t].[AssignedDate], CAST(1 AS bit), [u].[FirstName], [u].[LastName], CASE
    WHEN [u0].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u0].[FirstName], [u0].[LastName], CASE
    WHEN [u1].[UserId] IS NOT NULL THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END, [u1].[FirstName], [u1].[LastName], (
    SELECT COUNT(*)
    FROM [LeadDocuments] AS [l0]
    WHERE [t].[LeadId] = [l0].[LeadId] AND [l0].[IsActive] = CAST(1 AS bit)), (
    SELECT COUNT(*)
    FROM [CroppedImages] AS [c]
    WHERE [t].[LeadId] = [c].[LeadId])
FROM (
    SELECT [l].[LeadId], [l].[AssignedDate], [l].[AssignedTo], [l].[CreatedBy], [l].[CreatedDate], [l].[CustomerName], [l].[LoanType], [l].[MobileNumber], [l].[ReviewedBy], [l].[Status]
    FROM [Leads] AS [l]
    ORDER BY [l].[CreatedDate] DESC
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Users] AS [u] ON [t].[CreatedBy] = [u].[UserId]
LEFT JOIN [Users] AS [u0] ON [t].[AssignedTo] = [u0].[UserId]
LEFT JOIN [Users] AS [u1] ON [t].[ReviewedBy] = [u1].[UserId]
ORDER BY [t].[CreatedDate] DESC
2025-06-04 15:12:08.860 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads/dashboard-stats - 200 null application/json; charset=utf-8 288.6907ms
2025-06-04 15:12:08.865 +05:30 [INF] Executing OkObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.PagedResultDto`1[[UBI.CPV.API.Models.DTOs.LeadListDto, UBI.CPV.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-04 15:12:08.897 +05:30 [INF] Executed action UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API) in 119.5945ms
2025-06-04 15:12:08.909 +05:30 [INF] Executed endpoint 'UBI.CPV.API.Controllers.LeadsController.GetLeads (UBI.CPV.API)'
2025-06-04 15:12:08.921 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/api/leads?pageNumber=1&pageSize=10 - 200 null application/json; charset=utf-8 272.3557ms
[2025-06-04 16:09:15.474 +05:30 INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-04 16:09:15.909 +05:30 INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-04 16:09:15.956 +05:30 INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-04 16:09:15.991 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-04 16:09:16.124 +05:30 INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId]; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-04 16:09:16.201 +05:30 INF] Applying migration '20250529130711_InitialCreate'. {"EventId":{"Id":20402,"Name":"Microsoft.EntityFrameworkCore.Migrations.MigrationApplying"},"SourceContext":"Microsoft.EntityFrameworkCore.Migrations"}
[2025-06-04 16:09:16.859 +05:30 ERR] Failed executing DbCommand (99ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
); {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-06-04 16:09:17.077 +05:30 ERR] An error occurred while creating the database or seeding data {}
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:56bd1d30-e385-40f7-8ab0-e757ab60dc98
Error Number:2714,State:6,Class:16
[2025-06-04 16:09:17.166 +05:30 INF] UBI-CPV API starting up... {}
[2025-06-04 16:09:17.344 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-06-04 16:09:18.361 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-04 16:09:18.393 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-04 16:09:18.625 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-04 16:09:18.652 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-04 16:09:18.666 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-06-04 16:09:19.710 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND3AG7KQD5U:00000001","RequestPath":"/","ConnectionId":"0HND3AG7KQD5U"}
[2025-06-04 16:09:22.010 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 2301.1301ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND3AG7KQD5U:00000001","RequestPath":"/","ConnectionId":"0HND3AG7KQD5U"}
[2025-06-04 16:09:22.044 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HND3AG7KQD5U:00000001","RequestPath":"/","ConnectionId":"0HND3AG7KQD5U"}
