/*! For license information please see main.79ac5a1e.js.LICENSE.txt */
(()=>{var e={4:(e,t,n)=>{"use strict";var r=n(853),o=n(43),a=n(950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function c(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function u(e){if(s(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),x=Symbol.for("react.provider"),b=Symbol.for("react.consumer"),w=Symbol.for("react.context"),j=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),k=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),E=Symbol.for("react.lazy");Symbol.for("react.scope");var T=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var A=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var P=Symbol.iterator;function R(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=P&&e[P]||e["@@iterator"])?e:null}var N=Symbol.for("react.client.reference");function _(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===N?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case y:return"Profiler";case v:return"StrictMode";case S:return"Suspense";case k:return"SuspenseList";case T:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case w:return(e.displayName||"Context")+".Provider";case b:return(e._context.displayName||"Context")+".Consumer";case j:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(t=e.displayName||null)?t:_(e.type)||"Memo";case E:t=e._payload,e=e._init;try{return _(e(t))}catch(n){}}return null}var L=Array.isArray,O=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z={pending:!1,data:null,method:null,action:null},I=[],F=-1;function U(e){return{current:e}}function M(e){0>F||(e.current=I[F],I[F]=null,F--)}function B(e,t){F++,I[F]=e.current,e.current=t}var H=U(null),W=U(null),$=U(null),V=U(null);function q(e,t){switch(B($,t),B(W,e),B(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?od(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ad(t=od(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}M(H),B(H,e)}function G(){M(H),M(W),M($)}function K(e){null!==e.memoizedState&&B(V,e);var t=H.current,n=ad(t,e.type);t!==n&&(B(W,e),B(H,n))}function Q(e){W.current===e&&(M(H),M(W)),V.current===e&&(M(V),Kd._currentValue=z)}var J=Object.prototype.hasOwnProperty,Y=r.unstable_scheduleCallback,Z=r.unstable_cancelCallback,X=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,oe=r.unstable_UserBlockingPriority,ae=r.unstable_NormalPriority,ie=r.unstable_LowPriority,le=r.unstable_IdlePriority,se=r.log,ce=r.unstable_setDisableYieldValue,ue=null,de=null;function fe(e){if("function"===typeof se&&ce(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ue,e)}catch(t){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(he(e)/me|0)|0},he=Math.log,me=Math.LN2;var ge=256,ve=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function xe(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var o=0,a=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var l=134217727&r;return 0!==l?0!==(r=l&~a)?o=ye(r):0!==(i&=l)?o=ye(i):n||0!==(n=l&~e)&&(o=ye(n)):0!==(l=r&~a)?o=ye(l):0!==i?o=ye(i):n||0!==(n=r&~e)&&(o=ye(n)),0===o?0:0!==t&&t!==o&&0===(t&a)&&((a=o&-o)>=(n=t&-t)||32===a&&0!==(4194048&n))?t:o}function be(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function we(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function je(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Se(){var e=ve;return 0===(62914560&(ve<<=1))&&(ve=4194304),e}function ke(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ce(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ee(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Te(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function Ae(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Pe(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Re(){var e=D.p;return 0!==e?e:void 0===(e=window.event)?32:uf(e.type)}var Ne=Math.random().toString(36).slice(2),_e="__reactFiber$"+Ne,Le="__reactProps$"+Ne,Oe="__reactContainer$"+Ne,De="__reactEvents$"+Ne,ze="__reactListeners$"+Ne,Ie="__reactHandles$"+Ne,Fe="__reactResources$"+Ne,Ue="__reactMarker$"+Ne;function Me(e){delete e[_e],delete e[Le],delete e[De],delete e[ze],delete e[Ie]}function Be(e){var t=e[_e];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Oe]||n[_e]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=xd(e);null!==e;){if(n=e[_e])return n;e=xd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[_e]||e[Oe]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function We(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function $e(e){var t=e[Fe];return t||(t=e[Fe]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[Ue]=!0}var qe=new Set,Ge={};function Ke(e,t){Qe(e,t),Qe(e+"Capture",t)}function Qe(e,t){for(Ge[e]=t,e=0;e<t.length;e++)qe.add(t[e])}var Je,Ye,Ze=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Xe={},et={};function tt(e,t,n){if(o=t,J.call(et,o)||!J.call(Xe,o)&&(Ze.test(o)?et[o]=!0:(Xe[o]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var o}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function ot(e){if(void 0===Je)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Je=t&&t[1]||"",Ye=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Je+e+Ye}var at=!1;function it(e,t){if(!e||at)return"";at=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(o){var r=o}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"===typeof n.catch&&n.catch((function(){}))}}catch(l){if(l&&r&&"string"===typeof l.stack)return[l.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),i=a[0],l=a[1];if(i&&l){var s=i.split("\n"),c=l.split("\n");for(o=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;o<c.length&&!c[o].includes("DetermineComponentFrameRoot");)o++;if(r===s.length||o===c.length)for(r=s.length-1,o=c.length-1;1<=r&&0<=o&&s[r]!==c[o];)o--;for(;1<=r&&0<=o;r--,o--)if(s[r]!==c[o]){if(1!==r||1!==o)do{if(r--,0>--o||s[r]!==c[o]){var u="\n"+s[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=o);break}}}finally{at=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ot(n):""}function lt(e){switch(e.tag){case 26:case 27:case 5:return ot(e.type);case 16:return ot("Lazy");case 13:return ot("Suspense");case 19:return ot("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return ot("Activity");default:return""}}function st(e){try{var t="";do{t+=lt(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var ht=/[\n"\\]/g;function mt(e){return e.replace(ht,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function gt(e,t,n,r,o,a,i,l){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?yt(e,i,ct(t)):null!=n?yt(e,i,ct(n)):null!=r&&e.removeAttribute("value"),null==o&&null!=a&&(e.defaultChecked=!!a),null!=o&&(e.checked=o&&"function"!==typeof o&&"symbol"!==typeof o),null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l?e.name=""+ct(l):e.removeAttribute("name")}function vt(e,t,n,r,o,a,i,l){if(null!=a&&"function"!==typeof a&&"symbol"!==typeof a&&"boolean"!==typeof a&&(e.type=a),null!=t||null!=n){if(!("submit"!==a&&"reset"!==a||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,l||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:o)&&"symbol"!==typeof r&&!!r,e.checked=l?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function yt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function xt(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function bt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function wt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(L(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function jt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function kt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ct(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var o in t)r=t[o],t.hasOwnProperty(o)&&n[o]!==r&&kt(e,o,r)}else for(var a in t)t.hasOwnProperty(a)&&kt(e,a,t[a])}function Et(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),At=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pt(e){return At.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Rt=null;function Nt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var _t=null,Lt=null;function Ot(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Le]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=r[Le]||null;if(!o)throw Error(i(90));gt(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":bt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&xt(e,!!n.multiple,t,!1)}}}var Dt=!1;function zt(e,t,n){if(Dt)return e(t,n);Dt=!0;try{return e(t)}finally{if(Dt=!1,(null!==_t||null!==Lt)&&(Bc(),_t&&(t=_t,e=Lt,Lt=_t=null,Ot(t),e)))for(t=0;t<e.length;t++)Ot(e[t])}}function It(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Le]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Ft=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Ut=!1;if(Ft)try{var Mt={};Object.defineProperty(Mt,"passive",{get:function(){Ut=!0}}),window.addEventListener("test",Mt,Mt),window.removeEventListener("test",Mt,Mt)}catch(Of){Ut=!1}var Bt=null,Ht=null,Wt=null;function $t(){if(Wt)return Wt;var e,t,n=Ht,r=n.length,o="value"in Bt?Bt.value:Bt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Wt=o.slice(e,1<t?1-t:void 0)}function Vt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function qt(){return!0}function Gt(){return!1}function Kt(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?qt:Gt,this.isPropagationStopped=Gt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=qt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=qt)},persist:function(){},isPersistent:qt}),t}var Qt,Jt,Yt,Zt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xt=Kt(Zt),en=f({},Zt,{view:0,detail:0}),tn=Kt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yt&&(Yt&&"mousemove"===e.type?(Qt=e.screenX-Yt.screenX,Jt=e.screenY-Yt.screenY):Jt=Qt=0,Yt=e),Qt)},movementY:function(e){return"movementY"in e?e.movementY:Jt}}),rn=Kt(nn),on=Kt(f({},nn,{dataTransfer:0})),an=Kt(f({},en,{relatedTarget:0})),ln=Kt(f({},Zt,{animationName:0,elapsedTime:0,pseudoElement:0})),sn=Kt(f({},Zt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),cn=Kt(f({},Zt,{data:0})),un={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function hn(){return pn}var mn=Kt(f({},en,{key:function(e){if(e.key){var t=un[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Vt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hn,charCode:function(e){return"keypress"===e.type?Vt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Vt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Kt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),vn=Kt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hn})),yn=Kt(f({},Zt,{propertyName:0,elapsedTime:0,pseudoElement:0})),xn=Kt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),bn=Kt(f({},Zt,{newState:0,oldState:0})),wn=[9,13,27,32],jn=Ft&&"CompositionEvent"in window,Sn=null;Ft&&"documentMode"in document&&(Sn=document.documentMode);var kn=Ft&&"TextEvent"in window&&!Sn,Cn=Ft&&(!jn||Sn&&8<Sn&&11>=Sn),En=String.fromCharCode(32),Tn=!1;function An(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Rn=!1;var Nn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Nn[e.type]:"textarea"===t}function Ln(e,t,n,r){_t?Lt?Lt.push(r):Lt=[r]:_t=r,0<(t=$u(t,"onChange")).length&&(n=new Xt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var On=null,Dn=null;function zn(e){zu(e,0)}function In(e){if(ft(We(e)))return e}function Fn(e,t){if("change"===e)return t}var Un=!1;if(Ft){var Mn;if(Ft){var Bn="oninput"in document;if(!Bn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Bn="function"===typeof Hn.oninput}Mn=Bn}else Mn=!1;Un=Mn&&(!document.documentMode||9<document.documentMode)}function Wn(){On&&(On.detachEvent("onpropertychange",$n),Dn=On=null)}function $n(e){if("value"===e.propertyName&&In(Dn)){var t=[];Ln(t,Dn,e,Nt(e)),zt(zn,t)}}function Vn(e,t,n){"focusin"===e?(Wn(),Dn=n,(On=t).attachEvent("onpropertychange",$n)):"focusout"===e&&Wn()}function qn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return In(Dn)}function Gn(e,t){if("click"===e)return In(t)}function Kn(e,t){if("input"===e||"change"===e)return In(t)}var Qn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Jn(e,t){if(Qn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!J.call(t,o)||!Qn(e[o],t[o]))return!1}return!0}function Yn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zn(e,t){var n,r=Yn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Yn(r)}}function Xn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Xn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=Ft&&"documentMode"in document&&11>=document.documentMode,rr=null,or=null,ar=null,ir=!1;function lr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&Jn(ar,r)||(ar=r,0<(r=$u(or,"onSelect")).length&&(t=new Xt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},ur={},dr={};function fr(e){if(ur[e])return ur[e];if(!cr[e])return e;var t,n=cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return ur[e]=n[t];return e}Ft&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var pr=fr("animationend"),hr=fr("animationiteration"),mr=fr("animationstart"),gr=fr("transitionrun"),vr=fr("transitionstart"),yr=fr("transitioncancel"),xr=fr("transitionend"),br=new Map,wr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function jr(e,t){br.set(e,t),Ke(t,[e])}wr.push("scrollEnd");var Sr=new WeakMap;function kr(e,t){if("object"===typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:st(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:st(t)}}var Cr=[],Er=0,Tr=0;function Ar(){for(var e=Er,t=Tr=Er=0;t<e;){var n=Cr[t];Cr[t++]=null;var r=Cr[t];Cr[t++]=null;var o=Cr[t];Cr[t++]=null;var a=Cr[t];if(Cr[t++]=null,null!==r&&null!==o){var i=r.pending;null===i?o.next=o:(o.next=i.next,i.next=o),r.pending=o}0!==a&&_r(n,o,a)}}function Pr(e,t,n,r){Cr[Er++]=e,Cr[Er++]=t,Cr[Er++]=n,Cr[Er++]=r,Tr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Rr(e,t,n,r){return Pr(e,t,n,r),Lr(e)}function Nr(e,t){return Pr(e,null,null,t),Lr(e)}function _r(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var o=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(o=!0)),e=a,a=a.return;return 3===e.tag?(a=e.stateNode,o&&null!==t&&(o=31-pe(n),null===(r=(e=a.hiddenUpdates)[o])?e[o]=[t]:r.push(t),t.lane=536870912|n),a):null}function Lr(e){if(50<_c)throw _c=0,Lc=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Or={};function Dr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zr(e,t,n,r){return new Dr(e,t,n,r)}function Ir(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Fr(e,t){var n=e.alternate;return null===n?((n=zr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ur(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Mr(e,t,n,r,o,a){var l=0;if(r=e,"function"===typeof e)Ir(e)&&(l=1);else if("string"===typeof e)l=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case T:return(e=zr(31,n,t,o)).elementType=T,e.lanes=a,e;case g:return Br(n.children,o,a,t);case v:l=8,o|=24;break;case y:return(e=zr(12,n,t,2|o)).elementType=y,e.lanes=a,e;case S:return(e=zr(13,n,t,o)).elementType=S,e.lanes=a,e;case k:return(e=zr(19,n,t,o)).elementType=k,e.lanes=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case x:case w:l=10;break e;case b:l=9;break e;case j:l=11;break e;case C:l=14;break e;case E:l=16,r=null;break e}l=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=zr(l,n,t,o)).elementType=e,t.type=r,t.lanes=a,t}function Br(e,t,n,r){return(e=zr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=zr(6,e,null,t)).lanes=n,e}function Wr(e,t,n){return(t=zr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var $r=[],Vr=0,qr=null,Gr=0,Kr=[],Qr=0,Jr=null,Yr=1,Zr="";function Xr(e,t){$r[Vr++]=Gr,$r[Vr++]=qr,qr=e,Gr=t}function eo(e,t,n){Kr[Qr++]=Yr,Kr[Qr++]=Zr,Kr[Qr++]=Jr,Jr=e;var r=Yr;e=Zr;var o=32-pe(r)-1;r&=~(1<<o),n+=1;var a=32-pe(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Yr=1<<32-pe(t)+o|n<<o|r,Zr=a+e}else Yr=1<<a|n<<o|r,Zr=e}function to(e){null!==e.return&&(Xr(e,1),eo(e,1,0))}function no(e){for(;e===qr;)qr=$r[--Vr],$r[Vr]=null,Gr=$r[--Vr],$r[Vr]=null;for(;e===Jr;)Jr=Kr[--Qr],Kr[Qr]=null,Zr=Kr[--Qr],Kr[Qr]=null,Yr=Kr[--Qr],Kr[Qr]=null}var ro=null,oo=null,ao=!1,io=null,lo=!1,so=Error(i(519));function co(e){throw go(kr(Error(i(418,"")),e)),so}function uo(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[_e]=e,t[Le]=r,n){case"dialog":Iu("cancel",t),Iu("close",t);break;case"iframe":case"object":case"embed":Iu("load",t);break;case"video":case"audio":for(n=0;n<Ou.length;n++)Iu(Ou[n],t);break;case"source":Iu("error",t);break;case"img":case"image":case"link":Iu("error",t),Iu("load",t);break;case"details":Iu("toggle",t);break;case"input":Iu("invalid",t),vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Iu("invalid",t);break;case"textarea":Iu("invalid",t),wt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Ju(t.textContent,n)?(null!=r.popover&&(Iu("beforetoggle",t),Iu("toggle",t)),null!=r.onScroll&&Iu("scroll",t),null!=r.onScrollEnd&&Iu("scrollend",t),null!=r.onClick&&(t.onclick=Yu),t=!0):t=!1,t||co(e)}function fo(e){for(ro=e.return;ro;)switch(ro.tag){case 5:case 13:return void(lo=!1);case 27:case 3:return void(lo=!0);default:ro=ro.return}}function po(e){if(e!==ro)return!1;if(!ao)return fo(e),ao=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||id(e.type,e.memoizedProps)),t=!t),t&&oo&&co(e),fo(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){oo=vd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}oo=null}}else 27===n?(n=oo,pd(e.type)?(e=yd,yd=null,oo=e):oo=n):oo=ro?vd(e.stateNode.nextSibling):null;return!0}function ho(){oo=ro=null,ao=!1}function mo(){var e=io;return null!==e&&(null===xc?xc=e:xc.push.apply(xc,e),io=null),e}function go(e){null===io?io=[e]:io.push(e)}var vo=U(null),yo=null,xo=null;function bo(e,t,n){B(vo,t._currentValue),t._currentValue=n}function wo(e){e._currentValue=vo.current,M(vo)}function jo(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function So(e,t,n,r){var o=e.child;for(null!==o&&(o.return=e);null!==o;){var a=o.dependencies;if(null!==a){var l=o.child;a=a.firstContext;e:for(;null!==a;){var s=a;a=o;for(var c=0;c<t.length;c++)if(s.context===t[c]){a.lanes|=n,null!==(s=a.alternate)&&(s.lanes|=n),jo(a.return,n,e),r||(l=null);break e}a=s.next}}else if(18===o.tag){if(null===(l=o.return))throw Error(i(341));l.lanes|=n,null!==(a=l.alternate)&&(a.lanes|=n),jo(l,n,e),l=null}else l=o.child;if(null!==l)l.return=o;else for(l=o;null!==l;){if(l===e){l=null;break}if(null!==(o=l.sibling)){o.return=l.return,l=o;break}l=l.return}o=l}}function ko(e,t,n,r){e=null;for(var o=t,a=!1;null!==o;){if(!a)if(0!==(524288&o.flags))a=!0;else if(0!==(262144&o.flags))break;if(10===o.tag){var l=o.alternate;if(null===l)throw Error(i(387));if(null!==(l=l.memoizedProps)){var s=o.type;Qn(o.pendingProps.value,l.value)||(null!==e?e.push(s):e=[s])}}else if(o===V.current){if(null===(l=o.alternate))throw Error(i(387));l.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(null!==e?e.push(Kd):e=[Kd])}o=o.return}null!==e&&So(t,e,n,r),t.flags|=262144}function Co(e){for(e=e.firstContext;null!==e;){if(!Qn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Eo(e){yo=e,xo=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function To(e){return Po(yo,e)}function Ao(e,t){return null===yo&&Eo(e),Po(e,t)}function Po(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===xo){if(null===e)throw Error(i(308));xo=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else xo=xo.next=t;return n}var Ro="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},No=r.unstable_scheduleCallback,_o=r.unstable_NormalPriority,Lo={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Oo(){return{controller:new Ro,data:new Map,refCount:0}}function Do(e){e.refCount--,0===e.refCount&&No(_o,(function(){e.controller.abort()}))}var zo=null,Io=0,Fo=0,Uo=null;function Mo(){if(0===--Io&&null!==zo){null!==Uo&&(Uo.status="fulfilled");var e=zo;zo=null,Fo=0,Uo=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Bo=O.S;O.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===zo){var n=zo=[];Io=0,Fo=Pu(),Uo={status:"pending",value:void 0,then:function(e){n.push(e)}}}Io++,t.then(Mo,Mo)}(0,t),null!==Bo&&Bo(e,t)};var Ho=U(null);function Wo(){var e=Ho.current;return null!==e?e:rc.pooledCache}function $o(e,t){B(Ho,null===t?Ho.current:t.pool)}function Vo(){var e=Wo();return null===e?null:{parent:Lo._currentValue,pool:e}}var qo=Error(i(460)),Go=Error(i(474)),Ko=Error(i(542)),Qo={then:function(){}};function Jo(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Yo(){}function Zo(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Yo,Yo),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ta(e=t.reason),e;default:if("string"===typeof t.status)t.then(Yo,Yo);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ta(e=t.reason),e}throw Xo=t,qo}}var Xo=null;function ea(){if(null===Xo)throw Error(i(459));var e=Xo;return Xo=null,e}function ta(e){if(e===qo||e===Ko)throw Error(i(483))}var na=!1;function ra(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function oa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function aa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ia(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nc)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=Lr(e),_r(e,null,n),t}return Pr(e,r,t,n),Lr(e)}function la(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Te(e,n)}}function sa(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ca=!1;function ua(){if(ca){if(null!==Uo)throw Uo}}function da(e,t,n,r){ca=!1;var o=e.updateQueue;na=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,c=s.next;s.next=null,null===i?a=c:i.next=c,i=s;var u=e.alternate;null!==u&&((l=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(i=0,u=c=s=null,l=a;;){var p=-536870913&l.lane,h=p!==l.lane;if(h?(ac&p)===p:(r&p)===p){0!==p&&p===Fo&&(ca=!0),null!==u&&(u=u.next={lane:0,tag:l.tag,payload:l.payload,callback:null,next:null});e:{var m=e,g=l;p=t;var v=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(v,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=g.payload)?m.call(v,d,p):m)||void 0===p)break e;d=f({},d,p);break e;case 2:na=!0}}null!==(p=l.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=o.callbacks)?o.callbacks=[p]:h.push(p))}else h={lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=h,s=d):u=u.next=h,i|=p;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(h=l).next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}null===u&&(s=d),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=u,null===a&&(o.shared.lanes=0),pc|=i,e.lanes=i,e.memoizedState=d}}function fa(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function pa(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)fa(n[e],t)}var ha=U(null),ma=U(0);function ga(e,t){B(ma,e=dc),B(ha,t),dc=e|t.baseLanes}function va(){B(ma,dc),B(ha,ha.current)}function ya(){dc=ma.current,M(ha),M(ma)}var xa=0,ba=null,wa=null,ja=null,Sa=!1,ka=!1,Ca=!1,Ea=0,Ta=0,Aa=null,Pa=0;function Ra(){throw Error(i(321))}function Na(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qn(e[n],t[n]))return!1;return!0}function _a(e,t,n,r,o,a){return xa=a,ba=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,O.H=null===e||null===e.memoizedState?qi:Gi,Ca=!1,a=n(r,o),Ca=!1,ka&&(a=Oa(t,n,r,o)),La(e),a}function La(e){O.H=Vi;var t=null!==wa&&null!==wa.next;if(xa=0,ja=wa=ba=null,Sa=!1,Ta=0,Aa=null,t)throw Error(i(300));null===e||Tl||null!==(e=e.dependencies)&&Co(e)&&(Tl=!0)}function Oa(e,t,n,r){ba=e;var o=0;do{if(ka&&(Aa=null),Ta=0,ka=!1,25<=o)throw Error(i(301));if(o+=1,ja=wa=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}O.H=Ki,a=t(n,r)}while(ka);return a}function Da(){var e=O.H,t=e.useState()[0];return t="function"===typeof t.then?Ba(t):t,e=e.useState()[0],(null!==wa?wa.memoizedState:null)!==e&&(ba.flags|=1024),t}function za(){var e=0!==Ea;return Ea=0,e}function Ia(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Fa(e){if(Sa){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Sa=!1}xa=0,ja=wa=ba=null,ka=!1,Ta=Ea=0,Aa=null}function Ua(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ja?ba.memoizedState=ja=e:ja=ja.next=e,ja}function Ma(){if(null===wa){var e=ba.alternate;e=null!==e?e.memoizedState:null}else e=wa.next;var t=null===ja?ba.memoizedState:ja.next;if(null!==t)ja=t,wa=e;else{if(null===e){if(null===ba.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(wa=e).memoizedState,baseState:wa.baseState,baseQueue:wa.baseQueue,queue:wa.queue,next:null},null===ja?ba.memoizedState=ja=e:ja=ja.next=e}return ja}function Ba(e){var t=Ta;return Ta+=1,null===Aa&&(Aa=[]),e=Zo(Aa,e,t),t=ba,null===(null===ja?t.memoizedState:ja.next)&&(t=t.alternate,O.H=null===t||null===t.memoizedState?qi:Gi),e}function Ha(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Ba(e);if(e.$$typeof===w)return To(e)}throw Error(i(438,String(e)))}function Wa(e){var t=null,n=ba.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=ba.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},ba.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=A;return t.index++,n}function $a(e,t){return"function"===typeof t?t(e):t}function Va(e){return qa(Ma(),wa,e)}function qa(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var o=e.baseQueue,a=r.pending;if(null!==a){if(null!==o){var l=o.next;o.next=a.next,a.next=l}t.baseQueue=o=a,r.pending=null}if(a=e.baseState,null===o)e.memoizedState=a;else{var s=l=null,c=null,u=t=o.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(ac&f)===f:(xa&f)===f){var p=u.revertLane;if(0===p)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===Fo&&(d=!0);else{if((xa&p)===p){u=u.next,p===Fo&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=f,l=a):c=c.next=f,ba.lanes|=p,pc|=p}f=u.action,Ca&&n(a,f),a=u.hasEagerState?u.eagerState:n(a,f)}else p={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=p,l=a):c=c.next=p,ba.lanes|=f,pc|=f;u=u.next}while(null!==u&&u!==t);if(null===c?l=a:c.next=s,!Qn(a,e.memoizedState)&&(Tl=!0,d&&null!==(n=Uo)))throw n;e.memoizedState=a,e.baseState=l,e.baseQueue=c,r.lastRenderedState=a}return null===o&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ga(e){var t=Ma(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,a=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{a=e(a,l.action),l=l.next}while(l!==o);Qn(a,t.memoizedState)||(Tl=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Ka(e,t,n){var r=ba,o=Ma(),a=ao;if(a){if(void 0===n)throw Error(i(407));n=n()}else n=t();var l=!Qn((wa||o).memoizedState,n);if(l&&(o.memoizedState=n,Tl=!0),o=o.queue,vi(2048,8,Ya.bind(null,r,o,e),[e]),o.getSnapshot!==t||l||null!==ja&&1&ja.memoizedState.tag){if(r.flags|=2048,hi(9,{destroy:void 0,resource:void 0},Ja.bind(null,r,o,n,t),null),null===rc)throw Error(i(349));a||0!==(124&xa)||Qa(r,t,n)}return n}function Qa(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ba.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},ba.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ja(e,t,n,r){t.value=n,t.getSnapshot=r,Za(t)&&Xa(e)}function Ya(e,t,n){return n((function(){Za(t)&&Xa(e)}))}function Za(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qn(e,n)}catch(r){return!0}}function Xa(e){var t=Nr(e,2);null!==t&&zc(t,e,2)}function ei(e){var t=Ua();if("function"===typeof e){var n=e;if(e=n(),Ca){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$a,lastRenderedState:e},t}function ti(e,t,n,r){return e.baseState=n,qa(e,wa,"function"===typeof r?r:$a)}function ni(e,t,n,r,o){if(Hi(e))throw Error(i(485));if(null!==(e=t.action)){var a={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==O.T?n(!0):a.isTransition=!1,r(a),null===(n=t.pending)?(a.next=t.pending=a,ri(t,a)):(a.next=n.next,t.pending=n.next=a)}}function ri(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var a=O.T,i={};O.T=i;try{var l=n(o,r),s=O.S;null!==s&&s(i,l),oi(e,t,l)}catch(c){ii(e,t,c)}finally{O.T=a}}else try{oi(e,t,a=n(o,r))}catch(u){ii(e,t,u)}}function oi(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then((function(n){ai(e,t,n)}),(function(n){return ii(e,t,n)})):ai(e,t,n)}function ai(e,t,n){t.status="fulfilled",t.value=n,li(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ri(e,n)))}function ii(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,li(t),t=t.next}while(t!==r)}e.action=null}function li(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function si(e,t){return t}function ci(e,t){if(ao){var n=rc.formState;if(null!==n){e:{var r=ba;if(ao){if(oo){t:{for(var o=oo,a=lo;8!==o.nodeType;){if(!a){o=null;break t}if(null===(o=vd(o.nextSibling))){o=null;break t}}o="F!"===(a=o.data)||"F"===a?o:null}if(o){oo=vd(o.nextSibling),r="F!"===o.data;break e}}co(r)}r=!1}r&&(t=n[0])}}return(n=Ua()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:si,lastRenderedState:t},n.queue=r,n=Ui.bind(null,ba,r),r.dispatch=n,r=ei(!1),a=Bi.bind(null,ba,!1,r.queue),o={state:t,dispatch:null,action:e,pending:null},(r=Ua()).queue=o,n=ni.bind(null,ba,o,a,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function ui(e){return di(Ma(),wa,e)}function di(e,t,n){if(t=qa(e,t,si)[0],e=Va($a)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Ba(t)}catch(i){if(i===qo)throw Ko;throw i}else r=t;var o=(t=Ma()).queue,a=o.dispatch;return n!==t.memoizedState&&(ba.flags|=2048,hi(9,{destroy:void 0,resource:void 0},fi.bind(null,o,n),null)),[r,a,e]}function fi(e,t){e.action=t}function pi(e){var t=Ma(),n=wa;if(null!==n)return di(t,n,e);Ma(),t=t.memoizedState;var r=(n=Ma()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function hi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=ba.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},ba.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function mi(){return Ma().memoizedState}function gi(e,t,n,r){var o=Ua();r=void 0===r?null:r,ba.flags|=e,o.memoizedState=hi(1|t,{destroy:void 0,resource:void 0},n,r)}function vi(e,t,n,r){var o=Ma();r=void 0===r?null:r;var a=o.memoizedState.inst;null!==wa&&null!==r&&Na(r,wa.memoizedState.deps)?o.memoizedState=hi(t,a,n,r):(ba.flags|=e,o.memoizedState=hi(1|t,a,n,r))}function yi(e,t){gi(8390656,8,e,t)}function xi(e,t){vi(2048,8,e,t)}function bi(e,t){return vi(4,2,e,t)}function wi(e,t){return vi(4,4,e,t)}function ji(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function Si(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,vi(4,4,ji.bind(null,t,e),n)}function ki(){}function Ci(e,t){var n=Ma();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Na(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ei(e,t){var n=Ma();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Na(t,r[1]))return r[0];if(r=e(),Ca){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Ti(e,t,n){return void 0===n||0!==(1073741824&xa)?e.memoizedState=t:(e.memoizedState=n,e=Dc(),ba.lanes|=e,pc|=e,n)}function Ai(e,t,n,r){return Qn(n,t)?n:null!==ha.current?(e=Ti(e,n,r),Qn(e,t)||(Tl=!0),e):0===(42&xa)?(Tl=!0,e.memoizedState=n):(e=Dc(),ba.lanes|=e,pc|=e,t)}function Pi(e,t,n,r,o){var a=D.p;D.p=0!==a&&8>a?a:8;var i=O.T,l={};O.T=l,Bi(e,!1,t,n);try{var s=o(),c=O.S;if(null!==c&&c(l,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)Mi(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then((function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)}),(function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)})),r}(s,r),Oc());else Mi(e,t,r,Oc())}catch(u){Mi(e,t,{then:function(){},status:"rejected",reason:u},Oc())}finally{D.p=a,O.T=i}}function Ri(){}function Ni(e,t,n,r){if(5!==e.tag)throw Error(i(476));var o=_i(e).queue;Pi(e,o,t,z,null===n?Ri:function(){return Li(e),n(r)})}function _i(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:z,baseState:z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$a,lastRenderedState:z},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$a,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Li(e){Mi(e,_i(e).next.queue,{},Oc())}function Oi(){return To(Kd)}function Di(){return Ma().memoizedState}function zi(){return Ma().memoizedState}function Ii(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Oc(),r=ia(t,e=aa(n),n);return null!==r&&(zc(r,t,n),la(r,t,n)),t={cache:Oo()},void(e.payload=t)}t=t.return}}function Fi(e,t,n){var r=Oc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Hi(e)?Wi(t,n):null!==(n=Rr(e,t,n,r))&&(zc(n,e,r),$i(n,t,r))}function Ui(e,t,n){Mi(e,t,n,Oc())}function Mi(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hi(e))Wi(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,Qn(l,i))return Pr(e,t,o,0),null===rc&&Ar(),!1}catch(s){}if(null!==(n=Rr(e,t,o,r)))return zc(n,e,r),$i(n,t,r),!0}return!1}function Bi(e,t,n,r){if(r={lane:2,revertLane:Pu(),action:r,hasEagerState:!1,eagerState:null,next:null},Hi(e)){if(t)throw Error(i(479))}else null!==(t=Rr(e,n,r,2))&&zc(t,e,2)}function Hi(e){var t=e.alternate;return e===ba||null!==t&&t===ba}function Wi(e,t){ka=Sa=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function $i(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Te(e,n)}}var Vi={readContext:To,use:Ha,useCallback:Ra,useContext:Ra,useEffect:Ra,useImperativeHandle:Ra,useLayoutEffect:Ra,useInsertionEffect:Ra,useMemo:Ra,useReducer:Ra,useRef:Ra,useState:Ra,useDebugValue:Ra,useDeferredValue:Ra,useTransition:Ra,useSyncExternalStore:Ra,useId:Ra,useHostTransitionStatus:Ra,useFormState:Ra,useActionState:Ra,useOptimistic:Ra,useMemoCache:Ra,useCacheRefresh:Ra},qi={readContext:To,use:Ha,useCallback:function(e,t){return Ua().memoizedState=[e,void 0===t?null:t],e},useContext:To,useEffect:yi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gi(4194308,4,ji.bind(null,t,e),n)},useLayoutEffect:function(e,t){return gi(4194308,4,e,t)},useInsertionEffect:function(e,t){gi(4,2,e,t)},useMemo:function(e,t){var n=Ua();t=void 0===t?null:t;var r=e();if(Ca){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ua();if(void 0!==n){var o=n(t);if(Ca){fe(!0);try{n(t)}finally{fe(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=Fi.bind(null,ba,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ua().memoizedState=e},useState:function(e){var t=(e=ei(e)).queue,n=Ui.bind(null,ba,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:ki,useDeferredValue:function(e,t){return Ti(Ua(),e,t)},useTransition:function(){var e=ei(!1);return e=Pi.bind(null,ba,e.queue,!0,!1),Ua().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=ba,o=Ua();if(ao){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===rc)throw Error(i(349));0!==(124&ac)||Qa(r,t,n)}o.memoizedState=n;var a={value:n,getSnapshot:t};return o.queue=a,yi(Ya.bind(null,r,a,e),[e]),r.flags|=2048,hi(9,{destroy:void 0,resource:void 0},Ja.bind(null,r,a,n,t),null),n},useId:function(){var e=Ua(),t=rc.identifierPrefix;if(ao){var n=Zr;t="\xab"+t+"R"+(n=(Yr&~(1<<32-pe(Yr)-1)).toString(32)+n),0<(n=Ea++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Pa++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Oi,useFormState:ci,useActionState:ci,useOptimistic:function(e){var t=Ua();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bi.bind(null,ba,!0,n),n.dispatch=t,[e,t]},useMemoCache:Wa,useCacheRefresh:function(){return Ua().memoizedState=Ii.bind(null,ba)}},Gi={readContext:To,use:Ha,useCallback:Ci,useContext:To,useEffect:xi,useImperativeHandle:Si,useInsertionEffect:bi,useLayoutEffect:wi,useMemo:Ei,useReducer:Va,useRef:mi,useState:function(){return Va($a)},useDebugValue:ki,useDeferredValue:function(e,t){return Ai(Ma(),wa.memoizedState,e,t)},useTransition:function(){var e=Va($a)[0],t=Ma().memoizedState;return["boolean"===typeof e?e:Ba(e),t]},useSyncExternalStore:Ka,useId:Di,useHostTransitionStatus:Oi,useFormState:ui,useActionState:ui,useOptimistic:function(e,t){return ti(Ma(),0,e,t)},useMemoCache:Wa,useCacheRefresh:zi},Ki={readContext:To,use:Ha,useCallback:Ci,useContext:To,useEffect:xi,useImperativeHandle:Si,useInsertionEffect:bi,useLayoutEffect:wi,useMemo:Ei,useReducer:Ga,useRef:mi,useState:function(){return Ga($a)},useDebugValue:ki,useDeferredValue:function(e,t){var n=Ma();return null===wa?Ti(n,e,t):Ai(n,wa.memoizedState,e,t)},useTransition:function(){var e=Ga($a)[0],t=Ma().memoizedState;return["boolean"===typeof e?e:Ba(e),t]},useSyncExternalStore:Ka,useId:Di,useHostTransitionStatus:Oi,useFormState:pi,useActionState:pi,useOptimistic:function(e,t){var n=Ma();return null!==wa?ti(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Wa,useCacheRefresh:zi},Qi=null,Ji=0;function Yi(e){var t=Ji;return Ji+=1,null===Qi&&(Qi=[]),Zo(Qi,e,t)}function Zi(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Xi(e,t){if(t.$$typeof===p)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function el(e){return(0,e._init)(e._payload)}function tl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function o(e,t){return(e=Fr(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function c(e,t,n,r){var a=n.type;return a===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===E&&el(a)===t.type)?(Zi(t=o(t,n.props),n),t.return=e,t):(Zi(t=Mr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Wr(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Br(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case h:return Zi(n=Mr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=Wr(t,e.mode,n)).return=e,t;case E:return f(e,t=(0,t._init)(t._payload),n)}if(L(t)||R(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Yi(t),n);if(t.$$typeof===w)return f(e,Ao(e,t),n);Xi(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case h:return n.key===o?c(e,t,n,r):null;case m:return n.key===o?u(e,t,n,r):null;case E:return p(e,t,n=(o=n._init)(n._payload),r)}if(L(n)||R(n))return null!==o?null:d(e,t,n,r,null);if("function"===typeof n.then)return p(e,t,Yi(n),r);if(n.$$typeof===w)return p(e,t,Ao(e,n),r);Xi(e,n)}return null}function v(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case m:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case E:return v(e,t,n,r=(0,r._init)(r._payload),o)}if(L(r)||R(r))return d(t,e=e.get(n)||null,r,o,null);if("function"===typeof r.then)return v(e,t,n,Yi(r),o);if(r.$$typeof===w)return v(e,t,n,Ao(t,r),o);Xi(t,r)}return null}function y(s,c,u,d){if("object"===typeof u&&null!==u&&u.type===g&&null===u.key&&(u=u.props.children),"object"===typeof u&&null!==u){switch(u.$$typeof){case h:e:{for(var x=u.key;null!==c;){if(c.key===x){if((x=u.type)===g){if(7===c.tag){n(s,c.sibling),(d=o(c,u.props.children)).return=s,s=d;break e}}else if(c.elementType===x||"object"===typeof x&&null!==x&&x.$$typeof===E&&el(x)===c.type){n(s,c.sibling),Zi(d=o(c,u.props),u),d.return=s,s=d;break e}n(s,c);break}t(s,c),c=c.sibling}u.type===g?((d=Br(u.props.children,s.mode,d,u.key)).return=s,s=d):(Zi(d=Mr(u.type,u.key,u.props,null,s.mode,d),u),d.return=s,s=d)}return l(s);case m:e:{for(x=u.key;null!==c;){if(c.key===x){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(s,c.sibling),(d=o(c,u.children||[])).return=s,s=d;break e}n(s,c);break}t(s,c),c=c.sibling}(d=Wr(u,s.mode,d)).return=s,s=d}return l(s);case E:return y(s,c,u=(x=u._init)(u._payload),d)}if(L(u))return function(o,i,l,s){for(var c=null,u=null,d=i,h=i=0,m=null;null!==d&&h<l.length;h++){d.index>h?(m=d,d=null):m=d.sibling;var g=p(o,d,l[h],s);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(o,d),i=a(g,i,h),null===u?c=g:u.sibling=g,u=g,d=m}if(h===l.length)return n(o,d),ao&&Xr(o,h),c;if(null===d){for(;h<l.length;h++)null!==(d=f(o,l[h],s))&&(i=a(d,i,h),null===u?c=d:u.sibling=d,u=d);return ao&&Xr(o,h),c}for(d=r(d);h<l.length;h++)null!==(m=v(d,o,h,l[h],s))&&(e&&null!==m.alternate&&d.delete(null===m.key?h:m.key),i=a(m,i,h),null===u?c=m:u.sibling=m,u=m);return e&&d.forEach((function(e){return t(o,e)})),ao&&Xr(o,h),c}(s,c,u,d);if(R(u)){if("function"!==typeof(x=R(u)))throw Error(i(150));return function(o,l,s,c){if(null==s)throw Error(i(151));for(var u=null,d=null,h=l,m=l=0,g=null,y=s.next();null!==h&&!y.done;m++,y=s.next()){h.index>m?(g=h,h=null):g=h.sibling;var x=p(o,h,y.value,c);if(null===x){null===h&&(h=g);break}e&&h&&null===x.alternate&&t(o,h),l=a(x,l,m),null===d?u=x:d.sibling=x,d=x,h=g}if(y.done)return n(o,h),ao&&Xr(o,m),u;if(null===h){for(;!y.done;m++,y=s.next())null!==(y=f(o,y.value,c))&&(l=a(y,l,m),null===d?u=y:d.sibling=y,d=y);return ao&&Xr(o,m),u}for(h=r(h);!y.done;m++,y=s.next())null!==(y=v(h,o,m,y.value,c))&&(e&&null!==y.alternate&&h.delete(null===y.key?m:y.key),l=a(y,l,m),null===d?u=y:d.sibling=y,d=y);return e&&h.forEach((function(e){return t(o,e)})),ao&&Xr(o,m),u}(s,c,u=x.call(u),d)}if("function"===typeof u.then)return y(s,c,Yi(u),d);if(u.$$typeof===w)return y(s,c,Ao(s,u),d);Xi(s,u)}return"string"===typeof u&&""!==u||"number"===typeof u||"bigint"===typeof u?(u=""+u,null!==c&&6===c.tag?(n(s,c.sibling),(d=o(c,u)).return=s,s=d):(n(s,c),(d=Hr(u,s.mode,d)).return=s,s=d),l(s)):n(s,c)}return function(e,t,n,r){try{Ji=0;var o=y(e,t,n,r);return Qi=null,o}catch(i){if(i===qo||i===Ko)throw i;var a=zr(29,i,null,e.mode);return a.lanes=r,a.return=e,a}}}var nl=tl(!0),rl=tl(!1),ol=U(null),al=null;function il(e){var t=e.alternate;B(ul,1&ul.current),B(ol,e),null===al&&(null===t||null!==ha.current||null!==t.memoizedState)&&(al=e)}function ll(e){if(22===e.tag){if(B(ul,ul.current),B(ol,e),null===al){var t=e.alternate;null!==t&&null!==t.memoizedState&&(al=e)}}else sl()}function sl(){B(ul,ul.current),B(ol,ol.current)}function cl(e){M(ol),al===e&&(al=null),M(ul)}var ul=U(0);function dl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fl(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var pl={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Oc(),o=aa(r);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=ia(e,o,r))&&(zc(t,e,r),la(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Oc(),o=aa(r);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=ia(e,o,r))&&(zc(t,e,r),la(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Oc(),r=aa(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=ia(e,r,n))&&(zc(t,e,n),la(t,e,n))}};function hl(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!Jn(n,r)||!Jn(o,a))}function ml(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&pl.enqueueReplaceState(t,t.state,null)}function gl(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var o in n===t&&(n=f({},n)),e)void 0===n[o]&&(n[o]=e[o]);return n}var vl="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function yl(e){vl(e)}function xl(e){console.error(e)}function bl(e){vl(e)}function wl(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function jl(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function Sl(e,t,n){return(n=aa(n)).tag=3,n.payload={element:null},n.callback=function(){wl(e,t)},n}function kl(e){return(e=aa(e)).tag=3,e}function Cl(e,t,n,r){var o=n.type.getDerivedStateFromError;if("function"===typeof o){var a=r.value;e.payload=function(){return o(a)},e.callback=function(){jl(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){jl(t,n,r),"function"!==typeof o&&(null===kc?kc=new Set([this]):kc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var El=Error(i(461)),Tl=!1;function Al(e,t,n,r){t.child=null===e?rl(t,null,n,r):nl(t,e.child,n,r)}function Pl(e,t,n,r,o){n=n.render;var a=t.ref;if("ref"in r){var i={};for(var l in r)"ref"!==l&&(i[l]=r[l])}else i=r;return Eo(t),r=_a(e,t,n,i,a,o),l=za(),null===e||Tl?(ao&&l&&to(t),t.flags|=1,Al(e,t,r,o),t.child):(Ia(e,t,o),Ql(e,t,o))}function Rl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Ir(a)||void 0!==a.defaultProps||null!==n.compare?((e=Mr(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Nl(e,t,a,r,o))}if(a=e.child,!Jl(e,o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:Jn)(i,r)&&e.ref===t.ref)return Ql(e,t,o)}return t.flags|=1,(e=Fr(a,r)).ref=t.ref,e.return=t,t.child=e}function Nl(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(Jn(a,r)&&e.ref===t.ref){if(Tl=!1,t.pendingProps=r=a,!Jl(e,o))return t.lanes=e.lanes,Ql(e,t,o);0!==(131072&e.flags)&&(Tl=!0)}}return Dl(e,t,n,r,o)}function _l(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==a?a.baseLanes|n:n,null!==e){for(o=t.child=e.child,a=0;null!==o;)a=a|o.lanes|o.childLanes,o=o.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return Ll(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ll(e,t,null!==a?a.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&$o(0,null!==a?a.cachePool:null),null!==a?ga(t,a):va(),ll(t)}else null!==a?($o(0,a.cachePool),ga(t,a),sl(),t.memoizedState=null):(null!==e&&$o(0,null),va(),sl());return Al(e,t,o,n),t.child}function Ll(e,t,n,r){var o=Wo();return o=null===o?null:{parent:Lo._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},null!==e&&$o(0,null),va(),ll(t),null!==e&&ko(e,t,r,!0),null}function Ol(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Dl(e,t,n,r,o){return Eo(t),n=_a(e,t,n,r,void 0,o),r=za(),null===e||Tl?(ao&&r&&to(t),t.flags|=1,Al(e,t,n,o),t.child):(Ia(e,t,o),Ql(e,t,o))}function zl(e,t,n,r,o,a){return Eo(t),t.updateQueue=null,n=Oa(t,r,n,o),La(e),r=za(),null===e||Tl?(ao&&r&&to(t),t.flags|=1,Al(e,t,n,a),t.child):(Ia(e,t,a),Ql(e,t,a))}function Il(e,t,n,r,o){if(Eo(t),null===t.stateNode){var a=Or,i=n.contextType;"object"===typeof i&&null!==i&&(a=To(i)),a=new n(r,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=pl,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=r,a.state=t.memoizedState,a.refs={},ra(t),i=n.contextType,a.context="object"===typeof i&&null!==i?To(i):Or,a.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(fl(t,n,i,r),a.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(i=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),i!==a.state&&pl.enqueueReplaceState(a,a.state,null),da(t,r,a,o),ua(),a.state=t.memoizedState),"function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){a=t.stateNode;var l=t.memoizedProps,s=gl(n,l);a.props=s;var c=a.context,u=n.contextType;i=Or,"object"===typeof u&&null!==u&&(i=To(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof a.getSnapshotBeforeUpdate,l=t.pendingProps!==l,u||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(l||c!==i)&&ml(t,a,r,i),na=!1;var f=t.memoizedState;a.state=f,da(t,r,a,o),ua(),c=t.memoizedState,l||f!==c||na?("function"===typeof d&&(fl(t,n,d,r),c=t.memoizedState),(s=na||hl(t,n,s,r,f,c,i))?(u||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),a.props=r,a.state=c,a.context=i,r=s):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,oa(e,t),u=gl(n,i=t.memoizedProps),a.props=u,d=t.pendingProps,f=a.context,c=n.contextType,s=Or,"object"===typeof c&&null!==c&&(s=To(c)),(c="function"===typeof(l=n.getDerivedStateFromProps)||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(i!==d||f!==s)&&ml(t,a,r,s),na=!1,f=t.memoizedState,a.state=f,da(t,r,a,o),ua();var p=t.memoizedState;i!==d||f!==p||na||null!==e&&null!==e.dependencies&&Co(e.dependencies)?("function"===typeof l&&(fl(t,n,l,r),p=t.memoizedState),(u=na||hl(t,n,u,r,f,p,s)||null!==e&&null!==e.dependencies&&Co(e.dependencies))?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,s),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=s,r=u):("function"!==typeof a.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,Ol(e,t),r=0!==(128&t.flags),a||r?(a=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&r?(t.child=nl(t,e.child,null,o),t.child=nl(t,null,n,o)):Al(e,t,n,o),t.memoizedState=a.state,e=t.child):e=Ql(e,t,o),e}function Fl(e,t,n,r){return ho(),t.flags|=256,Al(e,t,n,r),t.child}var Ul={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ml(e){return{baseLanes:e,cachePool:Vo()}}function Bl(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gc),e}function Hl(e,t,n){var r,o=t.pendingProps,a=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&ul.current)),r&&(a=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(ao){if(a?il(t):sl(),ao){var s,c=oo;if(s=c){e:{for(s=c,c=lo;8!==s.nodeType;){if(!c){c=null;break e}if(null===(s=vd(s.nextSibling))){c=null;break e}}c=s}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Jr?{id:Yr,overflow:Zr}:null,retryLane:536870912,hydrationErrors:null},(s=zr(18,null,null,0)).stateNode=c,s.return=t,t.child=s,ro=t,oo=null,s=!0):s=!1}s||co(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return gd(c)?t.lanes=32:t.lanes=536870912,null;cl(t)}return c=o.children,o=o.fallback,a?(sl(),c=$l({mode:"hidden",children:c},a=t.mode),o=Br(o,a,n,null),c.return=t,o.return=t,c.sibling=o,t.child=c,(a=t.child).memoizedState=Ml(n),a.childLanes=Bl(e,r,n),t.memoizedState=Ul,o):(il(t),Wl(t,c))}if(null!==(s=e.memoizedState)&&null!==(c=s.dehydrated)){if(l)256&t.flags?(il(t),t.flags&=-257,t=Vl(e,t,n)):null!==t.memoizedState?(sl(),t.child=e.child,t.flags|=128,t=null):(sl(),a=o.fallback,c=t.mode,o=$l({mode:"visible",children:o.children},c),(a=Br(a,c,n,null)).flags|=2,o.return=t,a.return=t,o.sibling=a,t.child=o,nl(t,e.child,null,n),(o=t.child).memoizedState=Ml(n),o.childLanes=Bl(e,r,n),t.memoizedState=Ul,t=a);else if(il(t),gd(c)){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(o=Error(i(419))).stack="",o.digest=r,go({value:o,source:null,stack:null}),t=Vl(e,t,n)}else if(Tl||ko(e,t,n,!1),r=0!==(n&e.childLanes),Tl||r){if(null!==(r=rc)&&(0!==(o=0!==((o=0!==(42&(o=n&-n))?1:Ae(o))&(r.suspendedLanes|n))?0:o)&&o!==s.retryLane))throw s.retryLane=o,Nr(e,o),zc(r,e,o),El;"$?"===c.data||Gc(),t=Vl(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,oo=vd(c.nextSibling),ro=t,ao=!0,io=null,lo=!1,null!==e&&(Kr[Qr++]=Yr,Kr[Qr++]=Zr,Kr[Qr++]=Jr,Yr=e.id,Zr=e.overflow,Jr=t),(t=Wl(t,o.children)).flags|=4096);return t}return a?(sl(),a=o.fallback,c=t.mode,u=(s=e.child).sibling,(o=Fr(s,{mode:"hidden",children:o.children})).subtreeFlags=65011712&s.subtreeFlags,null!==u?a=Fr(u,a):(a=Br(a,c,n,null)).flags|=2,a.return=t,o.return=t,o.sibling=a,t.child=o,o=a,a=t.child,null===(c=e.child.memoizedState)?c=Ml(n):(null!==(s=c.cachePool)?(u=Lo._currentValue,s=s.parent!==u?{parent:u,pool:u}:s):s=Vo(),c={baseLanes:c.baseLanes|n,cachePool:s}),a.memoizedState=c,a.childLanes=Bl(e,r,n),t.memoizedState=Ul,o):(il(t),e=(n=e.child).sibling,(n=Fr(n,{mode:"visible",children:o.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Wl(e,t){return(t=$l({mode:"visible",children:t},e.mode)).return=e,e.child=t}function $l(e,t){return(e=zr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vl(e,t,n){return nl(t,e.child,null,n),(e=Wl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function ql(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),jo(e.return,t,n)}function Gl(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Kl(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(Al(e,t,r.children,n),0!==(2&(r=ul.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&ql(e,n,t);else if(19===e.tag)ql(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(ul,r),o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===dl(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Gl(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===dl(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Gl(t,!0,n,null,a);break;case"together":Gl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ql(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(ko(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Fr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Fr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Jl(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Co(e))}function Yl(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Tl=!0;else{if(!Jl(e,n)&&0===(128&t.flags))return Tl=!1,function(e,t,n){switch(t.tag){case 3:q(t,t.stateNode.containerInfo),bo(0,Lo,e.memoizedState.cache),ho();break;case 27:case 5:K(t);break;case 4:q(t,t.stateNode.containerInfo);break;case 10:bo(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(il(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Hl(e,t,n):(il(t),null!==(e=Ql(e,t,n))?e.sibling:null);il(t);break;case 19:var o=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(ko(e,t,n,!1),r=0!==(n&t.childLanes)),o){if(r)return Kl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),B(ul,ul.current),r)break;return null;case 22:case 23:return t.lanes=0,_l(e,t,n);case 24:bo(0,Lo,e.memoizedState.cache)}return Ql(e,t,n)}(e,t,n);Tl=0!==(131072&e.flags)}else Tl=!1,ao&&0!==(1048576&t.flags)&&eo(t,Gr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,o=r._init;if(r=o(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((o=r.$$typeof)===j){t.tag=11,t=Pl(null,t,r,e,n);break e}if(o===C){t.tag=14,t=Rl(null,t,r,e,n);break e}}throw t=_(r)||r,Error(i(306,t,""))}Ir(r)?(e=gl(r,e),t.tag=1,t=Il(null,t,r,e,n)):(t.tag=0,t=Dl(null,t,r,e,n))}return t;case 0:return Dl(e,t,t.type,t.pendingProps,n);case 1:return Il(e,t,r=t.type,o=gl(r,t.pendingProps),n);case 3:e:{if(q(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var a=t.memoizedState;o=a.element,oa(e,t),da(t,r,null,n);var l=t.memoizedState;if(r=l.cache,bo(0,Lo,r),r!==a.cache&&So(t,[Lo],n,!0),ua(),r=l.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:l.cache},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Fl(e,t,r,n);break e}if(r!==o){go(o=kr(Error(i(424)),t)),t=Fl(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(oo=vd(e.firstChild),ro=t,ao=!0,io=null,lo=!0,n=rl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ho(),r===o){t=Ql(e,t,n);break e}Al(e,t,r,n)}t=t.child}return t;case 26:return Ol(e,t),null===e?(n=Ad(t.type,null,t.pendingProps,null))?t.memoizedState=n:ao||(n=t.type,e=t.pendingProps,(r=rd($.current).createElement(n))[_e]=t,r[Le]=e,ed(r,n,e),Ve(r),t.stateNode=r):t.memoizedState=Ad(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return K(t),null===e&&ao&&(r=t.stateNode=bd(t.type,t.pendingProps,$.current),ro=t,lo=!0,o=oo,pd(t.type)?(yd=o,oo=vd(r.firstChild)):oo=o),Al(e,t,t.pendingProps.children,n),Ol(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ao&&((o=r=oo)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ue])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==o.rel||e.getAttribute("href")!==(null==o.href||""===o.href?null:o.href)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin)||e.getAttribute("title")!==(null==o.title?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==o.src?null:o.src)||e.getAttribute("type")!==(null==o.type?null:o.type)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==o.name?null:""+o.name;if("hidden"===o.type&&e.getAttribute("name")===a)return e}if(null===(e=vd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,lo))?(t.stateNode=r,ro=t,oo=vd(r.firstChild),lo=!1,o=!0):o=!1),o||co(t)),K(t),o=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,r=a.children,id(o,a)?r=null:null!==l&&id(o,l)&&(t.flags|=32),null!==t.memoizedState&&(o=_a(e,t,Da,null,null,n),Kd._currentValue=o),Ol(e,t),Al(e,t,r,n),t.child;case 6:return null===e&&ao&&((e=n=oo)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=vd(e.nextSibling)))return null}return e}(n,t.pendingProps,lo))?(t.stateNode=n,ro=t,oo=null,e=!0):e=!1),e||co(t)),null;case 13:return Hl(e,t,n);case 4:return q(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=nl(t,null,r,n):Al(e,t,r,n),t.child;case 11:return Pl(e,t,t.type,t.pendingProps,n);case 7:return Al(e,t,t.pendingProps,n),t.child;case 8:case 12:return Al(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,bo(0,t.type,r.value),Al(e,t,r.children,n),t.child;case 9:return o=t.type._context,r=t.pendingProps.children,Eo(t),r=r(o=To(o)),t.flags|=1,Al(e,t,r,n),t.child;case 14:return Rl(e,t,t.type,t.pendingProps,n);case 15:return Nl(e,t,t.type,t.pendingProps,n);case 19:return Kl(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=$l(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Fr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return _l(e,t,n);case 24:return Eo(t),r=To(Lo),null===e?(null===(o=Wo())&&(o=rc,a=Oo(),o.pooledCache=a,a.refCount++,null!==a&&(o.pooledCacheLanes|=n),o=a),t.memoizedState={parent:r,cache:o},ra(t),bo(0,Lo,o)):(0!==(e.lanes&n)&&(oa(e,t),da(t,null,null,n),ua()),o=e.memoizedState,a=t.memoizedState,o.parent!==r?(o={parent:r,cache:r},t.memoizedState=o,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=o),bo(0,Lo,r)):(r=a.cache,bo(0,Lo,r),r!==o.cache&&So(t,[Lo],n,!0))),Al(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Zl(e){e.flags|=4}function Xl(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=ol.current)&&((4194048&ac)===ac?null!==al:(62914560&ac)!==ac&&0===(536870912&ac)||t!==al))throw Xo=Qo,Go;e.flags|=8192}}function es(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,vc|=t)}function ts(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ns(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=65011712&o.subtreeFlags,r|=65011712&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rs(e,t,n){var r=t.pendingProps;switch(no(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ns(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wo(Lo),G(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(po(t)?Zl(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,mo())),ns(t),null;case 26:return n=t.memoizedState,null===e?(Zl(t),null!==n?(ns(t),Xl(t,n)):(ns(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Zl(t),ns(t),Xl(t,n)):(ns(t),t.flags&=-16777217):(e.memoizedProps!==r&&Zl(t),ns(t),t.flags&=-16777217),null;case 27:Q(t),n=$.current;var o=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zl(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return ns(t),null}e=H.current,po(t)?uo(t):(e=bd(o,r,n),t.stateNode=e,Zl(t))}return ns(t),null;case 5:if(Q(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zl(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return ns(t),null}if(e=H.current,po(t))uo(t);else{switch(o=rd($.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=o.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?o.createElement(n,{is:r.is}):o.createElement(n)}}e[_e]=t,e[Le]=r;e:for(o=t.child;null!==o;){if(5===o.tag||6===o.tag)e.appendChild(o.stateNode);else if(4!==o.tag&&27!==o.tag&&null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;null===o.sibling;){if(null===o.return||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zl(t)}}return ns(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Zl(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=$.current,po(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(o=ro))switch(o.tag){case 27:case 5:r=o.memoizedProps}e[_e]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Ju(e.nodeValue,n)))||co(t)}else(e=rd(e).createTextNode(r))[_e]=t,t.stateNode=e}return ns(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(o=po(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(i(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(i(317));o[_e]=t}else ho(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ns(t),o=!1}else o=mo(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=o),o=!0;if(!o)return 256&t.flags?(cl(t),t):(cl(t),null)}if(cl(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){o=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(o=r.alternate.memoizedState.cachePool.pool);var a=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==o&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),es(t,t.updateQueue),ns(t),null;case 4:return G(),null===e&&Mu(t.stateNode.containerInfo),ns(t),null;case 10:return wo(t.type),ns(t),null;case 19:if(M(ul),null===(o=t.memoizedState))return ns(t),null;if(r=0!==(128&t.flags),null===(a=o.rendering))if(r)ts(o,!1);else{if(0!==fc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(a=dl(e))){for(t.flags|=128,ts(o,!1),e=a.updateQueue,t.updateQueue=e,es(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ur(n,e),n=n.sibling;return B(ul,1&ul.current|2),t.child}e=e.sibling}null!==o.tail&&te()>jc&&(t.flags|=128,r=!0,ts(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=dl(a))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,es(t,e),ts(o,!0),null===o.tail&&"hidden"===o.tailMode&&!a.alternate&&!ao)return ns(t),null}else 2*te()-o.renderingStartTime>jc&&536870912!==n&&(t.flags|=128,r=!0,ts(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=o.last)?e.sibling=a:t.child=a,o.last=a)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=te(),t.sibling=null,e=ul.current,B(ul,r?1&e|2:1&e),t):(ns(t),null);case 22:case 23:return cl(t),ya(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(ns(t),6&t.subtreeFlags&&(t.flags|=8192)):ns(t),null!==(n=t.updateQueue)&&es(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&M(Ho),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wo(Lo),ns(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function os(e,t){switch(no(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wo(Lo),G(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Q(t),null;case 13:if(cl(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));ho()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return M(ul),null;case 4:return G(),null;case 10:return wo(t.type),null;case 22:case 23:return cl(t),ya(),null!==e&&M(Ho),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wo(Lo),null;default:return null}}function as(e,t){switch(no(t),t.tag){case 3:wo(Lo),G();break;case 26:case 27:case 5:Q(t);break;case 4:G();break;case 13:cl(t);break;case 19:M(ul);break;case 10:wo(t.type);break;case 22:case 23:cl(t),ya(),null!==e&&M(Ho);break;case 24:wo(Lo)}}function is(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var a=n.create,i=n.inst;r=a(),i.destroy=r}n=n.next}while(n!==o)}}catch(l){uu(t,t.return,l)}}function ls(e,t,n){try{var r=t.updateQueue,o=null!==r?r.lastEffect:null;if(null!==o){var a=o.next;r=a;do{if((r.tag&e)===e){var i=r.inst,l=i.destroy;if(void 0!==l){i.destroy=void 0,o=t;var s=n,c=l;try{c()}catch(u){uu(o,s,u)}}}r=r.next}while(r!==a)}}catch(u){uu(t,t.return,u)}}function ss(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{pa(t,n)}catch(r){uu(e,e.return,r)}}}function cs(e,t,n){n.props=gl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){uu(e,t,r)}}function us(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(o){uu(e,t,o)}}function ds(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(o){uu(e,t,o)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(a){uu(e,t,a)}else n.current=null}function fs(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){uu(e,e.return,o)}}function ps(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,a=null,l=null,s=null,c=null,u=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":c=f;default:r.hasOwnProperty(h)||Zu(e,t,h,null,r,f)}}for(var p in r){var h=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":a=h;break;case"name":o=h;break;case"checked":u=h;break;case"defaultChecked":d=h;break;case"value":l=h;break;case"defaultValue":s=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(i(137,t));break;default:h!==f&&Zu(e,t,p,h,r,f)}}return void gt(e,l,s,c,u,d,a,o);case"select":for(a in h=l=s=p=null,n)if(c=n[a],n.hasOwnProperty(a)&&null!=c)switch(a){case"value":break;case"multiple":h=c;default:r.hasOwnProperty(a)||Zu(e,t,a,null,r,c)}for(o in r)if(a=r[o],c=n[o],r.hasOwnProperty(o)&&(null!=a||null!=c))switch(o){case"value":p=a;break;case"defaultValue":s=a;break;case"multiple":l=a;default:a!==c&&Zu(e,t,o,a,r,c)}return t=s,n=l,r=h,void(null!=p?xt(e,!!n,p,!1):!!r!==!!n&&(null!=t?xt(e,!!n,t,!0):xt(e,!!n,n?[]:"",!1)));case"textarea":for(s in h=p=null,n)if(o=n[s],n.hasOwnProperty(s)&&null!=o&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Zu(e,t,s,null,r,o)}for(l in r)if(o=r[l],a=n[l],r.hasOwnProperty(l)&&(null!=o||null!=a))switch(l){case"value":p=o;break;case"defaultValue":h=o;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(i(91));break;default:o!==a&&Zu(e,t,l,o,r,a)}return void bt(e,p,h);case"option":for(var m in n)if(p=n[m],n.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Zu(e,t,m,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))if("selected"===c)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Zu(e,t,c,p,r,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Zu(e,t,g,null,r,p);for(u in r)if(p=r[u],h=n[u],r.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(i(137,t));break;default:Zu(e,t,u,p,r,h)}return;default:if(Et(t)){for(var v in n)p=n[v],n.hasOwnProperty(v)&&void 0!==p&&!r.hasOwnProperty(v)&&Xu(e,t,v,void 0,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||Xu(e,t,d,p,r,h);return}}for(var y in n)p=n[y],n.hasOwnProperty(y)&&null!=p&&!r.hasOwnProperty(y)&&Zu(e,t,y,null,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||null==p&&null==h||Zu(e,t,f,p,r,h)}(r,e.type,n,t),r[Le]=t}catch(o){uu(e,e.return,o)}}function hs(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function ms(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Yu));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gs(e,t,n),e=e.sibling;null!==e;)gs(e,t,n),e=e.sibling}function vs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(vs(e,t,n),e=e.sibling;null!==e;)vs(e,t,n),e=e.sibling}function ys(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);ed(t,r,n),t[_e]=e,t[Le]=n}catch(a){uu(e,e.return,a)}}var xs=!1,bs=!1,ws=!1,js="function"===typeof WeakSet?WeakSet:Set,Ss=null;function ks(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Is(e,n),4&r&&is(5,n);break;case 1:if(Is(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){uu(n,n.return,i)}else{var o=gl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(l){uu(n,n.return,l)}}64&r&&ss(n),512&r&&us(n,n.return);break;case 3:if(Is(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{pa(e,t)}catch(i){uu(n,n.return,i)}}break;case 27:null===t&&4&r&&ys(n);case 26:case 5:Is(e,n),null===t&&4&r&&fs(n),512&r&&us(n,n.return);break;case 12:Is(e,n);break;case 13:Is(e,n),4&r&&Rs(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=hu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||xs)){t=null!==t&&null!==t.memoizedState||bs,o=xs;var a=bs;xs=r,(bs=t)&&!a?Us(e,n,0!==(8772&n.subtreeFlags)):Is(e,n),xs=o,bs=a}break;case 30:break;default:Is(e,n)}}function Cs(e){var t=e.alternate;null!==t&&(e.alternate=null,Cs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Me(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Es=null,Ts=!1;function As(e,t,n){for(n=n.child;null!==n;)Ps(e,t,n),n=n.sibling}function Ps(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ue,n)}catch(a){}switch(n.tag){case 26:bs||ds(n,t),As(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:bs||ds(n,t);var r=Es,o=Ts;pd(n.type)&&(Es=n.stateNode,Ts=!1),As(e,t,n),wd(n.stateNode),Es=r,Ts=o;break;case 5:bs||ds(n,t);case 6:if(r=Es,o=Ts,Es=null,As(e,t,n),Ts=o,null!==(Es=r))if(Ts)try{(9===Es.nodeType?Es.body:"HTML"===Es.nodeName?Es.ownerDocument.body:Es).removeChild(n.stateNode)}catch(i){uu(n,t,i)}else try{Es.removeChild(n.stateNode)}catch(i){uu(n,t,i)}break;case 18:null!==Es&&(Ts?(hd(9===(e=Es).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Af(e)):hd(Es,n.stateNode));break;case 4:r=Es,o=Ts,Es=n.stateNode.containerInfo,Ts=!0,As(e,t,n),Es=r,Ts=o;break;case 0:case 11:case 14:case 15:bs||ls(2,n,t),bs||ls(4,n,t),As(e,t,n);break;case 1:bs||(ds(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&cs(n,t,r)),As(e,t,n);break;case 21:As(e,t,n);break;case 22:bs=(r=bs)||null!==n.memoizedState,As(e,t,n),bs=r;break;default:As(e,t,n)}}function Rs(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Af(e)}catch(n){uu(t,t.return,n)}}function Ns(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new js),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new js),t;default:throw Error(i(435,e.tag))}}(e);t.forEach((function(t){var r=mu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function _s(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r],a=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 27:if(pd(s.type)){Es=s.stateNode,Ts=!1;break e}break;case 5:Es=s.stateNode,Ts=!1;break e;case 3:case 4:Es=s.stateNode.containerInfo,Ts=!0;break e}s=s.return}if(null===Es)throw Error(i(160));Ps(a,l,o),Es=null,Ts=!1,null!==(a=o.alternate)&&(a.return=null),o.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Os(t,e),t=t.sibling}var Ls=null;function Os(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:_s(t,e),Ds(e),4&r&&(ls(3,e,e.return),is(3,e),ls(5,e,e.return));break;case 1:_s(t,e),Ds(e),512&r&&(bs||null===n||ds(n,n.return)),64&r&&xs&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var o=Ls;if(_s(t,e),Ds(e),512&r&&(bs||null===n||ds(n,n.return)),4&r){var a=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,o=o.ownerDocument||o;t:switch(r){case"title":(!(a=o.getElementsByTagName("title")[0])||a[Ue]||a[_e]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=o.createElement(r),o.head.insertBefore(a,o.querySelector("head > title"))),ed(a,r,n),a[_e]=e,Ve(a),r=a;break e;case"link":var l=Ud("link","href",o).get(r+(n.href||""));if(l)for(var s=0;s<l.length;s++)if((a=l[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){l.splice(s,1);break t}ed(a=o.createElement(r),r,n),o.head.appendChild(a);break;case"meta":if(l=Ud("meta","content",o).get(r+(n.content||"")))for(s=0;s<l.length;s++)if((a=l[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){l.splice(s,1);break t}ed(a=o.createElement(r),r,n),o.head.appendChild(a);break;default:throw Error(i(468,r))}a[_e]=e,Ve(a),r=a}e.stateNode=r}else Md(o,e.type,e.stateNode);else e.stateNode=Od(o,r,e.memoizedProps);else a!==r?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===r?Md(o,e.type,e.stateNode):Od(o,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ps(e,e.memoizedProps,n.memoizedProps)}break;case 27:_s(t,e),Ds(e),512&r&&(bs||null===n||ds(n,n.return)),null!==n&&4&r&&ps(e,e.memoizedProps,n.memoizedProps);break;case 5:if(_s(t,e),Ds(e),512&r&&(bs||null===n||ds(n,n.return)),32&e.flags){o=e.stateNode;try{jt(o,"")}catch(h){uu(e,e.return,h)}}4&r&&null!=e.stateNode&&ps(e,o=e.memoizedProps,null!==n?n.memoizedProps:o),1024&r&&(ws=!0);break;case 6:if(_s(t,e),Ds(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(h){uu(e,e.return,h)}}break;case 3:if(Fd=null,o=Ls,Ls=kd(t.containerInfo),_s(t,e),Ls=o,Ds(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Af(t.containerInfo)}catch(h){uu(e,e.return,h)}ws&&(ws=!1,zs(e));break;case 4:r=Ls,Ls=kd(e.stateNode.containerInfo),_s(t,e),Ds(e),Ls=r;break;case 12:default:_s(t,e),Ds(e);break;case 13:_s(t,e),Ds(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(wc=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ns(e,r)));break;case 22:o=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=xs,d=bs;if(xs=u||o,bs=d||c,_s(t,e),bs=d,xs=u,Ds(e),8192&r)e:for(t=e.stateNode,t._visibility=o?-2&t._visibility:1|t._visibility,o&&(null===n||c||xs||bs||Fs(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(a=c.stateNode,o)"function"===typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none";else{s=c.stateNode;var f=c.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(h){uu(c,c.return,h)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=o?"":c.memoizedProps}catch(h){uu(c,c.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Ns(e,n))));break;case 19:_s(t,e),Ds(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ns(e,r)));case 30:case 21:}}function Ds(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(hs(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var o=n.stateNode;vs(e,ms(e),o);break;case 5:var a=n.stateNode;32&n.flags&&(jt(a,""),n.flags&=-33),vs(e,ms(e),a);break;case 3:case 4:var l=n.stateNode.containerInfo;gs(e,ms(e),l);break;default:throw Error(i(161))}}catch(s){uu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function zs(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;zs(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Is(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)ks(e,t.alternate,t),t=t.sibling}function Fs(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ls(4,t,t.return),Fs(t);break;case 1:ds(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&cs(t,t.return,n),Fs(t);break;case 27:wd(t.stateNode);case 26:case 5:ds(t,t.return),Fs(t);break;case 22:null===t.memoizedState&&Fs(t);break;default:Fs(t)}e=e.sibling}}function Us(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,o=e,a=t,i=a.flags;switch(a.tag){case 0:case 11:case 15:Us(o,a,n),is(4,a);break;case 1:if(Us(o,a,n),"function"===typeof(o=(r=a).stateNode).componentDidMount)try{o.componentDidMount()}catch(c){uu(r,r.return,c)}if(null!==(o=(r=a).updateQueue)){var l=r.stateNode;try{var s=o.shared.hiddenCallbacks;if(null!==s)for(o.shared.hiddenCallbacks=null,o=0;o<s.length;o++)fa(s[o],l)}catch(c){uu(r,r.return,c)}}n&&64&i&&ss(a),us(a,a.return);break;case 27:ys(a);case 26:case 5:Us(o,a,n),n&&null===r&&4&i&&fs(a),us(a,a.return);break;case 12:Us(o,a,n);break;case 13:Us(o,a,n),n&&4&i&&Rs(o,a);break;case 22:null===a.memoizedState&&Us(o,a,n),us(a,a.return);break;case 30:break;default:Us(o,a,n)}t=t.sibling}}function Ms(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Do(n))}function Bs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Do(e))}function Hs(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Ws(e,t,n,r),t=t.sibling}function Ws(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:Hs(e,t,n,r),2048&o&&is(9,t);break;case 1:case 13:default:Hs(e,t,n,r);break;case 3:Hs(e,t,n,r),2048&o&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Do(e)));break;case 12:if(2048&o){Hs(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,i=a.id,l=a.onPostCommit;"function"===typeof l&&l(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){uu(t,t.return,s)}}else Hs(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,i=t.alternate,null!==t.memoizedState?2&a._visibility?Hs(e,t,n,r):Vs(e,t):2&a._visibility?Hs(e,t,n,r):(a._visibility|=2,$s(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&o&&Ms(i,t);break;case 24:Hs(e,t,n,r),2048&o&&Bs(t.alternate,t)}}function $s(e,t,n,r,o){for(o=o&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,i=t,l=n,s=r,c=i.flags;switch(i.tag){case 0:case 11:case 15:$s(a,i,l,s,o),is(8,i);break;case 23:break;case 22:var u=i.stateNode;null!==i.memoizedState?2&u._visibility?$s(a,i,l,s,o):Vs(a,i):(u._visibility|=2,$s(a,i,l,s,o)),o&&2048&c&&Ms(i.alternate,i);break;case 24:$s(a,i,l,s,o),o&&2048&c&&Bs(i.alternate,i);break;default:$s(a,i,l,s,o)}t=t.sibling}}function Vs(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:Vs(n,r),2048&o&&Ms(r.alternate,r);break;case 24:Vs(n,r),2048&o&&Bs(r.alternate,r);break;default:Vs(n,r)}t=t.sibling}}var qs=8192;function Gs(e){if(e.subtreeFlags&qs)for(e=e.child;null!==e;)Ks(e),e=e.sibling}function Ks(e){switch(e.tag){case 26:Gs(e),e.flags&qs&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(i(475));var r=Hd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var o=Pd(n.href),a=e.querySelector(Rd(o));if(a)return null!==(e=a._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=$d.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=a,void Ve(a);a=e.ownerDocument||e,n=Nd(n),(o=jd.get(o))&&zd(n,o),Ve(a=a.createElement("link"));var l=a;l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),ed(a,"link",n),t.instance=a}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=$d.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ls,e.memoizedState,e.memoizedProps);break;case 5:default:Gs(e);break;case 3:case 4:var t=Ls;Ls=kd(e.stateNode.containerInfo),Gs(e),Ls=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=qs,qs=16777216,Gs(e),qs=t):Gs(e))}}function Qs(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Js(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Xs(r,e)}Qs(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Ys(e),e=e.sibling}function Ys(e){switch(e.tag){case 0:case 11:case 15:Js(e),2048&e.flags&&ls(9,e,e.return);break;case 3:case 12:default:Js(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Zs(e)):Js(e)}}function Zs(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Xs(r,e)}Qs(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:ls(8,t,t.return),Zs(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Zs(t));break;default:Zs(t)}e=e.sibling}}function Xs(e,t){for(;null!==Ss;){var n=Ss;switch(n.tag){case 0:case 11:case 15:ls(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Do(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Ss=r;else e:for(n=e;null!==Ss;){var o=(r=Ss).sibling,a=r.return;if(Cs(r),r===n){Ss=null;break e}if(null!==o){o.return=a,Ss=o;break e}Ss=a}}}var ec={getCacheForType:function(e){var t=To(Lo),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tc="function"===typeof WeakMap?WeakMap:Map,nc=0,rc=null,oc=null,ac=0,ic=0,lc=null,sc=!1,cc=!1,uc=!1,dc=0,fc=0,pc=0,hc=0,mc=0,gc=0,vc=0,yc=null,xc=null,bc=!1,wc=0,jc=1/0,Sc=null,kc=null,Cc=0,Ec=null,Tc=null,Ac=0,Pc=0,Rc=null,Nc=null,_c=0,Lc=null;function Oc(){if(0!==(2&nc)&&0!==ac)return ac&-ac;if(null!==O.T){return 0!==Fo?Fo:Pu()}return Re()}function Dc(){0===gc&&(gc=0===(536870912&ac)||ao?je():536870912);var e=ol.current;return null!==e&&(e.flags|=32),gc}function zc(e,t,n){(e!==rc||2!==ic&&9!==ic)&&null===e.cancelPendingCommit||(Wc(e,0),Mc(e,ac,gc,!1)),Ce(e,n),0!==(2&nc)&&e===rc||(e===rc&&(0===(2&nc)&&(hc|=n),4===fc&&Mc(e,ac,gc,!1)),ju(e))}function Ic(e,t,n){if(0!==(6&nc))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||be(e,t),o=r?function(e,t){var n=nc;nc|=2;var r=Vc(),o=qc();rc!==e||ac!==t?(Sc=null,jc=te()+500,Wc(e,t)):cc=be(e,t);e:for(;;)try{if(0!==ic&&null!==oc){t=oc;var a=lc;t:switch(ic){case 1:ic=0,lc=null,Xc(e,t,a,1);break;case 2:case 9:if(Jo(a)){ic=0,lc=null,Zc(t);break}t=function(){2!==ic&&9!==ic||rc!==e||(ic=7),ju(e)},a.then(t,t);break e;case 3:ic=7;break e;case 4:ic=5;break e;case 7:Jo(a)?(ic=0,lc=null,Zc(t)):(ic=0,lc=null,Xc(e,t,a,7));break;case 5:var l=null;switch(oc.tag){case 26:l=oc.memoizedState;case 5:case 27:var s=oc;if(!l||Bd(l)){ic=0,lc=null;var c=s.sibling;if(null!==c)oc=c;else{var u=s.return;null!==u?(oc=u,eu(u)):oc=null}break t}}ic=0,lc=null,Xc(e,t,a,5);break;case 6:ic=0,lc=null,Xc(e,t,a,6);break;case 8:Hc(),fc=6;break e;default:throw Error(i(462))}}Jc();break}catch(d){$c(e,d)}return xo=yo=null,O.H=r,O.A=o,nc=n,null!==oc?0:(rc=null,ac=0,Ar(),fc)}(e,t):Kc(e,t,!0),a=r;;){if(0===o){cc&&!r&&Mc(e,t,0,!1);break}if(n=e.current.alternate,!a||Uc(n)){if(2===o){if(a=t,e.errorRecoveryDisabledLanes&a)var l=0;else l=0!==(l=-536870913&e.pendingLanes)?l:536870912&l?536870912:0;if(0!==l){t=l;e:{var s=e;o=yc;var c=s.current.memoizedState.isDehydrated;if(c&&(Wc(s,l).flags|=256),2!==(l=Kc(s,l,!1))){if(uc&&!c){s.errorRecoveryDisabledLanes|=a,hc|=a,o=4;break e}a=xc,xc=o,null!==a&&(null===xc?xc=a:xc.push.apply(xc,a))}o=l}if(a=!1,2!==o)continue}}if(1===o){Wc(e,0),Mc(e,t,0,!0);break}e:{switch(r=e,a=o){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Mc(r,t,gc,!sc);break e;case 2:xc=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(o=wc+300-te())){if(Mc(r,t,gc,!sc),0!==xe(r,0,!0))break e;r.timeoutHandle=sd(Fc.bind(null,r,n,xc,Sc,bc,t,gc,hc,vc,sc,a,2,-0,0),o)}else Fc(r,n,xc,Sc,bc,t,gc,hc,vc,sc,a,0,-0,0)}break}o=Kc(e,t,!1),a=!1}ju(e)}function Fc(e,t,n,r,o,a,l,s,c,u,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Hd={stylesheets:null,count:0,unsuspend:Wd},Ks(t),null!==(f=function(){if(null===Hd)throw Error(i(475));var e=Hd;return e.stylesheets&&0===e.count&&qd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&qd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nu.bind(null,e,t,a,n,r,o,l,s,c,d,1,p,h)),void Mc(e,a,l,!u);nu(e,t,a,n,r,o,l,s,c)}function Uc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!Qn(a(),o))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Mc(e,t,n,r){t&=~mc,t&=~hc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var a=31-pe(o),i=1<<a;r[a]=-1,o&=~i}0!==n&&Ee(e,n,t)}function Bc(){return 0!==(6&nc)||(Su(0,!1),!1)}function Hc(){if(null!==oc){if(0===ic)var e=oc.return;else xo=yo=null,Fa(e=oc),Qi=null,Ji=0,e=oc;for(;null!==e;)as(e.alternate,e),e=e.return;oc=null}}function Wc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,cd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hc(),rc=e,oc=n=Fr(e.current,null),ac=t,ic=0,lc=null,sc=!1,cc=be(e,t),uc=!1,vc=gc=mc=hc=pc=fc=0,xc=yc=null,bc=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var o=31-pe(r),a=1<<o;t|=e[o],r&=~a}return dc=t,Ar(),n}function $c(e,t){ba=null,O.H=Vi,t===qo||t===Ko?(t=ea(),ic=3):t===Go?(t=ea(),ic=4):ic=t===El?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,lc=t,null===oc&&(fc=1,wl(e,kr(t,e.current)))}function Vc(){var e=O.H;return O.H=Vi,null===e?Vi:e}function qc(){var e=O.A;return O.A=ec,e}function Gc(){fc=4,sc||(4194048&ac)!==ac&&null!==ol.current||(cc=!0),0===(134217727&pc)&&0===(134217727&hc)||null===rc||Mc(rc,ac,gc,!1)}function Kc(e,t,n){var r=nc;nc|=2;var o=Vc(),a=qc();rc===e&&ac===t||(Sc=null,Wc(e,t)),t=!1;var i=fc;e:for(;;)try{if(0!==ic&&null!==oc){var l=oc,s=lc;switch(ic){case 8:Hc(),i=6;break e;case 3:case 2:case 9:case 6:null===ol.current&&(t=!0);var c=ic;if(ic=0,lc=null,Xc(e,l,s,c),n&&cc){i=0;break e}break;default:c=ic,ic=0,lc=null,Xc(e,l,s,c)}}Qc(),i=fc;break}catch(u){$c(e,u)}return t&&e.shellSuspendCounter++,xo=yo=null,nc=r,O.H=o,O.A=a,null===oc&&(rc=null,ac=0,Ar()),i}function Qc(){for(;null!==oc;)Yc(oc)}function Jc(){for(;null!==oc&&!X();)Yc(oc)}function Yc(e){var t=Yl(e.alternate,e,dc);e.memoizedProps=e.pendingProps,null===t?eu(e):oc=t}function Zc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=zl(n,t,t.pendingProps,t.type,void 0,ac);break;case 11:t=zl(n,t,t.pendingProps,t.type.render,t.ref,ac);break;case 5:Fa(t);default:as(n,t),t=Yl(n,t=oc=Ur(t,dc),dc)}e.memoizedProps=e.pendingProps,null===t?eu(e):oc=t}function Xc(e,t,n,r){xo=yo=null,Fa(t),Qi=null,Ji=0;var o=t.return;try{if(function(e,t,n,r,o){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&ko(t,n,o,!0),null!==(n=ol.current)){switch(n.tag){case 13:return null===al?Gc():null===n.alternate&&0===fc&&(fc=3),n.flags&=-257,n.flags|=65536,n.lanes=o,r===Qo?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),du(e,r,o)),!1;case 22:return n.flags|=65536,r===Qo?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),du(e,r,o)),!1}throw Error(i(435,n.tag))}return du(e,r,o),Gc(),!1}if(ao)return null!==(t=ol.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=o,r!==so&&go(kr(e=Error(i(422),{cause:r}),n))):(r!==so&&go(kr(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,o&=-o,e.lanes|=o,r=kr(r,n),sa(e,o=Sl(e.stateNode,r,o)),4!==fc&&(fc=2)),!1;var a=Error(i(520),{cause:r});if(a=kr(a,n),null===yc?yc=[a]:yc.push(a),4!==fc&&(fc=2),null===t)return!0;r=kr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=o&-o,n.lanes|=e,sa(n,e=Sl(n.stateNode,r,e)),!1;case 1:if(t=n.type,a=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==a&&"function"===typeof a.componentDidCatch&&(null===kc||!kc.has(a))))return n.flags|=65536,o&=-o,n.lanes|=o,Cl(o=kl(o),e,n,r),sa(n,o),!1}n=n.return}while(null!==n);return!1}(e,o,t,n,ac))return fc=1,wl(e,kr(n,e.current)),void(oc=null)}catch(a){if(null!==o)throw oc=o,a;return fc=1,wl(e,kr(n,e.current)),void(oc=null)}32768&t.flags?(ao||1===r?e=!0:cc||0!==(536870912&ac)?e=!1:(sc=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ol.current)&&13===r.tag&&(r.flags|=16384))),tu(t,e)):eu(t)}function eu(e){var t=e;do{if(0!==(32768&t.flags))return void tu(t,sc);e=t.return;var n=rs(t.alternate,t,dc);if(null!==n)return void(oc=n);if(null!==(t=t.sibling))return void(oc=t);oc=t=e}while(null!==t);0===fc&&(fc=5)}function tu(e,t){do{var n=os(e.alternate,e);if(null!==n)return n.flags&=32767,void(oc=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(oc=e);oc=e=n}while(null!==e);fc=6,oc=null}function nu(e,t,n,r,o,a,l,s,c){e.cancelPendingCommit=null;do{lu()}while(0!==Cc);if(0!==(6&nc))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(a=t.lanes|t.childLanes,function(e,t,n,r,o,a){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var l=e.entanglements,s=e.expirationTimes,c=e.hiddenUpdates;for(n=i&~n;0<n;){var u=31-pe(n),d=1<<u;l[u]=0,s[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var p=f[u];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Ee(e,r,0),0!==a&&0===o&&0!==e.tag&&(e.suspendedLanes|=a&~(i&~t))}(e,n,a|=Tr,l,s,c),e===rc&&(oc=rc=null,ac=0),Tc=t,Ec=e,Ac=n,Pc=a,Rc=o,Nc=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,Y(ae,(function(){return su(),null}))):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=O.T,O.T=null,o=D.p,D.p=2,l=nc,nc|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var l=0,s=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(s=l+o),f!==a||0!==r&&3!==f.nodeType||(c=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++u===o&&(s=l),p===a&&++d===r&&(c=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,Ss=t;null!==Ss;)if(e=(t=Ss).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Ss=e;else for(;null!==Ss;){switch(a=(t=Ss).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==a){e=void 0,n=t,o=a.memoizedProps,a=a.memoizedState,r=n.stateNode;try{var m=gl(n.type,o,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,a),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){uu(n,n.return,v)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))md(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,Ss=e;break}Ss=t.return}}(e,t)}finally{nc=l,D.p=o,O.T=r}}Cc=1,ru(),ou(),au()}}function ru(){if(1===Cc){Cc=0;var e=Ec,t=Tc,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=O.T,O.T=null;var r=D.p;D.p=2;var o=nc;nc|=4;try{Os(t,e);var a=nd,i=er(e.containerInfo),l=a.focusedElem,s=a.selectionRange;if(i!==l&&l&&l.ownerDocument&&Xn(l.ownerDocument.documentElement,l)){if(null!==s&&tr(l)){var c=s.start,u=s.end;if(void 0===u&&(u=c),"selectionStart"in l)l.selectionStart=c,l.selectionEnd=Math.min(u,l.value.length);else{var d=l.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=l.textContent.length,m=Math.min(s.start,h),g=void 0===s.end?m:Math.min(s.end,h);!p.extend&&m>g&&(i=g,g=m,m=i);var v=Zn(l,m),y=Zn(l,g);if(v&&y&&(1!==p.rangeCount||p.anchorNode!==v.node||p.anchorOffset!==v.offset||p.focusNode!==y.node||p.focusOffset!==y.offset)){var x=d.createRange();x.setStart(v.node,v.offset),p.removeAllRanges(),m>g?(p.addRange(x),p.extend(y.node,y.offset)):(x.setEnd(y.node,y.offset),p.addRange(x))}}}}for(d=[],p=l;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof l.focus&&l.focus(),l=0;l<d.length;l++){var b=d[l];b.element.scrollLeft=b.left,b.element.scrollTop=b.top}}nf=!!td,nd=td=null}finally{nc=o,D.p=r,O.T=n}}e.current=t,Cc=2}}function ou(){if(2===Cc){Cc=0;var e=Ec,t=Tc,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=O.T,O.T=null;var r=D.p;D.p=2;var o=nc;nc|=4;try{ks(e,t.alternate,t)}finally{nc=o,D.p=r,O.T=n}}Cc=3}}function au(){if(4===Cc||3===Cc){Cc=0,ee();var e=Ec,t=Tc,n=Ac,r=Nc;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Cc=5:(Cc=0,Tc=Ec=null,iu(e,e.pendingLanes));var o=e.pendingLanes;if(0===o&&(kc=null),Pe(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ue,t,void 0,128===(128&t.current.flags))}catch(s){}if(null!==r){t=O.T,o=D.p,D.p=2,O.T=null;try{for(var a=e.onRecoverableError,i=0;i<r.length;i++){var l=r[i];a(l.value,{componentStack:l.stack})}}finally{O.T=t,D.p=o}}0!==(3&Ac)&&lu(),ju(e),o=e.pendingLanes,0!==(4194090&n)&&0!==(42&o)?e===Lc?_c++:(_c=0,Lc=e):_c=0,Su(0,!1)}}function iu(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Do(t)))}function lu(e){return ru(),ou(),au(),su()}function su(){if(5!==Cc)return!1;var e=Ec,t=Pc;Pc=0;var n=Pe(Ac),r=O.T,o=D.p;try{D.p=32>n?32:n,O.T=null,n=Rc,Rc=null;var a=Ec,l=Ac;if(Cc=0,Tc=Ec=null,Ac=0,0!==(6&nc))throw Error(i(331));var s=nc;if(nc|=4,Ys(a.current),Ws(a,a.current,l,n),nc=s,Su(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ue,a)}catch(c){}return!0}finally{D.p=o,O.T=r,iu(e,t)}}function cu(e,t,n){t=kr(n,t),null!==(e=ia(e,t=Sl(e.stateNode,t,2),2))&&(Ce(e,2),ju(e))}function uu(e,t,n){if(3===e.tag)cu(e,e,n);else for(;null!==t;){if(3===t.tag){cu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===kc||!kc.has(r))){e=kr(n,e),null!==(r=ia(t,n=kl(2),2))&&(Cl(n,r,t,e),Ce(r,2),ju(r));break}}t=t.return}}function du(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tc;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(uc=!0,o.add(n),e=fu.bind(null,e,t,n),t.then(e,e))}function fu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rc===e&&(ac&n)===n&&(4===fc||3===fc&&(62914560&ac)===ac&&300>te()-wc?0===(2&nc)&&Wc(e,0):mc|=n,vc===ac&&(vc=0)),ju(e)}function pu(e,t){0===t&&(t=Se()),null!==(e=Nr(e,t))&&(Ce(e,t),ju(e))}function hu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pu(e,n)}function mu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),pu(e,n)}var gu=null,vu=null,yu=!1,xu=!1,bu=!1,wu=0;function ju(e){e!==vu&&null===e.next&&(null===vu?gu=vu=e:vu=vu.next=e),xu=!0,yu||(yu=!0,dd((function(){0!==(6&nc)?Y(re,ku):Cu()})))}function Su(e,t){if(!bu&&xu){bu=!0;do{for(var n=!1,r=gu;null!==r;){if(!t)if(0!==e){var o=r.pendingLanes;if(0===o)var a=0;else{var i=r.suspendedLanes,l=r.pingedLanes;a=(1<<31-pe(42|e)+1)-1,a=201326741&(a&=o&~(i&~l))?201326741&a|1:a?2|a:0}0!==a&&(n=!0,Au(r,a))}else a=ac,0===(3&(a=xe(r,r===rc?a:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||be(r,a)||(n=!0,Au(r,a));r=r.next}}while(n);bu=!1}}function ku(){Cu()}function Cu(){xu=yu=!1;var e=0;0!==wu&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==ld&&(ld=e,!0);return ld=null,!1}()&&(e=wu),wu=0);for(var t=te(),n=null,r=gu;null!==r;){var o=r.next,a=Eu(r,t);0===a?(r.next=null,null===n?gu=o:n.next=o,null===o&&(vu=n)):(n=r,(0!==e||0!==(3&a))&&(xu=!0)),r=o}Su(e,!1)}function Eu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var i=31-pe(a),l=1<<i,s=o[i];-1===s?0!==(l&n)&&0===(l&r)||(o[i]=we(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}if(n=ac,n=xe(e,e===(t=rc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ic||9===ic)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&Z(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||be(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&Z(r),Pe(n)){case 2:case 8:n=oe;break;case 32:default:n=ae;break;case 268435456:n=le}return r=Tu.bind(null,e),n=Y(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&Z(r),e.callbackPriority=2,e.callbackNode=null,2}function Tu(e,t){if(0!==Cc&&5!==Cc)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(lu()&&e.callbackNode!==n)return null;var r=ac;return 0===(r=xe(e,e===rc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Ic(e,r,t),Eu(e,te()),null!=e.callbackNode&&e.callbackNode===n?Tu.bind(null,e):null)}function Au(e,t){if(lu())return null;Ic(e,t,!0)}function Pu(){return 0===wu&&(wu=je()),wu}function Ru(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Pt(""+e)}function Nu(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var _u=0;_u<wr.length;_u++){var Lu=wr[_u];jr(Lu.toLowerCase(),"on"+(Lu[0].toUpperCase()+Lu.slice(1)))}jr(pr,"onAnimationEnd"),jr(hr,"onAnimationIteration"),jr(mr,"onAnimationStart"),jr("dblclick","onDoubleClick"),jr("focusin","onFocus"),jr("focusout","onBlur"),jr(gr,"onTransitionRun"),jr(vr,"onTransitionStart"),jr(yr,"onTransitionCancel"),jr(xr,"onTransitionEnd"),Qe("onMouseEnter",["mouseout","mouseover"]),Qe("onMouseLeave",["mouseout","mouseover"]),Qe("onPointerEnter",["pointerout","pointerover"]),Qe("onPointerLeave",["pointerout","pointerover"]),Ke("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ke("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ke("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ke("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ou="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Du=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ou));function zu(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==a&&o.isPropagationStopped())break e;a=l,o.currentTarget=c;try{a(o)}catch(u){vl(u)}o.currentTarget=null,a=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,c=l.currentTarget,l=l.listener,s!==a&&o.isPropagationStopped())break e;a=l,o.currentTarget=c;try{a(o)}catch(u){vl(u)}o.currentTarget=null,a=s}}}}function Iu(e,t){var n=t[De];void 0===n&&(n=t[De]=new Set);var r=e+"__bubble";n.has(r)||(Bu(t,e,2,!1),n.add(r))}function Fu(e,t,n){var r=0;t&&(r|=4),Bu(n,e,r,t)}var Uu="_reactListening"+Math.random().toString(36).slice(2);function Mu(e){if(!e[Uu]){e[Uu]=!0,qe.forEach((function(t){"selectionchange"!==t&&(Du.has(t)||Fu(t,!1,e),Fu(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Uu]||(t[Uu]=!0,Fu("selectionchange",!1,t))}}function Bu(e,t,n,r){switch(uf(t)){case 2:var o=rf;break;case 8:o=of;break;default:o=af}n=o.bind(null,t,n,e),o=void 0,!Ut||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Hu(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o)break;if(4===i)for(i=r.return;null!==i;){var c=i.tag;if((3===c||4===c)&&i.stateNode.containerInfo===o)return;i=i.return}for(;null!==l;){if(null===(i=Be(l)))return;if(5===(c=i.tag)||6===c||26===c||27===c){r=a=i;continue e}l=l.parentNode}}r=r.return}zt((function(){var r=a,o=Nt(n),i=[];e:{var l=br.get(e);if(void 0!==l){var c=Xt,u=e;switch(e){case"keypress":if(0===Vt(n))break e;case"keydown":case"keyup":c=mn;break;case"focusin":u="focus",c=an;break;case"focusout":u="blur",c=an;break;case"beforeblur":case"afterblur":c=an;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=on;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=vn;break;case pr:case hr:case mr:c=ln;break;case xr:c=yn;break;case"scroll":case"scrollend":c=tn;break;case"wheel":c=xn;break;case"copy":case"cut":case"paste":c=sn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=gn;break;case"toggle":case"beforetoggle":c=bn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==l?l+"Capture":null:l;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=It(m,p))&&d.push(Wu(m,g,h)),f)break;m=m.return}0<d.length&&(l=new c(l,u,null,n,o),i.push({event:l,listeners:d}))}}if(0===(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===Rt||!(u=n.relatedTarget||n.fromElement)||!Be(u)&&!u[Oe])&&(c||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?Be(u):null)&&(f=s(u),d=u.tag,u!==f||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=rn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==c?l:We(c),h=null==u?l:We(u),(l=new d(g,m+"leave",c,n,o)).target=f,l.relatedTarget=h,g=null,Be(o)===r&&((d=new d(p,m+"enter",u,n,o)).target=h,d.relatedTarget=f,g=d),f=g,c&&u)e:{for(p=u,m=0,h=d=c;h;h=Vu(h))m++;for(h=0,g=p;g;g=Vu(g))h++;for(;0<m-h;)d=Vu(d),m--;for(;0<h-m;)p=Vu(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=Vu(d),p=Vu(p)}d=null}else d=null;null!==c&&qu(i,l,c,d,!1),null!==u&&null!==f&&qu(i,f,u,d,!0)}if("select"===(c=(l=r?We(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===c&&"file"===l.type)var v=Fn;else if(_n(l))if(Un)v=Kn;else{v=qn;var y=Vn}else!(c=l.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==l.type&&"radio"!==l.type?r&&Et(r.elementType)&&(v=Fn):v=Gn;switch(v&&(v=v(e,r))?Ln(i,v,n,o):(y&&y(e,l,r),"focusout"===e&&r&&"number"===l.type&&null!=r.memoizedProps.value&&yt(l,"number",l.value)),y=r?We(r):window,e){case"focusin":(_n(y)||"true"===y.contentEditable)&&(rr=y,or=r,ar=null);break;case"focusout":ar=or=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,lr(i,n,o);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":lr(i,n,o)}var x;if(jn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Rn?An(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Cn&&"ko"!==n.locale&&(Rn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Rn&&(x=$t()):(Ht="value"in(Bt=o)?Bt.value:Bt.textContent,Rn=!0)),0<(y=$u(r,b)).length&&(b=new cn(b,e,null,n,o),i.push({event:b,listeners:y}),x?b.data=x:null!==(x=Pn(n))&&(b.data=x))),(x=kn?function(e,t){switch(e){case"compositionend":return Pn(t);case"keypress":return 32!==t.which?null:(Tn=!0,En);case"textInput":return(e=t.data)===En&&Tn?null:e;default:return null}}(e,n):function(e,t){if(Rn)return"compositionend"===e||!jn&&An(e,t)?(e=$t(),Wt=Ht=Bt=null,Rn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(b=$u(r,"onBeforeInput")).length&&(y=new cn("onBeforeInput","beforeinput",null,n,o),i.push({event:y,listeners:b}),y.data=x)),function(e,t,n,r,o){if("submit"===t&&n&&n.stateNode===o){var a=Ru((o[Le]||null).action),i=r.submitter;i&&null!==(t=(t=i[Le]||null)?Ru(t.formAction):i.getAttribute("formAction"))&&(a=t,i=null);var l=new Xt("action","action",null,r,o);e.push({event:l,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==wu){var e=i?Nu(o,i):new FormData(o);Ni(n,{pending:!0,data:e,method:o.method,action:a},null,e)}}else"function"===typeof a&&(l.preventDefault(),e=i?Nu(o,i):new FormData(o),Ni(n,{pending:!0,data:e,method:o.method,action:a},a,e))},currentTarget:o}]})}}(i,e,r,n,o)}zu(i,t)}))}function Wu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $u(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;if(5!==(o=o.tag)&&26!==o&&27!==o||null===a||(null!=(o=It(e,n))&&r.unshift(Wu(e,o,a)),null!=(o=It(e,t))&&r.push(Wu(e,o,a))),3===e.tag)return r;e=e.return}return[]}function Vu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qu(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(l=l.tag,null!==s&&s===r)break;5!==l&&26!==l&&27!==l||null===c||(s=c,o?null!=(c=It(n,a))&&i.unshift(Wu(n,c,s)):o||null!=(c=It(n,a))&&i.push(Wu(n,c,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Gu=/\r\n?/g,Ku=/\u0000|\uFFFD/g;function Qu(e){return("string"===typeof e?e:""+e).replace(Gu,"\n").replace(Ku,"")}function Ju(e,t){return t=Qu(t),Qu(e)===t}function Yu(){}function Zu(e,t,n,r,o,a){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||jt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&jt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Ct(e,r,a);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Pt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof a&&("formAction"===n?("input"!==t&&Zu(e,t,"name",o.name,o,null),Zu(e,t,"formEncType",o.formEncType,o,null),Zu(e,t,"formMethod",o.formMethod,o,null),Zu(e,t,"formTarget",o.formTarget,o,null)):(Zu(e,t,"encType",o.encType,o,null),Zu(e,t,"method",o.method,o,null),Zu(e,t,"target",o.target,o,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Pt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Yu);break;case"onScroll":null!=r&&Iu("scroll",e);break;case"onScrollEnd":null!=r&&Iu("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Pt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Iu("beforetoggle",e),Iu("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Tt.get(n)||n,r)}}function Xu(e,t,n,r,o,a){switch(n){case"style":Ct(e,r,a);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?jt(e,r):("number"===typeof r||"bigint"===typeof r)&&jt(e,""+r);break;case"onScroll":null!=r&&Iu("scroll",e);break;case"onScrollEnd":null!=r&&Iu("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Yu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ge.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(o=n.endsWith("Capture"),t=n.slice(2,o?n.length-7:void 0),"function"===typeof(a=null!=(a=e[Le]||null)?a[n]:null)&&e.removeEventListener(t,a,o),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,o)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Iu("error",e),Iu("load",e);var r,o=!1,a=!1;for(r in n)if(n.hasOwnProperty(r)){var l=n[r];if(null!=l)switch(r){case"src":o=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Zu(e,t,r,l,n,null)}}return a&&Zu(e,t,"srcSet",n.srcSet,n,null),void(o&&Zu(e,t,"src",n.src,n,null));case"input":Iu("invalid",e);var s=r=l=a=null,c=null,u=null;for(o in n)if(n.hasOwnProperty(o)){var d=n[o];if(null!=d)switch(o){case"name":a=d;break;case"type":l=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:Zu(e,t,o,d,n,null)}}return vt(e,r,s,c,u,l,a,!1),void dt(e);case"select":for(a in Iu("invalid",e),o=l=r=null,n)if(n.hasOwnProperty(a)&&null!=(s=n[a]))switch(a){case"value":r=s;break;case"defaultValue":l=s;break;case"multiple":o=s;default:Zu(e,t,a,s,n,null)}return t=r,n=l,e.multiple=!!o,void(null!=t?xt(e,!!o,t,!1):null!=n&&xt(e,!!o,n,!0));case"textarea":for(l in Iu("invalid",e),r=a=o=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":o=s;break;case"defaultValue":a=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(i(91));break;default:Zu(e,t,l,s,n,null)}return wt(e,o,a,r),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(o=n[c]))if("selected"===c)e.selected=o&&"function"!==typeof o&&"symbol"!==typeof o;else Zu(e,t,c,o,n,null);return;case"dialog":Iu("beforetoggle",e),Iu("toggle",e),Iu("cancel",e),Iu("close",e);break;case"iframe":case"object":Iu("load",e);break;case"video":case"audio":for(o=0;o<Ou.length;o++)Iu(Ou[o],e);break;case"image":Iu("error",e),Iu("load",e);break;case"details":Iu("toggle",e);break;case"embed":case"source":case"link":Iu("error",e),Iu("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(o=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Zu(e,t,u,o,n,null)}return;default:if(Et(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(o=n[d])&&Xu(e,t,d,o,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(o=n[s])&&Zu(e,t,s,o,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function od(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ad(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function id(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ld=null;var sd="function"===typeof setTimeout?setTimeout:void 0,cd="function"===typeof clearTimeout?clearTimeout:void 0,ud="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ud?function(e){return ud.resolve(null).then(e).catch(fd)}:sd;function fd(e){setTimeout((function(){throw e}))}function pd(e){return"head"===e}function hd(e,t){var n=t,r=0,o=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&wd(i.documentElement),2&n&&wd(i.body),4&n)for(wd(n=i.head),i=n.firstChild;i;){var l=i.nextSibling,s=i.nodeName;i[Ue]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=l}}if(0===o)return e.removeChild(a),void Af(t);o--}else"$"===n||"$?"===n||"$!"===n?o++:r=n.charCodeAt(0)-48;else r=0;n=a}while(n);Af(t)}function md(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Me(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function vd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var yd=null;function xd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function bd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function wd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Me(e)}var jd=new Map,Sd=new Set;function kd(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=D.d;D.d={f:function(){var e=Cd.f(),t=Bc();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Li(t):Cd.r(e)},D:function(e){Cd.D(e),Td("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),Td("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=Ed;if(r&&e&&t){var o='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(o+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(o+='[imagesizes="'+mt(n.imageSizes)+'"]')):o+='[href="'+mt(e)+'"]';var a=o;switch(t){case"style":a=Pd(e);break;case"script":a=_d(e)}jd.has(a)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),jd.set(a,e),null!==r.querySelector(o)||"style"===t&&r.querySelector(Rd(a))||"script"===t&&r.querySelector(Ld(a))||(ed(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=Ed;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",o='link[rel="modulepreload"][as="'+mt(r)+'"][href="'+mt(e)+'"]',a=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=_d(e)}if(!jd.has(a)&&(e=f({rel:"modulepreload",href:e},t),jd.set(a,e),null===n.querySelector(o))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ld(a)))return}ed(r=n.createElement("link"),"link",e),Ve(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=Ed;if(n&&e){var r=$e(n).hoistableScripts,o=_d(e),a=r.get(o);a||((a=n.querySelector(Ld(o)))||(e=f({src:e,async:!0},t),(t=jd.get(o))&&Id(e,t),Ve(a=n.createElement("script")),ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}},S:function(e,t,n){Cd.S(e,t,n);var r=Ed;if(r&&e){var o=$e(r).hoistableStyles,a=Pd(e);t=t||"default";var i=o.get(a);if(!i){var l={loading:0,preload:null};if(i=r.querySelector(Rd(a)))l.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=jd.get(a))&&zd(e,n);var s=i=r.createElement("link");Ve(s),ed(s,"link",e),s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),s.addEventListener("load",(function(){l.loading|=1})),s.addEventListener("error",(function(){l.loading|=2})),l.loading|=4,Dd(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:l},o.set(a,i)}}},M:function(e,t){Cd.M(e,t);var n=Ed;if(n&&e){var r=$e(n).hoistableScripts,o=_d(e),a=r.get(o);a||((a=n.querySelector(Ld(o)))||(e=f({src:e,async:!0,type:"module"},t),(t=jd.get(o))&&Id(e,t),Ve(a=n.createElement("script")),ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}}};var Ed="undefined"===typeof document?null:document;function Td(e,t,n){var r=Ed;if(r&&"string"===typeof t&&t){var o=mt(t);o='link[rel="'+e+'"][href="'+o+'"]',"string"===typeof n&&(o+='[crossorigin="'+n+'"]'),Sd.has(o)||(Sd.add(o),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(o)&&(ed(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}}function Ad(e,t,n,r){var o,a,l,s,c=(c=$.current)?kd(c):null;if(!c)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Pd(n.href),(r=(n=$e(c).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Pd(n.href);var u=$e(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(Rd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),jd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},jd.set(e,n),u||(o=c,a=e,l=n,s=d.state,o.querySelector('link[rel="preload"][as="style"]['+a+"]")?s.loading=1:(a=o.createElement("link"),s.preload=a,a.addEventListener("load",(function(){return s.loading|=1})),a.addEventListener("error",(function(){return s.loading|=2})),ed(a,"link",l),Ve(a),o.head.appendChild(a))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=_d(n),(r=(n=$e(c).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Pd(e){return'href="'+mt(e)+'"'}function Rd(e){return'link[rel="stylesheet"]['+e+"]"}function Nd(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function _d(e){return'[src="'+mt(e)+'"]'}function Ld(e){return"script[async]"+e}function Od(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(r)return t.instance=r,Ve(r),r;var o=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ve(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",o),Dd(r,n.precedence,e),t.instance=r;case"stylesheet":o=Pd(n.href);var a=e.querySelector(Rd(o));if(a)return t.state.loading|=4,t.instance=a,Ve(a),a;r=Nd(n),(o=jd.get(o))&&zd(r,o),Ve(a=(e.ownerDocument||e).createElement("link"));var l=a;return l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),ed(a,"link",r),t.state.loading|=4,Dd(a,n.precedence,e),t.instance=a;case"script":return a=_d(n.src),(o=e.querySelector(Ld(a)))?(t.instance=o,Ve(o),o):(r=n,(o=jd.get(a))&&Id(r=f({},n),o),Ve(o=(e=e.ownerDocument||e).createElement("script")),ed(o,"link",r),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Dd(r,n.precedence,e));return t.instance}function Dd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,a=o,i=0;i<r.length;i++){var l=r[i];if(l.dataset.precedence===t)a=l;else if(a!==o)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function zd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Id(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Fd=null;function Ud(e,t,n){if(null===Fd){var r=new Map,o=Fd=new Map;o.set(n,r)}else(r=(o=Fd).get(n))||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var a=n[o];if(!(a[Ue]||a[_e]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var i=a.getAttribute(t)||"";i=e+i;var l=r.get(i);l?l.push(a):r.set(i,[a])}}return r}function Md(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Hd=null;function Wd(){}function $d(){if(this.count--,0===this.count)if(this.stylesheets)qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Vd=null;function qd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Vd=new Map,t.forEach(Gd,e),Vd=null,$d.call(e))}function Gd(e,t){if(!(4&t.state.loading)){var n=Vd.get(e);if(n)var r=n.get(null);else{n=new Map,Vd.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<o.length;a++){var i=o[a];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(o=t.instance).getAttribute("data-precedence"),(a=n.get(i)||r)===r&&n.set(null,o),n.set(i,o),this.count++,r=$d.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),a?a.parentNode.insertBefore(o,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(o,e.firstChild),t.state.loading|=4}}var Kd={$$typeof:w,Provider:null,Consumer:null,_currentValue:z,_currentValue2:z,_threadCount:0};function Qd(e,t,n,r,o,a,i,l){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ke(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ke(0),this.hiddenUpdates=ke(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=a,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=l,this.incompleteTransitions=new Map}function Jd(e,t,n,r,o,a,i,l,s,c,u,d){return e=new Qd(e,t,n,i,l,s,c,d),t=1,!0===a&&(t|=24),a=zr(3,null,null,t),e.current=a,a.stateNode=e,(t=Oo()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},ra(a),e}function Yd(e){return e?e=Or:Or}function Zd(e,t,n,r,o,a){o=Yd(o),null===r.context?r.context=o:r.pendingContext=o,(r=aa(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=ia(e,r,t))&&(zc(n,0,t),la(n,e,t))}function Xd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Xd(e,t),(e=e.alternate)&&Xd(e,t)}function tf(e){if(13===e.tag){var t=Nr(e,67108864);null!==t&&zc(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var o=O.T;O.T=null;var a=D.p;try{D.p=2,af(e,t,n,r)}finally{D.p=a,O.T=o}}function of(e,t,n,r){var o=O.T;O.T=null;var a=D.p;try{D.p=8,af(e,t,n,r)}finally{D.p=a,O.T=o}}function af(e,t,n,r){if(nf){var o=lf(r);if(null===o)Hu(e,t,r,sf,n),xf(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return ff=bf(ff,e,t,n,r,o),!0;case"dragenter":return pf=bf(pf,e,t,n,r,o),!0;case"mouseover":return hf=bf(hf,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return mf.set(a,bf(mf.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,gf.set(a,bf(gf.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(xf(e,r),4&t&&-1<yf.indexOf(e)){for(;null!==o;){var a=He(o);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var i=ye(a.pendingLanes);if(0!==i){var l=a;for(l.pendingLanes|=2,l.entangledLanes|=2;i;){var s=1<<31-pe(i);l.entanglements[1]|=s,i&=~s}ju(a),0===(6&nc)&&(jc=te()+500,Su(0,!1))}}break;case 13:null!==(l=Nr(a,2))&&zc(l,0,2),Bc(),ef(a,2)}if(null===(a=lf(r))&&Hu(e,t,r,sf,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Hu(e,t,r,null,n)}}function lf(e){return cf(e=Nt(e))}var sf=null;function cf(e){if(sf=null,null!==(e=Be(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=c(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return sf=e,null}function uf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case oe:return 8;case ae:case ie:return 32;case le:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,hf=null,mf=new Map,gf=new Map,vf=[],yf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function xf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":hf=null;break;case"pointerover":case"pointerout":mf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function bf(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=He(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function wf(e){var t=Be(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=c(n)))return e.blockedOn=t,void function(e,t){var n=D.p;try{return D.p=e,t()}finally{D.p=n}}(e.priority,(function(){if(13===n.tag){var e=Oc();e=Ae(e);var t=Nr(n,e);null!==t&&zc(t,0,e),ef(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function jf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=lf(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Rt=r,n.target.dispatchEvent(r),Rt=null,t.shift()}return!0}function Sf(e,t,n){jf(e)&&n.delete(t)}function kf(){df=!1,null!==ff&&jf(ff)&&(ff=null),null!==pf&&jf(pf)&&(pf=null),null!==hf&&jf(hf)&&(hf=null),mf.forEach(Sf),gf.forEach(Sf)}function Cf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,kf)))}var Ef=null;function Tf(e){Ef!==e&&(Ef=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,(function(){Ef===e&&(Ef=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],o=e[t+2];if("function"!==typeof r){if(null===cf(r||n))continue;break}var a=He(n);null!==a&&(e.splice(t,3),t-=3,Ni(a,{pending:!0,data:o,method:n.method,action:r},r,o))}})))}function Af(e){function t(t){return Cf(t,e)}null!==ff&&Cf(ff,e),null!==pf&&Cf(pf,e),null!==hf&&Cf(hf,e),mf.forEach(t),gf.forEach(t);for(var n=0;n<vf.length;n++){var r=vf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<vf.length&&null===(n=vf[0]).blockedOn;)wf(n),null===n.blockedOn&&vf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var o=n[r],a=n[r+1],i=o[Le]||null;if("function"===typeof a)i||Tf(n);else if(i){var l=null;if(a&&a.hasAttribute("formAction")){if(o=a,i=a[Le]||null)l=i.formAction;else if(null!==cf(o))continue}else l=i.action;"function"===typeof l?n[r+1]=l:(n.splice(r,3),r-=3),Tf(n)}}}function Pf(e){this._internalRoot=e}function Rf(e){this._internalRoot=e}Rf.prototype.render=Pf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Zd(t.current,Oc(),e,t,null,null)},Rf.prototype.unmount=Pf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Zd(e.current,2,null,e,null,null),Bc(),t[Oe]=null}},Rf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Re();e={blockedOn:null,target:e,priority:t};for(var n=0;n<vf.length&&0!==t&&t<vf[n].priority;n++);vf.splice(n,0,e),0===n&&wf(e)}};var Nf=o.version;if("19.1.0"!==Nf)throw Error(i(527,Nf,"19.1.0"));D.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(r=o.return)){n=r;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===n)return u(o),e;if(a===r)return u(o),t;a=a.sibling}throw Error(i(188))}if(n.return!==r.return)n=o,r=a;else{for(var l=!1,c=o.child;c;){if(c===n){l=!0,n=o,r=a;break}if(c===r){l=!0,r=o,n=a;break}c=c.sibling}if(!l){for(c=a.child;c;){if(c===n){l=!0,n=a,r=o;break}if(c===r){l=!0,r=a,n=o;break}c=c.sibling}if(!l)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var _f={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:O,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Lf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Lf.isDisabled&&Lf.supportsFiber)try{ue=Lf.inject(_f),de=Lf}catch(Df){}}t.createRoot=function(e,t){if(!l(e))throw Error(i(299));var n=!1,r="",o=yl,a=xl,s=bl;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(o=t.onUncaughtError),void 0!==t.onCaughtError&&(a=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Jd(e,1,!1,null,0,n,r,o,a,s,0,null),e[Oe]=t.current,Mu(e),new Pf(t)},t.hydrateRoot=function(e,t,n){if(!l(e))throw Error(i(299));var r=!1,o="",a=yl,s=xl,c=bl,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Jd(e,1,!0,t,0,r,o,a,s,c,0,u)).context=Yd(null),n=t.current,(o=aa(r=Ae(r=Oc()))).callback=null,ia(n,o,r),n=r,t.current.lanes=n,Ce(t,n),ju(t),e[Oe]=t.current,Mu(e),new Rf(t)},t.version="19.1.0"},43:(e,t,n)=>{"use strict";e.exports=n(288)},288:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function y(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var b=x.prototype=new y;b.constructor=x,m(b,v.prototype),b.isPureReactComponent=!0;var w=Array.isArray,j={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function k(e,t,r,o,a,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var E=/\/+/g;function T(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function A(){}function P(e,t,o,a,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s,c,u=!1;if(null===e)u=!0;else switch(l){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0;break;case f:return P((u=e._init)(e._payload),t,o,a,i)}}if(u)return i=i(e),u=""===a?"."+T(e,0):a,w(i)?(o="",null!=u&&(o=u.replace(E,"$&/")+"/"),P(i,t,o,"",(function(e){return e}))):null!=i&&(C(i)&&(s=i,c=o+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(E,"$&/")+"/")+u,i=k(s.type,c,void 0,0,0,s.props)),t.push(i)),1;u=0;var d,h=""===a?".":a+":";if(w(e))for(var m=0;m<e.length;m++)u+=P(a=e[m],t,o,l=h+T(a,m),i);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(a=e.next()).done;)u+=P(a=a.value,t,o,l=h+T(a,m++),i);else if("object"===l){if("function"===typeof e.then)return P(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(A,A):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,o,a,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function R(e,t,n){if(null==e)return e;var r=[],o=0;return P(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function N(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var _="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function L(){}t.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=o,t.Profiler=i,t.PureComponent=x,t.StrictMode=a,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=j,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return j.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),o=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!S.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var i=Array(a),l=0;l<a;l++)i[l]=arguments[l+2];r.children=i}return k(e.type,o,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:l,_context:e},e},t.createElement=function(e,t,n){var r,o={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var i=arguments.length-2;if(1===i)o.children=n;else if(1<i){for(var l=Array(i),s=0;s<i;s++)l[s]=arguments[s+2];o.children=l}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===o[r]&&(o[r]=i[r]);return k(e,a,void 0,0,0,o)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:N}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=j.T,n={};j.T=n;try{var r=e(),o=j.S;null!==o&&o(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(L,_)}catch(a){_(a)}finally{j.T=t}},t.unstable_useCacheRefresh=function(){return j.H.useCacheRefresh()},t.use=function(e){return j.H.use(e)},t.useActionState=function(e,t,n){return j.H.useActionState(e,t,n)},t.useCallback=function(e,t){return j.H.useCallback(e,t)},t.useContext=function(e){return j.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return j.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=j.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return j.H.useId()},t.useImperativeHandle=function(e,t,n){return j.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return j.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return j.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return j.H.useMemo(e,t)},t.useOptimistic=function(e,t){return j.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return j.H.useReducer(e,t,n)},t.useRef=function(e){return j.H.useRef(e)},t.useState=function(e){return j.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return j.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return j.H.useTransition()},t.version="19.1.0"},324:e=>{e.exports=function(e,t,n,r){var o=n?n.call(r,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!==typeof e||!e||"object"!==typeof t||!t)return!1;var a=Object.keys(e),i=Object.keys(t);if(a.length!==i.length)return!1;for(var l=Object.prototype.hasOwnProperty.bind(t),s=0;s<a.length;s++){var c=a[s];if(!l(c))return!1;var u=e[c],d=t[c];if(!1===(o=n?n.call(r,u,d,c):void 0)||void 0===o&&u!==d)return!1}return!0}},358:(e,t)=>{"use strict";const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,l=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function c(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},391:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},579:(e,t,n)=>{"use strict";e.exports=n(799)},672:(e,t,n)=>{"use strict";var r=n(43);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var i={d:{f:a,r:function(){throw Error(o(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},l=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(o(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:l,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=i.p;try{if(s.T=null,i.p=2,e)return e()}finally{s.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin),o="string"===typeof t.integrity?t.integrity:void 0,a="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:o,fetchPriority:a}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:o,fetchPriority:a,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(e,t,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==t.key&&(o=""+t.key),"key"in t)for(var a in r={},t)"key"!==a&&(r[a]=t[a]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:o,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=o,t.jsxs=o},853:(e,t,n)=>{"use strict";e.exports=n(896)},896:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>a(s,n))c<o&&0>a(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else{if(!(c<o&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var c=[],u=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,v=!1,y="function"===typeof setTimeout?setTimeout:null,x="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(u);null!==t;){if(null===t.callback)o(u);else{if(!(t.startTime<=e))break;o(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function j(e){if(g=!1,w(e),!m)if(null!==r(c))m=!0,k||(k=!0,S());else{var t=r(u);null!==t&&_(j,t.startTime-e)}}var S,k=!1,C=-1,E=5,T=-1;function A(){return!!v||!(t.unstable_now()-T<E)}function P(){if(v=!1,k){var e=t.unstable_now();T=e;var n=!0;try{e:{m=!1,g&&(g=!1,x(C),C=-1),h=!0;var a=p;try{t:{for(w(e),f=r(c);null!==f&&!(f.expirationTime>e&&A());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var l=i(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof l){f.callback=l,w(e),n=!0;break t}f===r(c)&&o(c),w(e)}else o(c);f=r(c)}if(null!==f)n=!0;else{var s=r(u);null!==s&&_(j,s.startTime-e),n=!1}}break e}finally{f=null,p=a,h=!1}n=void 0}}finally{n?S():k=!1}}}if("function"===typeof b)S=function(){b(P)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,N=R.port2;R.port1.onmessage=P,S=function(){N.postMessage(null)}}else S=function(){y(P,0)};function _(e,n){C=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){v=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(e.sortIndex=a,n(u,e),null===r(c)&&e===r(u)&&(g?(x(C),C=-1):g=!0,_(j,a-i))):(e.sortIndex=l,n(c,e),m||h||(m=!0,k||(k=!0,S()))),e},t.unstable_shouldYield=A,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[])),n.u=e=>"static/js/"+e+".e14fecc8.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="ubi-cpv-react-frontend:";n.l=(r,o,a,i)=>{if(e[r])e[r].push(o);else{var l,s;if(void 0!==a)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+a){l=d;break}}l||(s=!0,(l=document.createElement("script")).charset="utf-8",l.timeout=120,n.nc&&l.setAttribute("nonce",n.nc),l.setAttribute("data-webpack",t+a),l.src=r),e[r]=[o];var f=(t,n)=>{l.onerror=l.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],l.parentNode&&l.parentNode.removeChild(l),o&&o.forEach((e=>e(n))),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=f.bind(null,l.onerror),l.onload=f.bind(null,l.onload),s&&document.head.appendChild(l)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else{var a=new Promise(((n,r)=>o=e[t]=[n,r]));r.push(o[2]=a);var i=n.p+n.u(t),l=new Error;n.l(i,(r=>{if(n.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var a=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;l.message="Loading chunk "+t+" failed.\n("+a+": "+i+")",l.name="ChunkLoadError",l.type=a,l.request=i,o[1](l)}}),"chunk-"+t,t)}};var t=(t,r)=>{var o,a,i=r[0],l=r[1],s=r[2],c=0;if(i.some((t=>0!==e[t]))){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(s)s(n)}for(t&&t(r);c<i.length;c++)a=i[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self.webpackChunkubi_cpv_react_frontend=self.webpackChunkubi_cpv_react_frontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0,(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>Pn,hasStandardBrowserEnv:()=>Nn,hasStandardBrowserWebWorkerEnv:()=>_n,navigator:()=>Rn,origin:()=>Ln});var t=n(43),r=n(391);function o(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}function l(e,t,n){return(t=i(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}n(358);const u=["sri"],d=["page"],f=["page","matches"],p=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],h=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],m=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var g="popstate";function v(){return k((function(e,t){let{pathname:n,search:r,hash:o}=e.location;return w("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:j(t)}),null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function y(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function x(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function b(e,t){return{usr:e.state,key:e.key,idx:t}}function w(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return c(c({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?S(t):t),{},{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function j(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function S(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function k(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:o=document.defaultView,v5Compat:a=!1}=r,i=o.history,l="POP",s=null,u=d();function d(){return(i.state||{idx:null}).idx}function f(){l="POP";let e=d(),t=null==e?null:e-u;u=e,s&&s({action:l,location:h.location,delta:t})}function p(e){return C(e)}null==u&&(u=0,i.replaceState(c(c({},i.state),{},{idx:u}),""));let h={get action(){return l},get location(){return e(o,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return o.addEventListener(g,f),s=e,()=>{o.removeEventListener(g,f),s=null}},createHref:e=>t(o,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){l="PUSH";let r=w(h.location,e,t);n&&n(r,e),u=d()+1;let c=b(r,u),f=h.createHref(r);try{i.pushState(c,"",f)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;o.location.assign(f)}a&&s&&s({action:l,location:h.location,delta:1})},replace:function(e,t){l="REPLACE";let r=w(h.location,e,t);n&&n(r,e),u=d();let o=b(r,u),c=h.createHref(r);i.replaceState(o,"",c),a&&s&&s({action:l,location:h.location,delta:0})},go:e=>i.go(e)};return h}function C(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),y(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:j(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function E(e,t){return T(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function T(e,t,n,r){let o=H(("string"===typeof t?S(t):t).pathname||"/",n);if(null==o)return null;let a=A(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(a);let i=null;for(let l=0;null==i&&l<a.length;++l){let e=B(o);i=F(a[l],e,r)}return i}function A(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(y(i.relativePath.startsWith(r),'Absolute route path "'.concat(i.relativePath,'" nested under path "').concat(r,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),i.relativePath=i.relativePath.slice(r.length));let l=G([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(y(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(l,'".')),A(e.children,t,s,l)),(null!=e.path||e.index)&&t.push({path:l,score:I(l,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let r of P(e.path))o(e,t,r);else o(e,t)})),t}function P(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===r.length)return o?[a,""]:[a];let i=P(r.join("/")),l=[];return l.push(...i.map((e=>""===e?a:[a,e].join("/")))),o&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var R=/^:[\w-]+$/,N=3,_=2,L=1,O=10,D=-2,z=e=>"*"===e;function I(e,t){let n=e.split("/"),r=n.length;return n.some(z)&&(r+=D),t&&(r+=_),n.filter((e=>!z(e))).reduce(((e,t)=>e+(R.test(t)?N:""===t?L:O)),r)}function F(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,o={},a="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],s=l===r.length-1,c="/"===a?t:t.slice(a.length)||"/",u=U({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},c),d=e.route;if(!u&&s&&n&&!r[r.length-1].route.index&&(u=U({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(o,u.params),i.push({params:o,pathname:G([a,u.pathname]),pathnameBase:K(G([a,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(a=G([a,u.pathnameBase]))}return i}function U(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=M(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:o}=t;if("*"===r){let e=l[n]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[n];return e[r]=o&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:i,pattern:e}}function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];x("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function B(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return x(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function H(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function W(e,t,n,r){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(r),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function $(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function V(e){let t=$(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function q(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=S(e):(r=c({},e),y(!r.pathname||!r.pathname.includes("?"),W("?","pathname","search",r)),y(!r.pathname||!r.pathname.includes("#"),W("#","pathname","hash",r)),y(!r.search||!r.search.includes("#"),W("#","search","hash",r)));let a,i=""===e||""===r.pathname,l=i?"/":r.pathname;if(null==l)a=n;else{let e=t.length-1;if(!o&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}a=e>=0?t[e]:"/"}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:o=""}="string"===typeof e?S(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:Q(r),hash:J(o)}}(r,a),u=l&&"/"!==l&&l.endsWith("/"),d=(i||"."===l)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!d||(s.pathname+="/"),s}var G=e=>e.join("/").replace(/\/\/+/g,"/"),K=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Q=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",J=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function Y(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var Z=["POST","PUT","PATCH","DELETE"],X=(new Set(Z),["GET",...Z]);new Set(X),Symbol("ResetLoaderData");var ee=t.createContext(null);ee.displayName="DataRouter";var te=t.createContext(null);te.displayName="DataRouterState";var ne=t.createContext({isTransitioning:!1});ne.displayName="ViewTransition";var re=t.createContext(new Map);re.displayName="Fetchers";var oe=t.createContext(null);oe.displayName="Await";var ae=t.createContext(null);ae.displayName="Navigation";var ie=t.createContext(null);ie.displayName="Location";var le=t.createContext({outlet:null,matches:[],isDataRoute:!1});le.displayName="Route";var se=t.createContext(null);se.displayName="RouteError";function ce(){return null!=t.useContext(ie)}function ue(){return y(ce(),"useLocation() may be used only in the context of a <Router> component."),t.useContext(ie).location}var de="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function fe(e){t.useContext(ae).static||t.useLayoutEffect(e)}function pe(){let{isDataRoute:e}=t.useContext(le);return e?function(){let{router:e}=Se("useNavigate"),n=Ce("useNavigate"),r=t.useRef(!1);fe((()=>{r.current=!0}));let o=t.useCallback((async function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};x(r.current,de),r.current&&("number"===typeof t?e.navigate(t):await e.navigate(t,c({fromRouteId:n},o)))}),[e,n]);return o}():function(){y(ce(),"useNavigate() may be used only in the context of a <Router> component.");let e=t.useContext(ee),{basename:n,navigator:r}=t.useContext(ae),{matches:o}=t.useContext(le),{pathname:a}=ue(),i=JSON.stringify(V(o)),l=t.useRef(!1);fe((()=>{l.current=!0}));let s=t.useCallback((function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(x(l.current,de),!l.current)return;if("number"===typeof t)return void r.go(t);let s=q(t,JSON.parse(i),a,"path"===o.relative);null==e&&"/"!==n&&(s.pathname="/"===s.pathname?n:G([n,s.pathname])),(o.replace?r.replace:r.push)(s,o.state,o)}),[n,r,i,a,e]);return s}()}t.createContext(null);function he(){let{matches:e}=t.useContext(le),n=e[e.length-1];return n?n.params:{}}function me(e){let{relative:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:r}=t.useContext(le),{pathname:o}=ue(),a=JSON.stringify(V(r));return t.useMemo((()=>q(e,JSON.parse(a),o,"path"===n)),[e,a,o,n])}function ge(e,n,r,o){y(ce(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=t.useContext(ae),{matches:i}=t.useContext(le),l=i[i.length-1],s=l?l.params:{},u=l?l.pathname:"/",d=l?l.pathnameBase:"/",f=l&&l.route;{let e=f&&f.path||"";Ae(u,!f||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(u,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let p,h=ue();if(n){var m;let e="string"===typeof n?S(n):n;y("/"===d||(null===(m=e.pathname)||void 0===m?void 0:m.startsWith(d)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(d,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),p=e}else p=h;let g=p.pathname||"/",v=g;if("/"!==d){let e=d.replace(/^\//,"").split("/");v="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=E(e,{pathname:v});x(f||null!=b,'No routes matched location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" ')),x(null==b||void 0!==b[b.length-1].route.element||void 0!==b[b.length-1].route.Component||void 0!==b[b.length-1].route.lazy,'Matched leaf route at location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let w=we(b&&b.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:G([d,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:G([d,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,r,o);return n&&w?t.createElement(ie.Provider,{value:{location:c({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:"POP"}},w):w}function ve(){let e=Ee(),n=Y(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,o="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:o},i={padding:"2px 4px",backgroundColor:o},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=t.createElement(t.Fragment,null,t.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),t.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",t.createElement("code",{style:i},"ErrorBoundary")," or"," ",t.createElement("code",{style:i},"errorElement")," prop on your route.")),t.createElement(t.Fragment,null,t.createElement("h2",null,"Unexpected Application Error!"),t.createElement("h3",{style:{fontStyle:"italic"}},n),r?t.createElement("pre",{style:a},r):null,l)}var ye=t.createElement(ve,null),xe=class extends t.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?t.createElement(le.Provider,{value:this.props.routeContext},t.createElement(se.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function be(e){let{routeContext:n,match:r,children:o}=e,a=t.useContext(ee);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),t.createElement(le.Provider,{value:n},o)}function we(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==n.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let o=e,a=null===r||void 0===r?void 0:r.errors;if(null!=a){let e=o.findIndex((e=>e.route.id&&void 0!==(null===a||void 0===a?void 0:a[e.route.id])));y(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(a).join(","))),o=o.slice(0,Math.min(o.length,e+1))}let i=!1,l=-1;if(r)for(let t=0;t<o.length;t++){let e=o[t];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=t),e.route.id){let{loaderData:t,errors:n}=r,a=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!n||void 0===n[e.route.id]);if(e.route.lazy||a){i=!0,o=l>=0?o.slice(0,l+1):[o[0]];break}}}return o.reduceRight(((e,s,c)=>{let u,d=!1,f=null,p=null;r&&(u=a&&s.route.id?a[s.route.id]:void 0,f=s.route.errorElement||ye,i&&(l<0&&0===c?(Ae("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,p=null):l===c&&(d=!0,p=s.route.hydrateFallbackElement||null)));let h=n.concat(o.slice(0,c+1)),m=()=>{let n;return n=u?f:d?p:s.route.Component?t.createElement(s.route.Component,null):s.route.element?s.route.element:e,t.createElement(be,{match:s,routeContext:{outlet:e,matches:h,isDataRoute:null!=r},children:n})};return r&&(s.route.ErrorBoundary||s.route.errorElement||0===c)?t.createElement(xe,{location:r.location,revalidation:r.revalidation,component:f,error:u,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}function je(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function Se(e){let n=t.useContext(ee);return y(n,je(e)),n}function ke(e){let n=t.useContext(te);return y(n,je(e)),n}function Ce(e){let n=function(e){let n=t.useContext(le);return y(n,je(e)),n}(e),r=n.matches[n.matches.length-1];return y(r.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),r.route.id}function Ee(){var e;let n=t.useContext(se),r=ke("useRouteError"),o=Ce("useRouteError");return void 0!==n?n:null===(e=r.errors)||void 0===e?void 0:e[o]}var Te={};function Ae(e,t,n){t||Te[e]||(Te[e]=!0,x(!1,n))}t.memo((function(e){let{routes:t,future:n,state:r}=e;return ge(t,void 0,r,n)}));function Pe(e){let{to:n,replace:r,state:o,relative:a}=e;y(ce(),"<Navigate> may be used only in the context of a <Router> component.");let{static:i}=t.useContext(ae);x(!i,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:l}=t.useContext(le),{pathname:s}=ue(),c=pe(),u=q(n,V(l),s,"path"===a),d=JSON.stringify(u);return t.useEffect((()=>{c(JSON.parse(d),{replace:r,state:o,relative:a})}),[c,d,a,r,o]),null}function Re(e){y(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Ne(e){let{basename:n="/",children:r=null,location:o,navigationType:a="POP",navigator:i,static:l=!1}=e;y(!ce(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=n.replace(/^\/*/,"/"),c=t.useMemo((()=>({basename:s,navigator:i,static:l,future:{}})),[s,i,l]);"string"===typeof o&&(o=S(o));let{pathname:u="/",search:d="",hash:f="",state:p=null,key:h="default"}=o,m=t.useMemo((()=>{let e=H(u,s);return null==e?null:{location:{pathname:e,search:d,hash:f,state:p,key:h},navigationType:a}}),[s,u,d,f,p,h,a]);return x(null!=m,'<Router basename="'.concat(s,'"> is not able to match the URL "').concat(u).concat(d).concat(f,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==m?null:t.createElement(ae.Provider,{value:c},t.createElement(ie.Provider,{children:r,value:m}))}function _e(e){let{children:t,location:n}=e;return ge(Le(t),n)}t.Component;function Le(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=[];return t.Children.forEach(e,((e,o)=>{if(!t.isValidElement(e))return;let a=[...n,o];if(e.type===t.Fragment)return void r.push.apply(r,Le(e.props.children,a));y(e.type===Re,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),y(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Le(e.props.children,a)),r.push(i)})),r}var Oe="get",De="application/x-www-form-urlencoded";function ze(e){return null!=e&&"string"===typeof e.tagName}var Ie=null;var Fe=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ue(e){return null==e||Fe.has(e)?e:(x(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(De,'"')),null)}function Me(e,t){let n,r,o,a,i;if(ze(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?H(i,t):null,n=e.getAttribute("method")||Oe,o=Ue(e.getAttribute("enctype"))||De,a=new FormData(e)}else if(function(e){return ze(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return ze(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?H(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||Oe,o=Ue(e.getAttribute("formenctype"))||Ue(i.getAttribute("enctype"))||De,a=new FormData(i,e),!function(){if(null===Ie)try{new FormData(document.createElement("form"),0),Ie=!1}catch(e){Ie=!0}return Ie}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?"".concat(t,"."):"";a.append("".concat(e,"x"),"0"),a.append("".concat(e,"y"),"0")}else t&&a.append(t,r)}}else{if(ze(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Oe,r=null,o=De,i=e}var l;return a&&"text/plain"===o&&(i=a,a=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:a,body:i}}function Be(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function He(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}function We(e){return null!=e&&"string"===typeof e.page}function $e(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Ve(e,t,n,r,o,a){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null===(r=n[t].route.path)||void 0===r?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===a?t.filter(((e,t)=>i(e,t)||l(e,t))):"data"===a?t.filter(((t,a)=>{let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,a)||l(t,a))return!0;if(t.route.shouldRevalidate){var c;let r=t.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:(null===(c=n[0])||void 0===c?void 0:c.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0})):[]}function qe(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let o=[r.module];return r.clientActionModule&&(o=o.concat(r.clientActionModule)),r.clientLoaderModule&&(o=o.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(o=o.concat(r.hydrateFallbackModule)),r.imports&&(o=o.concat(r.imports)),o})).flat(1),[...new Set(r)];var r}function Ge(e,t){let n=new Set,r=new Set(t);return e.reduce(((e,o)=>{if(t&&!We(o)&&"script"===o.as&&o.href&&r.has(o.href))return e;let a=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(o));return n.has(a)||(n.add(a),e.push({key:a,link:o})),e}),[])}function Ke(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var Qe=new Set([100,101,204,205]);function Je(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===H(n.pathname,t)?n.pathname="".concat(t.replace(/\/$/,""),"/_root.data"):n.pathname="".concat(n.pathname.replace(/\/$/,""),".data"),n}t.Component;function Ye(e){let{error:n,isOutsideRemixApp:r}=e;console.error(n);let o,a=t.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(Y(n))return t.createElement(Ze,{title:"Unhandled Thrown Response!"},t.createElement("h1",{style:{fontSize:"24px"}},n.status," ",n.statusText),a);if(n instanceof Error)0;else{let e=null==n?"Unknown Error":"object"===typeof n&&"toString"in n?n.toString():JSON.stringify(n);new Error(e)}return t.createElement(Ze,{title:"Application Error!",isOutsideRemixApp:r},t.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),t.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},o.stack),a)}function Ze(e){var n;let{title:r,renderScripts:o,isOutsideRemixApp:a,children:i}=e,{routeModules:l}=rt();return null!==l.root&&void 0!==n&&n.Layout&&!a?i:t.createElement("html",{lang:"en"},t.createElement("head",null,t.createElement("meta",{charSet:"utf-8"}),t.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),t.createElement("title",null,r)),t.createElement("body",null,t.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},i,o?t.createElement(ut,null):null)))}function Xe(e,t){return"lazy"===e.mode&&!0===t}function et(){let e=t.useContext(ee);return Be(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function tt(){let e=t.useContext(te);return Be(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var nt=t.createContext(void 0);function rt(){let e=t.useContext(nt);return Be(e,"You must render this element inside a <HydratedRouter> element"),e}function ot(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function at(e,t,n){if(n&&!ct)return[e[0]];if(t){let n=e.findIndex((e=>void 0!==t[e.route.id]));return e.slice(0,n+1)}return e}function it(e){let{page:n}=e,r=o(e,d),{router:a}=et(),i=t.useMemo((()=>E(a.routes,n,a.basename)),[a.routes,n,a.basename]);return i?t.createElement(st,c({page:n,matches:i},r)):null}function lt(e){let{manifest:n,routeModules:r}=rt(),[o,a]=t.useState([]);return t.useEffect((()=>{let t=!1;return async function(e,t,n){let r=await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await He(r,n);return e.links?e.links():[]}return[]})));return Ge(r.flat(1).filter($e).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?c(c({},e),{},{rel:"prefetch",as:"style"}):c(c({},e),{},{rel:"prefetch"}))))}(e,n,r).then((e=>{t||a(e)})),()=>{t=!0}}),[e,n,r]),o}function st(e){let{page:n,matches:r}=e,a=o(e,f),i=ue(),{manifest:l,routeModules:s}=rt(),{basename:u}=et(),{loaderData:d,matches:p}=tt(),h=t.useMemo((()=>Ve(n,r,p,l,i,"data")),[n,r,p,l,i]),m=t.useMemo((()=>Ve(n,r,p,l,i,"assets")),[n,r,p,l,i]),g=t.useMemo((()=>{if(n===i.pathname+i.search+i.hash)return[];let e=new Set,t=!1;if(r.forEach((n=>{var r;let o=l.routes[n.route.id];o&&o.hasLoader&&(!h.some((e=>e.route.id===n.route.id))&&n.route.id in d&&null!==(r=s[n.route.id])&&void 0!==r&&r.shouldRevalidate||o.hasClientLoader?t=!0:e.add(n.route.id))})),0===e.size)return[];let o=Je(n,u);return t&&e.size>0&&o.searchParams.set("_routes",r.filter((t=>e.has(t.route.id))).map((e=>e.route.id)).join(",")),[o.pathname+o.search]}),[u,d,i,l,h,r,n,s]),v=t.useMemo((()=>qe(m,l)),[m,l]),y=lt(m);return t.createElement(t.Fragment,null,g.map((e=>t.createElement("link",c({key:e,rel:"prefetch",as:"fetch",href:e},a)))),v.map((e=>t.createElement("link",c({key:e,rel:"modulepreload",href:e},a)))),y.map((e=>{let{key:n,link:r}=e;return t.createElement("link",c({key:n},r))})))}nt.displayName="FrameworkContext";var ct=!1;function ut(e){let{manifest:n,serverHandoffString:r,isSpaMode:a,renderMeta:i,routeDiscovery:l,ssr:s}=rt(),{router:d,static:f,staticContext:p}=et(),{matches:h}=tt(),m=Xe(l,s);i&&(i.didRenderScripts=!0);let g=at(h,null,a);t.useEffect((()=>{0}),[]);let v=t.useMemo((()=>{var a;let i=p?"window.__reactRouterContext = ".concat(r,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",l=f?"".concat(null!==n.hmr&&void 0!==a&&a.runtime?"import ".concat(JSON.stringify(n.hmr.runtime),";"):"").concat(m?"":"import ".concat(JSON.stringify(n.url)),";\n").concat(g.map(((e,t)=>{let r="route".concat(t),o=n.routes[e.route.id];Be(o,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:a,clientLoaderModule:i,clientMiddlewareModule:l,hydrateFallbackModule:s,module:c}=o,u=[...a?[{module:a,varName:"".concat(r,"_clientAction")}]:[],...i?[{module:i,varName:"".concat(r,"_clientLoader")}]:[],...l?[{module:l,varName:"".concat(r,"_clientMiddleware")}]:[],...s?[{module:s,varName:"".concat(r,"_HydrateFallback")}]:[],{module:c,varName:"".concat(r,"_main")}];return 1===u.length?"import * as ".concat(r," from ").concat(JSON.stringify(c),";"):[u.map((e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";'))).join("\n"),"const ".concat(r," = {").concat(u.map((e=>"...".concat(e.varName))).join(","),"};")].join("\n")})).join("\n"),"\n  ").concat(m?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,r=o(e,u),a=new Set(t.state.matches.map((e=>e.route.id))),i=t.state.location.pathname.split("/").filter(Boolean),l=["/"];for(i.pop();i.length>0;)l.push("/".concat(i.join("/"))),i.pop();l.forEach((e=>{let n=E(t.routes,e,t.basename);n&&n.forEach((e=>a.add(e.route.id)))}));let s=[...a].reduce(((e,t)=>Object.assign(e,{[t]:r.routes[t]})),{});return c(c({},r),{},{routes:s,sri:!!n||void 0})}(n,d),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(g.map(((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t))).join(","),"};\n\nimport(").concat(JSON.stringify(n.entry.module),");"):" ";return t.createElement(t.Fragment,null,t.createElement("script",c(c({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(i),type:void 0})),t.createElement("script",c(c({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(l),type:"module",async:!0})))}),[]),y=ct?[]:(n.entry.imports.concat(qe(g,n,{includeHydrateFallback:!0})),[...new Set(x)]);var x;let b="object"===typeof n.sri?n.sri:{};return ct?null:t.createElement(t.Fragment,null,"object"===typeof n.sri?t.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:b})}}):null,m?null:t.createElement("link",{rel:"modulepreload",href:n.url,crossOrigin:e.crossOrigin,integrity:b[n.url],suppressHydrationWarning:!0}),t.createElement("link",{rel:"modulepreload",href:n.entry.module,crossOrigin:e.crossOrigin,integrity:b[n.entry.module],suppressHydrationWarning:!0}),y.map((n=>t.createElement("link",{key:n,rel:"modulepreload",href:n,crossOrigin:e.crossOrigin,integrity:b[n],suppressHydrationWarning:!0}))),v)}function dt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach((t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)}))}}var ft="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{ft&&(window.__reactRouterVersion="7.6.1")}catch(Pv){}function pt(e){let{basename:n,children:r,window:o}=e,a=t.useRef();null==a.current&&(a.current=v({window:o,v5Compat:!0}));let i=a.current,[l,s]=t.useState({action:i.action,location:i.location}),c=t.useCallback((e=>{t.startTransition((()=>s(e)))}),[s]);return t.useLayoutEffect((()=>i.listen(c)),[i,c]),t.createElement(Ne,{basename:n,children:r,location:l.location,navigationType:l.action,navigator:i})}var ht=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,mt=t.forwardRef((function(e,n){let r,{onClick:a,discover:i="render",prefetch:l="none",relative:s,reloadDocument:u,replace:d,state:f,target:h,to:m,preventScrollReset:g,viewTransition:v}=e,b=o(e,p),{basename:w}=t.useContext(ae),S="string"===typeof m&&ht.test(m),k=!1;if("string"===typeof m&&S&&(r=m,ft))try{let e=new URL(window.location.href),t=m.startsWith("//")?new URL(e.protocol+m):new URL(m),n=H(t.pathname,w);t.origin===e.origin&&null!=n?m=n+t.search+t.hash:k=!0}catch(Pv){x(!1,'<Link to="'.concat(m,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let C=function(e){let{relative:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};y(ce(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:o}=t.useContext(ae),{hash:a,pathname:i,search:l}=me(e,{relative:n}),s=i;return"/"!==r&&(s="/"===i?r:G([r,i])),o.createHref({pathname:s,search:l,hash:a})}(m,{relative:s}),[E,T,A]=function(e,n){let r=t.useContext(nt),[o,a]=t.useState(!1),[i,l]=t.useState(!1),{onFocus:s,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:f}=n,p=t.useRef(null);t.useEffect((()=>{if("render"===e&&l(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{l(e.isIntersecting)}))}),{threshold:.5});return p.current&&e.observe(p.current),()=>{e.disconnect()}}}),[e]),t.useEffect((()=>{if(o){let e=setTimeout((()=>{l(!0)}),100);return()=>{clearTimeout(e)}}}),[o]);let h=()=>{a(!0)},m=()=>{a(!1),l(!1)};return r?"intent"!==e?[i,p,{}]:[i,p,{onFocus:ot(s,h),onBlur:ot(c,m),onMouseEnter:ot(u,h),onMouseLeave:ot(d,m),onTouchStart:ot(f,h)}]:[!1,p,{}]}(l,b),P=function(e){let{target:n,replace:r,state:o,preventScrollReset:a,relative:i,viewTransition:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=pe(),c=ue(),u=me(e,{relative:i});return t.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:j(c)===j(u);s(e,{replace:n,state:o,preventScrollReset:a,relative:i,viewTransition:l})}}),[c,s,u,r,o,n,e,a,i,l])}(m,{replace:d,state:f,target:h,preventScrollReset:g,relative:s,viewTransition:v});let R=t.createElement("a",c(c(c({},b),A),{},{href:r||C,onClick:k||u?a:function(e){a&&a(e),e.defaultPrevented||P(e)},ref:dt(n,T),target:h,"data-discover":S||"render"!==i?void 0:"true"}));return E&&!S?t.createElement(t.Fragment,null,R,t.createElement(it,{page:C})):R}));mt.displayName="Link",t.forwardRef((function(e,n){let{"aria-current":r="page",caseSensitive:a=!1,className:i="",end:l=!1,style:s,to:u,viewTransition:d,children:f}=e,p=o(e,h),m=me(u,{relative:p.relative}),g=ue(),v=t.useContext(te),{navigator:x,basename:b}=t.useContext(ae),w=null!=v&&function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.useContext(ne);y(null!=r,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=yt("useViewTransitionState"),a=me(e,{relative:n.relative});if(!r.isTransitioning)return!1;let i=H(r.currentLocation.pathname,o)||r.currentLocation.pathname,l=H(r.nextLocation.pathname,o)||r.nextLocation.pathname;return null!=U(a.pathname,l)||null!=U(a.pathname,i)}(m)&&!0===d,j=x.encodeLocation?x.encodeLocation(m).pathname:m.pathname,S=g.pathname,k=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;a||(S=S.toLowerCase(),k=k?k.toLowerCase():null,j=j.toLowerCase()),k&&b&&(k=H(k,b)||k);const C="/"!==j&&j.endsWith("/")?j.length-1:j.length;let E,T=S===j||!l&&S.startsWith(j)&&"/"===S.charAt(C),A=null!=k&&(k===j||!l&&k.startsWith(j)&&"/"===k.charAt(j.length)),P={isActive:T,isPending:A,isTransitioning:w},R=T?r:void 0;E="function"===typeof i?i(P):[i,T?"active":null,A?"pending":null,w?"transitioning":null].filter(Boolean).join(" ");let N="function"===typeof s?s(P):s;return t.createElement(mt,c(c({},p),{},{"aria-current":R,className:E,ref:n,style:N,to:u,viewTransition:d}),"function"===typeof f?f(P):f)})).displayName="NavLink";var gt=t.forwardRef(((e,n)=>{let{discover:r="render",fetcherKey:a,navigate:i,reloadDocument:l,replace:s,state:u,method:d=Oe,action:f,onSubmit:p,relative:h,preventScrollReset:g,viewTransition:v}=e,x=o(e,m),b=wt(),w=function(e){let{relative:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:r}=t.useContext(ae),o=t.useContext(le);y(o,"useFormAction must be used inside a RouteContext");let[a]=o.matches.slice(-1),i=c({},me(e||".",{relative:n})),l=ue();if(null==e){i.search=l.search;let e=new URLSearchParams(i.search),t=e.getAll("index"),n=t.some((e=>""===e));if(n){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();i.search=n?"?".concat(n):""}}e&&"."!==e||!a.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==r&&(i.pathname="/"===i.pathname?r:G([r,i.pathname]));return j(i)}(f,{relative:h}),S="get"===d.toLowerCase()?"get":"post",k="string"===typeof f&&ht.test(f);return t.createElement("form",c(c({ref:n,method:S,action:w,onSubmit:l?p:e=>{if(p&&p(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||d;b(t||e.currentTarget,{fetcherKey:a,method:n,navigate:i,replace:s,state:u,relative:h,preventScrollReset:g,viewTransition:v})}},x),{},{"data-discover":k||"render"!==r?void 0:"true"}))}));function vt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function yt(e){let n=t.useContext(ee);return y(n,vt(e)),n}gt.displayName="Form";var xt=0,bt=()=>"__".concat(String(++xt),"__");function wt(){let{router:e}=yt("useSubmit"),{basename:n}=t.useContext(ae),r=Ce("useRouteId");return t.useCallback((async function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:a,method:i,encType:l,formData:s,body:c}=Me(t,n);if(!1===o.navigate){let t=o.fetcherKey||bt();await e.fetch(t,r,o.action||a,{preventScrollReset:o.preventScrollReset,formData:s,body:c,formMethod:o.method||i,formEncType:o.encType||l,flushSync:o.flushSync})}else await e.navigate(o.action||a,{preventScrollReset:o.preventScrollReset,formData:s,body:c,formMethod:o.method||i,formEncType:o.encType||l,replace:o.replace,state:o.state,fromRouteId:r,flushSync:o.flushSync,viewTransition:o.viewTransition})}),[e,n,r])}function jt(e,t){return function(){return e.apply(t,arguments)}}const{toString:St}=Object.prototype,{getPrototypeOf:kt}=Object,{iterator:Ct,toStringTag:Et}=Symbol,Tt=(At=Object.create(null),e=>{const t=St.call(e);return At[t]||(At[t]=t.slice(8,-1).toLowerCase())});var At;const Pt=e=>(e=e.toLowerCase(),t=>Tt(t)===e),Rt=e=>t=>typeof t===e,{isArray:Nt}=Array,_t=Rt("undefined");const Lt=Pt("ArrayBuffer");const Ot=Rt("string"),Dt=Rt("function"),zt=Rt("number"),It=e=>null!==e&&"object"===typeof e,Ft=e=>{if("object"!==Tt(e))return!1;const t=kt(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Et in e)&&!(Ct in e)},Ut=Pt("Date"),Mt=Pt("File"),Bt=Pt("Blob"),Ht=Pt("FileList"),Wt=Pt("URLSearchParams"),[$t,Vt,qt,Gt]=["ReadableStream","Request","Response","Headers"].map(Pt);function Kt(e,t){let n,r,{allOwnKeys:o=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Nt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=o?Object.getOwnPropertyNames(e):Object.keys(e),a=r.length;let i;for(n=0;n<a;n++)i=r[n],t.call(null,e[i],i,e)}}function Qt(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const Jt="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Yt=e=>!_t(e)&&e!==Jt;const Zt=(Xt="undefined"!==typeof Uint8Array&&kt(Uint8Array),e=>Xt&&e instanceof Xt);var Xt;const en=Pt("HTMLFormElement"),tn=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),nn=Pt("RegExp"),rn=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Kt(n,((n,o)=>{let a;!1!==(a=t(n,o,e))&&(r[o]=a||n)})),Object.defineProperties(e,r)};const on=Pt("AsyncFunction"),an=(ln="function"===typeof setImmediate,sn=Dt(Jt.postMessage),ln?setImmediate:sn?((e,t)=>(Jt.addEventListener("message",(n=>{let{source:r,data:o}=n;r===Jt&&o===e&&t.length&&t.shift()()}),!1),n=>{t.push(n),Jt.postMessage(e,"*")}))("axios@".concat(Math.random()),[]):e=>setTimeout(e));var ln,sn;const cn="undefined"!==typeof queueMicrotask?queueMicrotask.bind(Jt):"undefined"!==typeof process&&process.nextTick||an,un={isArray:Nt,isArrayBuffer:Lt,isBuffer:function(e){return null!==e&&!_t(e)&&null!==e.constructor&&!_t(e.constructor)&&Dt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Dt(e.append)&&("formdata"===(t=Tt(e))||"object"===t&&Dt(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Lt(e.buffer),t},isString:Ot,isNumber:zt,isBoolean:e=>!0===e||!1===e,isObject:It,isPlainObject:Ft,isReadableStream:$t,isRequest:Vt,isResponse:qt,isHeaders:Gt,isUndefined:_t,isDate:Ut,isFile:Mt,isBlob:Bt,isRegExp:nn,isFunction:Dt,isStream:e=>It(e)&&Dt(e.pipe),isURLSearchParams:Wt,isTypedArray:Zt,isFileList:Ht,forEach:Kt,merge:function e(){const{caseless:t}=Yt(this)&&this||{},n={},r=(r,o)=>{const a=t&&Qt(n,o)||o;Ft(n[a])&&Ft(r)?n[a]=e(n[a],r):Ft(r)?n[a]=e({},r):Nt(r)?n[a]=r.slice():n[a]=r};for(let o=0,a=arguments.length;o<a;o++)arguments[o]&&Kt(arguments[o],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Kt(t,((t,r)=>{n&&Dt(t)?e[r]=jt(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,a,i;const l={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),a=o.length;a-- >0;)i=o[a],r&&!r(i,e,t)||l[i]||(t[i]=e[i],l[i]=!0);e=!1!==n&&kt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Tt,kindOfTest:Pt,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Nt(e))return e;let t=e.length;if(!zt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Ct]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:en,hasOwnProperty:tn,hasOwnProp:tn,reduceDescriptors:rn,freezeMethods:e=>{rn(e,((t,n)=>{if(Dt(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Dt(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return Nt(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Qt,global:Jt,isContextDefined:Yt,isSpecCompliantForm:function(e){return!!(e&&Dt(e.append)&&"FormData"===e[Et]&&e[Ct])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(It(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=Nt(e)?[]:{};return Kt(e,((e,t)=>{const a=n(e,r+1);!_t(a)&&(o[t]=a)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:on,isThenable:e=>e&&(It(e)||Dt(e))&&Dt(e.then)&&Dt(e.catch),setImmediate:an,asap:cn,isIterable:e=>null!=e&&Dt(e[Ct])};function dn(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}un.inherits(dn,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:un.toJSONObject(this.config),code:this.code,status:this.status}}});const fn=dn.prototype,pn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{pn[e]={value:e}})),Object.defineProperties(dn,pn),Object.defineProperty(fn,"isAxiosError",{value:!0}),dn.from=(e,t,n,r,o,a)=>{const i=Object.create(fn);return un.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),dn.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,a&&Object.assign(i,a),i};const hn=dn;function mn(e){return un.isPlainObject(e)||un.isArray(e)}function gn(e){return un.endsWith(e,"[]")?e.slice(0,-2):e}function vn(e,t,n){return e?e.concat(t).map((function(e,t){return e=gn(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const yn=un.toFlatObject(un,{},null,(function(e){return/^is[A-Z]/.test(e)}));const xn=function(e,t,n){if(!un.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=un.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!un.isUndefined(t[e])}))).metaTokens,o=n.visitor||c,a=n.dots,i=n.indexes,l=(n.Blob||"undefined"!==typeof Blob&&Blob)&&un.isSpecCompliantForm(t);if(!un.isFunction(o))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(un.isDate(e))return e.toISOString();if(!l&&un.isBlob(e))throw new hn("Blob is not supported. Use a Buffer instead.");return un.isArrayBuffer(e)||un.isTypedArray(e)?l&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,o){let l=e;if(e&&!o&&"object"===typeof e)if(un.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(un.isArray(e)&&function(e){return un.isArray(e)&&!e.some(mn)}(e)||(un.isFileList(e)||un.endsWith(n,"[]"))&&(l=un.toArray(e)))return n=gn(n),l.forEach((function(e,r){!un.isUndefined(e)&&null!==e&&t.append(!0===i?vn([n],r,a):null===i?n:n+"[]",s(e))})),!1;return!!mn(e)||(t.append(vn(o,n,a),s(e)),!1)}const u=[],d=Object.assign(yn,{defaultVisitor:c,convertValue:s,isVisitable:mn});if(!un.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!un.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),un.forEach(n,(function(n,a){!0===(!(un.isUndefined(n)||null===n)&&o.call(t,n,un.isString(a)?a.trim():a,r,d))&&e(n,r?r.concat(a):[a])})),u.pop()}}(e),t};function bn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function wn(e,t){this._pairs=[],e&&xn(e,this,t)}const jn=wn.prototype;jn.append=function(e,t){this._pairs.push([e,t])},jn.toString=function(e){const t=e?function(t){return e.call(this,t,bn)}:bn;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Sn=wn;function kn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Cn(e,t,n){if(!t)return e;const r=n&&n.encode||kn;un.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let a;if(a=o?o(t,n):un.isURLSearchParams(t)?t.toString():new Sn(t,n).toString(r),a){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}const En=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){un.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},Tn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},An={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Sn,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Pn="undefined"!==typeof window&&"undefined"!==typeof document,Rn="object"===typeof navigator&&navigator||void 0,Nn=Pn&&(!Rn||["ReactNative","NativeScript","NS"].indexOf(Rn.product)<0),_n="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Ln=Pn&&window.location.href||"http://localhost",On=c(c({},e),An);const Dn=function(e){function t(e,n,r,o){let a=e[o++];if("__proto__"===a)return!0;const i=Number.isFinite(+a),l=o>=e.length;if(a=!a&&un.isArray(r)?r.length:a,l)return un.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!i;r[a]&&un.isObject(r[a])||(r[a]=[]);return t(e,n,r[a],o)&&un.isArray(r[a])&&(r[a]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let a;for(r=0;r<o;r++)a=n[r],t[a]=e[a];return t}(r[a])),!i}if(un.isFormData(e)&&un.isFunction(e.entries)){const n={};return un.forEachEntry(e,((e,r)=>{t(function(e){return un.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const zn={transitional:Tn,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=un.isObject(e);o&&un.isHTMLForm(e)&&(e=new FormData(e));if(un.isFormData(e))return r?JSON.stringify(Dn(e)):e;if(un.isArrayBuffer(e)||un.isBuffer(e)||un.isStream(e)||un.isFile(e)||un.isBlob(e)||un.isReadableStream(e))return e;if(un.isArrayBufferView(e))return e.buffer;if(un.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return xn(e,new On.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return On.isNode&&un.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((a=un.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return xn(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(un.isString(e))try{return(t||JSON.parse)(e),un.trim(e)}catch(Pv){if("SyntaxError"!==Pv.name)throw Pv}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||zn.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(un.isResponse(e)||un.isReadableStream(e))return e;if(e&&un.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(Pv){if(n){if("SyntaxError"===Pv.name)throw hn.from(Pv,hn.ERR_BAD_RESPONSE,this,null,this.response);throw Pv}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:On.classes.FormData,Blob:On.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};un.forEach(["delete","get","head","post","put","patch"],(e=>{zn.headers[e]={}}));const In=zn,Fn=un.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Un=Symbol("internals");function Mn(e){return e&&String(e).trim().toLowerCase()}function Bn(e){return!1===e||null==e?e:un.isArray(e)?e.map(Bn):String(e)}function Hn(e,t,n,r,o){return un.isFunction(r)?r.call(this,t,n):(o&&(t=n),un.isString(t)?un.isString(r)?-1!==t.indexOf(r):un.isRegExp(r)?r.test(t):void 0:void 0)}class Wn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=Mn(t);if(!o)throw new Error("header name must be a non-empty string");const a=un.findKey(r,o);(!a||void 0===r[a]||!0===n||void 0===n&&!1!==r[a])&&(r[a||t]=Bn(e))}const a=(e,t)=>un.forEach(e,((e,n)=>o(e,n,t)));if(un.isPlainObject(e)||e instanceof this.constructor)a(e,t);else if(un.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))a((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&Fn[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(un.isObject(e)&&un.isIterable(e)){let n,r,o={};for(const t of e){if(!un.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?un.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}a(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=Mn(e)){const n=un.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(un.isFunction(t))return t.call(this,e,n);if(un.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Mn(e)){const n=un.findKey(this,e);return!(!n||void 0===this[n]||t&&!Hn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=Mn(e)){const o=un.findKey(n,e);!o||t&&!Hn(0,n[o],o,t)||(delete n[o],r=!0)}}return un.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Hn(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return un.forEach(this,((r,o)=>{const a=un.findKey(n,o);if(a)return t[a]=Bn(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();i!==o&&delete t[o],t[i]=Bn(r),n[i]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return un.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&un.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[Un]=this[Un]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Mn(e);t[r]||(!function(e,t){const n=un.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return un.isArray(e)?e.forEach(r):r(e),this}}Wn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),un.reduceDescriptors(Wn.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),un.freezeMethods(Wn);const $n=Wn;function Vn(e,t){const n=this||In,r=t||n,o=$n.from(r.headers);let a=r.data;return un.forEach(e,(function(e){a=e.call(n,a,o.normalize(),t?t.status:void 0)})),o.normalize(),a}function qn(e){return!(!e||!e.__CANCEL__)}function Gn(e,t,n){hn.call(this,null==e?"canceled":e,hn.ERR_CANCELED,t,n),this.name="CanceledError"}un.inherits(Gn,hn,{__CANCEL__:!0});const Kn=Gn;function Qn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new hn("Request failed with status code "+n.status,[hn.ERR_BAD_REQUEST,hn.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Jn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,a=0,i=0;return t=void 0!==t?t:1e3,function(l){const s=Date.now(),c=r[i];o||(o=s),n[a]=l,r[a]=s;let u=i,d=0;for(;u!==a;)d+=n[u++],u%=e;if(a=(a+1)%e,a===i&&(i=(i+1)%e),s-o<t)return;const f=c&&s-c;return f?Math.round(1e3*d/f):void 0}};const Yn=function(e,t){let n,r,o=0,a=1e3/t;const i=function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();o=a,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-o;for(var l=arguments.length,s=new Array(l),c=0;c<l;c++)s[c]=arguments[c];t>=a?i(s,e):(n=s,r||(r=setTimeout((()=>{r=null,i(n)}),a-t)))},()=>n&&i(n)]},Zn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const o=Jn(50,250);return Yn((n=>{const a=n.loaded,i=n.lengthComputable?n.total:void 0,l=a-r,s=o(l);r=a;e({loaded:a,total:i,progress:i?a/i:void 0,bytes:l,rate:s||void 0,estimated:s&&i&&a<=i?(i-a)/s:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})}),n)},Xn=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},er=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return un.asap((()=>e(...n)))},tr=On.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,On.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(On.origin),On.navigator&&/(msie|trident)/i.test(On.navigator.userAgent)):()=>!0,nr=On.hasStandardBrowserEnv?{write(e,t,n,r,o,a){const i=[e+"="+encodeURIComponent(t)];un.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),un.isString(r)&&i.push("path="+r),un.isString(o)&&i.push("domain="+o),!0===a&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function rr(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const or=e=>e instanceof $n?c({},e):e;function ar(e,t){t=t||{};const n={};function r(e,t,n,r){return un.isPlainObject(e)&&un.isPlainObject(t)?un.merge.call({caseless:r},e,t):un.isPlainObject(t)?un.merge({},t):un.isArray(t)?t.slice():t}function o(e,t,n,o){return un.isUndefined(t)?un.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function a(e,t){if(!un.isUndefined(t))return r(void 0,t)}function i(e,t){return un.isUndefined(t)?un.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function l(n,o,a){return a in t?r(n,o):a in e?r(void 0,n):void 0}const s={url:a,method:a,data:a,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(e,t,n)=>o(or(e),or(t),0,!0)};return un.forEach(Object.keys(Object.assign({},e,t)),(function(r){const a=s[r]||o,i=a(e[r],t[r],r);un.isUndefined(i)&&a!==l||(n[r]=i)})),n}const ir=e=>{const t=ar({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:a,xsrfCookieName:i,headers:l,auth:s}=t;if(t.headers=l=$n.from(l),t.url=Cn(rr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&l.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),un.isFormData(r))if(On.hasStandardBrowserEnv||On.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if(!1!==(n=l.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];l.setContentType([e||"multipart/form-data",...t].join("; "))}if(On.hasStandardBrowserEnv&&(o&&un.isFunction(o)&&(o=o(t)),o||!1!==o&&tr(t.url))){const e=a&&i&&nr.read(i);e&&l.set(a,e)}return t},lr="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=ir(e);let o=r.data;const a=$n.from(r.headers).normalize();let i,l,s,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=$n.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Qn((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new hn("Request aborted",hn.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new hn("Network Error",hn.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||Tn;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new hn(t,o.clarifyTimeoutError?hn.ETIMEDOUT:hn.ECONNABORTED,e,m)),m=null},void 0===o&&a.setContentType(null),"setRequestHeader"in m&&un.forEach(a.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),un.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([s,u]=Zn(p,!0),m.addEventListener("progress",s)),f&&m.upload&&([l,c]=Zn(f),m.upload.addEventListener("progress",l),m.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new Kn(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const v=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);v&&-1===On.protocols.indexOf(v)?n(new hn("Unsupported protocol "+v+":",hn.ERR_BAD_REQUEST,e)):m.send(o||null)}))},sr=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof hn?t:new Kn(t instanceof Error?t.message:t))}};let a=t&&setTimeout((()=>{a=null,o(new hn("timeout ".concat(t," of ms exceeded"),hn.ETIMEDOUT))}),t);const i=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)})),e=null)};e.forEach((e=>e.addEventListener("abort",o)));const{signal:l}=r;return l.unsubscribe=()=>un.asap(i),l}};function cr(e,t){this.v=e,this.k=t}function ur(e){return function(){return new dr(e.apply(this,arguments))}}function dr(e){var t,n;function r(t,n){try{var a=e[t](n),i=a.value,l=i instanceof cr;Promise.resolve(l?i.v:i).then((function(n){if(l){var s="return"===t?"return":"next";if(!i.k||n.done)return r(s,n);n=e[s](n).value}o(a.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){o("throw",e)}}function o(e,o){switch(e){case"return":t.resolve({value:o,done:!0});break;case"throw":t.reject(o);break;default:t.resolve({value:o,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,o){return new Promise((function(a,i){var l={key:e,arg:o,resolve:a,reject:i,next:null};n?n=n.next=l:(t=n=l,r(e,o))}))},"function"!=typeof e.return&&(this.return=void 0)}function fr(e){return new cr(e,0)}function pr(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new cr(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function hr(e){var t,n,r,o=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);o--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new mr(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function mr(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return mr=function(e){this.s=e,this.n=e.next},mr.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new mr(e)}dr.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},dr.prototype.next=function(e){return this._invoke("next",e)},dr.prototype.throw=function(e){return this._invoke("throw",e)},dr.prototype.return=function(e){return this._invoke("return",e)};const gr=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},vr=function(){var e=ur((function*(e,t){var n,r=!1,o=!1;try{for(var a,i=hr(yr(e));r=!(a=yield fr(i.next())).done;r=!1){const e=a.value;yield*pr(hr(gr(e,t)))}}catch(l){o=!0,n=l}finally{try{r&&null!=i.return&&(yield fr(i.return()))}finally{if(o)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),yr=function(){var e=ur((function*(e){if(e[Symbol.asyncIterator])return void(yield*pr(hr(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield fr(t.read());if(e)break;yield n}}finally{yield fr(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),xr=(e,t,n,r)=>{const o=vr(e,t);let a,i=0,l=e=>{a||(a=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return l(),void e.close();let a=r.byteLength;if(n){let e=i+=a;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw l(t),t}},cancel:e=>(l(e),o.return())},{highWaterMark:2})},br="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,wr=br&&"function"===typeof ReadableStream,jr=br&&("function"===typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Sr=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(Pv){return!1}},kr=wr&&Sr((()=>{let e=!1;const t=new Request(On.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Cr=wr&&Sr((()=>un.isReadableStream(new Response("").body))),Er={stream:Cr&&(e=>e.body)};var Tr;br&&(Tr=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Er[e]&&(Er[e]=un.isFunction(Tr[e])?t=>t[e]():(t,n)=>{throw new hn("Response type '".concat(e,"' is not supported"),hn.ERR_NOT_SUPPORT,n)})})));const Ar=async(e,t)=>{const n=un.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(un.isBlob(e))return e.size;if(un.isSpecCompliantForm(e)){const t=new Request(On.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return un.isArrayBufferView(e)||un.isArrayBuffer(e)?e.byteLength:(un.isURLSearchParams(e)&&(e+=""),un.isString(e)?(await jr(e)).byteLength:void 0)})(t):n},Pr=br&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:a,timeout:i,onDownloadProgress:l,onUploadProgress:s,responseType:u,headers:d,withCredentials:f="same-origin",fetchOptions:p}=ir(e);u=u?(u+"").toLowerCase():"text";let h,m=sr([o,a&&a.toAbortSignal()],i);const g=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let v;try{if(s&&kr&&"get"!==n&&"head"!==n&&0!==(v=await Ar(d,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(un.isFormData(r)&&(e=n.headers.get("content-type"))&&d.setContentType(e),n.body){const[e,t]=Xn(v,Zn(er(s)));r=xr(n.body,65536,e,t)}}un.isString(f)||(f=f?"include":"omit");const o="credentials"in Request.prototype;h=new Request(t,c(c({},p),{},{signal:m,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:o?f:void 0}));let a=await fetch(h);const i=Cr&&("stream"===u||"response"===u);if(Cr&&(l||i&&g)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=a[t]}));const t=un.toFiniteNumber(a.headers.get("content-length")),[n,r]=l&&Xn(t,Zn(er(l),!0))||[];a=new Response(xr(a.body,65536,n,(()=>{r&&r(),g&&g()})),e)}u=u||"text";let y=await Er[un.findKey(Er,u)||"text"](a,e);return!i&&g&&g(),await new Promise(((t,n)=>{Qn(t,n,{data:y,headers:$n.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:h})}))}catch(y){if(g&&g(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new hn("Network Error",hn.ERR_NETWORK,e,h),{cause:y.cause||y});throw hn.from(y,y&&y.code,e,h)}}),Rr={http:null,xhr:lr,fetch:Pr};un.forEach(Rr,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(Pv){}Object.defineProperty(e,"adapterName",{value:t})}}));const Nr=e=>"- ".concat(e),_r=e=>un.isFunction(e)||null===e||!1===e,Lr=e=>{e=un.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let a=0;a<t;a++){let t;if(n=e[a],r=n,!_r(n)&&(r=Rr[(t=String(n)).toLowerCase()],void 0===r))throw new hn("Unknown adapter '".concat(t,"'"));if(r)break;o[t||"#"+a]=r}if(!r){const e=Object.entries(o).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(Nr).join("\n"):" "+Nr(e[0]):"as no adapter specified";throw new hn("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Or(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Kn(null,e)}function Dr(e){Or(e),e.headers=$n.from(e.headers),e.data=Vn.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Lr(e.adapter||In.adapter)(e).then((function(t){return Or(e),t.data=Vn.call(e,e.transformResponse,t),t.headers=$n.from(t.headers),t}),(function(t){return qn(t)||(Or(e),t&&t.response&&(t.response.data=Vn.call(e,e.transformResponse,t.response),t.response.headers=$n.from(t.response.headers))),Promise.reject(t)}))}const zr="1.9.0",Ir={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Ir[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Fr={};Ir.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,a)=>{if(!1===e)throw new hn(r(o," has been removed"+(t?" in "+t:"")),hn.ERR_DEPRECATED);return t&&!Fr[o]&&(Fr[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,a)}},Ir.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const Ur={assertOptions:function(e,t,n){if("object"!==typeof e)throw new hn("options must be an object",hn.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const a=r[o],i=t[a];if(i){const t=e[a],n=void 0===t||i(t,a,e);if(!0!==n)throw new hn("option "+a+" must be "+n,hn.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new hn("Unknown option "+a,hn.ERR_BAD_OPTION)}},validators:Ir},Mr=Ur.validators;class Br{constructor(e){this.defaults=e||{},this.interceptors={request:new En,response:new En}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(Pv){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=ar(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&Ur.assertOptions(n,{silentJSONParsing:Mr.transitional(Mr.boolean),forcedJSONParsing:Mr.transitional(Mr.boolean),clarifyTimeoutError:Mr.transitional(Mr.boolean)},!1),null!=r&&(un.isFunction(r)?t.paramsSerializer={serialize:r}:Ur.assertOptions(r,{encode:Mr.function,serialize:Mr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Ur.assertOptions(t,{baseUrl:Mr.spelling("baseURL"),withXsrfToken:Mr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=o&&un.merge(o.common,o[t.method]);o&&un.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=$n.concat(a,o);const i=[];let l=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(l=l&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));const s=[];let c;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let u,d=0;if(!l){const e=[Dr.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,s),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=i.length;let f=t;for(d=0;d<u;){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{c=Dr.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,u=s.length;d<u;)c=c.then(s[d++],s[d++]);return c}getUri(e){return Cn(rr((e=ar(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}un.forEach(["delete","get","head","options"],(function(e){Br.prototype[e]=function(t,n){return this.request(ar(n||{},{method:e,url:t,data:(n||{}).data}))}})),un.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(ar(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Br.prototype[e]=t(),Br.prototype[e+"Form"]=t(!0)}));const Hr=Br;class Wr{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new Kn(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Wr((function(t){e=t})),cancel:e}}}const $r=Wr;const Vr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Vr).forEach((e=>{let[t,n]=e;Vr[n]=t}));const qr=Vr;const Gr=function e(t){const n=new Hr(t),r=jt(Hr.prototype.request,n);return un.extend(r,Hr.prototype,n,{allOwnKeys:!0}),un.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(ar(t,n))},r}(In);Gr.Axios=Hr,Gr.CanceledError=Kn,Gr.CancelToken=$r,Gr.isCancel=qn,Gr.VERSION=zr,Gr.toFormData=xn,Gr.AxiosError=hn,Gr.Cancel=Gr.CanceledError,Gr.all=function(e){return Promise.all(e)},Gr.spread=function(e){return function(t){return e.apply(null,t)}},Gr.isAxiosError=function(e){return un.isObject(e)&&!0===e.isAxiosError},Gr.mergeConfig=ar,Gr.AxiosHeaders=$n,Gr.formToJSON=e=>Dn(un.isHTMLForm(e)?new FormData(e):e),Gr.getAdapter=Lr,Gr.HttpStatusCode=qr,Gr.default=Gr;const Kr=Gr;const Qr=new class{constructor(){this.api=void 0,this.baseURL=void 0,this.baseURL="https://localhost:59358/api",this.api=Kr.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.api.interceptors.request.use((e=>{const t=localStorage.getItem("authToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e}),(e=>Promise.reject(e))),this.api.interceptors.response.use((e=>e),(async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;const e=localStorage.getItem("refreshToken"),t=localStorage.getItem("authToken");if(e&&t)try{const r=await Kr.post("".concat(this.baseURL,"/auth/refresh-token"),{token:t,refreshToken:e});if(r.data.success)return localStorage.setItem("authToken",r.data.token),localStorage.setItem("refreshToken",r.data.refreshToken),localStorage.setItem("user",JSON.stringify(r.data.user)),n.headers.Authorization="Bearer ".concat(r.data.token),this.api(n)}catch(r){console.error("Token refresh failed:",r)}this.logoutUser()}return Promise.reject(e)}))}logoutUser(){localStorage.removeItem("authToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user"),window.location.href="/login"}async login(e,t,n){try{return(await this.api.post("/auth/login",{username:e,password:t,role:n})).data}catch(a){var r,o;throw console.error("Login API error:",a),new Error((null===(r=a.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.message)||"Login failed")}}async logout(){try{await this.api.post("/auth/logout")}catch(e){console.error("Logout API error:",e)}finally{this.logoutUser()}}async getCurrentUser(){try{return(await this.api.get("/auth/me")).data}catch(n){var e,t;throw console.error("Get current user API error:",n),new Error((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to get user information")}}async getLeads(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;try{const o=new URLSearchParams({pageNumber:e.toString(),pageSize:t.toString()});n&&o.append("status",n),r&&o.append("assignedTo",r.toString());const a=(await this.api.get("/leads?".concat(o.toString()))).data;return c(c({},a),{},{totalCount:a.totalRecords||a.totalCount||0})}catch(i){var o,a;throw console.error("Get leads API error:",i),new Error((null===(o=i.response)||void 0===o||null===(a=o.data)||void 0===a?void 0:a.message)||"Failed to fetch leads")}}async getLead(e){try{return(await this.api.get("/leads/".concat(e))).data}catch(r){var t,n;throw console.error("Get lead API error:",r),new Error((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to fetch lead")}}async createLead(e){try{return(await this.api.post("/leads",e)).data}catch(r){var t,n;throw console.error("Create lead API error:",r),new Error((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to create lead")}}async updateLeadStatus(e,t,n,r){try{await this.api.put("/leads/".concat(e,"/status"),{status:t,comments:n,rejectionReason:r})}catch(i){var o,a;throw console.error("Update lead status API error:",i),new Error((null===(o=i.response)||void 0===o||null===(a=o.data)||void 0===a?void 0:a.message)||"Failed to update lead status")}}async assignLead(e,t,n){try{await this.api.put("/leads/".concat(e,"/assign"),{agentId:t,comments:n})}catch(a){var r,o;throw console.error("Assign lead API error:",a),new Error((null===(r=a.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.message)||"Failed to assign lead")}}async getDashboardStats(){try{return(await this.api.get("/leads/dashboard-stats")).data}catch(n){var e,t;throw console.error("Get dashboard stats API error:",n),new Error((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to fetch dashboard statistics")}}async getAgentDashboardStats(){try{return(await this.api.get("/verification/agent/dashboard-stats")).data}catch(n){var e,t;throw console.error("Get agent dashboard stats API error:",n),new Error((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to fetch agent dashboard statistics")}}async getSupervisorDashboardStats(){try{return(await this.api.get("/verification/supervisor/dashboard-stats")).data}catch(n){var e,t;throw console.error("Get supervisor dashboard stats API error:",n),new Error((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to fetch supervisor dashboard statistics")}}async saveVerificationData(e,t){try{return(await this.api.post("/verification/leads/".concat(e),t)).data}catch(o){var n,r;throw console.error("Save verification data API error:",o),new Error((null===(n=o.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||"Failed to save verification data")}}async updateVerificationData(e,t){try{return(await this.api.put("/verification/leads/".concat(e),t)).data}catch(o){var n,r;throw console.error("Update verification data API error:",o),new Error((null===(n=o.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||"Failed to update verification data")}}async getVerificationData(e){try{return(await this.api.get("/verification/leads/".concat(e))).data}catch(r){var t,n;throw console.error("Get verification data API error:",r),new Error((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to fetch verification data")}}async getDocumentTypes(){try{return(await this.api.get("/documents/types")).data}catch(n){var e,t;throw console.error("Get document types API error:",n),new Error((null===(e=n.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to fetch document types")}}async uploadDocument(e,t,n){try{const r=new FormData;r.append("file",n),r.append("documentTypeId","1"),r.append("documentCategory",t);return(await this.api.post("/documents/leads/".concat(e,"/verification-documents"),r,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(a){var r,o;throw console.error("Upload document API error:",a),new Error((null===(r=a.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.message)||"Failed to upload document")}}async getVerificationDocuments(e){try{return(await this.api.get("/documents/leads/".concat(e,"/verification-documents"))).data}catch(r){var t,n;throw console.error("Get verification documents API error:",r),new Error((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to fetch verification documents")}}async getUsers(){try{return(await this.api.get("/users")).data}catch(e){return console.error("Get users API error:",e),[{userId:1,username:"agent1",firstName:"John",lastName:"Agent",email:"<EMAIL>",role:"Agent",isActive:!0,createdDate:"2024-01-01T00:00:00Z",lastLoginDate:"2024-01-16T08:30:00Z"},{userId:2,username:"supervisor1",firstName:"Jane",lastName:"Supervisor",email:"<EMAIL>",role:"Supervisor",isActive:!0,createdDate:"2024-01-01T00:00:00Z",lastLoginDate:"2024-01-16T09:15:00Z"},{userId:3,username:"admin1",firstName:"Admin",lastName:"User",email:"<EMAIL>",role:"Admin",isActive:!0,createdDate:"2024-01-01T00:00:00Z",lastLoginDate:"2024-01-16T10:00:00Z"}]}}async createUser(e){try{return(await this.api.post("/users",e)).data}catch(r){var t,n;throw console.error("Create user API error:",r),new Error((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to create user")}}async updateUser(e,t){try{return(await this.api.put("/users/".concat(e),t)).data}catch(o){var n,r;throw console.error("Update user API error:",o),new Error((null===(n=o.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||"Failed to update user")}}async toggleUserStatus(e){try{await this.api.put("/users/".concat(e,"/toggle-status"))}catch(r){var t,n;throw console.error("Toggle user status API error:",r),new Error((null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to toggle user status")}}};var Jr=n(579);const Yr=(0,t.createContext)(void 0),Zr=()=>{const e=(0,t.useContext)(Yr);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},Xr=e=>{let{children:n}=e;const[r,o]=(0,t.useState)(null),[a,i]=(0,t.useState)(null),[l,s]=(0,t.useState)(!0);(0,t.useEffect)((()=>{const e=localStorage.getItem("authToken"),t=localStorage.getItem("user");if(e&&t)try{const n=JSON.parse(t);i(e),o(n)}catch(n){console.error("Error parsing stored user data:",n),localStorage.removeItem("authToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user")}s(!1)}),[]);const c={user:r,token:a,login:async(e,t,n)=>{try{s(!0);const r=await Qr.login(e,t,n);if(!(r.success&&r.token&&r.user))throw new Error(r.message||"Login failed");localStorage.setItem("authToken",r.token),localStorage.setItem("refreshToken",r.refreshToken||""),localStorage.setItem("user",JSON.stringify(r.user)),i(r.token),o(r.user)}catch(r){throw console.error("Login error:",r),r}finally{s(!1)}},logout:async()=>{try{await Qr.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("authToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user"),i(null),o(null)}},isLoading:l,isAuthenticated:!!r&&!!a};return(0,Jr.jsx)(Yr.Provider,{value:c,children:n})};var eo=function(){return eo=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},eo.apply(this,arguments)};Object.create;function to(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"===typeof SuppressedError&&SuppressedError;var no=n(324),ro=n.n(no),oo="-ms-",ao="-moz-",io="-webkit-",lo="comm",so="rule",co="decl",uo="@keyframes",fo=Math.abs,po=String.fromCharCode,ho=Object.assign;function mo(e){return e.trim()}function go(e,t){return(e=t.exec(e))?e[0]:e}function vo(e,t,n){return e.replace(t,n)}function yo(e,t,n){return e.indexOf(t,n)}function xo(e,t){return 0|e.charCodeAt(t)}function bo(e,t,n){return e.slice(t,n)}function wo(e){return e.length}function jo(e){return e.length}function So(e,t){return t.push(e),e}function ko(e,t){return e.filter((function(e){return!go(e,t)}))}var Co=1,Eo=1,To=0,Ao=0,Po=0,Ro="";function No(e,t,n,r,o,a,i,l){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:Co,column:Eo,length:i,return:"",siblings:l}}function _o(e,t){return ho(No("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function Lo(e){for(;e.root;)e=_o(e.root,{children:[e]});So(e,e.siblings)}function Oo(){return Po=Ao>0?xo(Ro,--Ao):0,Eo--,10===Po&&(Eo=1,Co--),Po}function Do(){return Po=Ao<To?xo(Ro,Ao++):0,Eo++,10===Po&&(Eo=1,Co++),Po}function zo(){return xo(Ro,Ao)}function Io(){return Ao}function Fo(e,t){return bo(Ro,e,t)}function Uo(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Mo(e){return Co=Eo=1,To=wo(Ro=e),Ao=0,[]}function Bo(e){return Ro="",e}function Ho(e){return mo(Fo(Ao-1,Vo(91===e?e+2:40===e?e+1:e)))}function Wo(e){for(;(Po=zo())&&Po<33;)Do();return Uo(e)>2||Uo(Po)>3?"":" "}function $o(e,t){for(;--t&&Do()&&!(Po<48||Po>102||Po>57&&Po<65||Po>70&&Po<97););return Fo(e,Io()+(t<6&&32==zo()&&32==Do()))}function Vo(e){for(;Do();)switch(Po){case e:return Ao;case 34:case 39:34!==e&&39!==e&&Vo(Po);break;case 40:41===e&&Vo(e);break;case 92:Do()}return Ao}function qo(e,t){for(;Do()&&e+Po!==57&&(e+Po!==84||47!==zo()););return"/*"+Fo(t,Ao-1)+"*"+po(47===e?e:Do())}function Go(e){for(;!Uo(zo());)Do();return Fo(e,Ao)}function Ko(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function Qo(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case co:return e.return=e.return||e.value;case lo:return"";case uo:return e.return=e.value+"{"+Ko(e.children,r)+"}";case so:if(!wo(e.value=e.props.join(",")))return""}return wo(n=Ko(e.children,r))?e.return=e.value+"{"+n+"}":""}function Jo(e,t,n){switch(function(e,t){return 45^xo(e,0)?(((t<<2^xo(e,0))<<2^xo(e,1))<<2^xo(e,2))<<2^xo(e,3):0}(e,t)){case 5103:return io+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return io+e+e;case 4789:return ao+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return io+e+ao+e+oo+e+e;case 5936:switch(xo(e,t+11)){case 114:return io+e+oo+vo(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return io+e+oo+vo(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return io+e+oo+vo(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return io+e+oo+e+e;case 6165:return io+e+oo+"flex-"+e+e;case 5187:return io+e+vo(e,/(\w+).+(:[^]+)/,io+"box-$1$2"+oo+"flex-$1$2")+e;case 5443:return io+e+oo+"flex-item-"+vo(e,/flex-|-self/g,"")+(go(e,/flex-|baseline/)?"":oo+"grid-row-"+vo(e,/flex-|-self/g,""))+e;case 4675:return io+e+oo+"flex-line-pack"+vo(e,/align-content|flex-|-self/g,"")+e;case 5548:return io+e+oo+vo(e,"shrink","negative")+e;case 5292:return io+e+oo+vo(e,"basis","preferred-size")+e;case 6060:return io+"box-"+vo(e,"-grow","")+io+e+oo+vo(e,"grow","positive")+e;case 4554:return io+vo(e,/([^-])(transform)/g,"$1"+io+"$2")+e;case 6187:return vo(vo(vo(e,/(zoom-|grab)/,io+"$1"),/(image-set)/,io+"$1"),e,"")+e;case 5495:case 3959:return vo(e,/(image-set\([^]*)/,io+"$1$`$1");case 4968:return vo(vo(e,/(.+:)(flex-)?(.*)/,io+"box-pack:$3"+oo+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+io+e+e;case 4200:if(!go(e,/flex-|baseline/))return oo+"grid-column-align"+bo(e,t)+e;break;case 2592:case 3360:return oo+vo(e,"template-","")+e;case 4384:case 3616:return n&&n.some((function(e,n){return t=n,go(e.props,/grid-\w+-end/)}))?~yo(e+(n=n[t].value),"span",0)?e:oo+vo(e,"-start","")+e+oo+"grid-row-span:"+(~yo(n,"span",0)?go(n,/\d+/):+go(n,/\d+/)-+go(e,/\d+/))+";":oo+vo(e,"-start","")+e;case 4896:case 4128:return n&&n.some((function(e){return go(e.props,/grid-\w+-start/)}))?e:oo+vo(vo(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return vo(e,/(.+)-inline(.+)/,io+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(wo(e)-1-t>6)switch(xo(e,t+1)){case 109:if(45!==xo(e,t+4))break;case 102:return vo(e,/(.+:)(.+)-([^]+)/,"$1"+io+"$2-$3$1"+ao+(108==xo(e,t+3)?"$3":"$2-$3"))+e;case 115:return~yo(e,"stretch",0)?Jo(vo(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return vo(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(t,n,r,o,a,i,l){return oo+n+":"+r+l+(o?oo+n+"-span:"+(a?i:+i-+r)+l:"")+e}));case 4949:if(121===xo(e,t+6))return vo(e,":",":"+io)+e;break;case 6444:switch(xo(e,45===xo(e,14)?18:11)){case 120:return vo(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+io+(45===xo(e,14)?"inline-":"")+"box$3$1"+io+"$2$3$1"+oo+"$2box$3")+e;case 100:return vo(e,":",":"+oo)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return vo(e,"scroll-","scroll-snap-")+e}return e}function Yo(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case co:return void(e.return=Jo(e.value,e.length,n));case uo:return Ko([_o(e,{value:vo(e.value,"@","@"+io)})],r);case so:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,(function(t){switch(go(t,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":Lo(_o(e,{props:[vo(t,/:(read-\w+)/,":-moz-$1")]})),Lo(_o(e,{props:[t]})),ho(e,{props:ko(n,r)});break;case"::placeholder":Lo(_o(e,{props:[vo(t,/:(plac\w+)/,":"+io+"input-$1")]})),Lo(_o(e,{props:[vo(t,/:(plac\w+)/,":-moz-$1")]})),Lo(_o(e,{props:[vo(t,/:(plac\w+)/,oo+"input-$1")]})),Lo(_o(e,{props:[t]})),ho(e,{props:ko(n,r)})}return""}))}}function Zo(e){return Bo(Xo("",null,null,null,[""],e=Mo(e),0,[0],e))}function Xo(e,t,n,r,o,a,i,l,s){for(var c=0,u=0,d=i,f=0,p=0,h=0,m=1,g=1,v=1,y=0,x="",b=o,w=a,j=r,S=x;g;)switch(h=y,y=Do()){case 40:if(108!=h&&58==xo(S,d-1)){-1!=yo(S+=vo(Ho(y),"&","&\f"),"&\f",fo(c?l[c-1]:0))&&(v=-1);break}case 34:case 39:case 91:S+=Ho(y);break;case 9:case 10:case 13:case 32:S+=Wo(h);break;case 92:S+=$o(Io()-1,7);continue;case 47:switch(zo()){case 42:case 47:So(ta(qo(Do(),Io()),t,n,s),s);break;default:S+="/"}break;case 123*m:l[c++]=wo(S)*v;case 125*m:case 59:case 0:switch(y){case 0:case 125:g=0;case 59+u:-1==v&&(S=vo(S,/\f/g,"")),p>0&&wo(S)-d&&So(p>32?na(S+";",r,n,d-1,s):na(vo(S," ","")+";",r,n,d-2,s),s);break;case 59:S+=";";default:if(So(j=ea(S,t,n,c,u,o,l,x,b=[],w=[],d,a),a),123===y)if(0===u)Xo(S,t,j,j,b,a,d,l,w);else switch(99===f&&110===xo(S,3)?100:f){case 100:case 108:case 109:case 115:Xo(e,j,j,r&&So(ea(e,j,j,0,0,o,l,x,o,b=[],d,w),w),o,w,d,l,r?b:w);break;default:Xo(S,j,j,j,[""],w,0,l,w)}}c=u=p=0,m=v=1,x=S="",d=i;break;case 58:d=1+wo(S),p=h;default:if(m<1)if(123==y)--m;else if(125==y&&0==m++&&125==Oo())continue;switch(S+=po(y),y*m){case 38:v=u>0?1:(S+="\f",-1);break;case 44:l[c++]=(wo(S)-1)*v,v=1;break;case 64:45===zo()&&(S+=Ho(Do())),f=zo(),u=d=wo(x=S+=Go(Io())),y++;break;case 45:45===h&&2==wo(S)&&(m=0)}}return a}function ea(e,t,n,r,o,a,i,l,s,c,u,d){for(var f=o-1,p=0===o?a:[""],h=jo(p),m=0,g=0,v=0;m<r;++m)for(var y=0,x=bo(e,f+1,f=fo(g=i[m])),b=e;y<h;++y)(b=mo(g>0?p[y]+" "+x:vo(x,/&\f/g,p[y])))&&(s[v++]=b);return No(e,t,n,0===o?so:l,s,c,u,d)}function ta(e,t,n,r){return No(e,t,n,lo,po(Po),bo(e,2,-2),0,r)}function na(e,t,n,r,o){return No(e,t,n,co,bo(e,0,r),bo(e,r+1,-1),r,o)}var ra={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},oa="undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}&&({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.REACT_APP_SC_ATTR||{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.SC_ATTR)||"data-styled",aa="active",ia="data-styled-version",la="6.1.18",sa="/*!sc*/\n",ca="undefined"!=typeof window&&"undefined"!=typeof document,ua=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.REACT_APP_SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.SC_DISABLE_SPEEDY&&("false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://localhost:59358/api",REACT_APP_NAME:"UBI Customer Premises Verification",REACT_APP_VERSION:"1.0.0"}.SC_DISABLE_SPEEDY)),da={},fa=(new Set,Object.freeze([])),pa=Object.freeze({});function ha(e,t,n){return void 0===n&&(n=pa),e.theme!==n.theme&&e.theme||t||n.theme}var ma=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ga=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,va=/(^-|-$)/g;function ya(e){return e.replace(ga,"-").replace(va,"")}var xa=/(a)(d)/gi,ba=function(e){return String.fromCharCode(e+(e>25?39:97))};function wa(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=ba(t%52)+n;return(ba(t%52)+n).replace(xa,"$1-$2")}var ja,Sa=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},ka=function(e){return Sa(5381,e)};function Ca(e){return wa(ka(e)>>>0)}function Ea(e){return e.displayName||e.name||"Component"}function Ta(e){return"string"==typeof e&&!0}var Aa="function"==typeof Symbol&&Symbol.for,Pa=Aa?Symbol.for("react.memo"):60115,Ra=Aa?Symbol.for("react.forward_ref"):60112,Na={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},_a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},La={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Oa=((ja={})[Ra]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ja[Pa]=La,ja);function Da(e){return("type"in(t=e)&&t.type.$$typeof)===Pa?La:"$$typeof"in e?Oa[e.$$typeof]:Na;var t}var za=Object.defineProperty,Ia=Object.getOwnPropertyNames,Fa=Object.getOwnPropertySymbols,Ua=Object.getOwnPropertyDescriptor,Ma=Object.getPrototypeOf,Ba=Object.prototype;function Ha(e,t,n){if("string"!=typeof t){if(Ba){var r=Ma(t);r&&r!==Ba&&Ha(e,r,n)}var o=Ia(t);Fa&&(o=o.concat(Fa(t)));for(var a=Da(e),i=Da(t),l=0;l<o.length;++l){var s=o[l];if(!(s in _a||n&&n[s]||i&&s in i||a&&s in a)){var c=Ua(t,s);try{za(e,s,c)}catch(e){}}}}return e}function Wa(e){return"function"==typeof e}function $a(e){return"object"==typeof e&&"styledComponentId"in e}function Va(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function qa(e,t){if(0===e.length)return"";for(var n=e[0],r=1;r<e.length;r++)n+=t?t+e[r]:e[r];return n}function Ga(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Ka(e,t,n){if(void 0===n&&(n=!1),!n&&!Ga(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var r=0;r<t.length;r++)e[r]=Ka(e[r],t[r]);else if(Ga(t))for(var r in t)e[r]=Ka(e[r],t[r]);return e}function Qa(e,t){Object.defineProperty(e,"toString",{value:t})}function Ja(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var Ya=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,o=r;e>=o;)if((o<<=1)<0)throw Ja(16,"".concat(e));this.groupSizes=new Uint32Array(o),this.groupSizes.set(n),this.length=o;for(var a=r;a<o;a++)this.groupSizes[a]=0}for(var i=this.indexOfGroup(e+1),l=(a=0,t.length);a<l;a++)this.tag.insertRule(i,t[a])&&(this.groupSizes[e]++,i++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var o=n;o<r;o++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),o=r+n,a=r;a<o;a++)t+="".concat(this.tag.getRule(a)).concat(sa);return t},e}(),Za=new Map,Xa=new Map,ei=1,ti=function(e){if(Za.has(e))return Za.get(e);for(;Xa.has(ei);)ei++;var t=ei++;return Za.set(e,t),Xa.set(t,e),t},ni=function(e,t){ei=t+1,Za.set(e,t),Xa.set(t,e)},ri="style[".concat(oa,"][").concat(ia,'="').concat(la,'"]'),oi=new RegExp("^".concat(oa,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),ai=function(e,t,n){for(var r,o=n.split(","),a=0,i=o.length;a<i;a++)(r=o[a])&&e.registerName(t,r)},ii=function(e,t){for(var n,r=(null!==(n=t.textContent)&&void 0!==n?n:"").split(sa),o=[],a=0,i=r.length;a<i;a++){var l=r[a].trim();if(l){var s=l.match(oi);if(s){var c=0|parseInt(s[1],10),u=s[2];0!==c&&(ni(u,c),ai(e,u,s[3]),e.getTag().insertRules(c,o)),o.length=0}else o.push(l)}}},li=function(e){for(var t=document.querySelectorAll(ri),n=0,r=t.length;n<r;n++){var o=t[n];o&&o.getAttribute(oa)!==aa&&(ii(e,o),o.parentNode&&o.parentNode.removeChild(o))}};function si(){return n.nc}var ci=function(e){var t=document.head,n=e||t,r=document.createElement("style"),o=function(e){var t=Array.from(e.querySelectorAll("style[".concat(oa,"]")));return t[t.length-1]}(n),a=void 0!==o?o.nextSibling:null;r.setAttribute(oa,aa),r.setAttribute(ia,la);var i=si();return i&&r.setAttribute("nonce",i),n.insertBefore(r,a),r},ui=function(){function e(e){this.element=ci(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var o=t[n];if(o.ownerNode===e)return o}throw Ja(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),di=function(){function e(e){this.element=ci(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),fi=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),pi=ca,hi={isServer:!ca,useCSSOMInjection:!ua},mi=function(){function e(e,t,n){void 0===e&&(e=pa),void 0===t&&(t={});var r=this;this.options=eo(eo({},hi),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&ca&&pi&&(pi=!1,li(this)),Qa(this,(function(){return function(e){for(var t=e.getTag(),n=t.length,r="",o=function(n){var o=function(e){return Xa.get(e)}(n);if(void 0===o)return"continue";var a=e.names.get(o),i=t.getGroup(n);if(void 0===a||!a.size||0===i.length)return"continue";var l="".concat(oa,".g").concat(n,'[id="').concat(o,'"]'),s="";void 0!==a&&a.forEach((function(e){e.length>0&&(s+="".concat(e,","))})),r+="".concat(i).concat(l,'{content:"').concat(s,'"}').concat(sa)},a=0;a<n;a++)o(a);return r}(r)}))}return e.registerId=function(e){return ti(e)},e.prototype.rehydrate=function(){!this.server&&ca&&li(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(eo(eo({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new fi(n):t?new ui(n):new di(n)}(this.options),new Ya(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(ti(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(ti(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(ti(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),gi=/&/g,vi=/^\s*\/\/.*$/gm;function yi(e,t){return e.map((function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map((function(e){return"".concat(t," ").concat(e)}))),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=yi(e.children,t)),e}))}function xi(e){var t,n,r,o=void 0===e?pa:e,a=o.options,i=void 0===a?pa:a,l=o.plugins,s=void 0===l?fa:l,c=function(e,r,o){return o.startsWith(n)&&o.endsWith(n)&&o.replaceAll(n,"").length>0?".".concat(t):e},u=s.slice();u.push((function(e){e.type===so&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(gi,n).replace(r,c))})),i.prefix&&u.push(Yo),u.push(Qo);var d=function(e,o,a,l){void 0===o&&(o=""),void 0===a&&(a=""),void 0===l&&(l="&"),t=l,n=o,r=new RegExp("\\".concat(n,"\\b"),"g");var s=e.replace(vi,""),c=Zo(a||o?"".concat(a," ").concat(o," { ").concat(s," }"):s);i.namespace&&(c=yi(c,i.namespace));var d,f=[];return Ko(c,function(e){var t=jo(e);return function(n,r,o,a){for(var i="",l=0;l<t;l++)i+=e[l](n,r,o,a)||"";return i}}(u.concat((d=function(e){return f.push(e)},function(e){e.root||(e=e.return)&&d(e)})))),f};return d.hash=s.length?s.reduce((function(e,t){return t.name||Ja(15),Sa(e,t.name)}),5381).toString():"",d}var bi=new mi,wi=xi(),ji=t.createContext({shouldForwardProp:void 0,styleSheet:bi,stylis:wi}),Si=(ji.Consumer,t.createContext(void 0));function ki(){return(0,t.useContext)(ji)}function Ci(e){var n=(0,t.useState)(e.stylisPlugins),r=n[0],o=n[1],a=ki().styleSheet,i=(0,t.useMemo)((function(){var t=a;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t}),[e.disableCSSOMInjection,e.sheet,e.target,a]),l=(0,t.useMemo)((function(){return xi({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:r})}),[e.enableVendorPrefixes,e.namespace,r]);(0,t.useEffect)((function(){ro()(r,e.stylisPlugins)||o(e.stylisPlugins)}),[e.stylisPlugins]);var s=(0,t.useMemo)((function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:i,stylis:l}}),[e.shouldForwardProp,i,l]);return t.createElement(ji.Provider,{value:s},t.createElement(Si.Provider,{value:l},e.children))}var Ei=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=wi);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,Qa(this,(function(){throw Ja(12,String(n.name))}))}return e.prototype.getName=function(e){return void 0===e&&(e=wi),this.name+e.hash},e}(),Ti=function(e){return e>="A"&&e<="Z"};function Ai(e){for(var t="",n=0;n<e.length;n++){var r=e[n];if(1===n&&"-"===r&&"-"===e[0])return e;Ti(r)?t+="-"+r.toLowerCase():t+=r}return t.startsWith("ms-")?"-"+t:t}var Pi=function(e){return null==e||!1===e||""===e},Ri=function(e){var t,n,r=[];for(var o in e){var a=e[o];e.hasOwnProperty(o)&&!Pi(a)&&(Array.isArray(a)&&a.isCss||Wa(a)?r.push("".concat(Ai(o),":"),a,";"):Ga(a)?r.push.apply(r,to(to(["".concat(o," {")],Ri(a),!1),["}"],!1)):r.push("".concat(Ai(o),": ").concat((t=o,null==(n=a)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in ra||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return r};function Ni(e,t,n,r){return Pi(e)?[]:$a(e)?[".".concat(e.styledComponentId)]:Wa(e)?!Wa(o=e)||o.prototype&&o.prototype.isReactComponent||!t?[e]:Ni(e(t),t,n,r):e instanceof Ei?n?(e.inject(n,r),[e.getName(r)]):[e]:Ga(e)?Ri(e):Array.isArray(e)?Array.prototype.concat.apply(fa,e.map((function(e){return Ni(e,t,n,r)}))):[e.toString()];var o}function _i(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Wa(n)&&!$a(n))return!1}return!0}var Li=ka(la),Oi=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&_i(e),this.componentId=t,this.baseHash=Sa(Li,t),this.baseStyle=n,mi.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))r=Va(r,this.staticRulesId);else{var o=qa(Ni(this.rules,e,t,n)),a=wa(Sa(this.baseHash,o)>>>0);if(!t.hasNameForId(this.componentId,a)){var i=n(o,".".concat(a),void 0,this.componentId);t.insertRules(this.componentId,a,i)}r=Va(r,a),this.staticRulesId=a}else{for(var l=Sa(this.baseHash,n.hash),s="",c=0;c<this.rules.length;c++){var u=this.rules[c];if("string"==typeof u)s+=u;else if(u){var d=qa(Ni(u,e,t,n));l=Sa(l,d+c),s+=d}}if(s){var f=wa(l>>>0);t.hasNameForId(this.componentId,f)||t.insertRules(this.componentId,f,n(s,".".concat(f),void 0,this.componentId)),r=Va(r,f)}}return r},e}(),Di=t.createContext(void 0);Di.Consumer;function zi(e){var n=t.useContext(Di),r=(0,t.useMemo)((function(){return function(e,t){if(!e)throw Ja(14);if(Wa(e))return e(t);if(Array.isArray(e)||"object"!=typeof e)throw Ja(8);return t?eo(eo({},t),e):e}(e.theme,n)}),[e.theme,n]);return e.children?t.createElement(Di.Provider,{value:r},e.children):null}var Ii={};new Set;function Fi(e,n,r){var o=$a(e),a=e,i=!Ta(e),l=n.attrs,s=void 0===l?fa:l,c=n.componentId,u=void 0===c?function(e,t){var n="string"!=typeof e?"sc":ya(e);Ii[n]=(Ii[n]||0)+1;var r="".concat(n,"-").concat(Ca(la+n+Ii[n]));return t?"".concat(t,"-").concat(r):r}(n.displayName,n.parentComponentId):c,d=n.displayName,f=void 0===d?function(e){return Ta(e)?"styled.".concat(e):"Styled(".concat(Ea(e),")")}(e):d,p=n.displayName&&n.componentId?"".concat(ya(n.displayName),"-").concat(n.componentId):n.componentId||u,h=o&&a.attrs?a.attrs.concat(s).filter(Boolean):s,m=n.shouldForwardProp;if(o&&a.shouldForwardProp){var g=a.shouldForwardProp;if(n.shouldForwardProp){var v=n.shouldForwardProp;m=function(e,t){return g(e,t)&&v(e,t)}}else m=g}var y=new Oi(r,p,o?a.componentStyle:void 0);function x(e,n){return function(e,n,r){var o=e.attrs,a=e.componentStyle,i=e.defaultProps,l=e.foldedComponentIds,s=e.styledComponentId,c=e.target,u=t.useContext(Di),d=ki(),f=e.shouldForwardProp||d.shouldForwardProp,p=ha(n,u,i)||pa,h=function(e,t,n){for(var r,o=eo(eo({},t),{className:void 0,theme:n}),a=0;a<e.length;a+=1){var i=Wa(r=e[a])?r(o):r;for(var l in i)o[l]="className"===l?Va(o[l],i[l]):"style"===l?eo(eo({},o[l]),i[l]):i[l]}return t.className&&(o.className=Va(o.className,t.className)),o}(o,n,p),m=h.as||c,g={};for(var v in h)void 0===h[v]||"$"===v[0]||"as"===v||"theme"===v&&h.theme===p||("forwardedAs"===v?g.as=h.forwardedAs:f&&!f(v,m)||(g[v]=h[v]));var y=function(e,t){var n=ki();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(a,h),x=Va(l,s);return y&&(x+=" "+y),h.className&&(x+=" "+h.className),g[Ta(m)&&!ma.has(m)?"class":"className"]=x,r&&(g.ref=r),(0,t.createElement)(m,g)}(b,e,n)}x.displayName=f;var b=t.forwardRef(x);return b.attrs=h,b.componentStyle=y,b.displayName=f,b.shouldForwardProp=m,b.foldedComponentIds=o?Va(a.foldedComponentIds,a.styledComponentId):"",b.styledComponentId=p,b.target=o?a.target:e,Object.defineProperty(b,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=o?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=0,o=t;r<o.length;r++)Ka(e,o[r],!0);return e}({},a.defaultProps,e):e}}),Qa(b,(function(){return".".concat(b.styledComponentId)})),i&&Ha(b,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),b}function Ui(e,t){for(var n=[e[0]],r=0,o=t.length;r<o;r+=1)n.push(t[r],e[r+1]);return n}var Mi=function(e){return Object.assign(e,{isCss:!0})};function Bi(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(Wa(e)||Ga(e))return Mi(Ni(Ui(fa,to([e],t,!0))));var r=e;return 0===t.length&&1===r.length&&"string"==typeof r[0]?Ni(r):Mi(Ni(Ui(r,t)))}function Hi(e,t,n){if(void 0===n&&(n=pa),!t)throw Ja(1,t);var r=function(r){for(var o=[],a=1;a<arguments.length;a++)o[a-1]=arguments[a];return e(t,n,Bi.apply(void 0,to([r],o,!1)))};return r.attrs=function(r){return Hi(e,t,eo(eo({},n),{attrs:Array.prototype.concat(n.attrs,r).filter(Boolean)}))},r.withConfig=function(r){return Hi(e,t,eo(eo({},n),r))},r}var Wi=function(e){return Hi(Fi,e)},$i=Wi;ma.forEach((function(e){$i[e]=Wi(e)}));var Vi=function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=_i(e),mi.registerId(this.componentId+1)}return e.prototype.createStyles=function(e,t,n,r){var o=r(qa(Ni(this.rules,t,n,r)),""),a=this.componentId+e;n.insertRules(a,a,o)},e.prototype.removeStyles=function(e,t){t.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,t,n,r){e>2&&mi.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)},e}();var qi,Gi,Ki,Qi,Ji,Yi,Zi,Xi,el,tl;(function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=si(),r=qa([n&&'nonce="'.concat(n,'"'),"".concat(oa,'="true"'),"".concat(ia,'="').concat(la,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(t,"</style>")},this.getStyleTags=function(){if(e.sealed)throw Ja(2);return e._emitSheetCSS()},this.getStyleElement=function(){var n;if(e.sealed)throw Ja(2);var r=e.instance.toString();if(!r)return[];var o=((n={})[oa]="",n[ia]=la,n.dangerouslySetInnerHTML={__html:r},n),a=si();return a&&(o.nonce=a),[t.createElement("style",eo({},o,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new mi({isServer:!0}),this.sealed=!1}e.prototype.collectStyles=function(e){if(this.sealed)throw Ja(2);return t.createElement(Ci,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw Ja(3)}})(),"__sc-".concat(oa,"__");function nl(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}const rl={colors:{primary:"#007E3A",primaryDark:"#005a2a",secondary:"#FFD100",secondaryDark:"#e6bc00",white:"#FFFFFF",offWhite:"#f9f9f9",lightGray:"#F5F5F5",mediumGray:"#e0e0e0",textDark:"#333333",textMedium:"#555555",textLight:"#777777",error:"#dc3545",success:"#28a745",warning:"#ffc107",info:"#17a2b8"},shadows:{sm:"0 2px 8px rgba(0, 0, 0, 0.05)",md:"0 4px 12px rgba(0, 0, 0, 0.08)",lg:"0 8px 24px rgba(0, 0, 0, 0.12)"},borderRadius:{sm:"6px",md:"12px",lg:"20px"},transitions:{default:"all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)"},breakpoints:{mobile:"480px",tablet:"768px",desktop:"1024px"}},ol=function(e){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var o=Bi.apply(void 0,to([e],n,!1)),a="sc-global-".concat(Ca(JSON.stringify(o))),i=new Vi(o,a),l=function(e){var n=ki(),r=t.useContext(Di),o=t.useRef(n.styleSheet.allocateGSInstance(a)).current;return n.styleSheet.server&&s(o,e,n.styleSheet,r,n.stylis),t.useLayoutEffect((function(){if(!n.styleSheet.server)return s(o,e,n.styleSheet,r,n.stylis),function(){return i.removeStyles(o,n.styleSheet)}}),[o,e,n.styleSheet,r,n.stylis]),null};function s(e,t,n,r,o){if(i.isStatic)i.renderStyles(e,da,n,o);else{var a=eo(eo({},t),{theme:ha(t,r,l.defaultProps)});i.renderStyles(e,a,n,o)}}return t.memo(l)}(qi||(qi=nl(["\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  body {\n    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\n    color: ",";\n    min-height: 100vh;\n    line-height: 1.6;\n  }\n\n  code {\n    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n      monospace;\n  }\n\n  button {\n    font-family: inherit;\n    cursor: pointer;\n    border: none;\n    outline: none;\n  }\n\n  input, textarea, select {\n    font-family: inherit;\n    outline: none;\n  }\n\n  a {\n    text-decoration: none;\n    color: inherit;\n  }\n\n  ul, ol {\n    list-style: none;\n  }\n\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n"])),rl.colors.textDark),al=($i.div(Gi||(Gi=nl(["\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n"]))),$i.div(Ki||(Ki=nl(["\n  background: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  padding: 20px;\n  margin-bottom: 20px;\n  transition: ",";\n\n  &:hover {\n    box-shadow: ",";\n  }\n"])),rl.colors.white,rl.borderRadius.md,rl.shadows.md,rl.transitions.default,rl.shadows.lg)),il=$i.button(Qi||(Qi=nl(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  border-radius: ",";\n  font-size: ",";\n  font-weight: 600;\n  transition: ",";\n  text-align: center;\n  letter-spacing: 0.5px;\n  box-shadow: ",";\n  width: ",";\n\n  ","\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n"])),(e=>{switch(e.size){case"sm":return"8px 16px";case"lg":return"14px 28px";default:return"10px 20px"}}),rl.borderRadius.sm,(e=>{switch(e.size){case"sm":return"12px";case"lg":return"16px";default:return"14px"}}),rl.transitions.default,rl.shadows.sm,(e=>e.fullWidth?"100%":"auto"),(e=>{switch(e.variant){case"secondary":return"\n          background: linear-gradient(135deg, ".concat(rl.colors.secondary,", ").concat(rl.colors.secondaryDark,");\n          color: ").concat(rl.colors.textDark,";\n          &:hover {\n            background: linear-gradient(135deg, ").concat(rl.colors.secondaryDark,", ").concat(rl.colors.secondary,");\n            transform: translateY(-2px);\n            box-shadow: ").concat(rl.shadows.md,";\n          }\n        ");case"outline":return"\n          background: transparent;\n          color: ".concat(rl.colors.primary,";\n          border: 1px solid ").concat(rl.colors.primary,";\n          &:hover {\n            background: ").concat(rl.colors.primary,";\n            color: ").concat(rl.colors.white,";\n          }\n        ");case"danger":return"\n          background: ".concat(rl.colors.error,";\n          color: ").concat(rl.colors.white,";\n          &:hover {\n            background: #c82333;\n            transform: translateY(-2px);\n            box-shadow: ").concat(rl.shadows.md,";\n          }\n        ");default:return"\n          background: linear-gradient(135deg, ".concat(rl.colors.primary,", ").concat(rl.colors.primaryDark,");\n          color: ").concat(rl.colors.white,";\n          &:hover {\n            background: linear-gradient(135deg, ").concat(rl.colors.primaryDark,", ").concat(rl.colors.primary,");\n            transform: translateY(-2px);\n            box-shadow: ").concat(rl.shadows.md,";\n          }\n        ")}})),ll=$i.input(Ji||(Ji=nl(["\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  transition: ",";\n  background-color: ",";\n  color: ",";\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);\n\n  &:focus {\n    border-color: ",";\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    background-color: ",";\n  }\n\n  &::placeholder {\n    color: ",";\n  }\n"])),rl.colors.mediumGray,rl.borderRadius.sm,rl.transitions.default,rl.colors.offWhite,rl.colors.textDark,rl.colors.primary,rl.colors.white,rl.colors.textLight),sl=$i.select(Yi||(Yi=nl(["\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  transition: ",";\n  background-color: ",";\n  color: ",";\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);\n\n  &:focus {\n    border-color: ",";\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    background-color: ",";\n  }\n"])),rl.colors.mediumGray,rl.borderRadius.sm,rl.transitions.default,rl.colors.offWhite,rl.colors.textDark,rl.colors.primary,rl.colors.white),cl=$i.label(Zi||(Zi=nl(["\n  display: block;\n  margin-bottom: 6px;\n  font-weight: 500;\n  color: ",";\n  font-size: 14px;\n  transition: ",";\n"])),rl.colors.textMedium,rl.transitions.default),ul=$i.div(Xi||(Xi=nl(["\n  margin-bottom: 20px;\n\n  &:focus-within "," {\n    color: ",";\n  }\n"])),cl,rl.colors.primary),dl=$i.div(el||(el=nl(["\n  color: ",";\n  font-size: 12px;\n  margin-top: 5px;\n"])),rl.colors.error),fl=$i.div(tl||(tl=nl(["\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid ",";\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n"])),rl.colors.primary);var pl,hl,ml,gl,vl,yl,xl,bl,wl,jl,Sl;const kl=$i.div(pl||(pl=nl(["\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n"]))),Cl=$i.div(hl||(hl=nl(["\n  width: 100%;\n  max-width: 400px;\n  background: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  padding: 30px;\n  transition: ",";\n  border-top: 4px solid ",";\n\n  &:hover {\n    box-shadow: ",";\n  }\n"])),(e=>e.theme.colors.white),(e=>e.theme.borderRadius.md),(e=>e.theme.shadows.md),(e=>e.theme.transitions.default),(e=>e.theme.colors.secondary),(e=>e.theme.shadows.lg)),El=$i.div(ml||(ml=nl(["\n  text-align: center;\n  margin-bottom: 30px;\n"]))),Tl=$i.div(gl||(gl=nl(["\n  display: flex;\n  justify-content: center;\n  margin-bottom: 15px;\n"]))),Al=$i.div(vl||(vl=nl(["\n  width: 120px;\n  height: 60px;\n  position: relative;\n"]))),Pl=$i.div(yl||(yl=nl(["\n  width: 50px;\n  height: 50px;\n  background-color: ",";\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  left: 15px;\n  transform: rotate(180deg);\n  box-shadow: ",";\n"])),(e=>e.theme.colors.primary),(e=>e.theme.shadows.sm)),Rl=$i.div(xl||(xl=nl(["\n  width: 50px;\n  height: 50px;\n  background-color: ",";\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  right: 15px;\n  box-shadow: ",";\n"])),(e=>e.theme.colors.secondary),(e=>e.theme.shadows.sm)),Nl=$i.h1(bl||(bl=nl(["\n  color: ",";\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 5px;\n  letter-spacing: 0.5px;\n"])),(e=>e.theme.colors.secondary)),_l=$i.p(wl||(wl=nl(["\n  color: ",";\n  font-size: 14px;\n  margin-bottom: 5px;\n  font-weight: 500;\n"])),(e=>e.theme.colors.textLight)),Ll=$i.div(jl||(jl=nl(["\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n"]))),Ol=$i.div(Sl||(Sl=nl(["\n  flex: 1;\n  text-align: center;\n  padding: 10px;\n  border: 1px solid ",";\n  background-color: ",";\n  color: ",";\n  border-color: ",";\n  cursor: pointer;\n  transition: ",";\n  font-weight: 500;\n  font-size: 13px;\n\n  &:first-child {\n    border-radius: "," 0 0 ",";\n  }\n\n  &:last-child {\n    border-radius: 0 "," "," 0;\n  }\n\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.active?e.theme.colors.secondary:e.theme.colors.offWhite),(e=>e.active?e.theme.colors.white:e.theme.colors.textDark),(e=>e.active?e.theme.colors.secondary:e.theme.colors.mediumGray),(e=>e.theme.transitions.default),(e=>e.theme.borderRadius.sm),(e=>e.theme.borderRadius.sm),(e=>e.theme.borderRadius.sm),(e=>e.theme.borderRadius.sm),(e=>e.active?e.theme.colors.secondaryDark:e.theme.colors.lightGray)),Dl=()=>{const[e,n]=(0,t.useState)(""),[r,o]=(0,t.useState)(""),[a,i]=(0,t.useState)("Agent"),[l,s]=(0,t.useState)(""),[c,u]=(0,t.useState)(!1),{login:d,isAuthenticated:f,user:p}=Zr(),h=pe();(0,t.useEffect)((()=>{if(f&&p)switch(p.role){case"Agent":h("/agent/dashboard");break;case"Supervisor":h("/supervisor/dashboard");break;case"Admin":h("/admin/dashboard");break;default:h("/")}}),[f,p,h]);return(0,Jr.jsx)(kl,{children:(0,Jr.jsxs)(Cl,{children:[(0,Jr.jsxs)(El,{children:[(0,Jr.jsx)(Tl,{children:(0,Jr.jsxs)(Al,{children:[(0,Jr.jsx)(Pl,{}),(0,Jr.jsx)(Rl,{})]})}),(0,Jr.jsx)(Nl,{children:"Union Bank of India"}),(0,Jr.jsx)(_l,{children:"Office Verification System"})]}),(0,Jr.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),s(""),e&&r){u(!0);try{await d(e,r,a)}catch(n){s(n.message||"Login failed. Please check your credentials.")}finally{u(!1)}}else s("Please enter both username and password")},children:[(0,Jr.jsxs)(Ll,{children:[(0,Jr.jsx)(Ol,{active:"Agent"===a,onClick:()=>i("Agent"),children:"Agent"}),(0,Jr.jsx)(Ol,{active:"Supervisor"===a,onClick:()=>i("Supervisor"),children:"Supervisor"}),(0,Jr.jsx)(Ol,{active:"Admin"===a,onClick:()=>i("Admin"),children:"Admin"})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"username",children:"Username"}),(0,Jr.jsx)(ll,{type:"text",id:"username",value:e,onChange:e=>n(e.target.value),placeholder:"Enter your username",required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"password",children:"Password"}),(0,Jr.jsx)(ll,{type:"password",id:"password",value:r,onChange:e=>o(e.target.value),placeholder:"Enter your password",required:!0}),l&&(0,Jr.jsx)(dl,{children:l})]}),(0,Jr.jsx)(il,{type:"submit",variant:"primary",fullWidth:!0,disabled:c,children:c?(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(fl,{}),(0,Jr.jsx)("span",{style:{marginLeft:"8px"},children:"Logging in..."})]}):"Login"})]})]})})};var zl,Il;const Fl=$i.div(zl||(zl=nl(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  flex-direction: column;\n  gap: 16px;\n"]))),Ul=$i.div(Il||(Il=nl(["\n  color: ",";\n  font-size: 16px;\n"])),(e=>e.theme.colors.textMedium)),Ml=e=>{let{children:t,allowedRoles:n}=e;const{isAuthenticated:r,user:o,isLoading:a}=Zr();if(a)return(0,Jr.jsxs)(Fl,{children:[(0,Jr.jsx)(fl,{}),(0,Jr.jsx)(Ul,{children:"Loading..."})]});if(!r||!o)return(0,Jr.jsx)(Pe,{to:"/login",replace:!0});if(!n.includes(o.role))switch(o.role){case"Agent":return(0,Jr.jsx)(Pe,{to:"/agent/dashboard",replace:!0});case"Supervisor":return(0,Jr.jsx)(Pe,{to:"/supervisor/dashboard",replace:!0});case"Admin":return(0,Jr.jsx)(Pe,{to:"/admin/dashboard",replace:!0});default:return(0,Jr.jsx)(Pe,{to:"/login",replace:!0})}return(0,Jr.jsx)(Jr.Fragment,{children:t})};var Bl,Hl,Wl,$l,Vl,ql,Gl,Kl,Ql,Jl,Yl,Zl,Xl,es,ts,ns,rs,os,as,is;const ls=$i.div(Bl||(Bl=nl(["\n  display: flex;\n  min-height: 100vh;\n"]))),ss=$i.div(Hl||(Hl=nl(["\n  width: ",";\n  background-color: ",";\n  color: ",";\n  padding: 20px;\n  position: fixed;\n  height: 100vh;\n  overflow-y: auto;\n  transition: ",";\n  z-index: 1000;\n\n  @media (max-width: ",") {\n    width: ",";\n    padding: 15px 10px;\n  }\n"])),(e=>e.collapsed?"70px":"250px"),(e=>e.theme.colors.primary),(e=>e.theme.colors.white),(e=>e.theme.transitions.default),(e=>e.theme.breakpoints.tablet),(e=>e.collapsed?"70px":"250px")),cs=$i.div(Wl||(Wl=nl(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 30px;\n  justify-content: ",";\n"])),(e=>e.collapsed?"center":"flex-start")),us=$i.div($l||($l=nl(["\n  width: 40px;\n  height: 40px;\n  position: relative;\n  margin-right: 10px;\n"]))),ds=$i.div(Vl||(Vl=nl(["\n  width: 20px;\n  height: 20px;\n  background-color: ",";\n  border-radius: 10px 10px 0 0;\n  position: absolute;\n  left: 5px;\n  transform: rotate(180deg);\n"])),(e=>e.theme.colors.secondary)),fs=$i.div(ql||(ql=nl(["\n  width: 20px;\n  height: 20px;\n  background-color: ",";\n  border-radius: 10px 10px 0 0;\n  position: absolute;\n  right: 5px;\n"])),(e=>e.theme.colors.secondary)),ps=$i.div(Gl||(Gl=nl(["\n  font-size: 18px;\n  font-weight: 600;\n  display: ",";\n"])),(e=>e.collapsed?"none":"block")),hs=$i.div(Kl||(Kl=nl(["\n  padding: 12px 15px;\n  border-radius: ",";\n  margin-bottom: 5px;\n  cursor: pointer;\n  transition: ",";\n  display: flex;\n  align-items: center;\n  background-color: ",";\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n"])),(e=>e.theme.borderRadius.sm),(e=>e.theme.transitions.default),(e=>e.active?"rgba(255, 255, 255, 0.2)":"transparent")),ms=$i.div(Ql||(Ql=nl(["\n  margin-right: ",";\n  width: 20px;\n  text-align: center;\n  font-size: 16px;\n"])),(e=>e.collapsed?"0":"10px")),gs=$i.div(Jl||(Jl=nl(["\n  display: ",";\n"])),(e=>e.collapsed?"none":"block")),vs=$i.div(Yl||(Yl=nl(["\n  margin-top: auto;\n  padding: 15px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  align-items: center;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  background-color: ",";\n  justify-content: ",";\n"])),(e=>e.theme.colors.primary),(e=>e.collapsed?"center":"flex-start")),ys=$i.div(Zl||(Zl=nl(["\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.secondary),(e=>e.theme.colors.textDark)),xs=$i.div(Xl||(Xl=nl(["\n  display: ",";\n"])),(e=>e.collapsed?"none":"block")),bs=$i.div(es||(es=nl(["\n  font-size: 14px;\n  font-weight: 500;\n"]))),ws=$i.div(ts||(ts=nl(["\n  font-size: 12px;\n  opacity: 0.8;\n"]))),js=$i.div(ns||(ns=nl(["\n  flex: 1;\n  margin-left: ",";\n  padding: 20px;\n  transition: ",";\n"])),(e=>e.sidebarCollapsed?"70px":"250px"),(e=>e.theme.transitions.default)),Ss=$i.div(rs||(rs=nl(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.mediumGray)),ks=$i.h1(os||(os=nl(["\n  font-size: 24px;\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.primary)),Cs=$i.div(as||(as=nl(["\n  display: flex;\n  align-items: center;\n  gap: 10px;\n"]))),Es=$i.button(is||(is=nl(["\n  background: none;\n  border: none;\n  color: ",";\n  font-size: 18px;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: ",";\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n"])),(e=>e.theme.colors.white),(e=>e.theme.transitions.default)),Ts=e=>{var n;let{children:r,title:o,headerActions:a,navigationItems:i}=e;const[l,s]=(0,t.useState)(!1),{user:c,logout:u}=Zr();return(0,Jr.jsxs)(ls,{children:[(0,Jr.jsxs)(ss,{collapsed:l,children:[(0,Jr.jsxs)(cs,{collapsed:l,children:[(0,Jr.jsxs)(us,{children:[(0,Jr.jsx)(ds,{}),(0,Jr.jsx)(fs,{})]}),(0,Jr.jsx)(ps,{collapsed:l,children:"UBI Verify"})]}),(0,Jr.jsx)(Es,{onClick:()=>{s(!l)},children:l?"\u2630":"\u2190"}),i.map(((e,t)=>(0,Jr.jsxs)(hs,{active:e.active,onClick:e.onClick,children:[(0,Jr.jsx)(ms,{collapsed:l,children:e.icon}),(0,Jr.jsx)(gs,{collapsed:l,children:e.label})]},t))),(0,Jr.jsxs)(hs,{onClick:u,children:[(0,Jr.jsx)(ms,{collapsed:l,children:"\ud83d\udeaa"}),(0,Jr.jsx)(gs,{collapsed:l,children:"Logout"})]}),(0,Jr.jsxs)(vs,{collapsed:l,children:[(0,Jr.jsx)(ys,{children:(null===c||void 0===c||null===(n=c.firstName)||void 0===n?void 0:n.charAt(0))||"U"}),(0,Jr.jsxs)(xs,{collapsed:l,children:[(0,Jr.jsx)(bs,{children:(null===c||void 0===c?void 0:c.firstName)||"User"}),(0,Jr.jsx)(ws,{children:(null===c||void 0===c?void 0:c.role)||"Role"})]})]})]}),(0,Jr.jsxs)(js,{sidebarCollapsed:l,children:[(0,Jr.jsxs)(Ss,{children:[(0,Jr.jsx)(ks,{children:o}),(0,Jr.jsxs)(Cs,{children:[a,(0,Jr.jsx)("span",{children:(new Date).toLocaleDateString()})]})]}),r]})]})};var As,Ps,Rs,Ns,_s,Ls,Os,Ds,zs,Is,Fs,Us;const Ms=$i.div(As||(As=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),Bs=$i(al)(Ps||(Ps=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ",";\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ",";\n  }\n"])),(e=>e.theme.transitions.default),(e=>e.theme.shadows.lg)),Hs=$i.div(Rs||(Rs=nl(["\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ",";\n  background: ",";\n"])),(e=>e.theme.colors.white),(e=>e.color)),Ws=$i.div(Ns||(Ns=nl(["\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ",";\n"])),(e=>e.theme.colors.textDark)),$s=$i.div(_s||(_s=nl(["\n  font-size: 14px;\n  color: ",";\n  font-weight: 500;\n"])),(e=>e.theme.colors.textLight)),Vs=$i.div(Ls||(Ls=nl(["\n  overflow-x: auto;\n"]))),qs=$i.table(Os||(Os=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),Gs=$i.th(Ds||(Ds=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),Ks=$i.td(zs||(zs=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),Qs=$i.tr(Is||(Is=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),Js=$i.span(Fs||(Fs=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"new":return"\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        ";case"assigned":return"\n          background-color: #fff3e0;\n          color: #e65100;\n        ";case"in-progress":return"\n          background-color: #fff8e1;\n          color: #ff8f00;\n        ";case"pending-review":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";case"completed":case"approved":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";case"rejected":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),Ys=$i.input(Us||(Us=nl(["\n  width: 100%;\n  padding: 10px 15px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  margin-bottom: 20px;\n  transition: ",";\n\n  &:focus {\n    border-color: ",";\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.transitions.default),(e=>e.theme.colors.primary)),Zs=()=>{const[e,n]=(0,t.useState)([]),[r,o]=(0,t.useState)(null),[a,i]=(0,t.useState)(!0),[l,s]=(0,t.useState)(""),c=pe();(0,t.useEffect)((()=>{u()}),[]);const u=async()=>{try{i(!0);const[e,t]=await Promise.all([Qr.getLeads(1,50,"assigned"),Qr.getAgentDashboardStats()]);n(e.data||[]),o(t)}catch(e){console.error("Error loading dashboard data:",e),n([{leadId:1,customerName:"John Doe",mobileNumber:"9876543210",loanType:"Personal Loan",status:"assigned",createdDate:"2024-01-15T10:30:00Z",assignedDate:"2024-01-15T11:00:00Z",createdByName:"Admin User",assignedToName:"Current Agent",documentCount:0,croppedImageCount:0},{leadId:2,customerName:"Jane Smith",mobileNumber:"9876543211",loanType:"Home Loan",status:"in-progress",createdDate:"2024-01-14T09:15:00Z",assignedDate:"2024-01-14T10:00:00Z",createdByName:"Admin User",assignedToName:"Current Agent",documentCount:2,croppedImageCount:1}]),o({pendingLeads:1,inProgressLeads:2,completedLeads:0,rejectedLeads:0,totalAssigned:3})}finally{i(!1)}},d=e.filter((e=>e.customerName.toLowerCase().includes(l.toLowerCase())||e.mobileNumber.includes(l)||e.loanType.toLowerCase().includes(l.toLowerCase()))),f=[{icon:"\ud83c\udfe0",label:"Dashboard",active:!0},{icon:"\ud83d\udccb",label:"My Tasks",onClick:()=>c("/agent/tasks")},{icon:"\u2705",label:"Completed",onClick:()=>c("/agent/completed")},{icon:"\ud83d\udcca",label:"Reports",onClick:()=>c("/agent/reports")}];return a?(0,Jr.jsx)(Ts,{title:"Agent Dashboard",navigationItems:f,children:(0,Jr.jsx)("div",{children:"Loading..."})}):(0,Jr.jsxs)(Ts,{title:"Agent Dashboard",navigationItems:f,children:[(0,Jr.jsxs)(Ms,{children:[(0,Jr.jsxs)(Bs,{children:[(0,Jr.jsx)(Hs,{color:"linear-gradient(135deg, #FFD100, #e6bc00)",children:"\ud83d\udccb"}),(0,Jr.jsx)(Ws,{children:(null===r||void 0===r?void 0:r.pendingLeads)||0}),(0,Jr.jsx)($s,{children:"Pending Tasks"})]}),(0,Jr.jsxs)(Bs,{children:[(0,Jr.jsx)(Hs,{color:"linear-gradient(135deg, #007E3A, #005a2a)",children:"\u23f1\ufe0f"}),(0,Jr.jsx)(Ws,{children:(null===r||void 0===r?void 0:r.inProgressLeads)||0}),(0,Jr.jsx)($s,{children:"In Progress"})]}),(0,Jr.jsxs)(Bs,{children:[(0,Jr.jsx)(Hs,{color:"linear-gradient(135deg, #28a745, #1e7e34)",children:"\u2705"}),(0,Jr.jsx)(Ws,{children:(null===r||void 0===r?void 0:r.completedLeads)||0}),(0,Jr.jsx)($s,{children:"Completed"})]}),(0,Jr.jsxs)(Bs,{children:[(0,Jr.jsx)(Hs,{color:"linear-gradient(135deg, #dc3545, #c82333)",children:"\u274c"}),(0,Jr.jsx)(Ws,{children:(null===r||void 0===r?void 0:r.rejectedLeads)||0}),(0,Jr.jsx)($s,{children:"Rejected"})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h2",{style:{marginBottom:"20px",color:"#007E3A"},children:"My Assigned Leads"}),(0,Jr.jsx)(Ys,{type:"text",placeholder:"Search leads by name, mobile, or loan type...",value:l,onChange:e=>s(e.target.value)}),(0,Jr.jsx)(Vs,{children:(0,Jr.jsxs)(qs,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(Gs,{children:"Customer Name"}),(0,Jr.jsx)(Gs,{children:"Mobile"}),(0,Jr.jsx)(Gs,{children:"Loan Type"}),(0,Jr.jsx)(Gs,{children:"Status"}),(0,Jr.jsx)(Gs,{children:"Assigned Date"}),(0,Jr.jsx)(Gs,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:d.map((e=>{return(0,Jr.jsxs)(Qs,{children:[(0,Jr.jsx)(Ks,{children:e.customerName}),(0,Jr.jsx)(Ks,{children:e.mobileNumber}),(0,Jr.jsx)(Ks,{children:e.loanType}),(0,Jr.jsx)(Ks,{children:(0,Jr.jsx)(Js,{status:e.status,children:e.status.replace("-"," ").toUpperCase()})}),(0,Jr.jsx)(Ks,{children:e.assignedDate?(t=e.assignedDate,new Date(t).toLocaleDateString()):"-"}),(0,Jr.jsx)(Ks,{children:(0,Jr.jsx)(il,{size:"sm",onClick:()=>{return t=e.leadId,void c("/lead/".concat(t));var t},children:"View Details"})})]},e.leadId);var t}))})]})}),0===d.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:l?"No leads found matching your search.":"No leads assigned yet."})]})]})};var Xs,ec,tc,nc,rc,oc,ac,ic,lc,sc,cc,uc,dc;const fc=$i.div(Xs||(Xs=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),pc=$i(al)(ec||(ec=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ",";\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ",";\n  }\n"])),(e=>e.theme.transitions.default),(e=>e.theme.shadows.lg)),hc=$i.div(tc||(tc=nl(["\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ",";\n  background: ",";\n"])),(e=>e.theme.colors.white),(e=>e.color)),mc=$i.div(nc||(nc=nl(["\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ",";\n"])),(e=>e.theme.colors.textDark)),gc=$i.div(rc||(rc=nl(["\n  font-size: 14px;\n  color: ",";\n  font-weight: 500;\n"])),(e=>e.theme.colors.textLight)),vc=$i.div(oc||(oc=nl(["\n  overflow-x: auto;\n"]))),yc=$i.table(ac||(ac=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),xc=$i.th(ic||(ic=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),bc=$i.td(lc||(lc=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),wc=$i.tr(sc||(sc=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),jc=$i.span(cc||(cc=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"pending-review":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";case"approved":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";case"rejected":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),Sc=$i.div(uc||(uc=nl(["\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),kc=$i.select(dc||(dc=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background-color: ",";\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.white)),Cc=()=>{const[e,n]=(0,t.useState)([]),[r,o]=(0,t.useState)(null),[a,i]=(0,t.useState)(!0),[l,s]=(0,t.useState)("pending-review"),c=pe();(0,t.useEffect)((()=>{u()}),[l]);const u=async()=>{try{i(!0);const[e,t]=await Promise.all([Qr.getLeads(1,50,l||void 0),Qr.getSupervisorDashboardStats()]);n(e.data||[]),o(t)}catch(e){console.error("Error loading dashboard data:",e),n([{leadId:1,customerName:"John Doe",mobileNumber:"9876543210",loanType:"Personal Loan",status:"pending-review",createdDate:"2024-01-15T10:30:00Z",assignedToName:"Agent Smith",createdByName:"Admin User",documentCount:3,croppedImageCount:2},{leadId:2,customerName:"Jane Smith",mobileNumber:"9876543211",loanType:"Home Loan",status:"pending-review",createdDate:"2024-01-14T09:15:00Z",assignedToName:"Agent Johnson",createdByName:"Admin User",documentCount:5,croppedImageCount:3}]),o({pendingReviews:2,approvedToday:1,rejectedToday:0,totalReviewed:10,approvalRate:85.5,agentPerformance:[{agentId:1,agentName:"Agent Smith",assignedCount:5,completedCount:4,approvedCount:3,rejectedCount:1,completionRate:80,approvalRate:75}],totalLeads:15,completedLeads:10})}finally{i(!1)}},d=[{icon:"\ud83c\udfe0",label:"Dashboard",active:!0},{icon:"\ud83d\udc41\ufe0f",label:"Review Queue",onClick:()=>c("/supervisor/review")},{icon:"\u2705",label:"Approved",onClick:()=>c("/supervisor/approved")},{icon:"\u274c",label:"Rejected",onClick:()=>c("/supervisor/rejected")},{icon:"\ud83d\udcca",label:"Reports",onClick:()=>c("/supervisor/reports")}];return a?(0,Jr.jsx)(Ts,{title:"Supervisor Dashboard",navigationItems:d,children:(0,Jr.jsx)("div",{children:"Loading..."})}):(0,Jr.jsxs)(Ts,{title:"Supervisor Dashboard",navigationItems:d,children:[(0,Jr.jsxs)(fc,{children:[(0,Jr.jsxs)(pc,{children:[(0,Jr.jsx)(hc,{color:"linear-gradient(135deg, #f3e5f5, #4a148c)",children:"\u23f3"}),(0,Jr.jsx)(mc,{children:(null===r||void 0===r?void 0:r.pendingReviews)||0}),(0,Jr.jsx)(gc,{children:"Pending Review"})]}),(0,Jr.jsxs)(pc,{children:[(0,Jr.jsx)(hc,{color:"linear-gradient(135deg, #28a745, #1e7e34)",children:"\u2705"}),(0,Jr.jsx)(mc,{children:(null===r||void 0===r?void 0:r.approvedToday)||0}),(0,Jr.jsx)(gc,{children:"Approved Today"})]}),(0,Jr.jsxs)(pc,{children:[(0,Jr.jsx)(hc,{color:"linear-gradient(135deg, #dc3545, #c82333)",children:"\u274c"}),(0,Jr.jsx)(mc,{children:(null===r||void 0===r?void 0:r.rejectedToday)||0}),(0,Jr.jsx)(gc,{children:"Rejected Today"})]}),(0,Jr.jsxs)(pc,{children:[(0,Jr.jsx)(hc,{color:"linear-gradient(135deg, #007E3A, #005a2a)",children:"\ud83d\udcca"}),(0,Jr.jsx)(mc,{children:null!==r&&void 0!==r&&r.approvalRate?"".concat(r.approvalRate.toFixed(1),"%"):"0%"}),(0,Jr.jsx)(gc,{children:"Approval Rate"})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h2",{style:{marginBottom:"20px",color:"#007E3A"},children:"Review Queue"}),(0,Jr.jsx)(Sc,{children:(0,Jr.jsxs)(kc,{value:l,onChange:e=>s(e.target.value),children:[(0,Jr.jsx)("option",{value:"pending-review",children:"Pending Review"}),(0,Jr.jsx)("option",{value:"approved",children:"Approved"}),(0,Jr.jsx)("option",{value:"rejected",children:"Rejected"}),(0,Jr.jsx)("option",{value:"",children:"All Statuses"})]})}),(0,Jr.jsx)(vc,{children:(0,Jr.jsxs)(yc,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(xc,{children:"Customer Name"}),(0,Jr.jsx)(xc,{children:"Mobile"}),(0,Jr.jsx)(xc,{children:"Loan Type"}),(0,Jr.jsx)(xc,{children:"Status"}),(0,Jr.jsx)(xc,{children:"Submitted Date"}),(0,Jr.jsx)(xc,{children:"Agent"}),(0,Jr.jsx)(xc,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:e.map((e=>{return(0,Jr.jsxs)(wc,{children:[(0,Jr.jsx)(bc,{children:e.customerName}),(0,Jr.jsx)(bc,{children:e.mobileNumber}),(0,Jr.jsx)(bc,{children:e.loanType}),(0,Jr.jsx)(bc,{children:(0,Jr.jsx)(jc,{status:e.status,children:e.status.replace("-"," ").toUpperCase()})}),(0,Jr.jsx)(bc,{children:e.createdDate?(t=e.createdDate,new Date(t).toLocaleDateString()):"-"}),(0,Jr.jsx)(bc,{children:e.assignedToName||"Unassigned"}),(0,Jr.jsx)(bc,{children:(0,Jr.jsxs)("div",{style:{display:"flex",gap:"8px"},children:[(0,Jr.jsx)(il,{size:"sm",onClick:()=>{return t=e.leadId,void c("/lead/".concat(t));var t},children:"View"}),"pending-review"===e.status&&(0,Jr.jsx)(il,{size:"sm",variant:"secondary",onClick:()=>{return t=e.leadId,void c("/supervisor/review/".concat(t));var t},children:"Review"})]})})]},e.leadId);var t}))})]})}),0===e.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:"No leads found for the selected status."})]})]})};var Ec,Tc,Ac,Pc,Rc,Nc,_c,Lc,Oc,Dc,zc,Ic,Fc,Uc,Mc,Bc;const Hc=$i.div(Ec||(Ec=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),Wc=$i(al)(Tc||(Tc=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ",";\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ",";\n  }\n"])),(e=>e.theme.transitions.default),(e=>e.theme.shadows.lg)),$c=$i.div(Ac||(Ac=nl(["\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ",";\n  background: ",";\n"])),(e=>e.theme.colors.white),(e=>e.color)),Vc=$i.div(Pc||(Pc=nl(["\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ",";\n"])),(e=>e.theme.colors.textDark)),qc=$i.div(Rc||(Rc=nl(["\n  font-size: 14px;\n  color: ",";\n  font-weight: 500;\n"])),(e=>e.theme.colors.textLight)),Gc=$i.div(Nc||(Nc=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),Kc=$i(al)(_c||(_c=nl(["\n  text-align: center;\n  padding: 30px 20px;\n"]))),Qc=$i.div(Lc||(Lc=nl(["\n  font-size: 48px;\n  margin-bottom: 15px;\n"]))),Jc=$i.h3(Oc||(Oc=nl(["\n  color: ",";\n  margin-bottom: 10px;\n  font-size: 18px;\n"])),(e=>e.theme.colors.primary)),Yc=$i.p(Dc||(Dc=nl(["\n  color: ",";\n  margin-bottom: 20px;\n  font-size: 14px;\n"])),(e=>e.theme.colors.textMedium)),Zc=$i.div(zc||(zc=nl(["\n  overflow-x: auto;\n"]))),Xc=$i.table(Ic||(Ic=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),eu=$i.th(Fc||(Fc=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),tu=$i.td(Uc||(Uc=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),nu=$i.tr(Mc||(Mc=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),ru=$i.span(Bc||(Bc=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"new":return"\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        ";case"assigned":return"\n          background-color: #fff3e0;\n          color: #e65100;\n        ";case"in-progress":return"\n          background-color: #fff8e1;\n          color: #ff8f00;\n        ";case"pending-review":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";case"approved":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";case"rejected":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),ou=()=>{const[e,n]=(0,t.useState)([]),[r,o]=(0,t.useState)(null),[a,i]=(0,t.useState)(!0),l=pe();(0,t.useEffect)((()=>{s()}),[]);const s=async()=>{try{i(!0);const[e,t]=await Promise.all([Qr.getLeads(1,10),Qr.getDashboardStats()]);n(e.data||[]),o(t)}catch(e){console.error("Error loading dashboard data:",e),n([{leadId:1,customerName:"John Doe",mobileNumber:"9876543210",loanType:"Personal Loan",status:"new",createdDate:"2024-01-15T10:30:00Z",createdByName:"Admin User",documentCount:0,croppedImageCount:0},{leadId:2,customerName:"Jane Smith",mobileNumber:"9876543211",loanType:"Home Loan",status:"assigned",createdDate:"2024-01-14T09:15:00Z",createdByName:"Admin User",assignedToName:"Agent Smith",documentCount:2,croppedImageCount:1}]),o({totalLeads:25,newLeads:5,assignedLeads:8,inProgressLeads:6,pendingReviewLeads:4,approvedLeads:2,rejectedLeads:0,completedLeads:2,pendingLeads:10})}finally{i(!1)}},c=[{icon:"\ud83c\udfe0",label:"Dashboard",active:!0},{icon:"\u2795",label:"Create Lead",onClick:()=>l("/admin/create-lead")},{icon:"\ud83d\udc65",label:"Manage Users",onClick:()=>l("/admin/users")},{icon:"\ud83d\udccb",label:"All Leads",onClick:()=>l("/admin/leads")},{icon:"\ud83d\udcca",label:"Reports",onClick:()=>l("/admin/reports")},{icon:"\u2699\ufe0f",label:"Settings",onClick:()=>l("/admin/settings")}];return a?(0,Jr.jsx)(Ts,{title:"Admin Dashboard",navigationItems:c,children:(0,Jr.jsx)("div",{children:"Loading..."})}):(0,Jr.jsxs)(Ts,{title:"Admin Dashboard",navigationItems:c,children:[(0,Jr.jsxs)(Hc,{children:[(0,Jr.jsxs)(Wc,{children:[(0,Jr.jsx)($c,{color:"linear-gradient(135deg, #007E3A, #005a2a)",children:"\ud83d\udcca"}),(0,Jr.jsx)(Vc,{children:(null===r||void 0===r?void 0:r.totalLeads)||0}),(0,Jr.jsx)(qc,{children:"Total Leads"})]}),(0,Jr.jsxs)(Wc,{children:[(0,Jr.jsx)($c,{color:"linear-gradient(135deg, #e3f2fd, #0d47a1)",children:"\ud83c\udd95"}),(0,Jr.jsx)(Vc,{children:(null===r||void 0===r?void 0:r.newLeads)||0}),(0,Jr.jsx)(qc,{children:"New Leads"})]}),(0,Jr.jsxs)(Wc,{children:[(0,Jr.jsx)($c,{color:"linear-gradient(135deg, #fff8e1, #ff8f00)",children:"\u23f3"}),(0,Jr.jsx)(Vc,{children:(null===r||void 0===r?void 0:r.inProgressLeads)||0}),(0,Jr.jsx)(qc,{children:"In Progress"})]}),(0,Jr.jsxs)(Wc,{children:[(0,Jr.jsx)($c,{color:"linear-gradient(135deg, #f3e5f5, #4a148c)",children:"\ud83d\udc41\ufe0f"}),(0,Jr.jsx)(Vc,{children:(null===r||void 0===r?void 0:r.pendingReviewLeads)||0}),(0,Jr.jsx)(qc,{children:"Pending Review"})]}),(0,Jr.jsxs)(Wc,{children:[(0,Jr.jsx)($c,{color:"linear-gradient(135deg, #28a745, #1e7e34)",children:"\u2705"}),(0,Jr.jsx)(Vc,{children:(null===r||void 0===r?void 0:r.approvedLeads)||0}),(0,Jr.jsx)(qc,{children:"Approved"})]}),(0,Jr.jsxs)(Wc,{children:[(0,Jr.jsx)($c,{color:"linear-gradient(135deg, #dc3545, #c82333)",children:"\u274c"}),(0,Jr.jsx)(Vc,{children:(null===r||void 0===r?void 0:r.rejectedLeads)||0}),(0,Jr.jsx)(qc,{children:"Rejected"})]})]}),(0,Jr.jsxs)(Gc,{children:[(0,Jr.jsxs)(Kc,{children:[(0,Jr.jsx)(Qc,{children:"\u2795"}),(0,Jr.jsx)(Jc,{children:"Create New Lead"}),(0,Jr.jsx)(Yc,{children:"Add a new customer verification lead to the system"}),(0,Jr.jsx)(il,{onClick:()=>{l("/admin/create-lead")},children:"Create Lead"})]}),(0,Jr.jsxs)(Kc,{children:[(0,Jr.jsx)(Qc,{children:"\ud83d\udc65"}),(0,Jr.jsx)(Jc,{children:"Manage Users"}),(0,Jr.jsx)(Yc,{children:"Add, edit, or deactivate agents and supervisors"}),(0,Jr.jsx)(il,{onClick:()=>{l("/admin/users")},children:"Manage Users"})]}),(0,Jr.jsxs)(Kc,{children:[(0,Jr.jsx)(Qc,{children:"\ud83d\udcca"}),(0,Jr.jsx)(Jc,{children:"View Reports"}),(0,Jr.jsx)(Yc,{children:"Generate and view system performance reports"}),(0,Jr.jsx)(il,{onClick:()=>{l("/admin/reports")},children:"View Reports"})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h2",{style:{marginBottom:"20px",color:"#007E3A"},children:"Recent Leads"}),(0,Jr.jsx)(Zc,{children:(0,Jr.jsxs)(Xc,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(eu,{children:"Customer Name"}),(0,Jr.jsx)(eu,{children:"Mobile"}),(0,Jr.jsx)(eu,{children:"Loan Type"}),(0,Jr.jsx)(eu,{children:"Status"}),(0,Jr.jsx)(eu,{children:"Created Date"}),(0,Jr.jsx)(eu,{children:"Assigned Agent"}),(0,Jr.jsx)(eu,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:e.map((e=>{return(0,Jr.jsxs)(nu,{children:[(0,Jr.jsx)(tu,{children:e.customerName}),(0,Jr.jsx)(tu,{children:e.mobileNumber}),(0,Jr.jsx)(tu,{children:e.loanType}),(0,Jr.jsx)(tu,{children:(0,Jr.jsx)(ru,{status:e.status,children:e.status.replace("-"," ").toUpperCase()})}),(0,Jr.jsx)(tu,{children:(t=e.createdDate,new Date(t).toLocaleDateString())}),(0,Jr.jsx)(tu,{children:e.assignedToName||"Unassigned"}),(0,Jr.jsx)(tu,{children:(0,Jr.jsx)(il,{size:"sm",onClick:()=>{return t=e.leadId,void l("/lead/".concat(t));var t},children:"View Details"})})]},e.leadId);var t}))})]})}),0===e.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:"No leads found."})]})]})};var au,iu,lu,su,cu,uu,du,fu,pu,hu,mu,gu,vu,yu,xu,bu;const wu=$i.div(au||(au=nl(["\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n"]))),ju=$i.div(iu||(iu=nl(["\n  display: flex;\n  justify-content: between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.mediumGray)),Su=$i.h1(lu||(lu=nl(["\n  font-size: 24px;\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.primary)),ku=$i(il)(su||(su=nl(["\n  margin-right: 20px;\n"]))),Cu=$i.div(cu||(cu=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),Eu=$i.div(uu||(uu=nl(["\n  margin-bottom: 15px;\n"]))),Tu=$i.div(du||(du=nl(["\n  font-weight: 600;\n  color: ",";\n  margin-bottom: 5px;\n  font-size: 14px;\n"])),(e=>e.theme.colors.textMedium)),Au=$i.div(fu||(fu=nl(["\n  color: ",";\n  font-size: 16px;\n"])),(e=>e.theme.colors.textDark)),Pu=$i.span(pu||(pu=nl(["\n  display: inline-block;\n  padding: 6px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"new":return"\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        ";case"assigned":return"\n          background-color: #fff3e0;\n          color: #e65100;\n        ";case"in-progress":return"\n          background-color: #fff8e1;\n          color: #ff8f00;\n        ";case"pending-review":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";case"approved":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";case"rejected":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),Ru=$i.div(hu||(hu=nl(["\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n"]))),Nu=$i.div(mu||(mu=nl(["\n  margin-top: 20px;\n"]))),_u=$i.div(gu||(gu=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n"]))),Lu=$i.div(vu||(vu=nl(["\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: 15px;\n  text-align: center;\n  background: ",";\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.white)),Ou=$i.div(yu||(yu=nl(["\n  font-size: 32px;\n  margin-bottom: 10px;\n"]))),Du=$i.div(xu||(xu=nl(["\n  font-size: 14px;\n  font-weight: 500;\n  margin-bottom: 5px;\n"]))),zu=$i.div(bu||(bu=nl(["\n  font-size: 12px;\n  color: ",";\n"])),(e=>e.theme.colors.textLight)),Iu=()=>{const{id:e}=he(),n=pe(),{user:r}=Zr(),[o,a]=(0,t.useState)(null),[i,l]=(0,t.useState)(!0);(0,t.useEffect)((()=>{e&&s(parseInt(e))}),[e]);const s=async e=>{try{l(!0);const t=await Qr.getLead(e);a(t)}catch(t){console.error("Error loading lead details:",t),a({leadId:e,customerName:"John Doe",mobileNumber:"9876543210",loanType:"Personal Loan",status:"assigned",createdDate:"2024-01-15T10:30:00Z",assignedDate:"2024-01-15T11:00:00Z",addresses:[],statusHistory:[],documents:[],croppedImages:[]})}finally{l(!1)}},u=async(e,t,n)=>{if(o)try{await Qr.updateLeadStatus(o.leadId,e,t,n),a(c(c({},o),{},{status:e})),alert("Lead status updated to ".concat(e.replace("-"," ")))}catch(r){console.error("Error updating status:",r),alert("Failed to update status")}},d=e=>new Date(e).toLocaleDateString();return i?(0,Jr.jsx)(wu,{children:(0,Jr.jsx)("div",{children:"Loading lead details..."})}):o?(0,Jr.jsxs)(wu,{children:[(0,Jr.jsxs)(ju,{children:[(0,Jr.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,Jr.jsx)(ku,{variant:"outline",onClick:()=>{"Agent"===(null===r||void 0===r?void 0:r.role)?n("/agent/dashboard"):"Supervisor"===(null===r||void 0===r?void 0:r.role)?n("/supervisor/dashboard"):"Admin"===(null===r||void 0===r?void 0:r.role)&&n("/admin/dashboard")},children:"\u2190 Back"}),(0,Jr.jsxs)(Su,{children:["Lead Details - ",o.customerName]})]}),(0,Jr.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[(0,Jr.jsx)(Pu,{status:o.status,children:o.status.replace("-"," ").toUpperCase()}),(0,Jr.jsx)(il,{variant:"outline",size:"sm",onClick:()=>{window.print()},children:"\ud83d\udda8\ufe0f Print"}),(0,Jr.jsx)(il,{variant:"outline",size:"sm",onClick:()=>{alert("Document download functionality would be implemented here")},children:"\ud83d\udce5 Download"})]})]}),(0,Jr.jsxs)(Cu,{children:[(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"15px",color:"#007E3A"},children:"Customer Information"}),(0,Jr.jsxs)(Eu,{children:[(0,Jr.jsx)(Tu,{children:"Customer Name"}),(0,Jr.jsx)(Au,{children:o.customerName})]}),(0,Jr.jsxs)(Eu,{children:[(0,Jr.jsx)(Tu,{children:"Mobile Number"}),(0,Jr.jsx)(Au,{children:o.mobileNumber})]}),(0,Jr.jsxs)(Eu,{children:[(0,Jr.jsx)(Tu,{children:"Loan Type"}),(0,Jr.jsx)(Au,{children:o.loanType})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"15px",color:"#007E3A"},children:"Lead Timeline"}),(0,Jr.jsxs)(Eu,{children:[(0,Jr.jsx)(Tu,{children:"Created Date"}),(0,Jr.jsx)(Au,{children:d(o.createdDate)})]}),o.assignedDate&&(0,Jr.jsxs)(Eu,{children:[(0,Jr.jsx)(Tu,{children:"Assigned Date"}),(0,Jr.jsx)(Au,{children:d(o.assignedDate)})]}),o.startedDate&&(0,Jr.jsxs)(Eu,{children:[(0,Jr.jsx)(Tu,{children:"Started Date"}),(0,Jr.jsx)(Au,{children:d(o.startedDate)})]}),o.submittedDate&&(0,Jr.jsxs)(Eu,{children:[(0,Jr.jsx)(Tu,{children:"Submitted Date"}),(0,Jr.jsx)(Au,{children:d(o.submittedDate)})]})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"15px",color:"#007E3A"},children:"Actions"}),(0,Jr.jsxs)(Ru,{children:["Agent"===(null===r||void 0===r?void 0:r.role)&&"assigned"===o.status&&(0,Jr.jsx)(il,{onClick:()=>u("in-progress"),children:"Start Verification"}),"Agent"===(null===r||void 0===r?void 0:r.role)&&"in-progress"===o.status&&(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(il,{onClick:()=>{n("/lead/".concat(e,"/verification"))},children:"Continue Verification"}),(0,Jr.jsx)(il,{variant:"secondary",onClick:()=>{n("/lead/".concat(e,"/documents"))},children:"Upload Documents"}),(0,Jr.jsx)(il,{variant:"outline",onClick:()=>u("pending-review"),children:"Submit for Review"})]}),"Supervisor"===(null===r||void 0===r?void 0:r.role)&&"pending-review"===o.status&&(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(il,{onClick:()=>u("approved","Approved by supervisor"),children:"\u2705 Approve"}),(0,Jr.jsx)(il,{variant:"danger",onClick:()=>u("rejected","Rejected by supervisor","verification-failed"),children:"\u274c Reject"})]}),"Admin"===(null===r||void 0===r?void 0:r.role)&&(0,Jr.jsxs)(Jr.Fragment,{children:["new"===o.status&&(0,Jr.jsx)(il,{variant:"secondary",onClick:()=>alert("Assignment functionality would open a modal"),children:"\ud83d\udc64 Assign Agent"}),(0,Jr.jsx)(il,{variant:"outline",onClick:()=>n("/admin/leads"),children:"\ud83d\udccb View All Leads"})]}),("Supervisor"===(null===r||void 0===r?void 0:r.role)||"Admin"===(null===r||void 0===r?void 0:r.role))&&(0,Jr.jsx)(il,{variant:"outline",onClick:()=>alert("Lead history modal would open here"),children:"\ud83d\udcca View History"})]})]}),(0,Jr.jsx)(al,{children:(0,Jr.jsxs)(Nu,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"15px",color:"#007E3A"},children:"Documents"}),(0,Jr.jsxs)(_u,{children:[(0,Jr.jsxs)(Lu,{children:[(0,Jr.jsx)(Ou,{children:"\ud83d\udcc4"}),(0,Jr.jsx)(Du,{children:"ID Proof"}),(0,Jr.jsx)(zu,{children:"Not uploaded"})]}),(0,Jr.jsxs)(Lu,{children:[(0,Jr.jsx)(Ou,{children:"\ud83c\udfe0"}),(0,Jr.jsx)(Du,{children:"Address Proof"}),(0,Jr.jsx)(zu,{children:"Not uploaded"})]}),(0,Jr.jsxs)(Lu,{children:[(0,Jr.jsx)(Ou,{children:"\ud83d\udcbc"}),(0,Jr.jsx)(Du,{children:"Income Proof"}),(0,Jr.jsx)(zu,{children:"Not uploaded"})]})]})]})})]}):(0,Jr.jsx)(wu,{children:(0,Jr.jsx)("div",{children:"Lead not found"})})};var Fu,Uu,Mu,Bu,Hu,Wu,$u;const Vu=$i.div(Fu||(Fu=nl(["\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n"]))),qu=$i.div(Uu||(Uu=nl(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.mediumGray)),Gu=$i.h1(Mu||(Mu=nl(["\n  font-size: 24px;\n  font-weight: 600;\n  color: ",";\n  margin-left: 20px;\n"])),(e=>e.theme.colors.primary)),Ku=$i.div(Bu||(Bu=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n"]))),Qu=$i.div(Hu||(Hu=nl(["\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 15px;\n"]))),Ju=$i.input(Wu||(Wu=nl(["\n  width: 18px;\n  height: 18px;\n"]))),Yu=$i.textarea($u||($u=nl(["\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  font-family: inherit;\n  resize: vertical;\n  min-height: 100px;\n  transition: ",";\n\n  &:focus {\n    border-color: ",";\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.transitions.default),(e=>e.theme.colors.primary)),Zu=()=>{const{id:e}=he(),n=pe(),[r,o]=(0,t.useState)(!1),[a,i]=(0,t.useState)({agentName:"",agentContact:"",addressConfirmed:!1,personMet:"",relationship:"",officeAddress:"",officeState:"",officeDistrict:"",officePincode:"",landmark:"",companyType:"",businessNature:"",establishmentYear:"",employeesCount:"",grossSalary:"",netSalary:"",proofType:"",verificationDate:"",additionalNotes:""}),l=e=>{const{name:t,value:n,type:r}=e.target;if("checkbox"===r){const n=e.target.checked;i((e=>c(c({},e),{},{[t]:n})))}else i((e=>c(c({},e),{},{[t]:n})))},s=()=>{n("/lead/".concat(e))};return(0,Jr.jsxs)(Vu,{children:[(0,Jr.jsxs)(qu,{children:[(0,Jr.jsx)(il,{variant:"outline",onClick:s,children:"\u2190 Back"}),(0,Jr.jsxs)(Gu,{children:["Verification Form - Lead #",e]})]}),(0,Jr.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),e){o(!0);try{const t={agentName:a.agentName,agentContact:a.agentContact,addressConfirmed:a.addressConfirmed?"Yes":"No",personMet:a.personMet,relationship:a.relationship,officeAddress:a.officeAddress,officeState:a.officeState,officeDistrict:a.officeDistrict,officePincode:a.officePincode,landmark:a.landmark,companyType:a.companyType,businessNature:a.businessNature,establishmentYear:parseInt(a.establishmentYear),employeesCount:a.employeesCount,grossSalary:parseFloat(a.grossSalary),netSalary:parseFloat(a.netSalary),proofType:a.proofType,verificationDate:a.verificationDate,additionalNotes:a.additionalNotes};await Qr.saveVerificationData(parseInt(e),t),alert("Verification data saved successfully!"),n("/lead/".concat(e))}catch(r){console.error("Error saving verification data:",r),alert("Failed to save verification data")}finally{o(!1)}}},children:[(0,Jr.jsxs)(Ku,{children:[(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Agent Information"}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"agentName",children:"Agent Name"}),(0,Jr.jsx)(ll,{type:"text",id:"agentName",name:"agentName",value:a.agentName,onChange:l,required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"agentContact",children:"Agent Contact"}),(0,Jr.jsx)(ll,{type:"tel",id:"agentContact",name:"agentContact",value:a.agentContact,onChange:l,required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"verificationDate",children:"Verification Date"}),(0,Jr.jsx)(ll,{type:"date",id:"verificationDate",name:"verificationDate",value:a.verificationDate,onChange:l,required:!0})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Verification Details"}),(0,Jr.jsxs)(Qu,{children:[(0,Jr.jsx)(Ju,{type:"checkbox",id:"addressConfirmed",name:"addressConfirmed",checked:a.addressConfirmed,onChange:l}),(0,Jr.jsx)(cl,{htmlFor:"addressConfirmed",children:"Address Confirmed"})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"personMet",children:"Person Met"}),(0,Jr.jsx)(ll,{type:"text",id:"personMet",name:"personMet",value:a.personMet,onChange:l,required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"relationship",children:"Relationship"}),(0,Jr.jsxs)(sl,{id:"relationship",name:"relationship",value:a.relationship,onChange:l,required:!0,children:[(0,Jr.jsx)("option",{value:"",children:"Select Relationship"}),(0,Jr.jsx)("option",{value:"self",children:"Self"}),(0,Jr.jsx)("option",{value:"spouse",children:"Spouse"}),(0,Jr.jsx)("option",{value:"parent",children:"Parent"}),(0,Jr.jsx)("option",{value:"sibling",children:"Sibling"}),(0,Jr.jsx)("option",{value:"other",children:"Other"})]})]})]})]}),(0,Jr.jsxs)(al,{style:{marginTop:"20px"},children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Office Information"}),(0,Jr.jsxs)(Ku,{children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"officeAddress",children:"Office Address"}),(0,Jr.jsx)(Yu,{id:"officeAddress",name:"officeAddress",value:a.officeAddress,onChange:l,required:!0})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"officeState",children:"State"}),(0,Jr.jsx)(ll,{type:"text",id:"officeState",name:"officeState",value:a.officeState,onChange:l,required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"officeDistrict",children:"District"}),(0,Jr.jsx)(ll,{type:"text",id:"officeDistrict",name:"officeDistrict",value:a.officeDistrict,onChange:l,required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"officePincode",children:"Pincode"}),(0,Jr.jsx)(ll,{type:"text",id:"officePincode",name:"officePincode",value:a.officePincode,onChange:l,required:!0})]})]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"landmark",children:"Landmark"}),(0,Jr.jsx)(ll,{type:"text",id:"landmark",name:"landmark",value:a.landmark,onChange:l})]})]}),(0,Jr.jsxs)(al,{style:{marginTop:"20px"},children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Company Information"}),(0,Jr.jsxs)(Ku,{children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"companyType",children:"Company Type"}),(0,Jr.jsxs)(sl,{id:"companyType",name:"companyType",value:a.companyType,onChange:l,required:!0,children:[(0,Jr.jsx)("option",{value:"",children:"Select Company Type"}),(0,Jr.jsx)("option",{value:"private",children:"Private Limited"}),(0,Jr.jsx)("option",{value:"public",children:"Public Limited"}),(0,Jr.jsx)("option",{value:"partnership",children:"Partnership"}),(0,Jr.jsx)("option",{value:"proprietorship",children:"Proprietorship"}),(0,Jr.jsx)("option",{value:"government",children:"Government"})]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"businessNature",children:"Business Nature"}),(0,Jr.jsx)(ll,{type:"text",id:"businessNature",name:"businessNature",value:a.businessNature,onChange:l,required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"establishmentYear",children:"Establishment Year"}),(0,Jr.jsx)(ll,{type:"number",id:"establishmentYear",name:"establishmentYear",value:a.establishmentYear,onChange:l,min:"1900",max:(new Date).getFullYear(),required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"employeesCount",children:"Number of Employees"}),(0,Jr.jsx)(ll,{type:"number",id:"employeesCount",name:"employeesCount",value:a.employeesCount,onChange:l,min:"1",required:!0})]})]})]}),(0,Jr.jsxs)(al,{style:{marginTop:"20px"},children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Salary Information"}),(0,Jr.jsxs)(Ku,{children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"grossSalary",children:"Gross Salary (\u20b9)"}),(0,Jr.jsx)(ll,{type:"number",id:"grossSalary",name:"grossSalary",value:a.grossSalary,onChange:l,min:"0",required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"netSalary",children:"Net Salary (\u20b9)"}),(0,Jr.jsx)(ll,{type:"number",id:"netSalary",name:"netSalary",value:a.netSalary,onChange:l,min:"0",required:!0})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"proofType",children:"Proof Type"}),(0,Jr.jsxs)(sl,{id:"proofType",name:"proofType",value:a.proofType,onChange:l,required:!0,children:[(0,Jr.jsx)("option",{value:"",children:"Select Proof Type"}),(0,Jr.jsx)("option",{value:"salary-slip",children:"Salary Slip"}),(0,Jr.jsx)("option",{value:"bank-statement",children:"Bank Statement"}),(0,Jr.jsx)("option",{value:"appointment-letter",children:"Appointment Letter"}),(0,Jr.jsx)("option",{value:"id-card",children:"ID Card"})]})]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"additionalNotes",children:"Additional Notes"}),(0,Jr.jsx)(Yu,{id:"additionalNotes",name:"additionalNotes",value:a.additionalNotes,onChange:l,placeholder:"Any additional observations or notes..."})]})]}),(0,Jr.jsxs)("div",{style:{marginTop:"30px",display:"flex",gap:"10px",justifyContent:"flex-end"},children:[(0,Jr.jsx)(il,{type:"button",variant:"outline",onClick:s,disabled:r,children:"Cancel"}),(0,Jr.jsx)(il,{type:"submit",disabled:r,children:r?(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(fl,{}),(0,Jr.jsx)("span",{style:{marginLeft:"8px"},children:"Saving..."})]}):"Save Verification Data"})]})]})]})};var Xu,ed,td,nd,rd,od,ad,id;const ld=$i.div(Xu||(Xu=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n  padding: 20px;\n  background: ",";\n  border-radius: ",";\n  max-width: 600px;\n  margin: 0 auto;\n"])),(e=>e.theme.colors.white),(e=>e.theme.borderRadius.md)),sd=$i.div(ed||(ed=nl(["\n  position: relative;\n  border-radius: ",";\n  overflow: hidden;\n  box-shadow: ",";\n"])),(e=>e.theme.borderRadius.md),(e=>e.theme.shadows.md)),cd=$i.video(td||(td=nl(["\n  width: 100%;\n  max-width: 500px;\n  height: auto;\n  display: block;\n"]))),ud=$i.canvas(nd||(nd=nl(["\n  display: none;\n"]))),dd=$i.img(rd||(rd=nl(["\n  width: 100%;\n  max-width: 500px;\n  height: auto;\n  border-radius: ",";\n  box-shadow: ",";\n"])),(e=>e.theme.borderRadius.md),(e=>e.theme.shadows.md)),fd=$i.div(od||(od=nl(["\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  justify-content: center;\n"]))),pd=$i.div(ad||(ad=nl(["\n  color: ",";\n  text-align: center;\n  padding: 10px;\n  background: #ffebee;\n  border-radius: ",";\n  border: 1px solid ",";\n"])),(e=>e.theme.colors.error),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.error)),hd=$i.div(id||(id=nl(["\n  text-align: center;\n  color: ",";\n  font-size: 14px;\n  max-width: 400px;\n"])),(e=>e.theme.colors.textMedium)),md=e=>{let{onCapture:n,onCancel:r,documentType:o}=e;const a=(0,t.useRef)(null),i=(0,t.useRef)(null),l=(0,t.useRef)(null),[s,c]=(0,t.useState)(!1),[u,d]=(0,t.useState)(null),[f,p]=(0,t.useState)(""),[h,m]=(0,t.useState)(!1);(0,t.useEffect)((()=>(g(),()=>{v()})),[]);const g=async()=>{try{m(!0),p("");const e=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:1280},height:{ideal:720},facingMode:"environment"},audio:!1});a.current&&(a.current.srcObject=e,l.current=e,c(!0))}catch(e){console.error("Error accessing camera:",e),p("Unable to access camera. Please check permissions and try again.")}finally{m(!1)}},v=()=>{l.current&&(l.current.getTracks().forEach((e=>e.stop())),l.current=null),c(!1)},y=()=>{v(),r()};return f?(0,Jr.jsxs)(ld,{children:[(0,Jr.jsx)(pd,{children:f}),(0,Jr.jsxs)(fd,{children:[(0,Jr.jsx)(il,{onClick:g,children:"Try Again"}),(0,Jr.jsx)(il,{variant:"outline",onClick:y,children:"Cancel"})]})]}):(0,Jr.jsxs)(ld,{children:[(0,Jr.jsxs)("h3",{style:{color:"#007E3A",marginBottom:"10px"},children:["Capture ",o]}),(0,Jr.jsx)(hd,{children:"Position the document clearly in the frame and ensure good lighting for best results."}),h&&(0,Jr.jsx)("div",{children:"Loading camera..."}),u?(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(dd,{src:u,alt:"Captured document"}),(0,Jr.jsxs)(fd,{children:[(0,Jr.jsx)(il,{onClick:()=>{u&&i.current&&i.current.toBlob((e=>{if(e){const t="".concat(o.toLowerCase().replace(/\s+/g,"_"),"_").concat(Date.now(),".jpg");n(e,t)}}),"image/jpeg",.8)},children:"Use This Photo"}),(0,Jr.jsx)(il,{variant:"outline",onClick:()=>{d(null),g()},children:"Retake"}),(0,Jr.jsx)(il,{variant:"outline",onClick:y,children:"Cancel"})]})]}):(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(sd,{children:(0,Jr.jsx)(cd,{ref:a,autoPlay:!0,playsInline:!0,muted:!0,style:{display:s?"block":"none"}})}),(0,Jr.jsx)(ud,{ref:i}),s&&(0,Jr.jsxs)(fd,{children:[(0,Jr.jsx)(il,{onClick:()=>{if(!a.current||!i.current)return;const e=a.current,t=i.current,n=t.getContext("2d");n&&(t.width=e.videoWidth,t.height=e.videoHeight,n.drawImage(e,0,0,t.width,t.height),t.toBlob((e=>{if(e){const t=URL.createObjectURL(e);d(t),v()}}),"image/jpeg",.8))},children:"\ud83d\udcf7 Capture Photo"}),(0,Jr.jsx)(il,{variant:"outline",onClick:y,children:"Cancel"})]})]})]})};var gd,vd,yd,xd,bd,wd,jd,Sd,kd,Cd,Ed,Td,Ad,Pd,Rd,Nd;const _d=$i.div(gd||(gd=nl(["\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n"]))),Ld=$i.div(vd||(vd=nl(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.mediumGray)),Od=$i.h1(yd||(yd=nl(["\n  font-size: 24px;\n  font-weight: 600;\n  color: ",";\n  margin-left: 20px;\n"])),(e=>e.theme.colors.primary)),Dd=$i.div(xd||(xd=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n"]))),zd=$i(al)(bd||(bd=nl(["\n  text-align: center;\n  padding: 30px 20px;\n  border: 2px dashed ",";\n  transition: ",";\n\n  &:hover {\n    border-color: ",";\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.transitions.default),(e=>e.theme.colors.primary),(e=>e.theme.colors.offWhite)),Id=$i.div(wd||(wd=nl(["\n  font-size: 48px;\n  margin-bottom: 15px;\n  color: ",";\n"])),(e=>e.theme.colors.primary)),Fd=$i.h3(jd||(jd=nl(["\n  color: ",";\n  margin-bottom: 10px;\n  font-size: 18px;\n"])),(e=>e.theme.colors.primary)),Ud=$i.p(Sd||(Sd=nl(["\n  color: ",";\n  margin-bottom: 20px;\n  font-size: 14px;\n"])),(e=>e.theme.colors.textMedium)),Md=$i.div(kd||(kd=nl(["\n  border: 2px dashed ",";\n  border-radius: ",";\n  padding: 40px 20px;\n  text-align: center;\n  background-color: ",";\n  transition: ",";\n  cursor: pointer;\n  margin-bottom: 15px;\n\n  &:hover {\n    border-color: ",";\n    background-color: ",";\n  }\n"])),(e=>e.isDragOver?e.theme.colors.primary:e.theme.colors.mediumGray),(e=>e.theme.borderRadius.md),(e=>e.isDragOver?e.theme.colors.offWhite:"transparent"),(e=>e.theme.transitions.default),(e=>e.theme.colors.primary),(e=>e.theme.colors.offWhite)),Bd=$i.div(Cd||(Cd=nl(["\n  font-size: 32px;\n  margin-bottom: 10px;\n  color: ",";\n"])),(e=>e.theme.colors.primary)),Hd=$i.div(Ed||(Ed=nl(["\n  color: ",";\n  font-size: 14px;\n"])),(e=>e.theme.colors.textMedium)),Wd=$i.input(Td||(Td=nl(["\n  display: none;\n"]))),$d=$i.img(Ad||(Ad=nl(["\n  max-width: 100%;\n  max-height: 200px;\n  border-radius: ",";\n  margin-bottom: 10px;\n"])),(e=>e.theme.borderRadius.sm)),Vd=$i.div(Pd||(Pd=nl(["\n  font-size: 14px;\n  color: ",";\n  margin-bottom: 10px;\n  word-break: break-all;\n"])),(e=>e.theme.colors.textDark)),qd=$i.div(Rd||(Rd=nl(["\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  flex-wrap: wrap;\n"]))),Gd=$i.div(Nd||(Nd=nl(["\n  display: ",";\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.8);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n"])),(e=>e.show?"flex":"none")),Kd=[{id:"id-proof",title:"ID Proof",description:"Aadhaar Card, PAN Card, Passport, or Driving License",icon:"\ud83c\udd94",required:!0},{id:"address-proof",title:"Address Proof",description:"Utility Bill, Bank Statement, or Rental Agreement",icon:"\ud83c\udfe0",required:!0},{id:"income-proof",title:"Income Proof",description:"Salary Slip, Bank Statement, or ITR",icon:"\ud83d\udcb0",required:!0},{id:"office-photo",title:"Office Photo",description:"Photo of the office premises",icon:"\ud83c\udfe2",required:!1}],Qd=()=>{var e;const{id:n}=he(),r=pe(),[o,a]=(0,t.useState)({}),[i,l]=(0,t.useState)({}),[s,u]=(0,t.useState)(!1),[d,f]=(0,t.useState)(""),[p,h]=(0,t.useState)(!1),[m,g]=(0,t.useState)({}),v=(0,t.useRef)({}),y=(e,t)=>{a((n=>c(c({},n),{},{[e]:t})))},x=e=>{var t;null===(t=v.current[e])||void 0===t||t.click()},b=e=>e.type.startsWith("image/");return(0,Jr.jsxs)(_d,{children:[(0,Jr.jsxs)(Ld,{children:[(0,Jr.jsx)(il,{variant:"outline",onClick:()=>{r("/lead/".concat(n))},children:"\u2190 Back"}),(0,Jr.jsxs)(Od,{children:["Document Upload - Lead #",n]})]}),(0,Jr.jsx)(Dd,{children:Kd.map((e=>{const t=o[e.id],n=i[e.id]||!1;return(0,Jr.jsxs)(zd,{children:[(0,Jr.jsx)(Id,{children:e.icon}),(0,Jr.jsxs)(Fd,{children:[e.title,e.required&&(0,Jr.jsx)("span",{style:{color:"red"},children:" *"})]}),(0,Jr.jsx)(Ud,{children:e.description}),t?(0,Jr.jsxs)("div",{children:[b(t)&&(0,Jr.jsx)($d,{src:(r=t,(b(r)?URL.createObjectURL(r):null)||""),alt:"Preview"}),(0,Jr.jsx)(Vd,{children:t.name}),m[e.id]?(0,Jr.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",gap:"8px"},children:[(0,Jr.jsx)(fl,{}),(0,Jr.jsx)("span",{children:"Uploading..."})]}):(0,Jr.jsxs)(qd,{children:[(0,Jr.jsx)(il,{size:"sm",variant:"outline",onClick:()=>(e=>{a((t=>{const n=c({},t);return delete n[e],n}))})(e.id),disabled:p,children:"Remove"}),(0,Jr.jsx)(il,{size:"sm",onClick:()=>x(e.id),disabled:p,children:"Replace"})]})]}):(0,Jr.jsxs)("div",{children:[(0,Jr.jsxs)(Md,{isDragOver:n,onClick:()=>x(e.id),onDragOver:t=>((e,t)=>{t.preventDefault(),l((t=>c(c({},t),{},{[e]:!0})))})(e.id,t),onDragLeave:()=>(e=>{l((t=>c(c({},t),{},{[e]:!1})))})(e.id),onDrop:t=>((e,t)=>{t.preventDefault(),l((t=>c(c({},t),{},{[e]:!1})));const n=t.dataTransfer.files[0];n&&y(e,n)})(e.id,t),children:[(0,Jr.jsx)(Bd,{children:"\ud83d\udcc1"}),(0,Jr.jsx)(Hd,{children:"Click to upload or drag and drop"})]}),(0,Jr.jsxs)(qd,{children:[(0,Jr.jsx)(il,{size:"sm",onClick:()=>x(e.id),children:"Choose File"}),(0,Jr.jsx)(il,{size:"sm",variant:"secondary",onClick:()=>(e=>{f(e),u(!0)})(e.id),children:"\ud83d\udcf7 Camera"})]})]}),(0,Jr.jsx)(Wd,{ref:t=>{v.current[e.id]=t},type:"file",accept:"image/*,.pdf",onChange:t=>((e,t)=>{var n;const r=null===(n=t.target.files)||void 0===n?void 0:n[0];r&&y(e,r)})(e.id,t)})]},e.id);var r}))}),(0,Jr.jsx)("div",{style:{marginTop:"30px",textAlign:"center"},children:(0,Jr.jsx)(il,{onClick:async()=>{if(!n)return;const e=parseInt(n),t=Object.entries(o);if(0!==t.length){h(!0);try{for(const[n,r]of t)g((e=>c(c({},e),{},{[n]:!0}))),await Qr.uploadDocument(e,n,r),g((e=>c(c({},e),{},{[n]:!1})));alert("All documents uploaded successfully!"),r("/lead/".concat(n))}catch(a){console.error("Error uploading documents:",a),alert("Failed to upload some documents. Please try again.")}finally{h(!1),g({})}}else alert("Please select at least one document to upload.")},size:"lg",disabled:p,children:p?(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(fl,{}),(0,Jr.jsx)("span",{style:{marginLeft:"8px"},children:"Uploading..."})]}):"Upload All Documents"})}),(0,Jr.jsx)(Gd,{show:s,children:(0,Jr.jsx)(md,{documentType:(null===(e=Kd.find((e=>e.id===d)))||void 0===e?void 0:e.title)||"Document",onCapture:(e,t)=>{const n=new File([e],t,{type:"image/jpeg"});y(d,n),u(!1)},onCancel:()=>u(!1)})})]})};var Jd,Yd,Zd,Xd,ef,tf,nf,rf;const of=$i.div(Jd||(Jd=nl(["\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n"]))),af=$i.div(Yd||(Yd=nl(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.mediumGray)),lf=$i.h1(Zd||(Zd=nl(["\n  font-size: 24px;\n  font-weight: 600;\n  color: ",";\n  margin-left: 20px;\n"])),(e=>e.theme.colors.primary)),sf=$i.div(Xd||(Xd=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n"]))),cf=$i.div(ef||(ef=nl(["\n  margin-top: 20px;\n"]))),uf=$i(al)(tf||(tf=nl(["\n  margin-bottom: 15px;\n  position: relative;\n"]))),df=$i(il)(nf||(nf=nl(["\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  padding: 5px 10px;\n  font-size: 12px;\n"]))),ff=$i(il)(rf||(rf=nl(["\n  margin-top: 10px;\n"]))),pf=()=>{const e=pe(),[n,r]=(0,t.useState)(!1),[o,a]=(0,t.useState)({}),[i,l]=(0,t.useState)({customerName:"",mobileNumber:"",loanType:""}),[s,u]=(0,t.useState)([{type:"Residential",address:"",pincode:"",state:"",district:"",landmark:""}]),d=e=>{const{name:t,value:n}=e.target;l((e=>c(c({},e),{},{[t]:n}))),o[t]&&a((e=>c(c({},e),{},{[t]:""})))},f=(e,t,n)=>{u((r=>r.map(((r,o)=>o===e?c(c({},r),{},{[t]:n}):r))))},p=()=>{e("/admin/dashboard")};return(0,Jr.jsxs)(of,{children:[(0,Jr.jsxs)(af,{children:[(0,Jr.jsx)(il,{variant:"outline",onClick:p,children:"\u2190 Back"}),(0,Jr.jsx)(lf,{children:"Create New Lead"})]}),(0,Jr.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),(()=>{const e={};return i.customerName.trim()||(e.customerName="Customer name is required"),i.mobileNumber.trim()?/^\d{10}$/.test(i.mobileNumber)||(e.mobileNumber="Mobile number must be 10 digits"):e.mobileNumber="Mobile number is required",i.loanType||(e.loanType="Loan type is required"),s.forEach(((t,n)=>{t.address.trim()||(e["address_".concat(n)]="Address is required"),t.pincode.trim()?/^\d{6}$/.test(t.pincode)||(e["pincode_".concat(n)]="Pincode must be 6 digits"):e["pincode_".concat(n)]="Pincode is required",t.state.trim()||(e["state_".concat(n)]="State is required"),t.district.trim()||(e["district_".concat(n)]="District is required")})),a(e),0===Object.keys(e).length})()){r(!0);try{const t={customerName:i.customerName,mobileNumber:i.mobileNumber,loanType:i.loanType,addresses:s};await Qr.createLead(t),alert("Lead created successfully!"),e("/admin/dashboard")}catch(n){console.error("Error creating lead:",n),alert("Failed to create lead. Please try again.")}finally{r(!1)}}},children:[(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Customer Information"}),(0,Jr.jsxs)(sf,{children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"customerName",children:"Customer Name *"}),(0,Jr.jsx)(ll,{type:"text",id:"customerName",name:"customerName",value:i.customerName,onChange:d,placeholder:"Enter customer full name",required:!0}),o.customerName&&(0,Jr.jsx)(dl,{children:o.customerName})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"mobileNumber",children:"Mobile Number *"}),(0,Jr.jsx)(ll,{type:"tel",id:"mobileNumber",name:"mobileNumber",value:i.mobileNumber,onChange:d,placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0}),o.mobileNumber&&(0,Jr.jsx)(dl,{children:o.mobileNumber})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"loanType",children:"Loan Type *"}),(0,Jr.jsxs)(sl,{id:"loanType",name:"loanType",value:i.loanType,onChange:d,required:!0,children:[(0,Jr.jsx)("option",{value:"",children:"Select Loan Type"}),(0,Jr.jsx)("option",{value:"Personal Loan",children:"Personal Loan"}),(0,Jr.jsx)("option",{value:"Home Loan",children:"Home Loan"}),(0,Jr.jsx)("option",{value:"Car Loan",children:"Car Loan"}),(0,Jr.jsx)("option",{value:"Business Loan",children:"Business Loan"}),(0,Jr.jsx)("option",{value:"Education Loan",children:"Education Loan"}),(0,Jr.jsx)("option",{value:"Gold Loan",children:"Gold Loan"})]}),o.loanType&&(0,Jr.jsx)(dl,{children:o.loanType})]})]})]}),(0,Jr.jsxs)(cf,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Address Information"}),s.map(((e,t)=>(0,Jr.jsxs)(uf,{children:[s.length>1&&(0,Jr.jsx)(df,{type:"button",variant:"danger",size:"sm",onClick:()=>(e=>{s.length>1&&u((t=>t.filter(((t,n)=>n!==e))))})(t),children:"Remove"}),(0,Jr.jsxs)("h4",{style:{marginBottom:"15px",color:"#555"},children:["Address ",t+1]}),(0,Jr.jsxs)(sf,{children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{children:"Address Type"}),(0,Jr.jsxs)(sl,{value:e.type,onChange:e=>f(t,"type",e.target.value),children:[(0,Jr.jsx)("option",{value:"Residential",children:"Residential"}),(0,Jr.jsx)("option",{value:"Office",children:"Office"}),(0,Jr.jsx)("option",{value:"Business",children:"Business"})]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{children:"State *"}),(0,Jr.jsx)(ll,{type:"text",value:e.state,onChange:e=>f(t,"state",e.target.value),placeholder:"Enter state",required:!0}),o["state_".concat(t)]&&(0,Jr.jsx)(dl,{children:o["state_".concat(t)]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{children:"District *"}),(0,Jr.jsx)(ll,{type:"text",value:e.district,onChange:e=>f(t,"district",e.target.value),placeholder:"Enter district",required:!0}),o["district_".concat(t)]&&(0,Jr.jsx)(dl,{children:o["district_".concat(t)]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{children:"Pincode *"}),(0,Jr.jsx)(ll,{type:"text",value:e.pincode,onChange:e=>f(t,"pincode",e.target.value),placeholder:"Enter 6-digit pincode",maxLength:6,required:!0}),o["pincode_".concat(t)]&&(0,Jr.jsx)(dl,{children:o["pincode_".concat(t)]})]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{children:"Full Address *"}),(0,Jr.jsx)(ll,{type:"text",value:e.address,onChange:e=>f(t,"address",e.target.value),placeholder:"Enter complete address",required:!0}),o["address_".concat(t)]&&(0,Jr.jsx)(dl,{children:o["address_".concat(t)]})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{children:"Landmark"}),(0,Jr.jsx)(ll,{type:"text",value:e.landmark,onChange:e=>f(t,"landmark",e.target.value),placeholder:"Enter nearby landmark (optional)"})]})]},t))),(0,Jr.jsx)(ff,{type:"button",variant:"outline",onClick:()=>{u((e=>[...e,{type:"Residential",address:"",pincode:"",state:"",district:"",landmark:""}]))},children:"+ Add Another Address"})]}),(0,Jr.jsxs)("div",{style:{marginTop:"30px",display:"flex",gap:"10px",justifyContent:"flex-end"},children:[(0,Jr.jsx)(il,{type:"button",variant:"outline",onClick:p,children:"Cancel"}),(0,Jr.jsx)(il,{type:"submit",disabled:n,children:n?"Creating...":"Create Lead"})]})]})]})};var hf,mf,gf,vf,yf,xf,bf,wf,jf,Sf,kf,Cf,Ef,Tf,Af;const Pf=$i.div(hf||(hf=nl(["\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n"]))),Rf=$i.div(mf||(mf=nl(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.mediumGray)),Nf=$i.h1(gf||(gf=nl(["\n  font-size: 24px;\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.primary)),_f=$i.div(vf||(vf=nl(["\n  overflow-x: auto;\n"]))),Lf=$i.table(yf||(yf=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),Of=$i.th(xf||(xf=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),Df=$i.td(bf||(bf=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),zf=$i.tr(wf||(wf=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),If=$i.span(jf||(jf=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ",";\n  color: ",";\n"])),(e=>e.active?"#e8f5e9":"#ffebee"),(e=>e.active?"#2e7d32":"#c62828")),Ff=$i.span(Sf||(Sf=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  \n  ","\n"])),(e=>{switch(e.role){case"Admin":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";case"Supervisor":return"\n          background-color: #fff3e0;\n          color: #e65100;\n        ";case"Agent":return"\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),Uf=$i.div(kf||(kf=nl(["\n  display: ",";\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n"])),(e=>e.show?"flex":"none")),Mf=$i.div(Cf||(Cf=nl(["\n  background: ",";\n  border-radius: ",";\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n"])),(e=>e.theme.colors.white),(e=>e.theme.borderRadius.md)),Bf=$i.div(Ef||(Ef=nl(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.mediumGray)),Hf=$i.h2(Tf||(Tf=nl(["\n  color: ",";\n  margin: 0;\n"])),(e=>e.theme.colors.primary)),Wf=$i.button(Af||(Af=nl(["\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ",";\n  \n  &:hover {\n    color: ",";\n  }\n"])),(e=>e.theme.colors.textMedium),(e=>e.theme.colors.textDark)),$f=()=>{const e=pe(),[n,r]=(0,t.useState)([]),[o,a]=(0,t.useState)(!1),[i,l]=(0,t.useState)(null),[s,u]=(0,t.useState)(!1),[d,f]=(0,t.useState)({username:"",firstName:"",lastName:"",email:"",role:"Agent",password:"",confirmPassword:""}),[p,h]=(0,t.useState)({});(0,t.useEffect)((()=>{m()}),[]);const m=async()=>{try{u(!0);r([{userId:1,username:"agent1",firstName:"John",lastName:"Agent",email:"<EMAIL>",role:"Agent",isActive:!0,createdDate:"2024-01-01T00:00:00Z",lastLoginDate:"2024-01-16T08:30:00Z"},{userId:2,username:"supervisor1",firstName:"Jane",lastName:"Supervisor",email:"<EMAIL>",role:"Supervisor",isActive:!0,createdDate:"2024-01-01T00:00:00Z",lastLoginDate:"2024-01-16T09:15:00Z"},{userId:3,username:"admin1",firstName:"Admin",lastName:"User",email:"<EMAIL>",role:"Admin",isActive:!0,createdDate:"2024-01-01T00:00:00Z",lastLoginDate:"2024-01-16T10:00:00Z"},{userId:4,username:"agent2",firstName:"Bob",lastName:"Agent",email:"<EMAIL>",role:"Agent",isActive:!1,createdDate:"2024-01-05T00:00:00Z",lastLoginDate:"2024-01-10T14:20:00Z"}])}catch(e){console.error("Error loading users:",e)}finally{u(!1)}},g=e=>{const{name:t,value:n}=e.target;f((e=>c(c({},e),{},{[t]:n}))),p[t]&&h((e=>c(c({},e),{},{[t]:""})))},v=()=>{a(!1),l(null),f({username:"",firstName:"",lastName:"",email:"",role:"Agent",password:"",confirmPassword:""}),h({})},y=e=>new Date(e).toLocaleDateString();return(0,Jr.jsxs)(Pf,{children:[(0,Jr.jsxs)(Rf,{children:[(0,Jr.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,Jr.jsx)(il,{variant:"outline",onClick:()=>{e("/admin/dashboard")},children:"\u2190 Back"}),(0,Jr.jsx)(Nf,{style:{marginLeft:"20px"},children:"User Management"})]}),(0,Jr.jsx)(il,{onClick:()=>{l(null),f({username:"",firstName:"",lastName:"",email:"",role:"Agent",password:"",confirmPassword:""}),h({}),a(!0)},children:"+ Create User"})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)(_f,{children:(0,Jr.jsxs)(Lf,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(Of,{children:"Name"}),(0,Jr.jsx)(Of,{children:"Username"}),(0,Jr.jsx)(Of,{children:"Email"}),(0,Jr.jsx)(Of,{children:"Role"}),(0,Jr.jsx)(Of,{children:"Status"}),(0,Jr.jsx)(Of,{children:"Created Date"}),(0,Jr.jsx)(Of,{children:"Last Login"}),(0,Jr.jsx)(Of,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:n.map((e=>(0,Jr.jsxs)(zf,{children:[(0,Jr.jsxs)(Df,{children:[e.firstName," ",e.lastName]}),(0,Jr.jsx)(Df,{children:e.username}),(0,Jr.jsx)(Df,{children:e.email}),(0,Jr.jsx)(Df,{children:(0,Jr.jsx)(Ff,{role:e.role,children:e.role})}),(0,Jr.jsx)(Df,{children:(0,Jr.jsx)(If,{active:e.isActive,children:e.isActive?"Active":"Inactive"})}),(0,Jr.jsx)(Df,{children:y(e.createdDate)}),(0,Jr.jsx)(Df,{children:e.lastLoginDate?y(e.lastLoginDate):"Never"}),(0,Jr.jsx)(Df,{children:(0,Jr.jsxs)("div",{style:{display:"flex",gap:"8px"},children:[(0,Jr.jsx)(il,{size:"sm",onClick:()=>(e=>{l(e),f({username:e.username,firstName:e.firstName,lastName:e.lastName,email:e.email,role:e.role,password:"",confirmPassword:""}),h({}),a(!0)})(e),children:"Edit"}),(0,Jr.jsx)(il,{size:"sm",variant:e.isActive?"danger":"secondary",onClick:()=>{return t=e.userId,void r((e=>e.map((e=>e.userId===t?c(c({},e),{},{isActive:!e.isActive}):e))));var t},children:e.isActive?"Deactivate":"Activate"})]})})]},e.userId)))})]})}),0===n.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:"No users found."})]}),(0,Jr.jsx)(Uf,{show:o,children:(0,Jr.jsxs)(Mf,{children:[(0,Jr.jsxs)(Bf,{children:[(0,Jr.jsx)(Hf,{children:i?"Edit User":"Create New User"}),(0,Jr.jsx)(Wf,{onClick:v,children:"\xd7"})]}),(0,Jr.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return d.username.trim()||(e.username="Username is required"),d.firstName.trim()||(e.firstName="First name is required"),d.lastName.trim()||(e.lastName="Last name is required"),d.email.trim()?/\S+@\S+\.\S+/.test(d.email)||(e.email="Email is invalid"):e.email="Email is required",i||(d.password?d.password.length<6&&(e.password="Password must be at least 6 characters"):e.password="Password is required",d.password!==d.confirmPassword&&(e.confirmPassword="Passwords do not match")),h(e),0===Object.keys(e).length})()){u(!0);try{if(i){const e=c(c({},i),{},{username:d.username,firstName:d.firstName,lastName:d.lastName,email:d.email,role:d.role});r((t=>t.map((t=>t.userId===i.userId?e:t)))),alert("User updated successfully!")}else{const e={userId:Math.max(...n.map((e=>e.userId)))+1,username:d.username,firstName:d.firstName,lastName:d.lastName,email:d.email,role:d.role,isActive:!0,createdDate:(new Date).toISOString()};r((t=>[...t,e])),alert("User created successfully!")}v()}catch(t){console.error("Error saving user:",t),alert("Failed to save user. Please try again.")}finally{u(!1)}}},children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"username",children:"Username *"}),(0,Jr.jsx)(ll,{type:"text",id:"username",name:"username",value:d.username,onChange:g,required:!0}),p.username&&(0,Jr.jsx)(dl,{children:p.username})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"firstName",children:"First Name *"}),(0,Jr.jsx)(ll,{type:"text",id:"firstName",name:"firstName",value:d.firstName,onChange:g,required:!0}),p.firstName&&(0,Jr.jsx)(dl,{children:p.firstName})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"lastName",children:"Last Name *"}),(0,Jr.jsx)(ll,{type:"text",id:"lastName",name:"lastName",value:d.lastName,onChange:g,required:!0}),p.lastName&&(0,Jr.jsx)(dl,{children:p.lastName})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"email",children:"Email *"}),(0,Jr.jsx)(ll,{type:"email",id:"email",name:"email",value:d.email,onChange:g,required:!0}),p.email&&(0,Jr.jsx)(dl,{children:p.email})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"role",children:"Role *"}),(0,Jr.jsxs)(sl,{id:"role",name:"role",value:d.role,onChange:g,required:!0,children:[(0,Jr.jsx)("option",{value:"Agent",children:"Agent"}),(0,Jr.jsx)("option",{value:"Supervisor",children:"Supervisor"}),(0,Jr.jsx)("option",{value:"Admin",children:"Admin"})]})]}),!i&&(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"password",children:"Password *"}),(0,Jr.jsx)(ll,{type:"password",id:"password",name:"password",value:d.password,onChange:g,required:!0}),p.password&&(0,Jr.jsx)(dl,{children:p.password})]}),(0,Jr.jsxs)(ul,{children:[(0,Jr.jsx)(cl,{htmlFor:"confirmPassword",children:"Confirm Password *"}),(0,Jr.jsx)(ll,{type:"password",id:"confirmPassword",name:"confirmPassword",value:d.confirmPassword,onChange:g,required:!0}),p.confirmPassword&&(0,Jr.jsx)(dl,{children:p.confirmPassword})]})]}),(0,Jr.jsxs)("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end",marginTop:"20px"},children:[(0,Jr.jsx)(il,{type:"button",variant:"outline",onClick:v,children:"Cancel"}),(0,Jr.jsx)(il,{type:"submit",disabled:s,children:s?"Saving...":i?"Update User":"Create User"})]})]})]})})]})};var Vf,qf,Gf,Kf,Qf,Jf,Yf,Zf,Xf,ep,tp;const np=$i.div(Vf||(Vf=nl(["\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),rp=$i.select(qf||(qf=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),op=$i.input(Gf||(Gf=nl(["\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),ap=$i.div(Kf||(Kf=nl(["\n  overflow-x: auto;\n"]))),ip=$i.table(Qf||(Qf=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),lp=$i.th(Jf||(Jf=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),sp=$i.td(Yf||(Yf=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),cp=$i.tr(Zf||(Zf=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),up=$i.span(Xf||(Xf=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"assigned":return"\n          background-color: #fff3e0;\n          color: #e65100;\n        ";case"in-progress":return"\n          background-color: #fff8e1;\n          color: #ff8f00;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),dp=$i.span(ep||(ep=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.priority){case"high":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";case"medium":return"\n          background-color: #fff8e1;\n          color: #ff8f00;\n        ";case"low":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),fp=$i.div(tp||(tp=nl(["\n  display: flex;\n  gap: 8px;\n"]))),pp=()=>{const[e,n]=(0,t.useState)([]),[r,o]=(0,t.useState)(!0),[a,i]=(0,t.useState)("all"),[l,s]=(0,t.useState)(""),c=pe();(0,t.useEffect)((()=>{u()}),[a]);const u=async()=>{try{o(!0);const e="all"===a?void 0:a,t=await Qr.getLeads(1,100,e);n(t.data||[])}catch(e){console.error("Error loading tasks:",e),n([{leadId:1,customerName:"John Doe",mobileNumber:"9876543210",loanType:"Personal Loan",status:"assigned",createdDate:"2024-01-15T10:30:00Z",assignedDate:"2024-01-15T11:00:00Z",createdByName:"Admin User",assignedToName:"Current Agent",documentCount:0,croppedImageCount:0},{leadId:2,customerName:"Jane Smith",mobileNumber:"9876543211",loanType:"Home Loan",status:"in-progress",createdDate:"2024-01-14T09:15:00Z",assignedDate:"2024-01-14T10:00:00Z",createdByName:"Admin User",assignedToName:"Current Agent",documentCount:2,croppedImageCount:1}])}finally{o(!1)}},d=e.filter((e=>e.customerName.toLowerCase().includes(l.toLowerCase())||e.mobileNumber.includes(l)||e.loanType.toLowerCase().includes(l.toLowerCase()))),f=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>c("/agent/dashboard")},{icon:"\ud83d\udccb",label:"My Tasks",active:!0},{icon:"\u2705",label:"Completed",onClick:()=>c("/agent/completed")},{icon:"\ud83d\udcca",label:"Reports",onClick:()=>c("/agent/reports")}],p=async(e,t)=>{try{"start"===t?await Qr.updateLeadStatus(e,"in-progress","Started verification process"):"submit"===t&&await Qr.updateLeadStatus(e,"pending-review","Submitted for review"),u()}catch(n){console.error("Error updating task:",n),alert("Failed to update task")}},h=e=>{const t=Math.floor((Date.now()-new Date(e).getTime())/864e5);return t>3?"high":t>1?"medium":"low"};return r?(0,Jr.jsx)(Ts,{title:"My Tasks",navigationItems:f,children:(0,Jr.jsx)(fl,{})}):(0,Jr.jsx)(Ts,{title:"My Tasks",navigationItems:f,children:(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h2",{style:{marginBottom:"20px",color:"#007E3A"},children:"Assigned Tasks"}),(0,Jr.jsxs)(np,{children:[(0,Jr.jsxs)(rp,{value:a,onChange:e=>i(e.target.value),children:[(0,Jr.jsx)("option",{value:"all",children:"All Tasks"}),(0,Jr.jsx)("option",{value:"assigned",children:"Assigned"}),(0,Jr.jsx)("option",{value:"in-progress",children:"In Progress"})]}),(0,Jr.jsx)(op,{type:"text",placeholder:"Search by customer name, mobile, or loan type...",value:l,onChange:e=>s(e.target.value)})]}),(0,Jr.jsx)(ap,{children:(0,Jr.jsxs)(ip,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(lp,{children:"Customer"}),(0,Jr.jsx)(lp,{children:"Mobile"}),(0,Jr.jsx)(lp,{children:"Loan Type"}),(0,Jr.jsx)(lp,{children:"Status"}),(0,Jr.jsx)(lp,{children:"Priority"}),(0,Jr.jsx)(lp,{children:"Assigned Date"}),(0,Jr.jsx)(lp,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:d.map((e=>{return(0,Jr.jsxs)(cp,{children:[(0,Jr.jsx)(sp,{children:e.customerName}),(0,Jr.jsx)(sp,{children:e.mobileNumber}),(0,Jr.jsx)(sp,{children:e.loanType}),(0,Jr.jsx)(sp,{children:(0,Jr.jsx)(up,{status:e.status,children:e.status.replace("-"," ").toUpperCase()})}),(0,Jr.jsx)(sp,{children:(0,Jr.jsx)(dp,{priority:h(e.createdDate),children:h(e.createdDate).toUpperCase()})}),(0,Jr.jsx)(sp,{children:e.assignedDate?(t=e.assignedDate,new Date(t).toLocaleDateString()):"-"}),(0,Jr.jsx)(sp,{children:(0,Jr.jsxs)(fp,{children:[(0,Jr.jsx)(il,{size:"sm",onClick:()=>c("/lead/".concat(e.leadId)),children:"View"}),"assigned"===e.status&&(0,Jr.jsx)(il,{size:"sm",variant:"secondary",onClick:()=>p(e.leadId,"start"),children:"Start"}),"in-progress"===e.status&&(0,Jr.jsx)(il,{size:"sm",variant:"outline",onClick:()=>p(e.leadId,"submit"),children:"Submit"})]})})]},e.leadId);var t}))})]})}),0===d.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:l?"No tasks found matching your search.":"No tasks assigned yet."})]})})};var hp,mp,gp,vp,yp,xp,bp,wp,jp,Sp,kp,Cp,Ep;const Tp=$i.div(hp||(hp=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),Ap=$i(al)(mp||(mp=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n"]))),Pp=$i.div(gp||(gp=nl(["\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ",";\n"])),(e=>e.theme.colors.textDark)),Rp=$i.div(vp||(vp=nl(["\n  font-size: 14px;\n  color: ",";\n  font-weight: 500;\n"])),(e=>e.theme.colors.textLight)),Np=$i.div(yp||(yp=nl(["\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),_p=$i.select(xp||(xp=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),Lp=$i.input(bp||(bp=nl(["\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),Op=$i.div(wp||(wp=nl(["\n  overflow-x: auto;\n"]))),Dp=$i.table(jp||(jp=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),zp=$i.th(Sp||(Sp=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),Ip=$i.td(kp||(kp=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),Fp=$i.tr(Cp||(Cp=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),Up=$i.span(Ep||(Ep=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"approved":case"completed":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";case"rejected":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";case"pending-review":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),Mp=()=>{const[e,n]=(0,t.useState)([]),[r,o]=(0,t.useState)(!0),[a,i]=(0,t.useState)("all"),[l,s]=(0,t.useState)(""),[c,u]=(0,t.useState)({totalCompleted:0,approved:0,rejected:0,pendingReview:0}),d=pe();(0,t.useEffect)((()=>{f()}),[a]);const f=async()=>{try{o(!0);const t="all"===a?["approved","rejected","pending-review"]:[a],r=[];for(const n of t)try{const e=await Qr.getLeads(1,100,n);r.push(...e.data||[])}catch(e){console.error("Error loading ".concat(n," tasks:"),e)}n(r);const i=r.filter((e=>"approved"===e.status)).length,l=r.filter((e=>"rejected"===e.status)).length,s=r.filter((e=>"pending-review"===e.status)).length;u({totalCompleted:r.length,approved:i,rejected:l,pendingReview:s})}catch(e){console.error("Error loading completed tasks:",e);n([{leadId:3,customerName:"Alice Johnson",mobileNumber:"9876543212",loanType:"Car Loan",status:"approved",createdDate:"2024-01-10T08:00:00Z",assignedDate:"2024-01-10T09:00:00Z",submittedDate:"2024-01-12T16:30:00Z",createdByName:"Admin User",assignedToName:"Current Agent",documentCount:3,croppedImageCount:2},{leadId:4,customerName:"Bob Wilson",mobileNumber:"9876543213",loanType:"Personal Loan",status:"rejected",createdDate:"2024-01-08T10:15:00Z",assignedDate:"2024-01-08T11:00:00Z",submittedDate:"2024-01-09T14:20:00Z",createdByName:"Admin User",assignedToName:"Current Agent",documentCount:2,croppedImageCount:1}]),u({totalCompleted:2,approved:1,rejected:1,pendingReview:0})}finally{o(!1)}},p=e.filter((e=>e.customerName.toLowerCase().includes(l.toLowerCase())||e.mobileNumber.includes(l)||e.loanType.toLowerCase().includes(l.toLowerCase()))),h=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>d("/agent/dashboard")},{icon:"\ud83d\udccb",label:"My Tasks",onClick:()=>d("/agent/tasks")},{icon:"\u2705",label:"Completed",active:!0},{icon:"\ud83d\udcca",label:"Reports",onClick:()=>d("/agent/reports")}],m=(e,t)=>{const n=new Date(e),r=new Date(t),o=Math.abs(r.getTime()-n.getTime()),a=Math.ceil(o/864e5);return"".concat(a," day").concat(1!==a?"s":"")};return r?(0,Jr.jsx)(Ts,{title:"Completed Tasks",navigationItems:h,children:(0,Jr.jsx)(fl,{})}):(0,Jr.jsxs)(Ts,{title:"Completed Tasks",navigationItems:h,children:[(0,Jr.jsxs)(Tp,{children:[(0,Jr.jsxs)(Ap,{children:[(0,Jr.jsx)(Pp,{children:c.totalCompleted}),(0,Jr.jsx)(Rp,{children:"Total Completed"})]}),(0,Jr.jsxs)(Ap,{children:[(0,Jr.jsx)(Pp,{style:{color:"#2e7d32"},children:c.approved}),(0,Jr.jsx)(Rp,{children:"Approved"})]}),(0,Jr.jsxs)(Ap,{children:[(0,Jr.jsx)(Pp,{style:{color:"#c62828"},children:c.rejected}),(0,Jr.jsx)(Rp,{children:"Rejected"})]}),(0,Jr.jsxs)(Ap,{children:[(0,Jr.jsx)(Pp,{style:{color:"#4a148c"},children:c.pendingReview}),(0,Jr.jsx)(Rp,{children:"Pending Review"})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h2",{style:{marginBottom:"20px",color:"#007E3A"},children:"Completed Verifications"}),(0,Jr.jsxs)(Np,{children:[(0,Jr.jsxs)(_p,{value:a,onChange:e=>i(e.target.value),children:[(0,Jr.jsx)("option",{value:"all",children:"All Completed"}),(0,Jr.jsx)("option",{value:"approved",children:"Approved"}),(0,Jr.jsx)("option",{value:"rejected",children:"Rejected"}),(0,Jr.jsx)("option",{value:"pending-review",children:"Pending Review"})]}),(0,Jr.jsx)(Lp,{type:"text",placeholder:"Search by customer name, mobile, or loan type...",value:l,onChange:e=>s(e.target.value)})]}),(0,Jr.jsx)(Op,{children:(0,Jr.jsxs)(Dp,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(zp,{children:"Customer"}),(0,Jr.jsx)(zp,{children:"Mobile"}),(0,Jr.jsx)(zp,{children:"Loan Type"}),(0,Jr.jsx)(zp,{children:"Status"}),(0,Jr.jsx)(zp,{children:"Submitted Date"}),(0,Jr.jsx)(zp,{children:"Duration"}),(0,Jr.jsx)(zp,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:p.map((e=>{return(0,Jr.jsxs)(Fp,{children:[(0,Jr.jsx)(Ip,{children:e.customerName}),(0,Jr.jsx)(Ip,{children:e.mobileNumber}),(0,Jr.jsx)(Ip,{children:e.loanType}),(0,Jr.jsx)(Ip,{children:(0,Jr.jsx)(Up,{status:e.status,children:e.status.replace("-"," ").toUpperCase()})}),(0,Jr.jsx)(Ip,{children:e.submittedDate?(t=e.submittedDate,new Date(t).toLocaleDateString()):"-"}),(0,Jr.jsx)(Ip,{children:e.assignedDate&&e.submittedDate?m(e.assignedDate,e.submittedDate):"-"}),(0,Jr.jsx)(Ip,{children:(0,Jr.jsx)(il,{size:"sm",onClick:()=>d("/lead/".concat(e.leadId)),children:"View Details"})})]},e.leadId);var t}))})]})}),0===p.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:l?"No completed tasks found matching your search.":"No completed tasks yet."})]})]})};var Bp,Hp,Wp,$p,Vp,qp,Gp,Kp,Qp,Jp,Yp,Zp,Xp,eh,th,nh,rh,oh;const ah=$i.div(Bp||(Bp=nl(["\n  display: grid;\n  gap: 20px;\n"]))),ih=$i.div(Hp||(Hp=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),lh=$i(al)(Wp||(Wp=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n"]))),sh=$i.div($p||($p=nl(["\n  font-size: 32px;\n  font-weight: 700;\n  margin-bottom: 8px;\n"]))),ch=$i.div(Vp||(Vp=nl(["\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n"]))),uh=$i.div(qp||(qp=nl(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n"]))),dh=$i(al)(Gp||(Gp=nl(["\n  padding: 20px;\n"]))),fh=$i.h3(Kp||(Kp=nl(["\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n"]))),ph=$i.div(Qp||(Qp=nl(["\n  width: 100%;\n  height: 20px;\n  background-color: #f0f0f0;\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 10px;\n  \n  &::after {\n    content: '';\n    display: block;\n    width: ","%;\n    height: 100%;\n    background-color: ",";\n    transition: width 0.3s ease;\n  }\n"])),(e=>e.percentage),(e=>e.color)),hh=$i.div(Jp||(Jp=nl(["\n  margin-bottom: 15px;\n"]))),mh=$i.div(Yp||(Yp=nl(["\n  display: flex;\n  justify-content: between;\n  margin-bottom: 5px;\n  font-size: 14px;\n  font-weight: 500;\n"]))),gh=$i.div(Zp||(Zp=nl(["\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),vh=$i.select(Xp||(Xp=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),yh=$i.div(eh||(eh=nl(["\n  max-height: 300px;\n  overflow-y: auto;\n"]))),xh=$i.div(th||(th=nl(["\n  display: flex;\n  justify-content: between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid ",";\n  \n  &:last-child {\n    border-bottom: none;\n  }\n"])),(e=>e.theme.colors.lightGray)),bh=$i.div(nh||(nh=nl(["\n  flex: 1;\n"]))),wh=$i.div(rh||(rh=nl(["\n  font-weight: 500;\n  margin-bottom: 4px;\n"]))),jh=$i.div(oh||(oh=nl(["\n  font-size: 12px;\n  color: ",";\n"])),(e=>e.theme.colors.textLight)),Sh=()=>{const[e,n]=(0,t.useState)(!0),[r,o]=(0,t.useState)("week"),[a,i]=(0,t.useState)({totalTasks:0,completedTasks:0,approvedTasks:0,rejectedTasks:0,averageTime:0,efficiency:0}),[l,s]=(0,t.useState)([]),c=pe();(0,t.useEffect)((()=>{u()}),[r]);const u=async()=>{try{n(!0);const[e]=await Promise.all([Qr.getAgentDashboardStats()]);i({totalTasks:e.totalAssigned||0,completedTasks:e.completedLeads||0,approvedTasks:e.completedLeads||0,rejectedTasks:e.rejectedLeads||0,averageTime:2.5,efficiency:85}),s([{id:1,title:"Completed verification for John Doe",date:"2024-01-16T14:30:00Z",type:"completed"},{id:2,title:"Started verification for Jane Smith",date:"2024-01-16T10:15:00Z",type:"started"},{id:3,title:"Uploaded documents for Alice Johnson",date:"2024-01-15T16:45:00Z",type:"upload"}])}catch(e){console.error("Error loading reports data:",e),i({totalTasks:15,completedTasks:12,approvedTasks:10,rejectedTasks:2,averageTime:2.5,efficiency:85})}finally{n(!1)}},d=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>c("/agent/dashboard")},{icon:"\ud83d\udccb",label:"My Tasks",onClick:()=>c("/agent/tasks")},{icon:"\u2705",label:"Completed",onClick:()=>c("/agent/completed")},{icon:"\ud83d\udcca",label:"Reports",active:!0}],f=()=>a.totalTasks>0?Math.round(a.completedTasks/a.totalTasks*100):0,p=()=>a.completedTasks>0?Math.round(a.approvedTasks/a.completedTasks*100):0;return e?(0,Jr.jsx)(Ts,{title:"Performance Reports",navigationItems:d,children:(0,Jr.jsx)(fl,{})}):(0,Jr.jsx)(Ts,{title:"Performance Reports",navigationItems:d,children:(0,Jr.jsxs)(ah,{children:[(0,Jr.jsxs)(gh,{children:[(0,Jr.jsxs)(vh,{value:r,onChange:e=>o(e.target.value),children:[(0,Jr.jsx)("option",{value:"week",children:"This Week"}),(0,Jr.jsx)("option",{value:"month",children:"This Month"}),(0,Jr.jsx)("option",{value:"quarter",children:"This Quarter"}),(0,Jr.jsx)("option",{value:"year",children:"This Year"})]}),(0,Jr.jsx)(il,{variant:"outline",onClick:()=>window.print(),children:"\ud83d\udcc4 Export Report"})]}),(0,Jr.jsxs)(ih,{children:[(0,Jr.jsxs)(lh,{children:[(0,Jr.jsx)(sh,{children:a.totalTasks}),(0,Jr.jsx)(ch,{children:"Total Tasks Assigned"})]}),(0,Jr.jsxs)(lh,{children:[(0,Jr.jsx)(sh,{children:a.completedTasks}),(0,Jr.jsx)(ch,{children:"Tasks Completed"})]}),(0,Jr.jsxs)(lh,{children:[(0,Jr.jsx)(sh,{children:a.averageTime}),(0,Jr.jsx)(ch,{children:"Avg. Days per Task"})]}),(0,Jr.jsxs)(lh,{children:[(0,Jr.jsxs)(sh,{children:[a.efficiency,"%"]}),(0,Jr.jsx)(ch,{children:"Efficiency Score"})]})]}),(0,Jr.jsxs)(uh,{children:[(0,Jr.jsxs)(dh,{children:[(0,Jr.jsx)(fh,{children:"Task Performance"}),(0,Jr.jsxs)(hh,{children:[(0,Jr.jsxs)(mh,{children:[(0,Jr.jsx)("span",{children:"Completion Rate"}),(0,Jr.jsxs)("span",{children:[f(),"%"]})]}),(0,Jr.jsx)(ph,{percentage:f(),color:"#2e7d32"})]}),(0,Jr.jsxs)(hh,{children:[(0,Jr.jsxs)(mh,{children:[(0,Jr.jsx)("span",{children:"Approval Rate"}),(0,Jr.jsxs)("span",{children:[p(),"%"]})]}),(0,Jr.jsx)(ph,{percentage:p(),color:"#007E3A"})]}),(0,Jr.jsxs)(hh,{children:[(0,Jr.jsxs)(mh,{children:[(0,Jr.jsx)("span",{children:"Efficiency"}),(0,Jr.jsxs)("span",{children:[a.efficiency,"%"]})]}),(0,Jr.jsx)(ph,{percentage:a.efficiency,color:"#FFD100"})]})]}),(0,Jr.jsxs)(dh,{children:[(0,Jr.jsx)(fh,{children:"Recent Activity"}),(0,Jr.jsx)(yh,{children:l.map((e=>{return(0,Jr.jsx)(xh,{children:(0,Jr.jsxs)(bh,{children:[(0,Jr.jsx)(wh,{children:e.title}),(0,Jr.jsx)(jh,{children:(t=e.date,new Date(t).toLocaleDateString())})]})},e.id);var t}))})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Performance Summary"}),(0,Jr.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px"},children:[(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{children:"Tasks Overview"}),(0,Jr.jsxs)("p",{children:["Total Assigned: ",a.totalTasks]}),(0,Jr.jsxs)("p",{children:["Completed: ",a.completedTasks]}),(0,Jr.jsxs)("p",{children:["Pending: ",a.totalTasks-a.completedTasks]})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{children:"Quality Metrics"}),(0,Jr.jsxs)("p",{children:["Approved: ",a.approvedTasks]}),(0,Jr.jsxs)("p",{children:["Rejected: ",a.rejectedTasks]}),(0,Jr.jsxs)("p",{children:["Success Rate: ",p(),"%"]})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{children:"Efficiency"}),(0,Jr.jsxs)("p",{children:["Avg. Time: ",a.averageTime," days"]}),(0,Jr.jsxs)("p",{children:["Efficiency: ",a.efficiency,"%"]}),(0,Jr.jsx)("p",{children:"Productivity: High"})]})]})]})]})})};var kh,Ch,Eh,Th,Ah,Ph,Rh,Nh,_h,Lh,Oh,Dh,zh,Ih,Fh,Uh;const Mh=$i.div(kh||(kh=nl(["\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),Bh=$i.select(Ch||(Ch=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),Hh=$i.input(Eh||(Eh=nl(["\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),Wh=$i.div(Th||(Th=nl(["\n  overflow-x: auto;\n"]))),$h=$i.table(Ah||(Ah=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),Vh=$i.th(Ph||(Ph=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),qh=$i.td(Rh||(Rh=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),Gh=$i.tr(Nh||(Nh=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),Kh=$i.span(_h||(_h=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"pending-review":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";case"approved":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";case"rejected":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),Qh=$i.span(Lh||(Lh=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.priority){case"high":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";case"medium":return"\n          background-color: #fff8e1;\n          color: #ff8f00;\n        ";case"low":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),Jh=$i.div(Oh||(Oh=nl(["\n  display: flex;\n  gap: 8px;\n"]))),Yh=$i.div(Dh||(Dh=nl(["\n  display: ",";\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n"])),(e=>e.isOpen?"flex":"none")),Zh=$i.div(zh||(zh=nl(["\n  background: white;\n  padding: 30px;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 80vh;\n  overflow-y: auto;\n"]))),Xh=$i.h3(Ih||(Ih=nl(["\n  margin-bottom: 20px;\n  color: #007E3A;\n"]))),em=$i.textarea(Fh||(Fh=nl(["\n  width: 100%;\n  min-height: 100px;\n  padding: 10px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),tm=$i.div(Uh||(Uh=nl(["\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n"]))),nm=()=>{const[e,n]=(0,t.useState)([]),[r,o]=(0,t.useState)(!0),[a,i]=(0,t.useState)("pending-review"),[l,s]=(0,t.useState)(""),[u,d]=(0,t.useState)(null),[f,p]=(0,t.useState)(null),[h,m]=(0,t.useState)(""),[g,v]=(0,t.useState)(""),y=pe();(0,t.useEffect)((()=>{x()}),[a]);const x=async()=>{try{o(!0);const e=await Qr.getLeads(1,100,"all"===a?void 0:a);n(e.data||[])}catch(e){console.error("Error loading review tasks:",e),n([{leadId:5,customerName:"Charlie Brown",mobileNumber:"9876543214",loanType:"Home Loan",status:"pending-review",createdDate:"2024-01-12T09:00:00Z",assignedDate:"2024-01-12T10:00:00Z",submittedDate:"2024-01-15T17:30:00Z",createdByName:"Admin User",assignedToName:"Agent Smith",documentCount:4,croppedImageCount:3},{leadId:6,customerName:"Diana Prince",mobileNumber:"9876543215",loanType:"Car Loan",status:"pending-review",createdDate:"2024-01-13T11:15:00Z",assignedDate:"2024-01-13T12:00:00Z",submittedDate:"2024-01-16T09:45:00Z",createdByName:"Admin User",assignedToName:"Agent Johnson",documentCount:3,croppedImageCount:2}])}finally{o(!1)}},b=e.filter((e=>e.customerName.toLowerCase().includes(l.toLowerCase())||e.mobileNumber.includes(l)||e.loanType.toLowerCase().includes(l.toLowerCase())||e.assignedToName&&e.assignedToName.toLowerCase().includes(l.toLowerCase()))),w=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>y("/supervisor/dashboard")},{icon:"\ud83d\udc41\ufe0f",label:"Review Queue",active:!0},{icon:"\ud83d\udcca",label:"Reports",onClick:()=>y("/supervisor/reports")},{icon:"\ud83d\udc65",label:"Team",onClick:()=>y("/supervisor/team")}],j=(e,t)=>{d(e),p(t),m(""),v("")},S=e=>{const t=Math.floor((Date.now()-new Date(e).getTime())/864e5);return t>2?"high":t>1?"medium":"low"},k=e=>{const t=Math.floor((Date.now()-new Date(e).getTime())/864e5);return"".concat(t," day").concat(1!==t?"s":"")};return r?(0,Jr.jsx)(Ts,{title:"Review Queue",navigationItems:w,children:(0,Jr.jsx)(fl,{})}):(0,Jr.jsxs)(Ts,{title:"Review Queue",navigationItems:w,children:[(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h2",{style:{marginBottom:"20px",color:"#007E3A"},children:"Pending Reviews"}),(0,Jr.jsxs)(Mh,{children:[(0,Jr.jsxs)(Bh,{value:a,onChange:e=>i(e.target.value),children:[(0,Jr.jsx)("option",{value:"pending-review",children:"Pending Review"}),(0,Jr.jsx)("option",{value:"approved",children:"Approved"}),(0,Jr.jsx)("option",{value:"rejected",children:"Rejected"}),(0,Jr.jsx)("option",{value:"all",children:"All Reviews"})]}),(0,Jr.jsx)(Hh,{type:"text",placeholder:"Search by customer, mobile, loan type, or agent...",value:l,onChange:e=>s(e.target.value)})]}),(0,Jr.jsx)(Wh,{children:(0,Jr.jsxs)($h,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(Vh,{children:"Customer"}),(0,Jr.jsx)(Vh,{children:"Mobile"}),(0,Jr.jsx)(Vh,{children:"Loan Type"}),(0,Jr.jsx)(Vh,{children:"Agent"}),(0,Jr.jsx)(Vh,{children:"Status"}),(0,Jr.jsx)(Vh,{children:"Priority"}),(0,Jr.jsx)(Vh,{children:"Submitted"}),(0,Jr.jsx)(Vh,{children:"Wait Time"}),(0,Jr.jsx)(Vh,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:b.map((e=>{return(0,Jr.jsxs)(Gh,{children:[(0,Jr.jsx)(qh,{children:e.customerName}),(0,Jr.jsx)(qh,{children:e.mobileNumber}),(0,Jr.jsx)(qh,{children:e.loanType}),(0,Jr.jsx)(qh,{children:e.assignedToName||"-"}),(0,Jr.jsx)(qh,{children:(0,Jr.jsx)(Kh,{status:e.status,children:e.status.replace("-"," ").toUpperCase()})}),(0,Jr.jsx)(qh,{children:e.submittedDate&&(0,Jr.jsx)(Qh,{priority:S(e.submittedDate),children:S(e.submittedDate).toUpperCase()})}),(0,Jr.jsx)(qh,{children:e.submittedDate?(t=e.submittedDate,new Date(t).toLocaleDateString()):"-"}),(0,Jr.jsx)(qh,{children:e.submittedDate?k(e.submittedDate):"-"}),(0,Jr.jsx)(qh,{children:(0,Jr.jsxs)(Jh,{children:[(0,Jr.jsx)(il,{size:"sm",onClick:()=>y("/lead/".concat(e.leadId)),children:"View"}),"pending-review"===e.status&&(0,Jr.jsxs)(Jr.Fragment,{children:[(0,Jr.jsx)(il,{size:"sm",variant:"secondary",onClick:()=>j(e,"approve"),children:"Approve"}),(0,Jr.jsx)(il,{size:"sm",variant:"danger",onClick:()=>j(e,"reject"),children:"Reject"})]})]})})]},e.leadId);var t}))})]})}),0===b.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:l?"No tasks found matching your search.":"No tasks pending review."})]}),(0,Jr.jsx)(Yh,{isOpen:!!u&&!!f,children:(0,Jr.jsxs)(Zh,{children:[(0,Jr.jsxs)(Xh,{children:["approve"===f?"Approve":"Reject"," Lead - ",null===u||void 0===u?void 0:u.customerName]}),(0,Jr.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,Jr.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Comments:"}),(0,Jr.jsx)(em,{value:h,onChange:e=>m(e.target.value),placeholder:"Add your review comments..."})]}),"reject"===f&&(0,Jr.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,Jr.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Rejection Reason:"}),(0,Jr.jsxs)(Bh,{value:g,onChange:e=>v(e.target.value),style:{width:"100%"},children:[(0,Jr.jsx)("option",{value:"",children:"Select reason..."}),(0,Jr.jsx)("option",{value:"incomplete-documents",children:"Incomplete Documents"}),(0,Jr.jsx)("option",{value:"poor-quality",children:"Poor Quality Images"}),(0,Jr.jsx)("option",{value:"verification-failed",children:"Verification Failed"}),(0,Jr.jsx)("option",{value:"incorrect-information",children:"Incorrect Information"}),(0,Jr.jsx)("option",{value:"other",children:"Other"})]})]}),(0,Jr.jsxs)(tm,{children:[(0,Jr.jsx)(il,{variant:"outline",onClick:()=>{d(null),p(null)},children:"Cancel"}),(0,Jr.jsx)(il,{variant:"approve"===f?"secondary":"danger",onClick:async()=>{if(u&&f)try{const e="approve"===f?"approved":"rejected";await Qr.updateLeadStatus(u.leadId,e,h,"reject"===f?g:void 0),n((t=>t.map((t=>t.leadId===u.leadId?c(c({},t),{},{status:e}):t)))),d(null),p(null),"all"!==a&&x()}catch(e){console.error("Error submitting review:",e),alert("Failed to submit review")}},disabled:!h||"reject"===f&&!g,children:"approve"===f?"Approve":"Reject"})]})]})})]})};var rm,om,am,im,lm,sm,cm,um,dm,fm,pm,hm,mm,gm,vm;const ym=$i.div(rm||(rm=nl(["\n  display: grid;\n  gap: 20px;\n"]))),xm=$i.div(om||(om=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),bm=$i(al)(am||(am=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n"]))),wm=$i.div(im||(im=nl(["\n  font-size: 28px;\n  font-weight: 700;\n  margin-bottom: 8px;\n"]))),jm=$i.div(lm||(lm=nl(["\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n"]))),Sm=$i.div(sm||(sm=nl(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n"]))),km=$i(al)(cm||(cm=nl(["\n  padding: 20px;\n"]))),Cm=$i.h3(um||(um=nl(["\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n"]))),Em=$i.div(dm||(dm=nl(["\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),Tm=$i.select(fm||(fm=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background: white;\n\n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),Am=$i.table(pm||(pm=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),Pm=$i.th(hm||(hm=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),Rm=$i.td(mm||(mm=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),Nm=$i.tr(gm||(gm=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),_m=$i.div(vm||(vm=nl(["\n  width: 100%;\n  height: 15px;\n  background-color: #f0f0f0;\n  border-radius: 8px;\n  overflow: hidden;\n\n  &::after {\n    content: '';\n    display: block;\n    width: ","%;\n    height: 100%;\n    background-color: ",";\n    transition: width 0.3s ease;\n  }\n"])),(e=>e.percentage),(e=>e.color)),Lm=()=>{const[e,n]=(0,t.useState)(!0),[r,o]=(0,t.useState)("month"),[a,i]=(0,t.useState)({totalAgents:0,totalTasks:0,completedTasks:0,pendingReviews:0,averageTime:0,teamEfficiency:0}),[l,s]=(0,t.useState)([]),c=pe();(0,t.useEffect)((()=>{u()}),[r]);const u=async()=>{try{n(!0);const[e]=await Promise.all([Qr.getSupervisorDashboardStats()]);i({totalAgents:5,totalTasks:e.totalLeads||0,completedTasks:e.completedLeads||0,pendingReviews:e.pendingReviews||0,averageTime:2.8,teamEfficiency:82}),s([{agentName:"John Agent",tasksAssigned:15,tasksCompleted:12,approvalRate:90,avgTime:2.5,efficiency:85},{agentName:"Jane Agent",tasksAssigned:18,tasksCompleted:16,approvalRate:95,avgTime:2.2,efficiency:92},{agentName:"Bob Agent",tasksAssigned:12,tasksCompleted:10,approvalRate:80,avgTime:3.1,efficiency:75}])}catch(e){console.error("Error loading reports data:",e),i({totalAgents:5,totalTasks:45,completedTasks:38,pendingReviews:7,averageTime:2.8,teamEfficiency:82})}finally{n(!1)}},d=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>c("/supervisor/dashboard")},{icon:"\ud83d\udc41\ufe0f",label:"Review Queue",onClick:()=>c("/supervisor/review")},{icon:"\ud83d\udcca",label:"Reports",active:!0},{icon:"\ud83d\udc65",label:"Team",onClick:()=>c("/supervisor/team")}],f=()=>a.totalTasks>0?Math.round(a.completedTasks/a.totalTasks*100):0;return e?(0,Jr.jsx)(Ts,{title:"Team Reports",navigationItems:d,children:(0,Jr.jsx)(fl,{})}):(0,Jr.jsx)(Ts,{title:"Team Reports",navigationItems:d,children:(0,Jr.jsxs)(ym,{children:[(0,Jr.jsxs)(Em,{children:[(0,Jr.jsxs)(Tm,{value:r,onChange:e=>o(e.target.value),children:[(0,Jr.jsx)("option",{value:"week",children:"This Week"}),(0,Jr.jsx)("option",{value:"month",children:"This Month"}),(0,Jr.jsx)("option",{value:"quarter",children:"This Quarter"}),(0,Jr.jsx)("option",{value:"year",children:"This Year"})]}),(0,Jr.jsx)(il,{variant:"outline",onClick:()=>window.print(),children:"\ud83d\udcc4 Export Report"})]}),(0,Jr.jsxs)(xm,{children:[(0,Jr.jsxs)(bm,{children:[(0,Jr.jsx)(wm,{children:a.totalAgents}),(0,Jr.jsx)(jm,{children:"Team Members"})]}),(0,Jr.jsxs)(bm,{children:[(0,Jr.jsx)(wm,{children:a.totalTasks}),(0,Jr.jsx)(jm,{children:"Total Tasks"})]}),(0,Jr.jsxs)(bm,{children:[(0,Jr.jsx)(wm,{children:a.completedTasks}),(0,Jr.jsx)(jm,{children:"Completed"})]}),(0,Jr.jsxs)(bm,{children:[(0,Jr.jsx)(wm,{children:a.pendingReviews}),(0,Jr.jsx)(jm,{children:"Pending Review"})]}),(0,Jr.jsxs)(bm,{children:[(0,Jr.jsx)(wm,{children:a.averageTime}),(0,Jr.jsx)(jm,{children:"Avg. Days"})]}),(0,Jr.jsxs)(bm,{children:[(0,Jr.jsxs)(wm,{children:[a.teamEfficiency,"%"]}),(0,Jr.jsx)(jm,{children:"Team Efficiency"})]})]}),(0,Jr.jsxs)(Sm,{children:[(0,Jr.jsxs)(km,{children:[(0,Jr.jsx)(Cm,{children:"Team Performance Overview"}),(0,Jr.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,Jr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"8px"},children:[(0,Jr.jsx)("span",{children:"Completion Rate"}),(0,Jr.jsxs)("span",{children:[f(),"%"]})]}),(0,Jr.jsx)(_m,{percentage:f(),color:"#2e7d32"})]}),(0,Jr.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,Jr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"8px"},children:[(0,Jr.jsx)("span",{children:"Team Efficiency"}),(0,Jr.jsxs)("span",{children:[a.teamEfficiency,"%"]})]}),(0,Jr.jsx)(_m,{percentage:a.teamEfficiency,color:"#007E3A"})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"8px"},children:[(0,Jr.jsx)("span",{children:"Quality Score"}),(0,Jr.jsx)("span",{children:"88%"})]}),(0,Jr.jsx)(_m,{percentage:88,color:"#FFD100"})]})]}),(0,Jr.jsxs)(km,{children:[(0,Jr.jsx)(Cm,{children:"Task Distribution"}),(0,Jr.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px",textAlign:"center"},children:[(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#2e7d32"},children:a.completedTasks}),(0,Jr.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Completed"})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#4a148c"},children:a.pendingReviews}),(0,Jr.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Pending"})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#ff8f00"},children:a.totalTasks-a.completedTasks-a.pendingReviews}),(0,Jr.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"In Progress"})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#007E3A"},children:a.totalTasks}),(0,Jr.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Total"})]})]})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Agent Performance"}),(0,Jr.jsx)("div",{style:{overflowX:"auto"},children:(0,Jr.jsxs)(Am,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(Pm,{children:"Agent Name"}),(0,Jr.jsx)(Pm,{children:"Tasks Assigned"}),(0,Jr.jsx)(Pm,{children:"Completed"}),(0,Jr.jsx)(Pm,{children:"Completion Rate"}),(0,Jr.jsx)(Pm,{children:"Approval Rate"}),(0,Jr.jsx)(Pm,{children:"Avg. Time"}),(0,Jr.jsx)(Pm,{children:"Efficiency"})]})}),(0,Jr.jsx)("tbody",{children:l.map(((e,t)=>(0,Jr.jsxs)(Nm,{children:[(0,Jr.jsx)(Rm,{style:{fontWeight:"500"},children:e.agentName}),(0,Jr.jsx)(Rm,{children:e.tasksAssigned}),(0,Jr.jsx)(Rm,{children:e.tasksCompleted}),(0,Jr.jsxs)(Rm,{children:[Math.round(e.tasksCompleted/e.tasksAssigned*100),"%"]}),(0,Jr.jsxs)(Rm,{children:[e.approvalRate,"%"]}),(0,Jr.jsxs)(Rm,{children:[e.avgTime," days"]}),(0,Jr.jsx)(Rm,{children:(0,Jr.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[(0,Jr.jsxs)("span",{children:[e.efficiency,"%"]}),(0,Jr.jsx)("div",{style:{flex:1,minWidth:"60px"},children:(0,Jr.jsx)(_m,{percentage:e.efficiency,color:e.efficiency>=85?"#2e7d32":e.efficiency>=70?"#ff8f00":"#c62828"})})]})})]},t)))})]})})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Key Insights"}),(0,Jr.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"20px"},children:[(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{style:{color:"#2e7d32",marginBottom:"10px"},children:"\ud83c\udfaf Top Performer"}),(0,Jr.jsx)("p",{children:"Jane Agent leads with 95% approval rate and 92% efficiency"})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{style:{color:"#ff8f00",marginBottom:"10px"},children:"\u26a0\ufe0f Needs Attention"}),(0,Jr.jsx)("p",{children:"Bob Agent requires support to improve efficiency from 75% to team average"})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{style:{color:"#007E3A",marginBottom:"10px"},children:"\ud83d\udcc8 Team Trend"}),(0,Jr.jsx)("p",{children:"Overall team performance improved by 8% compared to last month"})]})]})]})]})})};var Om,Dm,zm,Im,Fm,Um,Mm,Bm,Hm,Wm,$m,Vm,qm,Gm,Km;const Qm=$i.div(Om||(Om=nl(["\n  display: grid;\n  gap: 20px;\n"]))),Jm=$i.div(Dm||(Dm=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n"]))),Ym=$i(al)(zm||(zm=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n"]))),Zm=$i.div(Im||(Im=nl(["\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 8px;\n"]))),Xm=$i.div(Fm||(Fm=nl(["\n  font-size: 12px;\n  opacity: 0.9;\n  font-weight: 500;\n"]))),eg=$i.div(Um||(Um=nl(["\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n"]))),tg=$i(al)(Mm||(Mm=nl(["\n  padding: 20px;\n"]))),ng=$i.h3(Bm||(Bm=nl(["\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n"]))),rg=$i.div(Hm||(Hm=nl(["\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),og=$i.select(Wm||(Wm=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),ag=$i.table($m||($m=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),ig=$i.th(Vm||(Vm=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium)),lg=$i.td(qm||(qm=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),sg=$i.tr(Gm||(Gm=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),cg=$i.span(Km||(Km=nl(["\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  \n  ","\n"])),(e=>{switch(e.trend){case"up":return"color: #2e7d32;";case"down":return"color: #c62828;";case"stable":return"color: #666;"}})),ug=()=>{const[e,n]=(0,t.useState)(!0),[r,o]=(0,t.useState)("month"),[a,i]=(0,t.useState)({totalUsers:0,totalLeads:0,completedLeads:0,pendingLeads:0,rejectedLeads:0,averageProcessingTime:0,systemEfficiency:0,activeAgents:0}),[l,s]=(0,t.useState)([]),[c,u]=(0,t.useState)([]),d=pe();(0,t.useEffect)((()=>{f()}),[r]);const f=async()=>{try{n(!0);const[e,t]=await Promise.all([Qr.getDashboardStats(),Qr.getUsers()]);i({totalUsers:t.length||0,totalLeads:e.totalLeads||0,completedLeads:e.completedLeads||0,pendingLeads:e.pendingLeads||0,rejectedLeads:e.rejectedLeads||0,averageProcessingTime:2.8,systemEfficiency:87,activeAgents:t.filter((e=>"Agent"===e.role&&e.isActive)).length||0}),s([{department:"Personal Loans",totalLeads:150,completed:128,pending:15,rejected:7,avgTime:2.5,efficiency:89},{department:"Home Loans",totalLeads:89,completed:76,pending:8,rejected:5,avgTime:3.2,efficiency:85},{department:"Car Loans",totalLeads:67,completed:58,pending:6,rejected:3,avgTime:2.1,efficiency:92}]),u([{month:"Jan",leads:120,completed:105,efficiency:87},{month:"Feb",leads:135,completed:118,efficiency:89},{month:"Mar",leads:156,completed:142,efficiency:91},{month:"Apr",leads:142,completed:128,efficiency:88}])}catch(e){console.error("Error loading reports data:",e),i({totalUsers:25,totalLeads:306,completedLeads:262,pendingLeads:29,rejectedLeads:15,averageProcessingTime:2.8,systemEfficiency:87,activeAgents:15})}finally{n(!1)}},p=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>d("/admin/dashboard")},{icon:"\ud83d\udc65",label:"Users",onClick:()=>d("/admin/users")},{icon:"\ud83d\udccb",label:"Leads",onClick:()=>d("/admin/leads")},{icon:"\ud83d\udcca",label:"Reports",active:!0},{icon:"\u2699\ufe0f",label:"Settings",onClick:()=>d("/admin/settings")}];return e?(0,Jr.jsx)(Ts,{title:"System Reports",navigationItems:p,children:(0,Jr.jsx)(fl,{})}):(0,Jr.jsx)(Ts,{title:"System Reports",navigationItems:p,children:(0,Jr.jsxs)(Qm,{children:[(0,Jr.jsxs)(rg,{children:[(0,Jr.jsxs)(og,{value:r,onChange:e=>o(e.target.value),children:[(0,Jr.jsx)("option",{value:"week",children:"This Week"}),(0,Jr.jsx)("option",{value:"month",children:"This Month"}),(0,Jr.jsx)("option",{value:"quarter",children:"This Quarter"}),(0,Jr.jsx)("option",{value:"year",children:"This Year"})]}),(0,Jr.jsx)(il,{variant:"outline",onClick:()=>window.print(),children:"\ud83d\udcc4 Export Report"}),(0,Jr.jsx)(il,{variant:"secondary",onClick:()=>d("/admin/analytics"),children:"\ud83d\udcc8 Advanced Analytics"})]}),(0,Jr.jsxs)(Jm,{children:[(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsx)(Zm,{children:a.totalUsers}),(0,Jr.jsx)(Xm,{children:"Total Users"})]}),(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsx)(Zm,{children:a.activeAgents}),(0,Jr.jsx)(Xm,{children:"Active Agents"})]}),(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsx)(Zm,{children:a.totalLeads}),(0,Jr.jsx)(Xm,{children:"Total Leads"})]}),(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsx)(Zm,{children:a.completedLeads}),(0,Jr.jsx)(Xm,{children:"Completed"})]}),(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsx)(Zm,{children:a.pendingLeads}),(0,Jr.jsx)(Xm,{children:"Pending"})]}),(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsx)(Zm,{children:a.rejectedLeads}),(0,Jr.jsx)(Xm,{children:"Rejected"})]}),(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsx)(Zm,{children:a.averageProcessingTime}),(0,Jr.jsx)(Xm,{children:"Avg. Days"})]}),(0,Jr.jsxs)(Ym,{children:[(0,Jr.jsxs)(Zm,{children:[a.systemEfficiency,"%"]}),(0,Jr.jsx)(Xm,{children:"System Efficiency"})]})]}),(0,Jr.jsxs)(eg,{children:[(0,Jr.jsxs)(tg,{children:[(0,Jr.jsx)(ng,{children:"Monthly Performance Trends"}),(0,Jr.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(4, 1fr)",gap:"15px",textAlign:"center"},children:c.map(((e,t)=>(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("div",{style:{fontSize:"14px",fontWeight:"bold",marginBottom:"8px"},children:e.month}),(0,Jr.jsx)("div",{style:{fontSize:"18px",color:"#007E3A",fontWeight:"bold"},children:e.completed}),(0,Jr.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["of ",e.leads]}),(0,Jr.jsxs)("div",{style:{fontSize:"12px",color:"#2e7d32",marginTop:"4px"},children:[e.efficiency,"% eff."]})]},t)))})]}),(0,Jr.jsxs)(tg,{children:[(0,Jr.jsx)(ng,{children:"System Health"}),(0,Jr.jsxs)("div",{style:{display:"grid",gap:"15px"},children:[(0,Jr.jsxs)("div",{style:{textAlign:"center"},children:[(0,Jr.jsxs)("div",{style:{fontSize:"32px",color:"#2e7d32",fontWeight:"bold"},children:[a.totalLeads>0?Math.round(a.completedLeads/a.totalLeads*100):0,"%"]}),(0,Jr.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Completion Rate"})]}),(0,Jr.jsxs)("div",{style:{textAlign:"center"},children:[(0,Jr.jsxs)("div",{style:{fontSize:"32px",color:"#007E3A",fontWeight:"bold"},children:[(()=>{const e=a.completedLeads+a.rejectedLeads;return e>0?Math.round(a.completedLeads/e*100):0})(),"%"]}),(0,Jr.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"Success Rate"})]}),(0,Jr.jsxs)("div",{style:{textAlign:"center"},children:[(0,Jr.jsxs)("div",{style:{fontSize:"32px",color:"#FFD100",fontWeight:"bold"},children:[a.systemEfficiency,"%"]}),(0,Jr.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"System Efficiency"})]})]})]})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Department Performance"}),(0,Jr.jsx)("div",{style:{overflowX:"auto"},children:(0,Jr.jsxs)(ag,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsx)(ig,{children:"Department"}),(0,Jr.jsx)(ig,{children:"Total Leads"}),(0,Jr.jsx)(ig,{children:"Completed"}),(0,Jr.jsx)(ig,{children:"Pending"}),(0,Jr.jsx)(ig,{children:"Rejected"}),(0,Jr.jsx)(ig,{children:"Avg. Time"}),(0,Jr.jsx)(ig,{children:"Efficiency"}),(0,Jr.jsx)(ig,{children:"Trend"})]})}),(0,Jr.jsx)("tbody",{children:l.map(((e,t)=>(0,Jr.jsxs)(sg,{children:[(0,Jr.jsx)(lg,{style:{fontWeight:"500"},children:e.department}),(0,Jr.jsx)(lg,{children:e.totalLeads}),(0,Jr.jsx)(lg,{style:{color:"#2e7d32"},children:e.completed}),(0,Jr.jsx)(lg,{style:{color:"#ff8f00"},children:e.pending}),(0,Jr.jsx)(lg,{style:{color:"#c62828"},children:e.rejected}),(0,Jr.jsxs)(lg,{children:[e.avgTime," days"]}),(0,Jr.jsxs)(lg,{children:[e.efficiency,"%"]}),(0,Jr.jsx)(lg,{children:(0,Jr.jsxs)(cg,{trend:e.efficiency>88?"up":e.efficiency>85?"stable":"down",children:[e.efficiency>88?"\u2197\ufe0f":e.efficiency>85?"\u27a1\ufe0f":"\u2198\ufe0f",e.efficiency>88?"Improving":e.efficiency>85?"Stable":"Declining"]})})]},t)))})]})})]}),(0,Jr.jsxs)(al,{children:[(0,Jr.jsx)("h3",{style:{marginBottom:"20px",color:"#007E3A"},children:"Executive Summary"}),(0,Jr.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"20px"},children:[(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{style:{color:"#2e7d32",marginBottom:"10px"},children:"\ud83c\udfaf Performance Highlights"}),(0,Jr.jsxs)("ul",{style:{paddingLeft:"20px",lineHeight:"1.6"},children:[(0,Jr.jsx)("li",{children:"Car Loans department leads with 92% efficiency"}),(0,Jr.jsx)("li",{children:"Overall system efficiency at 87%, up 3% from last month"}),(0,Jr.jsx)("li",{children:"Average processing time reduced to 2.8 days"})]})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{style:{color:"#ff8f00",marginBottom:"10px"},children:"\u26a0\ufe0f Areas for Improvement"}),(0,Jr.jsxs)("ul",{style:{paddingLeft:"20px",lineHeight:"1.6"},children:[(0,Jr.jsx)("li",{children:"Home Loans processing time needs optimization"}),(0,Jr.jsx)("li",{children:"Rejection rate in Personal Loans requires attention"}),(0,Jr.jsx)("li",{children:"Agent workload distribution could be improved"})]})]}),(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("h4",{style:{color:"#007E3A",marginBottom:"10px"},children:"\ud83d\udcc8 Recommendations"}),(0,Jr.jsxs)("ul",{style:{paddingLeft:"20px",lineHeight:"1.6"},children:[(0,Jr.jsx)("li",{children:"Implement best practices from Car Loans team"}),(0,Jr.jsx)("li",{children:"Provide additional training for Home Loans agents"}),(0,Jr.jsx)("li",{children:"Review and update verification guidelines"})]})]})]})]})]})})};var dg,fg,pg,hg,mg,gg,vg,yg,xg,bg,wg,jg,Sg,kg,Cg,Eg;const Tg=$i.div(dg||(dg=nl(["\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n"]))),Ag=$i.select(fg||(fg=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),Pg=$i.input(pg||(pg=nl(["\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),Rg=$i.div(hg||(hg=nl(["\n  overflow-x: auto;\n"]))),Ng=$i.table(mg||(mg=nl(["\n  width: 100%;\n  border-collapse: collapse;\n"]))),_g=$i.th(gg||(gg=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n  background-color: ",";\n  font-weight: 600;\n  color: ",";\n  cursor: pointer;\n  \n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray),(e=>e.theme.colors.offWhite),(e=>e.theme.colors.textMedium),(e=>e.theme.colors.mediumGray)),Lg=$i.td(vg||(vg=nl(["\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ",";\n"])),(e=>e.theme.colors.lightGray)),Og=$i.tr(yg||(yg=nl(["\n  &:hover {\n    background-color: ",";\n  }\n"])),(e=>e.theme.colors.lightGray)),Dg=$i.span(xg||(xg=nl(["\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ","\n"])),(e=>{switch(e.status){case"new":return"\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        ";case"assigned":return"\n          background-color: #fff3e0;\n          color: #e65100;\n        ";case"in-progress":return"\n          background-color: #fff8e1;\n          color: #ff8f00;\n        ";case"pending-review":return"\n          background-color: #f3e5f5;\n          color: #4a148c;\n        ";case"approved":return"\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        ";case"rejected":return"\n          background-color: #ffebee;\n          color: #c62828;\n        ";default:return"\n          background-color: #f5f5f5;\n          color: #666;\n        "}})),zg=$i.div(bg||(bg=nl(["\n  display: flex;\n  gap: 8px;\n"]))),Ig=$i.div(wg||(wg=nl(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 10px;\n  margin-top: 20px;\n"]))),Fg=$i.button(jg||(jg=nl(["\n  padding: 8px 12px;\n  border: 1px solid ",";\n  background: ",";\n  color: ",";\n  border-radius: ",";\n  cursor: pointer;\n  \n  &:hover {\n    background: ",";\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.active?e.theme.colors.primary:"white"),(e=>e.active?"white":e.theme.colors.textDark),(e=>e.theme.borderRadius.sm),(e=>e.active?e.theme.colors.primary:e.theme.colors.lightGray)),Ug=$i.div(Sg||(Sg=nl(["\n  display: ",";\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n"])),(e=>e.isOpen?"flex":"none")),Mg=$i.div(kg||(kg=nl(["\n  background: white;\n  padding: 30px;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 400px;\n"]))),Bg=$i.h3(Cg||(Cg=nl(["\n  margin-bottom: 20px;\n  color: #007E3A;\n"]))),Hg=$i.div(Eg||(Eg=nl(["\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n"]))),Wg=()=>{const[e,n]=(0,t.useState)([]),[r,o]=(0,t.useState)([]),[a,i]=(0,t.useState)(!0),[l,s]=(0,t.useState)("all"),[u,d]=(0,t.useState)(""),[f,p]=(0,t.useState)(1),[h,m]=(0,t.useState)(1),[g,v]=(0,t.useState)(null),[y,x]=(0,t.useState)(""),[b,w]=(0,t.useState)("createdDate"),[j,S]=(0,t.useState)("desc"),k=pe();(0,t.useEffect)((()=>{C(),E()}),[f,l,b,j]);const C=async()=>{try{i(!0);const e=await Qr.getLeads(f,20,"all"===l?void 0:l);n(e.data||[]),m(Math.ceil((e.totalCount||0)/20))}catch(e){console.error("Error loading leads:",e),n([{leadId:1,customerName:"John Doe",mobileNumber:"9876543210",loanType:"Personal Loan",status:"new",createdDate:"2024-01-15T10:30:00Z",createdByName:"Admin User",assignedToName:"",documentCount:0,croppedImageCount:0},{leadId:2,customerName:"Jane Smith",mobileNumber:"9876543211",loanType:"Home Loan",status:"assigned",createdDate:"2024-01-14T09:15:00Z",assignedDate:"2024-01-14T10:00:00Z",createdByName:"Admin User",assignedToName:"Agent Johnson",documentCount:2,croppedImageCount:1}]),m(1)}finally{i(!1)}},E=async()=>{try{const e=await Qr.getUsers();o(e.filter((e=>"Agent"===e.role&&e.isActive)))}catch(e){console.error("Error loading agents:",e)}},T=e.filter((e=>e.customerName.toLowerCase().includes(u.toLowerCase())||e.mobileNumber.includes(u)||e.loanType.toLowerCase().includes(u.toLowerCase())||e.assignedToName&&e.assignedToName.toLowerCase().includes(u.toLowerCase()))),A=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>k("/admin/dashboard")},{icon:"\ud83d\udc65",label:"Users",onClick:()=>k("/admin/users")},{icon:"\ud83d\udccb",label:"Leads",active:!0},{icon:"\ud83d\udcca",label:"Reports",onClick:()=>k("/admin/reports")},{icon:"\u2699\ufe0f",label:"Settings",onClick:()=>k("/admin/settings")}],P=e=>{b===e?S("asc"===j?"desc":"asc"):(w(e),S("asc"))},R=e=>b!==e?"\u2195\ufe0f":"asc"===j?"\u2191":"\u2193";return a?(0,Jr.jsx)(Ts,{title:"All Leads",navigationItems:A,children:(0,Jr.jsx)(fl,{})}):(0,Jr.jsxs)(Ts,{title:"All Leads",navigationItems:A,children:[(0,Jr.jsxs)(al,{children:[(0,Jr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[(0,Jr.jsx)("h2",{style:{color:"#007E3A"},children:"Lead Management"}),(0,Jr.jsx)(il,{onClick:()=>k("/admin/create-lead"),children:"+ Create New Lead"})]}),(0,Jr.jsxs)(Tg,{children:[(0,Jr.jsxs)(Ag,{value:l,onChange:e=>s(e.target.value),children:[(0,Jr.jsx)("option",{value:"all",children:"All Status"}),(0,Jr.jsx)("option",{value:"new",children:"New"}),(0,Jr.jsx)("option",{value:"assigned",children:"Assigned"}),(0,Jr.jsx)("option",{value:"in-progress",children:"In Progress"}),(0,Jr.jsx)("option",{value:"pending-review",children:"Pending Review"}),(0,Jr.jsx)("option",{value:"approved",children:"Approved"}),(0,Jr.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,Jr.jsx)(Pg,{type:"text",placeholder:"Search by customer, mobile, loan type, or agent...",value:u,onChange:e=>d(e.target.value)})]}),(0,Jr.jsx)(Rg,{children:(0,Jr.jsxs)(Ng,{children:[(0,Jr.jsx)("thead",{children:(0,Jr.jsxs)("tr",{children:[(0,Jr.jsxs)(_g,{onClick:()=>P("leadId"),children:["ID ",R("leadId")]}),(0,Jr.jsxs)(_g,{onClick:()=>P("customerName"),children:["Customer ",R("customerName")]}),(0,Jr.jsx)(_g,{children:"Mobile"}),(0,Jr.jsxs)(_g,{onClick:()=>P("loanType"),children:["Loan Type ",R("loanType")]}),(0,Jr.jsxs)(_g,{onClick:()=>P("status"),children:["Status ",R("status")]}),(0,Jr.jsx)(_g,{children:"Assigned To"}),(0,Jr.jsxs)(_g,{onClick:()=>P("createdDate"),children:["Created ",R("createdDate")]}),(0,Jr.jsx)(_g,{children:"Actions"})]})}),(0,Jr.jsx)("tbody",{children:T.map((e=>{return(0,Jr.jsxs)(Og,{children:[(0,Jr.jsxs)(Lg,{children:["#",e.leadId]}),(0,Jr.jsx)(Lg,{style:{fontWeight:"500"},children:e.customerName}),(0,Jr.jsx)(Lg,{children:e.mobileNumber}),(0,Jr.jsx)(Lg,{children:e.loanType}),(0,Jr.jsx)(Lg,{children:(0,Jr.jsx)(Dg,{status:e.status,children:e.status.replace("-"," ").toUpperCase()})}),(0,Jr.jsx)(Lg,{children:e.assignedToName||"Unassigned"}),(0,Jr.jsx)(Lg,{children:(t=e.createdDate,new Date(t).toLocaleDateString())}),(0,Jr.jsx)(Lg,{children:(0,Jr.jsxs)(zg,{children:[(0,Jr.jsx)(il,{size:"sm",onClick:()=>k("/lead/".concat(e.leadId)),children:"View"}),"new"===e.status&&(0,Jr.jsx)(il,{size:"sm",variant:"secondary",onClick:()=>(e=>{v(e),x("")})(e),children:"Assign"})]})})]},e.leadId);var t}))})]})}),0===T.length&&(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px",color:"#777"},children:u?"No leads found matching your search.":"No leads found."}),(0,Jr.jsxs)(Ig,{children:[(0,Jr.jsx)(Fg,{onClick:()=>p(1),disabled:1===f,children:"First"}),(0,Jr.jsx)(Fg,{onClick:()=>p(f-1),disabled:1===f,children:"Previous"}),Array.from({length:Math.min(5,h)},((e,t)=>{const n=Math.max(1,f-2)+t;return n<=h?(0,Jr.jsx)(Fg,{active:n===f,onClick:()=>p(n),children:n},n):null})),(0,Jr.jsx)(Fg,{onClick:()=>p(f+1),disabled:f===h,children:"Next"}),(0,Jr.jsx)(Fg,{onClick:()=>p(h),disabled:f===h,children:"Last"})]})]}),(0,Jr.jsx)(Ug,{isOpen:!!g,children:(0,Jr.jsxs)(Mg,{children:[(0,Jr.jsxs)(Bg,{children:["Assign Lead - ",null===g||void 0===g?void 0:g.customerName]}),(0,Jr.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,Jr.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"500"},children:"Select Agent:"}),(0,Jr.jsxs)(Ag,{value:y,onChange:e=>x(e.target.value),style:{width:"100%"},children:[(0,Jr.jsx)("option",{value:"",children:"Choose an agent..."}),r.map((e=>(0,Jr.jsxs)("option",{value:e.userId,children:[e.firstName," ",e.lastName," (",e.username,")"]},e.userId)))]})]}),(0,Jr.jsxs)(Hg,{children:[(0,Jr.jsx)(il,{variant:"outline",onClick:()=>{v(null),x("")},children:"Cancel"}),(0,Jr.jsx)(il,{onClick:async()=>{if(g&&y)try{await Qr.assignLead(g.leadId,parseInt(y),"Assigned by admin"),n((e=>e.map((e=>{var t,n;return e.leadId===g.leadId?c(c({},e),{},{status:"assigned",assignedToName:(null===(t=r.find((e=>e.userId===parseInt(y))))||void 0===t?void 0:t.firstName)+" "+(null===(n=r.find((e=>e.userId===parseInt(y))))||void 0===n?void 0:n.lastName),assignedDate:(new Date).toISOString()}):e})))),v(null),x("")}catch(e){console.error("Error assigning lead:",e),alert("Failed to assign lead")}},disabled:!y,children:"Assign Lead"})]})]})})]})};var $g,Vg,qg,Gg,Kg,Qg,Jg,Yg,Zg,Xg,ev,tv,nv,rv,ov,av,iv,lv;const sv=$i.div($g||($g=nl(["\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 20px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n"]))),cv=$i(al)(Vg||(Vg=nl(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 30px;\n"]))),uv=$i.div(qg||(qg=nl(["\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 36px;\n  color: white;\n  font-weight: bold;\n  margin-bottom: 20px;\n"]))),dv=$i.h2(Gg||(Gg=nl(["\n  margin-bottom: 8px;\n  color: ",";\n"])),(e=>e.theme.colors.textDark)),fv=$i.div(Kg||(Kg=nl(["\n  color: ",";\n  font-size: 16px;\n  margin-bottom: 20px;\n"])),(e=>e.theme.colors.textMedium)),pv=$i.span(Qg||(Qg=nl(["\n  display: inline-block;\n  padding: 6px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n  background-color: ",";\n  color: ",";\n"])),(e=>e.isActive?"#e8f5e9":"#ffebee"),(e=>e.isActive?"#2e7d32":"#c62828")),hv=$i.div(Jg||(Jg=nl(["\n  display: grid;\n  gap: 20px;\n"]))),mv=$i(al)(Yg||(Yg=nl(["\n  padding: 20px;\n"]))),gv=$i.h3(Zg||(Zg=nl(["\n  margin-bottom: 20px;\n  color: #007E3A;\n  border-bottom: 1px solid ",";\n  padding-bottom: 10px;\n"])),(e=>e.theme.colors.lightGray)),vv=$i.div(Xg||(Xg=nl(["\n  margin-bottom: 20px;\n"]))),yv=$i.label(ev||(ev=nl(["\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 500;\n  color: ",";\n"])),(e=>e.theme.colors.textMedium)),xv=$i.input(tv||(tv=nl(["\n  width: 100%;\n  padding: 10px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n  \n  &:disabled {\n    background-color: ",";\n    cursor: not-allowed;\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary),(e=>e.theme.colors.lightGray)),bv=$i.textarea(nv||(nv=nl(["\n  width: 100%;\n  min-height: 80px;\n  padding: 10px 12px;\n  border: 1px solid ",";\n  border-radius: ",";\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ",";\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n"])),(e=>e.theme.colors.mediumGray),(e=>e.theme.borderRadius.sm),(e=>e.theme.colors.primary)),wv=$i.div(rv||(rv=nl(["\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n"]))),jv=$i.div(ov||(ov=nl(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 15px;\n  margin-top: 20px;\n"]))),Sv=$i.div(av||(av=nl(["\n  text-align: center;\n  padding: 15px;\n  background: ",";\n  border-radius: ",";\n"])),(e=>e.theme.colors.offWhite),(e=>e.theme.borderRadius.sm)),kv=$i.div(iv||(iv=nl(["\n  font-size: 20px;\n  font-weight: bold;\n  color: #007E3A;\n  margin-bottom: 5px;\n"]))),Cv=$i.div(lv||(lv=nl(["\n  font-size: 12px;\n  color: ",";\n"])),(e=>e.theme.colors.textLight)),Ev=()=>{const{user:e}=Zr(),[n,r]=(0,t.useState)(null),[o,a]=(0,t.useState)(!0),[i,l]=(0,t.useState)(!1),[s,u]=(0,t.useState)({firstName:"",lastName:"",email:"",phoneNumber:"",bio:""}),[d,f]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[p,h]=(0,t.useState)({totalTasks:0,completedTasks:0,approvalRate:0,avgTime:0}),m=pe();(0,t.useEffect)((()=>{g(),v()}),[]);const g=async()=>{try{a(!0);const e=await Qr.getCurrentUser();r(e),u({firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",phoneNumber:e.phoneNumber||"",bio:""})}catch(t){console.error("Error loading user profile:",t),e&&(r(e),u({firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",phoneNumber:e.phoneNumber||"",bio:""}))}finally{a(!1)}},v=async()=>{try{if("Agent"===(null===e||void 0===e?void 0:e.role)){const e=await Qr.getAgentDashboardStats();h({totalTasks:e.totalAssigned||0,completedTasks:e.completedLeads||0,approvalRate:85,avgTime:2.5})}}catch(t){console.error("Error loading user stats:",t)}},y=[{icon:"\ud83c\udfe0",label:"Dashboard",onClick:()=>{switch(null===e||void 0===e?void 0:e.role){case"Agent":m("/agent/dashboard");break;case"Supervisor":m("/supervisor/dashboard");break;case"Admin":m("/admin/dashboard");break;default:m("/")}}},{icon:"\ud83d\udc64",label:"Profile",active:!0},{icon:"\u2699\ufe0f",label:"Settings",onClick:()=>m("/settings")}],x=e=>{const{name:t,value:n}=e.target;u((e=>c(c({},e),{},{[t]:n})))},b=e=>{const{name:t,value:n}=e.target;f((e=>c(c({},e),{},{[t]:n})))},w=e=>new Date(e).toLocaleDateString();return o?(0,Jr.jsx)(Ts,{title:"User Profile",navigationItems:y,children:(0,Jr.jsx)(fl,{})}):n?(0,Jr.jsx)(Ts,{title:"User Profile",navigationItems:y,children:(0,Jr.jsxs)(sv,{children:[(0,Jr.jsxs)(cv,{children:[(0,Jr.jsx)(uv,{children:(j=n.firstName,S=n.lastName,"".concat(j.charAt(0)).concat(S.charAt(0)).toUpperCase())}),(0,Jr.jsxs)(dv,{children:[n.firstName," ",n.lastName]}),(0,Jr.jsx)(fv,{children:n.role}),(0,Jr.jsx)(pv,{isActive:n.isActive,children:n.isActive?"Active":"Inactive"}),(0,Jr.jsxs)("div",{style:{marginTop:"20px",width:"100%"},children:[(0,Jr.jsxs)("div",{style:{marginBottom:"10px"},children:[(0,Jr.jsx)("strong",{children:"Username:"})," ",n.username]}),(0,Jr.jsxs)("div",{style:{marginBottom:"10px"},children:[(0,Jr.jsx)("strong",{children:"Email:"})," ",n.email]}),(0,Jr.jsxs)("div",{style:{marginBottom:"10px"},children:[(0,Jr.jsx)("strong",{children:"Joined:"})," ",w(n.createdDate)]}),n.lastLoginDate&&(0,Jr.jsxs)("div",{children:[(0,Jr.jsx)("strong",{children:"Last Login:"})," ",w(n.lastLoginDate)]})]}),"Agent"===n.role&&(0,Jr.jsxs)(jv,{children:[(0,Jr.jsxs)(Sv,{children:[(0,Jr.jsx)(kv,{children:p.totalTasks}),(0,Jr.jsx)(Cv,{children:"Total Tasks"})]}),(0,Jr.jsxs)(Sv,{children:[(0,Jr.jsx)(kv,{children:p.completedTasks}),(0,Jr.jsx)(Cv,{children:"Completed"})]}),(0,Jr.jsxs)(Sv,{children:[(0,Jr.jsxs)(kv,{children:[p.approvalRate,"%"]}),(0,Jr.jsx)(Cv,{children:"Approval Rate"})]}),(0,Jr.jsxs)(Sv,{children:[(0,Jr.jsx)(kv,{children:p.avgTime}),(0,Jr.jsx)(Cv,{children:"Avg. Days"})]})]})]}),(0,Jr.jsxs)(hv,{children:[(0,Jr.jsxs)(mv,{children:[(0,Jr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[(0,Jr.jsx)(gv,{style:{marginBottom:0},children:"Personal Information"}),(0,Jr.jsx)(il,{variant:"outline",size:"sm",onClick:()=>l(!i),children:i?"Cancel":"Edit"})]}),(0,Jr.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px"},children:[(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"First Name"}),(0,Jr.jsx)(xv,{type:"text",name:"firstName",value:s.firstName,onChange:x,disabled:!i})]}),(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"Last Name"}),(0,Jr.jsx)(xv,{type:"text",name:"lastName",value:s.lastName,onChange:x,disabled:!i})]})]}),(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"Email"}),(0,Jr.jsx)(xv,{type:"email",name:"email",value:s.email,onChange:x,disabled:!i})]}),(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"Phone Number"}),(0,Jr.jsx)(xv,{type:"tel",name:"phoneNumber",value:s.phoneNumber,onChange:x,disabled:!i})]}),(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"Bio"}),(0,Jr.jsx)(bv,{name:"bio",value:s.bio,onChange:x,disabled:!i,placeholder:"Tell us about yourself..."})]}),i&&(0,Jr.jsxs)(wv,{children:[(0,Jr.jsx)(il,{variant:"outline",onClick:()=>l(!1),children:"Cancel"}),(0,Jr.jsx)(il,{onClick:async()=>{try{if(!n)return;await Qr.updateUser(n.userId,s),r((e=>e?c(c({},e),s):null)),l(!1),alert("Profile updated successfully!")}catch(e){console.error("Error updating profile:",e),alert("Failed to update profile")}},children:"Save Changes"})]})]}),(0,Jr.jsxs)(mv,{children:[(0,Jr.jsx)(gv,{children:"Change Password"}),(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"Current Password"}),(0,Jr.jsx)(xv,{type:"password",name:"currentPassword",value:d.currentPassword,onChange:b})]}),(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"New Password"}),(0,Jr.jsx)(xv,{type:"password",name:"newPassword",value:d.newPassword,onChange:b})]}),(0,Jr.jsxs)(vv,{children:[(0,Jr.jsx)(yv,{children:"Confirm New Password"}),(0,Jr.jsx)(xv,{type:"password",name:"confirmPassword",value:d.confirmPassword,onChange:b})]}),(0,Jr.jsx)(wv,{children:(0,Jr.jsx)(il,{variant:"secondary",onClick:async()=>{if(d.newPassword===d.confirmPassword)if(d.newPassword.length<6)alert("Password must be at least 6 characters long");else try{alert("Password change functionality would be implemented here"),f({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){console.error("Error changing password:",e),alert("Failed to change password")}else alert("New passwords do not match")},disabled:!d.currentPassword||!d.newPassword||!d.confirmPassword,children:"Change Password"})})]})]})]})}):(0,Jr.jsx)(Ts,{title:"User Profile",navigationItems:y,children:(0,Jr.jsx)(al,{children:(0,Jr.jsx)("div",{style:{textAlign:"center",padding:"40px"},children:"User profile not found"})})});var j,S};const Tv=function(){return(0,Jr.jsxs)(zi,{theme:rl,children:[(0,Jr.jsx)(ol,{}),(0,Jr.jsx)(Xr,{children:(0,Jr.jsx)(pt,{children:(0,Jr.jsxs)(_e,{children:[(0,Jr.jsx)(Re,{path:"/login",element:(0,Jr.jsx)(Dl,{})}),(0,Jr.jsx)(Re,{path:"/",element:(0,Jr.jsx)(Pe,{to:"/login",replace:!0})}),(0,Jr.jsx)(Re,{path:"/agent/dashboard",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent"],children:(0,Jr.jsx)(Zs,{})})}),(0,Jr.jsx)(Re,{path:"/agent/tasks",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent"],children:(0,Jr.jsx)(pp,{})})}),(0,Jr.jsx)(Re,{path:"/agent/completed",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent"],children:(0,Jr.jsx)(Mp,{})})}),(0,Jr.jsx)(Re,{path:"/agent/reports",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent"],children:(0,Jr.jsx)(Sh,{})})}),(0,Jr.jsx)(Re,{path:"/supervisor/dashboard",element:(0,Jr.jsx)(Ml,{allowedRoles:["Supervisor"],children:(0,Jr.jsx)(Cc,{})})}),(0,Jr.jsx)(Re,{path:"/supervisor/review",element:(0,Jr.jsx)(Ml,{allowedRoles:["Supervisor"],children:(0,Jr.jsx)(nm,{})})}),(0,Jr.jsx)(Re,{path:"/supervisor/reports",element:(0,Jr.jsx)(Ml,{allowedRoles:["Supervisor"],children:(0,Jr.jsx)(Lm,{})})}),(0,Jr.jsx)(Re,{path:"/admin/dashboard",element:(0,Jr.jsx)(Ml,{allowedRoles:["Admin"],children:(0,Jr.jsx)(ou,{})})}),(0,Jr.jsx)(Re,{path:"/admin/create-lead",element:(0,Jr.jsx)(Ml,{allowedRoles:["Admin"],children:(0,Jr.jsx)(pf,{})})}),(0,Jr.jsx)(Re,{path:"/admin/users",element:(0,Jr.jsx)(Ml,{allowedRoles:["Admin"],children:(0,Jr.jsx)($f,{})})}),(0,Jr.jsx)(Re,{path:"/admin/leads",element:(0,Jr.jsx)(Ml,{allowedRoles:["Admin"],children:(0,Jr.jsx)(Wg,{})})}),(0,Jr.jsx)(Re,{path:"/admin/reports",element:(0,Jr.jsx)(Ml,{allowedRoles:["Admin"],children:(0,Jr.jsx)(ug,{})})}),(0,Jr.jsx)(Re,{path:"/lead/:id",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent","Supervisor","Admin"],children:(0,Jr.jsx)(Iu,{})})}),(0,Jr.jsx)(Re,{path:"/lead/:id/verification",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent"],children:(0,Jr.jsx)(Zu,{})})}),(0,Jr.jsx)(Re,{path:"/lead/:id/documents",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent"],children:(0,Jr.jsx)(Qd,{})})}),(0,Jr.jsx)(Re,{path:"/profile",element:(0,Jr.jsx)(Ml,{allowedRoles:["Agent","Supervisor","Admin"],children:(0,Jr.jsx)(Ev,{})})})]})})})]})},Av=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then((t=>{let{getCLS:n,getFID:r,getFCP:o,getLCP:a,getTTFB:i}=t;n(e),r(e),o(e),a(e),i(e)}))};r.createRoot(document.getElementById("root")).render((0,Jr.jsx)(t.StrictMode,{children:(0,Jr.jsx)(Tv,{})})),Av()})()})();
//# sourceMappingURL=main.79ac5a1e.js.map