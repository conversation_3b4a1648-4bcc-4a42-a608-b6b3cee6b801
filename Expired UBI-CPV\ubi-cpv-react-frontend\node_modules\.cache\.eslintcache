[{"D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx": "1", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts": "2", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx": "3", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts": "4", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx": "5", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx": "6", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx": "7", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx": "8", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx": "9", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx": "10", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "11", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx": "12", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx": "13", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts": "14", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx": "15", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx": "16", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx": "17", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx": "18", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx": "19", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx": "20", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx": "21", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx": "22", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx": "23", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx": "24", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx": "25", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx": "26", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Demo\\ModernUIShowcase.tsx": "27", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Common\\DocumentUpload.tsx": "28"}, {"size": 554, "mtime": 1748411605451, "results": "29", "hashOfConfig": "30"}, {"size": 425, "mtime": 1748411601005, "results": "31", "hashOfConfig": "30"}, {"size": 6020, "mtime": 1749027797555, "results": "32", "hashOfConfig": "30"}, {"size": 29537, "mtime": 1749028318993, "results": "33", "hashOfConfig": "30"}, {"size": 3146, "mtime": 1748415256095, "results": "34", "hashOfConfig": "30"}, {"size": 9560, "mtime": 1748415614108, "results": "35", "hashOfConfig": "30"}, {"size": 8827, "mtime": 1749027203001, "results": "36", "hashOfConfig": "30"}, {"size": 11012, "mtime": 1748529015435, "results": "37", "hashOfConfig": "30"}, {"size": 10300, "mtime": 1748529032349, "results": "38", "hashOfConfig": "30"}, {"size": 11411, "mtime": 1748528557126, "results": "39", "hashOfConfig": "30"}, {"size": 1503, "mtime": 1748412009127, "results": "40", "hashOfConfig": "30"}, {"size": 11683, "mtime": 1748413757670, "results": "41", "hashOfConfig": "30"}, {"size": 14073, "mtime": 1748417381880, "results": "42", "hashOfConfig": "30"}, {"size": 21321, "mtime": 1749030821259, "results": "43", "hashOfConfig": "30"}, {"size": 6505, "mtime": 1748412041801, "results": "44", "hashOfConfig": "30"}, {"size": 18521, "mtime": 1749031556825, "results": "45", "hashOfConfig": "30"}, {"size": 6033, "mtime": 1748413518514, "results": "46", "hashOfConfig": "30"}, {"size": 18346, "mtime": 1749029728068, "results": "47", "hashOfConfig": "30"}, {"size": 9959, "mtime": 1748528093222, "results": "48", "hashOfConfig": "30"}, {"size": 10782, "mtime": 1748528140885, "results": "49", "hashOfConfig": "30"}, {"size": 9782, "mtime": 1748528186911, "results": "50", "hashOfConfig": "30"}, {"size": 15167, "mtime": 1748528252181, "results": "51", "hashOfConfig": "30"}, {"size": 12764, "mtime": 1748529149152, "results": "52", "hashOfConfig": "30"}, {"size": 14420, "mtime": 1748528379281, "results": "53", "hashOfConfig": "30"}, {"size": 15920, "mtime": 1748528446243, "results": "54", "hashOfConfig": "30"}, {"size": 14710, "mtime": 1748528507904, "results": "55", "hashOfConfig": "30"}, {"size": 6882, "mtime": 1749027719964, "results": "56", "hashOfConfig": "30"}, {"size": 11420, "mtime": 1749032677208, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nkgvnm", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx", ["142"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx", ["143"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx", ["144", "145"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx", ["146"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx", ["147", "148", "149"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx", ["150"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx", ["151"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx", ["152"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx", ["153"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx", ["154"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx", ["155"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Demo\\ModernUIShowcase.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Common\\DocumentUpload.tsx", ["156", "157"], [], {"ruleId": "158", "severity": 1, "message": "159", "line": 5, "column": 79, "nodeType": "160", "messageId": "161", "endLine": 5, "endColumn": 88}, {"ruleId": "158", "severity": 1, "message": "162", "line": 5, "column": 24, "nodeType": "160", "messageId": "161", "endLine": 5, "endColumn": 38}, {"ruleId": "158", "severity": 1, "message": "162", "line": 5, "column": 24, "nodeType": "160", "messageId": "161", "endLine": 5, "endColumn": 38}, {"ruleId": "163", "severity": 1, "message": "164", "line": 141, "column": 6, "nodeType": "165", "endLine": 141, "endColumn": 20, "suggestions": "166"}, {"ruleId": "158", "severity": 1, "message": "167", "line": 217, "column": 9, "nodeType": "160", "messageId": "161", "endLine": 217, "endColumn": 25}, {"ruleId": "158", "severity": 1, "message": "168", "line": 107, "column": 10, "nodeType": "160", "messageId": "161", "endLine": 107, "endColumn": 23}, {"ruleId": "158", "severity": 1, "message": "169", "line": 109, "column": 10, "nodeType": "160", "messageId": "161", "endLine": 109, "endColumn": 24}, {"ruleId": "158", "severity": 1, "message": "170", "line": 231, "column": 11, "nodeType": "160", "messageId": "161", "endLine": 231, "endColumn": 28}, {"ruleId": "163", "severity": 1, "message": "171", "line": 206, "column": 6, "nodeType": "165", "endLine": 206, "endColumn": 8, "suggestions": "172"}, {"ruleId": "163", "severity": 1, "message": "173", "line": 147, "column": 6, "nodeType": "165", "endLine": 147, "endColumn": 20, "suggestions": "174"}, {"ruleId": "163", "severity": 1, "message": "175", "line": 149, "column": 6, "nodeType": "165", "endLine": 149, "endColumn": 20, "suggestions": "176"}, {"ruleId": "163", "severity": 1, "message": "177", "line": 206, "column": 6, "nodeType": "165", "endLine": 206, "endColumn": 20, "suggestions": "178"}, {"ruleId": "163", "severity": 1, "message": "179", "line": 207, "column": 6, "nodeType": "165", "endLine": 207, "endColumn": 59, "suggestions": "180"}, {"ruleId": "163", "severity": 1, "message": "181", "line": 185, "column": 6, "nodeType": "165", "endLine": 185, "endColumn": 8, "suggestions": "182"}, {"ruleId": "158", "severity": 1, "message": "183", "line": 4, "column": 26, "nodeType": "160", "messageId": "161", "endLine": 4, "endColumn": 40}, {"ruleId": "163", "severity": 1, "message": "184", "line": 195, "column": 6, "nodeType": "165", "endLine": 195, "endColumn": 84, "suggestions": "185"}, "@typescript-eslint/no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'LoadingSpinner' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["186"], "'handleAssignLead' is assigned a value but never used.", "'documentTypes' is assigned a value but never used.", "'uploadProgress' is assigned a value but never used.", "'requiredDocuments' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["187"], "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["188"], "React Hook useEffect has a missing dependency: 'loadCompletedTasks'. Either include it or remove the dependency array.", ["189"], "React Hook useEffect has a missing dependency: 'loadReviewTasks'. Either include it or remove the dependency array.", ["190"], "React Hook useEffect has a missing dependency: 'loadLeads'. Either include it or remove the dependency array.", ["191"], "React Hook useEffect has missing dependencies: 'loadUserProfile' and 'loadUserStats'. Either include them or remove the dependency array.", ["192"], "'UploadProgress' is defined but never used.", "React Hook useCallback has a missing dependency: 'validateFile'. Either include it or remove the dependency array.", ["193"], {"desc": "194", "fix": "195"}, {"desc": "196", "fix": "197"}, {"desc": "198", "fix": "199"}, {"desc": "200", "fix": "201"}, {"desc": "202", "fix": "203"}, {"desc": "204", "fix": "205"}, {"desc": "206", "fix": "207"}, {"desc": "208", "fix": "209"}, "Update the dependencies array to be: [loadDashboardData, statusFilter]", {"range": "210", "text": "211"}, "Update the dependencies array to be: [loadUsers]", {"range": "212", "text": "213"}, "Update the dependencies array to be: [loadTasks, statusFilter]", {"range": "214", "text": "215"}, "Update the dependencies array to be: [loadCompletedTasks, statusFilter]", {"range": "216", "text": "217"}, "Update the dependencies array to be: [loadReviewTasks, statusFilter]", {"range": "218", "text": "219"}, "Update the dependencies array to be: [currentPage, statusFilter, sortField, sortDirection, loadLeads]", {"range": "220", "text": "221"}, "Update the dependencies array to be: [loadUserProfile, loadUserStats]", {"range": "222", "text": "223"}, "Update the dependencies array to be: [onDocumentsChange, documents, maxFiles, validateFile, required]", {"range": "224", "text": "225"}, [3471, 3485], "[loadDashboardData, statusFilter]", [4949, 4951], "[loadUsers]", [3378, 3392], "[loadTasks, statusFilter]", [3512, 3526], "[loadCompletedTasks, statusFilter]", [4811, 4825], "[loadReviewTasks, statusFilter]", [5059, 5112], "[currentPage, statusFilter, sortField, sortDirection, loadLeads]", [4286, 4288], "[loadUserProfile, loadUserStats]", [5998, 6076], "[onDocumentsChange, documents, maxFiles, validateFile, required]"]