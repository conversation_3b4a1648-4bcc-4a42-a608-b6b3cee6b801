[{"D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx": "1", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts": "2", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx": "3", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts": "4", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx": "5", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx": "6", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx": "7", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx": "8", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx": "9", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx": "10", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "11", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx": "12", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx": "13", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts": "14", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx": "15", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx": "16", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx": "17", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx": "18", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx": "19", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx": "20", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx": "21", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx": "22", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx": "23", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx": "24", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx": "25", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx": "26", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Demo\\ModernUIShowcase.tsx": "27", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Common\\DocumentUpload.tsx": "28", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Common\\DocumentViewer.tsx": "29"}, {"size": 554, "mtime": 1748411605451, "results": "30", "hashOfConfig": "31"}, {"size": 425, "mtime": 1748411601005, "results": "32", "hashOfConfig": "31"}, {"size": 6020, "mtime": 1749027797555, "results": "33", "hashOfConfig": "31"}, {"size": 29537, "mtime": 1749028318993, "results": "34", "hashOfConfig": "31"}, {"size": 3146, "mtime": 1748415256095, "results": "35", "hashOfConfig": "31"}, {"size": 9560, "mtime": 1748415614108, "results": "36", "hashOfConfig": "31"}, {"size": 8827, "mtime": 1749027203001, "results": "37", "hashOfConfig": "31"}, {"size": 11012, "mtime": 1748529015435, "results": "38", "hashOfConfig": "31"}, {"size": 10300, "mtime": 1748529032349, "results": "39", "hashOfConfig": "31"}, {"size": 11739, "mtime": 1749034670465, "results": "40", "hashOfConfig": "31"}, {"size": 1503, "mtime": 1748412009127, "results": "41", "hashOfConfig": "31"}, {"size": 11683, "mtime": 1748413757670, "results": "42", "hashOfConfig": "31"}, {"size": 14073, "mtime": 1748417381880, "results": "43", "hashOfConfig": "31"}, {"size": 23467, "mtime": 1749034462035, "results": "44", "hashOfConfig": "31"}, {"size": 6505, "mtime": 1748412041801, "results": "45", "hashOfConfig": "31"}, {"size": 18521, "mtime": 1749031556825, "results": "46", "hashOfConfig": "31"}, {"size": 6033, "mtime": 1748413518514, "results": "47", "hashOfConfig": "31"}, {"size": 18346, "mtime": 1749029728068, "results": "48", "hashOfConfig": "31"}, {"size": 9959, "mtime": 1748528093222, "results": "49", "hashOfConfig": "31"}, {"size": 10782, "mtime": 1748528140885, "results": "50", "hashOfConfig": "31"}, {"size": 9782, "mtime": 1748528186911, "results": "51", "hashOfConfig": "31"}, {"size": 15167, "mtime": 1748528252181, "results": "52", "hashOfConfig": "31"}, {"size": 12764, "mtime": 1748529149152, "results": "53", "hashOfConfig": "31"}, {"size": 14420, "mtime": 1748528379281, "results": "54", "hashOfConfig": "31"}, {"size": 15920, "mtime": 1748528446243, "results": "55", "hashOfConfig": "31"}, {"size": 14710, "mtime": 1748528507904, "results": "56", "hashOfConfig": "31"}, {"size": 6882, "mtime": 1749027719964, "results": "57", "hashOfConfig": "31"}, {"size": 11420, "mtime": 1749032677208, "results": "58", "hashOfConfig": "31"}, {"size": 14646, "mtime": 1749034530153, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nkgvnm", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx", ["147"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx", ["148"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx", ["149", "150"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx", ["151"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx", ["152", "153", "154"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx", ["155"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx", ["156"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx", ["157"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx", ["158"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx", ["159"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx", ["160"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Demo\\ModernUIShowcase.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Common\\DocumentUpload.tsx", ["161", "162"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Common\\DocumentViewer.tsx", ["163"], [], {"ruleId": "164", "severity": 1, "message": "165", "line": 5, "column": 79, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 88}, {"ruleId": "164", "severity": 1, "message": "168", "line": 5, "column": 24, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 38}, {"ruleId": "164", "severity": 1, "message": "168", "line": 5, "column": 24, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 38}, {"ruleId": "169", "severity": 1, "message": "170", "line": 141, "column": 6, "nodeType": "171", "endLine": 141, "endColumn": 20, "suggestions": "172"}, {"ruleId": "164", "severity": 1, "message": "173", "line": 210, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 210, "endColumn": 25}, {"ruleId": "164", "severity": 1, "message": "174", "line": 107, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 107, "endColumn": 23}, {"ruleId": "164", "severity": 1, "message": "175", "line": 109, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 109, "endColumn": 24}, {"ruleId": "164", "severity": 1, "message": "176", "line": 231, "column": 11, "nodeType": "166", "messageId": "167", "endLine": 231, "endColumn": 28}, {"ruleId": "169", "severity": 1, "message": "177", "line": 206, "column": 6, "nodeType": "171", "endLine": 206, "endColumn": 8, "suggestions": "178"}, {"ruleId": "169", "severity": 1, "message": "179", "line": 147, "column": 6, "nodeType": "171", "endLine": 147, "endColumn": 20, "suggestions": "180"}, {"ruleId": "169", "severity": 1, "message": "181", "line": 149, "column": 6, "nodeType": "171", "endLine": 149, "endColumn": 20, "suggestions": "182"}, {"ruleId": "169", "severity": 1, "message": "183", "line": 206, "column": 6, "nodeType": "171", "endLine": 206, "endColumn": 20, "suggestions": "184"}, {"ruleId": "169", "severity": 1, "message": "185", "line": 207, "column": 6, "nodeType": "171", "endLine": 207, "endColumn": 59, "suggestions": "186"}, {"ruleId": "169", "severity": 1, "message": "187", "line": 185, "column": 6, "nodeType": "171", "endLine": 185, "endColumn": 8, "suggestions": "188"}, {"ruleId": "164", "severity": 1, "message": "189", "line": 4, "column": 26, "nodeType": "166", "messageId": "167", "endLine": 4, "endColumn": 40}, {"ruleId": "169", "severity": 1, "message": "190", "line": 195, "column": 6, "nodeType": "171", "endLine": 195, "endColumn": 84, "suggestions": "191"}, {"ruleId": "164", "severity": 1, "message": "168", "line": 3, "column": 18, "nodeType": "166", "messageId": "167", "endLine": 3, "endColumn": 32}, "@typescript-eslint/no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'LoadingSpinner' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["192"], "'handleAssignLead' is assigned a value but never used.", "'documentTypes' is assigned a value but never used.", "'uploadProgress' is assigned a value but never used.", "'requiredDocuments' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["193"], "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["194"], "React Hook useEffect has a missing dependency: 'loadCompletedTasks'. Either include it or remove the dependency array.", ["195"], "React Hook useEffect has a missing dependency: 'loadReviewTasks'. Either include it or remove the dependency array.", ["196"], "React Hook useEffect has a missing dependency: 'loadLeads'. Either include it or remove the dependency array.", ["197"], "React Hook useEffect has missing dependencies: 'loadUserProfile' and 'loadUserStats'. Either include them or remove the dependency array.", ["198"], "'UploadProgress' is defined but never used.", "React Hook useCallback has a missing dependency: 'validateFile'. Either include it or remove the dependency array.", ["199"], {"desc": "200", "fix": "201"}, {"desc": "202", "fix": "203"}, {"desc": "204", "fix": "205"}, {"desc": "206", "fix": "207"}, {"desc": "208", "fix": "209"}, {"desc": "210", "fix": "211"}, {"desc": "212", "fix": "213"}, {"desc": "214", "fix": "215"}, "Update the dependencies array to be: [loadDashboardData, statusFilter]", {"range": "216", "text": "217"}, "Update the dependencies array to be: [loadUsers]", {"range": "218", "text": "219"}, "Update the dependencies array to be: [loadTasks, statusFilter]", {"range": "220", "text": "221"}, "Update the dependencies array to be: [loadCompletedTasks, statusFilter]", {"range": "222", "text": "223"}, "Update the dependencies array to be: [loadReviewTasks, statusFilter]", {"range": "224", "text": "225"}, "Update the dependencies array to be: [currentPage, statusFilter, sortField, sortDirection, loadLeads]", {"range": "226", "text": "227"}, "Update the dependencies array to be: [loadUserProfile, loadUserStats]", {"range": "228", "text": "229"}, "Update the dependencies array to be: [onDocumentsChange, documents, maxFiles, validateFile, required]", {"range": "230", "text": "231"}, [3471, 3485], "[loadDashboardData, statusFilter]", [4949, 4951], "[loadUsers]", [3378, 3392], "[loadTasks, statusFilter]", [3512, 3526], "[loadCompletedTasks, statusFilter]", [4811, 4825], "[loadReviewTasks, statusFilter]", [5059, 5112], "[currentPage, statusFilter, sortField, sortDirection, loadLeads]", [4286, 4288], "[loadUserProfile, loadUserStats]", [5998, 6076], "[onDocumentsChange, documents, maxFiles, validateFile, required]"]