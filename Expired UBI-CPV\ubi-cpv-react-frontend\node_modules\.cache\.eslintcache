[{"D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx": "1", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts": "2", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx": "3", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts": "4", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx": "5", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx": "6", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx": "7", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx": "8", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx": "9", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx": "10", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "11", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx": "12", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx": "13", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts": "14", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx": "15", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx": "16", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx": "17", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx": "18", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx": "19", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx": "20", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx": "21", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx": "22", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx": "23", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx": "24", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx": "25", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx": "26", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Demo\\ModernUIShowcase.tsx": "27"}, {"size": 554, "mtime": 1748411605451, "results": "28", "hashOfConfig": "29"}, {"size": 425, "mtime": 1748411601005, "results": "30", "hashOfConfig": "29"}, {"size": 6020, "mtime": 1749027797555, "results": "31", "hashOfConfig": "29"}, {"size": 29537, "mtime": 1749028318993, "results": "32", "hashOfConfig": "29"}, {"size": 3146, "mtime": 1748415256095, "results": "33", "hashOfConfig": "29"}, {"size": 9560, "mtime": 1748415614108, "results": "34", "hashOfConfig": "29"}, {"size": 8827, "mtime": 1749027203001, "results": "35", "hashOfConfig": "29"}, {"size": 11012, "mtime": 1748529015435, "results": "36", "hashOfConfig": "29"}, {"size": 10300, "mtime": 1748529032349, "results": "37", "hashOfConfig": "29"}, {"size": 11411, "mtime": 1748528557126, "results": "38", "hashOfConfig": "29"}, {"size": 1503, "mtime": 1748412009127, "results": "39", "hashOfConfig": "29"}, {"size": 11683, "mtime": 1748413757670, "results": "40", "hashOfConfig": "29"}, {"size": 14073, "mtime": 1748417381880, "results": "41", "hashOfConfig": "29"}, {"size": 16568, "mtime": 1748528904470, "results": "42", "hashOfConfig": "29"}, {"size": 6505, "mtime": 1748412041801, "results": "43", "hashOfConfig": "29"}, {"size": 11264, "mtime": 1748417320847, "results": "44", "hashOfConfig": "29"}, {"size": 6033, "mtime": 1748413518514, "results": "45", "hashOfConfig": "29"}, {"size": 18346, "mtime": 1749029728068, "results": "46", "hashOfConfig": "29"}, {"size": 9959, "mtime": 1748528093222, "results": "47", "hashOfConfig": "29"}, {"size": 10782, "mtime": 1748528140885, "results": "48", "hashOfConfig": "29"}, {"size": 9782, "mtime": 1748528186911, "results": "49", "hashOfConfig": "29"}, {"size": 15167, "mtime": 1748528252181, "results": "50", "hashOfConfig": "29"}, {"size": 12764, "mtime": 1748529149152, "results": "51", "hashOfConfig": "29"}, {"size": 14420, "mtime": 1748528379281, "results": "52", "hashOfConfig": "29"}, {"size": 15920, "mtime": 1748528446243, "results": "53", "hashOfConfig": "29"}, {"size": 14710, "mtime": 1748528507904, "results": "54", "hashOfConfig": "29"}, {"size": 6882, "mtime": 1749027719964, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nkgvnm", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx", ["137"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx", ["138"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx", ["139", "140"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx", ["141"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx", ["142"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx", ["143"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx", ["144"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx", ["145"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx", ["146"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx", ["147"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Demo\\ModernUIShowcase.tsx", [], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 5, "column": 79, "nodeType": "150", "messageId": "151", "endLine": 5, "endColumn": 88}, {"ruleId": "148", "severity": 1, "message": "152", "line": 5, "column": 24, "nodeType": "150", "messageId": "151", "endLine": 5, "endColumn": 38}, {"ruleId": "148", "severity": 1, "message": "152", "line": 5, "column": 24, "nodeType": "150", "messageId": "151", "endLine": 5, "endColumn": 38}, {"ruleId": "153", "severity": 1, "message": "154", "line": 141, "column": 6, "nodeType": "155", "endLine": 141, "endColumn": 20, "suggestions": "156"}, {"ruleId": "148", "severity": 1, "message": "157", "line": 217, "column": 9, "nodeType": "150", "messageId": "151", "endLine": 217, "endColumn": 25}, {"ruleId": "153", "severity": 1, "message": "158", "line": 206, "column": 6, "nodeType": "155", "endLine": 206, "endColumn": 8, "suggestions": "159"}, {"ruleId": "153", "severity": 1, "message": "160", "line": 147, "column": 6, "nodeType": "155", "endLine": 147, "endColumn": 20, "suggestions": "161"}, {"ruleId": "153", "severity": 1, "message": "162", "line": 149, "column": 6, "nodeType": "155", "endLine": 149, "endColumn": 20, "suggestions": "163"}, {"ruleId": "153", "severity": 1, "message": "164", "line": 206, "column": 6, "nodeType": "155", "endLine": 206, "endColumn": 20, "suggestions": "165"}, {"ruleId": "153", "severity": 1, "message": "166", "line": 207, "column": 6, "nodeType": "155", "endLine": 207, "endColumn": 59, "suggestions": "167"}, {"ruleId": "153", "severity": 1, "message": "168", "line": 185, "column": 6, "nodeType": "155", "endLine": 185, "endColumn": 8, "suggestions": "169"}, "@typescript-eslint/no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'LoadingSpinner' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["170"], "'handleAssignLead' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["171"], "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["172"], "React Hook useEffect has a missing dependency: 'loadCompletedTasks'. Either include it or remove the dependency array.", ["173"], "React Hook useEffect has a missing dependency: 'loadReviewTasks'. Either include it or remove the dependency array.", ["174"], "React Hook useEffect has a missing dependency: 'loadLeads'. Either include it or remove the dependency array.", ["175"], "React Hook useEffect has missing dependencies: 'loadUserProfile' and 'loadUserStats'. Either include them or remove the dependency array.", ["176"], {"desc": "177", "fix": "178"}, {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, {"desc": "183", "fix": "184"}, {"desc": "185", "fix": "186"}, {"desc": "187", "fix": "188"}, {"desc": "189", "fix": "190"}, "Update the dependencies array to be: [loadDashboardData, statusFilter]", {"range": "191", "text": "192"}, "Update the dependencies array to be: [loadUsers]", {"range": "193", "text": "194"}, "Update the dependencies array to be: [loadTasks, statusFilter]", {"range": "195", "text": "196"}, "Update the dependencies array to be: [loadCompletedTasks, statusFilter]", {"range": "197", "text": "198"}, "Update the dependencies array to be: [loadReviewTasks, statusFilter]", {"range": "199", "text": "200"}, "Update the dependencies array to be: [currentPage, statusFilter, sortField, sortDirection, loadLeads]", {"range": "201", "text": "202"}, "Update the dependencies array to be: [loadUserProfile, loadUserStats]", {"range": "203", "text": "204"}, [3471, 3485], "[loadDashboardData, statusFilter]", [4949, 4951], "[loadUsers]", [3378, 3392], "[loadTasks, statusFilter]", [3512, 3526], "[loadCompletedTasks, statusFilter]", [4811, 4825], "[loadReviewTasks, statusFilter]", [5059, 5112], "[currentPage, statusFilter, sortField, sortDirection, loadLeads]", [4286, 4288], "[loadUserProfile, loadUserStats]"]