{"ast": null, "code": "import styled, { createGlobalStyle } from 'styled-components';\nexport const theme = {\n  colors: {\n    // Primary Brand Colors\n    primary: '#007E3A',\n    primaryDark: '#005a2a',\n    primaryLight: '#4CAF50',\n    primaryGradient: 'linear-gradient(135deg, #007E3A 0%, #4CAF50 100%)',\n    // Secondary Colors\n    secondary: '#FFD100',\n    secondaryDark: '#e6bc00',\n    secondaryLight: '#FFF176',\n    secondaryGradient: 'linear-gradient(135deg, #FFD100 0%, #FFF176 100%)',\n    // Neutral Colors\n    white: '#FFFFFF',\n    offWhite: '#FAFAFA',\n    lightGray: '#F5F7FA',\n    mediumGray: '#E2E8F0',\n    darkGray: '#64748B',\n    // Text Colors\n    textPrimary: '#1E293B',\n    textSecondary: '#475569',\n    textTertiary: '#94A3B8',\n    textInverse: '#FFFFFF',\n    // Status Colors\n    error: '#EF4444',\n    errorLight: '#FEF2F2',\n    success: '#10B981',\n    successLight: '#F0FDF4',\n    warning: '#F59E0B',\n    warningLight: '#FFFBEB',\n    info: '#3B82F6',\n    infoLight: '#EFF6FF',\n    // Background Colors\n    background: '#FFFFFF',\n    backgroundSecondary: '#F8FAFC',\n    backgroundTertiary: '#F1F5F9',\n    // Border Colors\n    border: '#E2E8F0',\n    borderLight: '#F1F5F9',\n    borderDark: '#CBD5E1',\n    // Glass Effect Colors\n    glass: 'rgba(255, 255, 255, 0.25)',\n    glassDark: 'rgba(0, 0, 0, 0.1)'\n  },\n  shadows: {\n    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',\n    glass: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',\n    neumorphism: '20px 20px 60px #d1d9e6, -20px -20px 60px #ffffff'\n  },\n  borderRadius: {\n    none: '0',\n    xs: '2px',\n    sm: '4px',\n    md: '8px',\n    lg: '12px',\n    xl: '16px',\n    '2xl': '24px',\n    '3xl': '32px',\n    full: '9999px'\n  },\n  spacing: {\n    xs: '4px',\n    sm: '8px',\n    md: '16px',\n    lg: '24px',\n    xl: '32px',\n    '2xl': '48px',\n    '3xl': '64px',\n    '4xl': '96px'\n  },\n  typography: {\n    fontFamily: {\n      sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],\n      mono: ['JetBrains Mono', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace']\n    },\n    fontSize: {\n      xs: '12px',\n      sm: '14px',\n      base: '16px',\n      lg: '18px',\n      xl: '20px',\n      '2xl': '24px',\n      '3xl': '30px',\n      '4xl': '36px',\n      '5xl': '48px'\n    },\n    fontWeight: {\n      light: '300',\n      normal: '400',\n      medium: '500',\n      semibold: '600',\n      bold: '700',\n      extrabold: '800'\n    },\n    lineHeight: {\n      tight: '1.25',\n      normal: '1.5',\n      relaxed: '1.75'\n    }\n  },\n  transitions: {\n    fast: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',\n    default: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    slow: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',\n    spring: 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)'\n  },\n  breakpoints: {\n    xs: '320px',\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px'\n  },\n  zIndex: {\n    dropdown: 1000,\n    sticky: 1020,\n    fixed: 1030,\n    modal: 1040,\n    popover: 1050,\n    tooltip: 1060\n  }\n};\nexport const GlobalStyles = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  body {\n    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\n    color: ${theme.colors.textDark};\n    min-height: 100vh;\n    line-height: 1.6;\n  }\n\n  code {\n    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n      monospace;\n  }\n\n  button {\n    font-family: inherit;\n    cursor: pointer;\n    border: none;\n    outline: none;\n  }\n\n  input, textarea, select {\n    font-family: inherit;\n    outline: none;\n  }\n\n  a {\n    text-decoration: none;\n    color: inherit;\n  }\n\n  ul, ol {\n    list-style: none;\n  }\n\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n`;\n\n// Common styled components\nexport const Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n`;\nexport const Card = styled.div`\n  background: ${theme.colors.white};\n  border-radius: ${theme.borderRadius.md};\n  box-shadow: ${theme.shadows.md};\n  padding: 20px;\n  margin-bottom: 20px;\n  transition: ${theme.transitions.default};\n\n  &:hover {\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\nexport const Button = styled.button`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: ${props => {\n  switch (props.size) {\n    case 'sm':\n      return '8px 16px';\n    case 'lg':\n      return '14px 28px';\n    default:\n      return '10px 20px';\n  }\n}};\n  border-radius: ${theme.borderRadius.sm};\n  font-size: ${props => {\n  switch (props.size) {\n    case 'sm':\n      return '12px';\n    case 'lg':\n      return '16px';\n    default:\n      return '14px';\n  }\n}};\n  font-weight: 600;\n  transition: ${theme.transitions.default};\n  text-align: center;\n  letter-spacing: 0.5px;\n  box-shadow: ${theme.shadows.sm};\n  width: ${props => props.fullWidth ? '100%' : 'auto'};\n\n  ${props => {\n  switch (props.variant) {\n    case 'secondary':\n      return `\n          background: linear-gradient(135deg, ${theme.colors.secondary}, ${theme.colors.secondaryDark});\n          color: ${theme.colors.textDark};\n          &:hover {\n            background: linear-gradient(135deg, ${theme.colors.secondaryDark}, ${theme.colors.secondary});\n            transform: translateY(-2px);\n            box-shadow: ${theme.shadows.md};\n          }\n        `;\n    case 'outline':\n      return `\n          background: transparent;\n          color: ${theme.colors.primary};\n          border: 1px solid ${theme.colors.primary};\n          &:hover {\n            background: ${theme.colors.primary};\n            color: ${theme.colors.white};\n          }\n        `;\n    case 'danger':\n      return `\n          background: ${theme.colors.error};\n          color: ${theme.colors.white};\n          &:hover {\n            background: #c82333;\n            transform: translateY(-2px);\n            box-shadow: ${theme.shadows.md};\n          }\n        `;\n    default:\n      return `\n          background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryDark});\n          color: ${theme.colors.white};\n          &:hover {\n            background: linear-gradient(135deg, ${theme.colors.primaryDark}, ${theme.colors.primary});\n            transform: translateY(-2px);\n            box-shadow: ${theme.shadows.md};\n          }\n        `;\n  }\n}}\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n`;\nexport const Input = styled.input`\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ${theme.colors.mediumGray};\n  border-radius: ${theme.borderRadius.sm};\n  font-size: 14px;\n  transition: ${theme.transitions.default};\n  background-color: ${theme.colors.offWhite};\n  color: ${theme.colors.textDark};\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);\n\n  &:focus {\n    border-color: ${theme.colors.primary};\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    background-color: ${theme.colors.white};\n  }\n\n  &::placeholder {\n    color: ${theme.colors.textLight};\n  }\n`;\nexport const Select = styled.select`\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ${theme.colors.mediumGray};\n  border-radius: ${theme.borderRadius.sm};\n  font-size: 14px;\n  transition: ${theme.transitions.default};\n  background-color: ${theme.colors.offWhite};\n  color: ${theme.colors.textDark};\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);\n\n  &:focus {\n    border-color: ${theme.colors.primary};\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    background-color: ${theme.colors.white};\n  }\n`;\nexport const Label = styled.label`\n  display: block;\n  margin-bottom: 6px;\n  font-weight: 500;\n  color: ${theme.colors.textMedium};\n  font-size: 14px;\n  transition: ${theme.transitions.default};\n`;\nexport const FormGroup = styled.div`\n  margin-bottom: 20px;\n\n  &:focus-within ${Label} {\n    color: ${theme.colors.primary};\n  }\n`;\nexport const ErrorMessage = styled.div`\n  color: ${theme.colors.error};\n  font-size: 12px;\n  margin-top: 5px;\n`;\nexport const LoadingSpinner = styled.div`\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid ${theme.colors.primary};\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;", "map": {"version": 3, "names": ["styled", "createGlobalStyle", "theme", "colors", "primary", "primaryDark", "primaryLight", "primaryGradient", "secondary", "secondaryDark", "secondaryLight", "secondaryGradient", "white", "offWhite", "lightGray", "mediumGray", "<PERSON><PERSON><PERSON>", "textPrimary", "textSecondary", "textTertiary", "textInverse", "error", "errorLight", "success", "successLight", "warning", "warningLight", "info", "infoLight", "background", "backgroundSecondary", "backgroundTertiary", "border", "borderLight", "borderDark", "glass", "glassDark", "shadows", "xs", "sm", "md", "lg", "xl", "inner", "neumorphism", "borderRadius", "none", "full", "spacing", "typography", "fontFamily", "sans", "mono", "fontSize", "base", "fontWeight", "light", "normal", "medium", "semibold", "bold", "extrabold", "lineHeight", "tight", "relaxed", "transitions", "fast", "default", "slow", "spring", "breakpoints", "zIndex", "dropdown", "sticky", "fixed", "modal", "popover", "tooltip", "GlobalStyles", "textDark", "Container", "div", "Card", "<PERSON><PERSON>", "button", "props", "size", "fullWidth", "variant", "Input", "input", "textLight", "Select", "select", "Label", "label", "textMedium", "FormGroup", "ErrorMessage", "LoadingSpinner"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/styles/GlobalStyles.ts"], "sourcesContent": ["import styled, { createGlobalStyle } from 'styled-components';\n\nexport const theme = {\n  colors: {\n    // Primary Brand Colors\n    primary: '#007E3A',\n    primaryDark: '#005a2a',\n    primaryLight: '#4CAF50',\n    primaryGradient: 'linear-gradient(135deg, #007E3A 0%, #4CAF50 100%)',\n\n    // Secondary Colors\n    secondary: '#FFD100',\n    secondaryDark: '#e6bc00',\n    secondaryLight: '#FFF176',\n    secondaryGradient: 'linear-gradient(135deg, #FFD100 0%, #FFF176 100%)',\n\n    // Neutral Colors\n    white: '#FFFFFF',\n    offWhite: '#FAFAFA',\n    lightGray: '#F5F7FA',\n    mediumGray: '#E2E8F0',\n    darkGray: '#64748B',\n\n    // Text Colors\n    textPrimary: '#1E293B',\n    textSecondary: '#475569',\n    textTertiary: '#94A3B8',\n    textInverse: '#FFFFFF',\n\n    // Status Colors\n    error: '#EF4444',\n    errorLight: '#FEF2F2',\n    success: '#10B981',\n    successLight: '#F0FDF4',\n    warning: '#F59E0B',\n    warningLight: '#FFFBEB',\n    info: '#3B82F6',\n    infoLight: '#EFF6FF',\n\n    // Background Colors\n    background: '#FFFFFF',\n    backgroundSecondary: '#F8FAFC',\n    backgroundTertiary: '#F1F5F9',\n\n    // Border Colors\n    border: '#E2E8F0',\n    borderLight: '#F1F5F9',\n    borderDark: '#CBD5E1',\n\n    // Glass Effect Colors\n    glass: 'rgba(255, 255, 255, 0.25)',\n    glassDark: 'rgba(0, 0, 0, 0.1)',\n  },\n  shadows: {\n    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',\n    glass: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',\n    neumorphism: '20px 20px 60px #d1d9e6, -20px -20px 60px #ffffff',\n  },\n  borderRadius: {\n    none: '0',\n    xs: '2px',\n    sm: '4px',\n    md: '8px',\n    lg: '12px',\n    xl: '16px',\n    '2xl': '24px',\n    '3xl': '32px',\n    full: '9999px',\n  },\n  spacing: {\n    xs: '4px',\n    sm: '8px',\n    md: '16px',\n    lg: '24px',\n    xl: '32px',\n    '2xl': '48px',\n    '3xl': '64px',\n    '4xl': '96px',\n  },\n  typography: {\n    fontFamily: {\n      sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],\n      mono: ['JetBrains Mono', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],\n    },\n    fontSize: {\n      xs: '12px',\n      sm: '14px',\n      base: '16px',\n      lg: '18px',\n      xl: '20px',\n      '2xl': '24px',\n      '3xl': '30px',\n      '4xl': '36px',\n      '5xl': '48px',\n    },\n    fontWeight: {\n      light: '300',\n      normal: '400',\n      medium: '500',\n      semibold: '600',\n      bold: '700',\n      extrabold: '800',\n    },\n    lineHeight: {\n      tight: '1.25',\n      normal: '1.5',\n      relaxed: '1.75',\n    },\n  },\n  transitions: {\n    fast: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',\n    default: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    slow: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',\n    spring: 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)',\n  },\n  breakpoints: {\n    xs: '320px',\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px',\n  },\n  zIndex: {\n    dropdown: 1000,\n    sticky: 1020,\n    fixed: 1030,\n    modal: 1040,\n    popover: 1050,\n    tooltip: 1060,\n  },\n};\n\nexport const GlobalStyles = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  body {\n    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\n    color: ${theme.colors.textDark};\n    min-height: 100vh;\n    line-height: 1.6;\n  }\n\n  code {\n    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n      monospace;\n  }\n\n  button {\n    font-family: inherit;\n    cursor: pointer;\n    border: none;\n    outline: none;\n  }\n\n  input, textarea, select {\n    font-family: inherit;\n    outline: none;\n  }\n\n  a {\n    text-decoration: none;\n    color: inherit;\n  }\n\n  ul, ol {\n    list-style: none;\n  }\n\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n`;\n\n// Common styled components\nexport const Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n`;\n\nexport const Card = styled.div`\n  background: ${theme.colors.white};\n  border-radius: ${theme.borderRadius.md};\n  box-shadow: ${theme.shadows.md};\n  padding: 20px;\n  margin-bottom: 20px;\n  transition: ${theme.transitions.default};\n\n  &:hover {\n    box-shadow: ${theme.shadows.lg};\n  }\n`;\n\nexport const Button = styled.button<{\n  variant?: 'primary' | 'secondary' | 'outline' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  fullWidth?: boolean;\n}>`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: ${props => {\n    switch (props.size) {\n      case 'sm': return '8px 16px';\n      case 'lg': return '14px 28px';\n      default: return '10px 20px';\n    }\n  }};\n  border-radius: ${theme.borderRadius.sm};\n  font-size: ${props => {\n    switch (props.size) {\n      case 'sm': return '12px';\n      case 'lg': return '16px';\n      default: return '14px';\n    }\n  }};\n  font-weight: 600;\n  transition: ${theme.transitions.default};\n  text-align: center;\n  letter-spacing: 0.5px;\n  box-shadow: ${theme.shadows.sm};\n  width: ${props => props.fullWidth ? '100%' : 'auto'};\n\n  ${props => {\n    switch (props.variant) {\n      case 'secondary':\n        return `\n          background: linear-gradient(135deg, ${theme.colors.secondary}, ${theme.colors.secondaryDark});\n          color: ${theme.colors.textDark};\n          &:hover {\n            background: linear-gradient(135deg, ${theme.colors.secondaryDark}, ${theme.colors.secondary});\n            transform: translateY(-2px);\n            box-shadow: ${theme.shadows.md};\n          }\n        `;\n      case 'outline':\n        return `\n          background: transparent;\n          color: ${theme.colors.primary};\n          border: 1px solid ${theme.colors.primary};\n          &:hover {\n            background: ${theme.colors.primary};\n            color: ${theme.colors.white};\n          }\n        `;\n      case 'danger':\n        return `\n          background: ${theme.colors.error};\n          color: ${theme.colors.white};\n          &:hover {\n            background: #c82333;\n            transform: translateY(-2px);\n            box-shadow: ${theme.shadows.md};\n          }\n        `;\n      default:\n        return `\n          background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryDark});\n          color: ${theme.colors.white};\n          &:hover {\n            background: linear-gradient(135deg, ${theme.colors.primaryDark}, ${theme.colors.primary});\n            transform: translateY(-2px);\n            box-shadow: ${theme.shadows.md};\n          }\n        `;\n    }\n  }}\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none !important;\n  }\n`;\n\nexport const Input = styled.input`\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ${theme.colors.mediumGray};\n  border-radius: ${theme.borderRadius.sm};\n  font-size: 14px;\n  transition: ${theme.transitions.default};\n  background-color: ${theme.colors.offWhite};\n  color: ${theme.colors.textDark};\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);\n\n  &:focus {\n    border-color: ${theme.colors.primary};\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    background-color: ${theme.colors.white};\n  }\n\n  &::placeholder {\n    color: ${theme.colors.textLight};\n  }\n`;\n\nexport const Select = styled.select`\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ${theme.colors.mediumGray};\n  border-radius: ${theme.borderRadius.sm};\n  font-size: 14px;\n  transition: ${theme.transitions.default};\n  background-color: ${theme.colors.offWhite};\n  color: ${theme.colors.textDark};\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);\n\n  &:focus {\n    border-color: ${theme.colors.primary};\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    background-color: ${theme.colors.white};\n  }\n`;\n\nexport const Label = styled.label`\n  display: block;\n  margin-bottom: 6px;\n  font-weight: 500;\n  color: ${theme.colors.textMedium};\n  font-size: 14px;\n  transition: ${theme.transitions.default};\n`;\n\nexport const FormGroup = styled.div`\n  margin-bottom: 20px;\n\n  &:focus-within ${Label} {\n    color: ${theme.colors.primary};\n  }\n`;\n\nexport const ErrorMessage = styled.div`\n  color: ${theme.colors.error};\n  font-size: 12px;\n  margin-top: 5px;\n`;\n\nexport const LoadingSpinner = styled.div`\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid ${theme.colors.primary};\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n"], "mappings": "AAAA,OAAOA,MAAM,IAAIC,iBAAiB,QAAQ,mBAAmB;AAE7D,OAAO,MAAMC,KAAK,GAAG;EACnBC,MAAM,EAAE;IACN;IACAC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,eAAe,EAAE,mDAAmD;IAEpE;IACAC,SAAS,EAAE,SAAS;IACpBC,aAAa,EAAE,SAAS;IACxBC,cAAc,EAAE,SAAS;IACzBC,iBAAiB,EAAE,mDAAmD;IAEtE;IACAC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,SAAS;IAEnB;IACAC,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,SAAS;IACxBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IAEtB;IACAC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IAEpB;IACAC,UAAU,EAAE,SAAS;IACrBC,mBAAmB,EAAE,SAAS;IAC9BC,kBAAkB,EAAE,SAAS;IAE7B;IACAC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IAErB;IACAC,KAAK,EAAE,2BAA2B;IAClCC,SAAS,EAAE;EACb,CAAC;EACDC,OAAO,EAAE;IACPC,EAAE,EAAE,iCAAiC;IACrCC,EAAE,EAAE,iEAAiE;IACrEC,EAAE,EAAE,uEAAuE;IAC3EC,EAAE,EAAE,yEAAyE;IAC7EC,EAAE,EAAE,2EAA2E;IAC/E,KAAK,EAAE,uCAAuC;IAC9CC,KAAK,EAAE,uCAAuC;IAC9CR,KAAK,EAAE,sCAAsC;IAC7CS,WAAW,EAAE;EACf,CAAC;EACDC,YAAY,EAAE;IACZC,IAAI,EAAE,GAAG;IACTR,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACV,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;IACbK,IAAI,EAAE;EACR,CAAC;EACDC,OAAO,EAAE;IACPV,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACV,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;IACb,KAAK,EAAE;EACT,CAAC;EACDO,UAAU,EAAE;IACVC,UAAU,EAAE;MACVC,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,oBAAoB,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;MACvGC,IAAI,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW;IACvG,CAAC;IACDC,QAAQ,EAAE;MACRf,EAAE,EAAE,MAAM;MACVC,EAAE,EAAE,MAAM;MACVe,IAAI,EAAE,MAAM;MACZb,EAAE,EAAE,MAAM;MACVC,EAAE,EAAE,MAAM;MACV,KAAK,EAAE,MAAM;MACb,KAAK,EAAE,MAAM;MACb,KAAK,EAAE,MAAM;MACb,KAAK,EAAE;IACT,CAAC;IACDa,UAAU,EAAE;MACVC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE;IACb,CAAC;IACDC,UAAU,EAAE;MACVC,KAAK,EAAE,MAAM;MACbN,MAAM,EAAE,KAAK;MACbO,OAAO,EAAE;IACX;EACF,CAAC;EACDC,WAAW,EAAE;IACXC,IAAI,EAAE,wCAAwC;IAC9CC,OAAO,EAAE,uCAAuC;IAChDC,IAAI,EAAE,uCAAuC;IAC7CC,MAAM,EAAE;EACV,CAAC;EACDC,WAAW,EAAE;IACXhC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,QAAQ;IACZ,KAAK,EAAE;EACT,CAAC;EACD6B,MAAM,EAAE;IACNC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG7E,iBAAiB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaC,KAAK,CAACC,MAAM,CAAC4E,QAAQ;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAGhF,MAAM,CAACiF,GAAG;AACnC;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,IAAI,GAAGlF,MAAM,CAACiF,GAAG;AAC9B,gBAAgB/E,KAAK,CAACC,MAAM,CAACS,KAAK;AAClC,mBAAmBV,KAAK,CAAC2C,YAAY,CAACL,EAAE;AACxC,gBAAgBtC,KAAK,CAACmC,OAAO,CAACG,EAAE;AAChC;AACA;AACA,gBAAgBtC,KAAK,CAAC+D,WAAW,CAACE,OAAO;AACzC;AACA;AACA,kBAAkBjE,KAAK,CAACmC,OAAO,CAACI,EAAE;AAClC;AACA,CAAC;AAED,OAAO,MAAM0C,MAAM,GAAGnF,MAAM,CAACoF,MAI3B;AACF;AACA;AACA;AACA,aAAaC,KAAK,IAAI;EAClB,QAAQA,KAAK,CAACC,IAAI;IAChB,KAAK,IAAI;MAAE,OAAO,UAAU;IAC5B,KAAK,IAAI;MAAE,OAAO,WAAW;IAC7B;MAAS,OAAO,WAAW;EAC7B;AACF,CAAC;AACH,mBAAmBpF,KAAK,CAAC2C,YAAY,CAACN,EAAE;AACxC,eAAe8C,KAAK,IAAI;EACpB,QAAQA,KAAK,CAACC,IAAI;IAChB,KAAK,IAAI;MAAE,OAAO,MAAM;IACxB,KAAK,IAAI;MAAE,OAAO,MAAM;IACxB;MAAS,OAAO,MAAM;EACxB;AACF,CAAC;AACH;AACA,gBAAgBpF,KAAK,CAAC+D,WAAW,CAACE,OAAO;AACzC;AACA;AACA,gBAAgBjE,KAAK,CAACmC,OAAO,CAACE,EAAE;AAChC,WAAW8C,KAAK,IAAIA,KAAK,CAACE,SAAS,GAAG,MAAM,GAAG,MAAM;AACrD;AACA,IAAIF,KAAK,IAAI;EACT,QAAQA,KAAK,CAACG,OAAO;IACnB,KAAK,WAAW;MACd,OAAO;AACf,gDAAgDtF,KAAK,CAACC,MAAM,CAACK,SAAS,KAAKN,KAAK,CAACC,MAAM,CAACM,aAAa;AACrG,mBAAmBP,KAAK,CAACC,MAAM,CAAC4E,QAAQ;AACxC;AACA,kDAAkD7E,KAAK,CAACC,MAAM,CAACM,aAAa,KAAKP,KAAK,CAACC,MAAM,CAACK,SAAS;AACvG;AACA,0BAA0BN,KAAK,CAACmC,OAAO,CAACG,EAAE;AAC1C;AACA,SAAS;IACH,KAAK,SAAS;MACZ,OAAO;AACf;AACA,mBAAmBtC,KAAK,CAACC,MAAM,CAACC,OAAO;AACvC,8BAA8BF,KAAK,CAACC,MAAM,CAACC,OAAO;AAClD;AACA,0BAA0BF,KAAK,CAACC,MAAM,CAACC,OAAO;AAC9C,qBAAqBF,KAAK,CAACC,MAAM,CAACS,KAAK;AACvC;AACA,SAAS;IACH,KAAK,QAAQ;MACX,OAAO;AACf,wBAAwBV,KAAK,CAACC,MAAM,CAACkB,KAAK;AAC1C,mBAAmBnB,KAAK,CAACC,MAAM,CAACS,KAAK;AACrC;AACA;AACA;AACA,0BAA0BV,KAAK,CAACmC,OAAO,CAACG,EAAE;AAC1C;AACA,SAAS;IACH;MACE,OAAO;AACf,gDAAgDtC,KAAK,CAACC,MAAM,CAACC,OAAO,KAAKF,KAAK,CAACC,MAAM,CAACE,WAAW;AACjG,mBAAmBH,KAAK,CAACC,MAAM,CAACS,KAAK;AACrC;AACA,kDAAkDV,KAAK,CAACC,MAAM,CAACE,WAAW,KAAKH,KAAK,CAACC,MAAM,CAACC,OAAO;AACnG;AACA,0BAA0BF,KAAK,CAACmC,OAAO,CAACG,EAAE;AAC1C;AACA,SAAS;EACL;AACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMiD,KAAK,GAAGzF,MAAM,CAAC0F,KAAK;AACjC;AACA;AACA,sBAAsBxF,KAAK,CAACC,MAAM,CAACY,UAAU;AAC7C,mBAAmBb,KAAK,CAAC2C,YAAY,CAACN,EAAE;AACxC;AACA,gBAAgBrC,KAAK,CAAC+D,WAAW,CAACE,OAAO;AACzC,sBAAsBjE,KAAK,CAACC,MAAM,CAACU,QAAQ;AAC3C,WAAWX,KAAK,CAACC,MAAM,CAAC4E,QAAQ;AAChC;AACA;AACA;AACA,oBAAoB7E,KAAK,CAACC,MAAM,CAACC,OAAO;AACxC;AACA,wBAAwBF,KAAK,CAACC,MAAM,CAACS,KAAK;AAC1C;AACA;AACA;AACA,aAAaV,KAAK,CAACC,MAAM,CAACwF,SAAS;AACnC;AACA,CAAC;AAED,OAAO,MAAMC,MAAM,GAAG5F,MAAM,CAAC6F,MAAM;AACnC;AACA;AACA,sBAAsB3F,KAAK,CAACC,MAAM,CAACY,UAAU;AAC7C,mBAAmBb,KAAK,CAAC2C,YAAY,CAACN,EAAE;AACxC;AACA,gBAAgBrC,KAAK,CAAC+D,WAAW,CAACE,OAAO;AACzC,sBAAsBjE,KAAK,CAACC,MAAM,CAACU,QAAQ;AAC3C,WAAWX,KAAK,CAACC,MAAM,CAAC4E,QAAQ;AAChC;AACA;AACA;AACA,oBAAoB7E,KAAK,CAACC,MAAM,CAACC,OAAO;AACxC;AACA,wBAAwBF,KAAK,CAACC,MAAM,CAACS,KAAK;AAC1C;AACA,CAAC;AAED,OAAO,MAAMkF,KAAK,GAAG9F,MAAM,CAAC+F,KAAK;AACjC;AACA;AACA;AACA,WAAW7F,KAAK,CAACC,MAAM,CAAC6F,UAAU;AAClC;AACA,gBAAgB9F,KAAK,CAAC+D,WAAW,CAACE,OAAO;AACzC,CAAC;AAED,OAAO,MAAM8B,SAAS,GAAGjG,MAAM,CAACiF,GAAG;AACnC;AACA;AACA,mBAAmBa,KAAK;AACxB,aAAa5F,KAAK,CAACC,MAAM,CAACC,OAAO;AACjC;AACA,CAAC;AAED,OAAO,MAAM8F,YAAY,GAAGlG,MAAM,CAACiF,GAAG;AACtC,WAAW/E,KAAK,CAACC,MAAM,CAACkB,KAAK;AAC7B;AACA;AACA,CAAC;AAED,OAAO,MAAM8E,cAAc,GAAGnG,MAAM,CAACiF,GAAG;AACxC;AACA;AACA;AACA;AACA,0BAA0B/E,KAAK,CAACC,MAAM,CAACC,OAAO;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}