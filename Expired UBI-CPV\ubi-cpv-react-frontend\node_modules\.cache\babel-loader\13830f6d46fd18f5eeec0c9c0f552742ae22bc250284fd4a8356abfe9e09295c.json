{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Admin\\\\CreateLead.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n  margin-left: 20px;\n`;\n_c3 = Title;\nconst FormGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n_c4 = FormGrid;\nconst AddressSection = styled.div`\n  margin-top: 20px;\n`;\n_c5 = AddressSection;\nconst AddressCard = styled(Card)`\n  margin-bottom: 15px;\n  position: relative;\n`;\n_c6 = AddressCard;\nconst RemoveButton = styled(Button)`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  padding: 5px 10px;\n  font-size: 12px;\n`;\n_c7 = RemoveButton;\nconst AddAddressButton = styled(Button)`\n  margin-top: 10px;\n`;\n_c8 = AddAddressButton;\nconst CreateLead = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [documentTypes, setDocumentTypes] = useState([]);\n  const [documents, setDocuments] = useState([]);\n  const [uploadProgress, setUploadProgress] = useState({});\n  const [formData, setFormData] = useState({\n    customerName: '',\n    mobileNumber: '',\n    loanType: ''\n  });\n  const [addresses, setAddresses] = useState([{\n    type: 'Residential',\n    address: '',\n    pincode: '',\n    state: '',\n    district: '',\n    landmark: ''\n  }]);\n  useEffect(() => {\n    loadDocumentTypes();\n  }, []);\n  const loadDocumentTypes = async () => {\n    try {\n      setLoading(true);\n      const types = await apiService.getDocumentTypes();\n      setDocumentTypes(types);\n    } catch (error) {\n      console.error('Error loading document types:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleAddressChange = (index, field, value) => {\n    setAddresses(prev => prev.map((addr, i) => i === index ? {\n      ...addr,\n      [field]: value\n    } : addr));\n  };\n  const addAddress = () => {\n    setAddresses(prev => [...prev, {\n      type: 'Residential',\n      address: '',\n      pincode: '',\n      state: '',\n      district: '',\n      landmark: ''\n    }]);\n  };\n  const removeAddress = index => {\n    if (addresses.length > 1) {\n      setAddresses(prev => prev.filter((_, i) => i !== index));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.customerName.trim()) {\n      newErrors.customerName = 'Customer name is required';\n    }\n    if (!formData.mobileNumber.trim()) {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else if (!/^\\d{10}$/.test(formData.mobileNumber)) {\n      newErrors.mobileNumber = 'Mobile number must be 10 digits';\n    }\n    if (!formData.loanType) {\n      newErrors.loanType = 'Loan type is required';\n    }\n\n    // Validate addresses\n    addresses.forEach((addr, index) => {\n      if (!addr.address.trim()) {\n        newErrors[`address_${index}`] = 'Address is required';\n      }\n      if (!addr.pincode.trim()) {\n        newErrors[`pincode_${index}`] = 'Pincode is required';\n      } else if (!/^\\d{6}$/.test(addr.pincode)) {\n        newErrors[`pincode_${index}`] = 'Pincode must be 6 digits';\n      }\n      if (!addr.state.trim()) {\n        newErrors[`state_${index}`] = 'State is required';\n      }\n      if (!addr.district.trim()) {\n        newErrors[`district_${index}`] = 'District is required';\n      }\n    });\n\n    // Validate documents\n    const requiredDocuments = documents.filter(doc => doc.isRequired);\n    const validDocuments = documents.filter(doc => doc.status !== 'error');\n    if (documents.length > 0 && validDocuments.length === 0) {\n      newErrors.documents = 'At least one valid document is required';\n    }\n\n    // Check for documents with errors\n    const documentsWithErrors = documents.filter(doc => doc.status === 'error');\n    if (documentsWithErrors.length > 0) {\n      newErrors.documents = 'Please fix document upload errors before submitting';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setSubmitting(true);\n    try {\n      const leadData = {\n        customerName: formData.customerName,\n        mobileNumber: formData.mobileNumber,\n        loanType: formData.loanType,\n        addresses: addresses\n      };\n      if (documents.length > 0) {\n        // Create lead with documents\n        const result = await apiService.createLeadWithDocuments(leadData, documents, (documentIndex, progress) => {\n          setUploadProgress(prev => ({\n            ...prev,\n            [documentIndex]: progress\n          }));\n\n          // Update document status\n          setDocuments(prev => prev.map((doc, index) => index === documentIndex ? {\n            ...doc,\n            status: 'uploading',\n            uploadProgress: progress\n          } : doc));\n        });\n\n        // Update document statuses based on upload results\n        setDocuments(prev => prev.map((doc, index) => {\n          var _result$uploadResults, _result$uploadResults2, _result$uploadResults3;\n          return {\n            ...doc,\n            status: (_result$uploadResults = result.uploadResults[index]) !== null && _result$uploadResults !== void 0 && _result$uploadResults.success ? 'completed' : 'error',\n            error: (_result$uploadResults2 = result.uploadResults[index]) !== null && _result$uploadResults2 !== void 0 && _result$uploadResults2.success ? undefined : (_result$uploadResults3 = result.uploadResults[index]) === null || _result$uploadResults3 === void 0 ? void 0 : _result$uploadResults3.message,\n            uploadResult: result.uploadResults[index]\n          };\n        }));\n        const failedUploads = result.uploadResults.filter(r => !r.success);\n        if (failedUploads.length > 0) {\n          alert(`Lead created successfully, but ${failedUploads.length} document(s) failed to upload. You can retry uploading them later.`);\n        } else {\n          alert('Lead and all documents uploaded successfully!');\n        }\n      } else {\n        // Create lead without documents\n        await apiService.createLead(leadData);\n        alert('Lead created successfully!');\n      }\n      navigate('/admin/dashboard');\n    } catch (error) {\n      console.error('Error creating lead:', error);\n      alert('Failed to create lead. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: handleBack,\n        children: \"\\u2190 Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: \"Create New Lead\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Customer Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGrid, {\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"customerName\",\n              children: \"Customer Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"customerName\",\n              name: \"customerName\",\n              value: formData.customerName,\n              onChange: handleInputChange,\n              placeholder: \"Enter customer full name\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), errors.customerName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.customerName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"mobileNumber\",\n              children: \"Mobile Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"tel\",\n              id: \"mobileNumber\",\n              name: \"mobileNumber\",\n              value: formData.mobileNumber,\n              onChange: handleInputChange,\n              placeholder: \"Enter 10-digit mobile number\",\n              maxLength: 10,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), errors.mobileNumber && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.mobileNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"loanType\",\n              children: \"Loan Type *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              id: \"loanType\",\n              name: \"loanType\",\n              value: formData.loanType,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Loan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Personal Loan\",\n                children: \"Personal Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Home Loan\",\n                children: \"Home Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Car Loan\",\n                children: \"Car Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Business Loan\",\n                children: \"Business Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Education Loan\",\n                children: \"Education Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Gold Loan\",\n                children: \"Gold Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), errors.loanType && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.loanType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AddressSection, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Address Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), addresses.map((address, index) => /*#__PURE__*/_jsxDEV(AddressCard, {\n          children: [addresses.length > 1 && /*#__PURE__*/_jsxDEV(RemoveButton, {\n            type: \"button\",\n            variant: \"danger\",\n            size: \"sm\",\n            onClick: () => removeAddress(index),\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '15px',\n              color: '#555'\n            },\n            children: [\"Address \", index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGrid, {\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Address Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: address.type,\n                onChange: e => handleAddressChange(index, 'type', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Residential\",\n                  children: \"Residential\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Office\",\n                  children: \"Office\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Business\",\n                  children: \"Business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"State *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                value: address.state,\n                onChange: e => handleAddressChange(index, 'state', e.target.value),\n                placeholder: \"Enter state\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), errors[`state_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors[`state_${index}`]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 48\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"District *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                value: address.district,\n                onChange: e => handleAddressChange(index, 'district', e.target.value),\n                placeholder: \"Enter district\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), errors[`district_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors[`district_${index}`]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Pincode *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                value: address.pincode,\n                onChange: e => handleAddressChange(index, 'pincode', e.target.value),\n                placeholder: \"Enter 6-digit pincode\",\n                maxLength: 6,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), errors[`pincode_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors[`pincode_${index}`]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 50\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Full Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              value: address.address,\n              onChange: e => handleAddressChange(index, 'address', e.target.value),\n              placeholder: \"Enter complete address\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), errors[`address_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors[`address_${index}`]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 48\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Landmark\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              value: address.landmark,\n              onChange: e => handleAddressChange(index, 'landmark', e.target.value),\n              placeholder: \"Enter nearby landmark (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(AddAddressButton, {\n          type: \"button\",\n          variant: \"outline\",\n          onClick: addAddress,\n          children: \"+ Add Another Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '30px',\n          display: 'flex',\n          gap: '10px',\n          justifyContent: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          variant: \"outline\",\n          onClick: handleBack,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? 'Creating...' : 'Create Lead'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateLead, \"myC3upUHiKQ4X8WleE+OaYlgL4U=\", false, function () {\n  return [useNavigate];\n});\n_c9 = CreateLead;\nexport default CreateLead;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"FormGrid\");\n$RefreshReg$(_c5, \"AddressSection\");\n$RefreshReg$(_c6, \"AddressCard\");\n$RefreshReg$(_c7, \"RemoveButton\");\n$RefreshReg$(_c8, \"AddAddressButton\");\n$RefreshReg$(_c9, \"CreateLead\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "Input", "Select", "FormGroup", "Label", "ErrorMessage", "apiService", "jsxDEV", "_jsxDEV", "Container", "div", "_c", "Header", "props", "theme", "colors", "mediumGray", "_c2", "Title", "h1", "primary", "_c3", "FormGrid", "_c4", "AddressSection", "_c5", "AddressCard", "_c6", "RemoveButton", "_c7", "AddAddressButton", "_c8", "CreateLead", "_s", "navigate", "loading", "setLoading", "submitting", "setSubmitting", "errors", "setErrors", "documentTypes", "setDocumentTypes", "documents", "setDocuments", "uploadProgress", "setUploadProgress", "formData", "setFormData", "customerName", "mobileNumber", "loanType", "addresses", "setAdd<PERSON>", "type", "address", "pincode", "state", "district", "landmark", "loadDocumentTypes", "types", "getDocumentTypes", "error", "console", "handleInputChange", "e", "name", "value", "target", "prev", "handleAddressChange", "index", "field", "map", "addr", "i", "addAddress", "removeAddress", "length", "filter", "_", "validateForm", "newErrors", "trim", "test", "for<PERSON>ach", "requiredDocuments", "doc", "isRequired", "validDocuments", "status", "documentsWithErrors", "Object", "keys", "handleSubmit", "preventDefault", "leadData", "result", "createLeadWithDocuments", "documentIndex", "progress", "_result$uploadResults", "_result$uploadResults2", "_result$uploadResults3", "uploadResults", "success", "undefined", "message", "uploadResult", "failedUploads", "r", "alert", "createLead", "handleBack", "children", "variant", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "style", "marginBottom", "color", "htmlFor", "id", "onChange", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "size", "marginTop", "display", "gap", "justifyContent", "disabled", "_c9", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/CreateLead.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, CreateLeadRequest, DocumentUpload, DocumentType, UploadProgress } from '../../services/apiService';\nimport DocumentUploadComponent from '../Common/DocumentUpload';\n\nconst Container = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n  margin-left: 20px;\n`;\n\nconst FormGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n\nconst AddressSection = styled.div`\n  margin-top: 20px;\n`;\n\nconst AddressCard = styled(Card)`\n  margin-bottom: 15px;\n  position: relative;\n`;\n\nconst RemoveButton = styled(Button)`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  padding: 5px 10px;\n  font-size: 12px;\n`;\n\nconst AddAddressButton = styled(Button)`\n  margin-top: 10px;\n`;\n\ninterface Address {\n  type: string;\n  address: string;\n  pincode: string;\n  state: string;\n  district: string;\n  landmark?: string;\n}\n\nconst CreateLead: React.FC = () => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\n  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);\n  const [documents, setDocuments] = useState<DocumentUpload[]>([]);\n  const [uploadProgress, setUploadProgress] = useState<{ [key: number]: UploadProgress }>({});\n\n  const [formData, setFormData] = useState({\n    customerName: '',\n    mobileNumber: '',\n    loanType: '',\n  });\n\n  const [addresses, setAddresses] = useState<Address[]>([\n    {\n      type: 'Residential',\n      address: '',\n      pincode: '',\n      state: '',\n      district: '',\n      landmark: '',\n    }\n  ]);\n\n  useEffect(() => {\n    loadDocumentTypes();\n  }, []);\n\n  const loadDocumentTypes = async () => {\n    try {\n      setLoading(true);\n      const types = await apiService.getDocumentTypes();\n      setDocumentTypes(types);\n    } catch (error) {\n      console.error('Error loading document types:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const handleAddressChange = (index: number, field: string, value: string) => {\n    setAddresses(prev => prev.map((addr, i) =>\n      i === index ? { ...addr, [field]: value } : addr\n    ));\n  };\n\n  const addAddress = () => {\n    setAddresses(prev => [...prev, {\n      type: 'Residential',\n      address: '',\n      pincode: '',\n      state: '',\n      district: '',\n      landmark: '',\n    }]);\n  };\n\n  const removeAddress = (index: number) => {\n    if (addresses.length > 1) {\n      setAddresses(prev => prev.filter((_, i) => i !== index));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: { [key: string]: string } = {};\n\n    if (!formData.customerName.trim()) {\n      newErrors.customerName = 'Customer name is required';\n    }\n\n    if (!formData.mobileNumber.trim()) {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else if (!/^\\d{10}$/.test(formData.mobileNumber)) {\n      newErrors.mobileNumber = 'Mobile number must be 10 digits';\n    }\n\n    if (!formData.loanType) {\n      newErrors.loanType = 'Loan type is required';\n    }\n\n    // Validate addresses\n    addresses.forEach((addr, index) => {\n      if (!addr.address.trim()) {\n        newErrors[`address_${index}`] = 'Address is required';\n      }\n      if (!addr.pincode.trim()) {\n        newErrors[`pincode_${index}`] = 'Pincode is required';\n      } else if (!/^\\d{6}$/.test(addr.pincode)) {\n        newErrors[`pincode_${index}`] = 'Pincode must be 6 digits';\n      }\n      if (!addr.state.trim()) {\n        newErrors[`state_${index}`] = 'State is required';\n      }\n      if (!addr.district.trim()) {\n        newErrors[`district_${index}`] = 'District is required';\n      }\n    });\n\n    // Validate documents\n    const requiredDocuments = documents.filter(doc => doc.isRequired);\n    const validDocuments = documents.filter(doc => doc.status !== 'error');\n\n    if (documents.length > 0 && validDocuments.length === 0) {\n      newErrors.documents = 'At least one valid document is required';\n    }\n\n    // Check for documents with errors\n    const documentsWithErrors = documents.filter(doc => doc.status === 'error');\n    if (documentsWithErrors.length > 0) {\n      newErrors.documents = 'Please fix document upload errors before submitting';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setSubmitting(true);\n\n    try {\n      const leadData: CreateLeadRequest = {\n        customerName: formData.customerName,\n        mobileNumber: formData.mobileNumber,\n        loanType: formData.loanType,\n        addresses: addresses,\n      };\n\n      if (documents.length > 0) {\n        // Create lead with documents\n        const result = await apiService.createLeadWithDocuments(\n          leadData,\n          documents,\n          (documentIndex, progress) => {\n            setUploadProgress(prev => ({\n              ...prev,\n              [documentIndex]: progress\n            }));\n\n            // Update document status\n            setDocuments(prev => prev.map((doc, index) =>\n              index === documentIndex\n                ? { ...doc, status: 'uploading', uploadProgress: progress }\n                : doc\n            ));\n          }\n        );\n\n        // Update document statuses based on upload results\n        setDocuments(prev => prev.map((doc, index) => ({\n          ...doc,\n          status: result.uploadResults[index]?.success ? 'completed' : 'error',\n          error: result.uploadResults[index]?.success ? undefined : result.uploadResults[index]?.message,\n          uploadResult: result.uploadResults[index]\n        })));\n\n        const failedUploads = result.uploadResults.filter(r => !r.success);\n        if (failedUploads.length > 0) {\n          alert(`Lead created successfully, but ${failedUploads.length} document(s) failed to upload. You can retry uploading them later.`);\n        } else {\n          alert('Lead and all documents uploaded successfully!');\n        }\n      } else {\n        // Create lead without documents\n        await apiService.createLead(leadData);\n        alert('Lead created successfully!');\n      }\n\n      navigate('/admin/dashboard');\n    } catch (error) {\n      console.error('Error creating lead:', error);\n      alert('Failed to create lead. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n\n  return (\n    <Container>\n      <Header>\n        <Button variant=\"outline\" onClick={handleBack}>\n          ← Back\n        </Button>\n        <Title>Create New Lead</Title>\n      </Header>\n\n      <form onSubmit={handleSubmit}>\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Customer Information</h3>\n\n          <FormGrid>\n            <FormGroup>\n              <Label htmlFor=\"customerName\">Customer Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"customerName\"\n                name=\"customerName\"\n                value={formData.customerName}\n                onChange={handleInputChange}\n                placeholder=\"Enter customer full name\"\n                required\n              />\n              {errors.customerName && <ErrorMessage>{errors.customerName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"mobileNumber\">Mobile Number *</Label>\n              <Input\n                type=\"tel\"\n                id=\"mobileNumber\"\n                name=\"mobileNumber\"\n                value={formData.mobileNumber}\n                onChange={handleInputChange}\n                placeholder=\"Enter 10-digit mobile number\"\n                maxLength={10}\n                required\n              />\n              {errors.mobileNumber && <ErrorMessage>{errors.mobileNumber}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"loanType\">Loan Type *</Label>\n              <Select\n                id=\"loanType\"\n                name=\"loanType\"\n                value={formData.loanType}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Select Loan Type</option>\n                <option value=\"Personal Loan\">Personal Loan</option>\n                <option value=\"Home Loan\">Home Loan</option>\n                <option value=\"Car Loan\">Car Loan</option>\n                <option value=\"Business Loan\">Business Loan</option>\n                <option value=\"Education Loan\">Education Loan</option>\n                <option value=\"Gold Loan\">Gold Loan</option>\n              </Select>\n              {errors.loanType && <ErrorMessage>{errors.loanType}</ErrorMessage>}\n            </FormGroup>\n          </FormGrid>\n        </Card>\n\n        <AddressSection>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Address Information</h3>\n\n          {addresses.map((address, index) => (\n            <AddressCard key={index}>\n              {addresses.length > 1 && (\n                <RemoveButton\n                  type=\"button\"\n                  variant=\"danger\"\n                  size=\"sm\"\n                  onClick={() => removeAddress(index)}\n                >\n                  Remove\n                </RemoveButton>\n              )}\n\n              <h4 style={{ marginBottom: '15px', color: '#555' }}>\n                Address {index + 1}\n              </h4>\n\n              <FormGrid>\n                <FormGroup>\n                  <Label>Address Type</Label>\n                  <Select\n                    value={address.type}\n                    onChange={(e) => handleAddressChange(index, 'type', e.target.value)}\n                  >\n                    <option value=\"Residential\">Residential</option>\n                    <option value=\"Office\">Office</option>\n                    <option value=\"Business\">Business</option>\n                  </Select>\n                </FormGroup>\n\n                <FormGroup>\n                  <Label>State *</Label>\n                  <Input\n                    type=\"text\"\n                    value={address.state}\n                    onChange={(e) => handleAddressChange(index, 'state', e.target.value)}\n                    placeholder=\"Enter state\"\n                    required\n                  />\n                  {errors[`state_${index}`] && <ErrorMessage>{errors[`state_${index}`]}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label>District *</Label>\n                  <Input\n                    type=\"text\"\n                    value={address.district}\n                    onChange={(e) => handleAddressChange(index, 'district', e.target.value)}\n                    placeholder=\"Enter district\"\n                    required\n                  />\n                  {errors[`district_${index}`] && <ErrorMessage>{errors[`district_${index}`]}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label>Pincode *</Label>\n                  <Input\n                    type=\"text\"\n                    value={address.pincode}\n                    onChange={(e) => handleAddressChange(index, 'pincode', e.target.value)}\n                    placeholder=\"Enter 6-digit pincode\"\n                    maxLength={6}\n                    required\n                  />\n                  {errors[`pincode_${index}`] && <ErrorMessage>{errors[`pincode_${index}`]}</ErrorMessage>}\n                </FormGroup>\n              </FormGrid>\n\n              <FormGroup>\n                <Label>Full Address *</Label>\n                <Input\n                  type=\"text\"\n                  value={address.address}\n                  onChange={(e) => handleAddressChange(index, 'address', e.target.value)}\n                  placeholder=\"Enter complete address\"\n                  required\n                />\n                {errors[`address_${index}`] && <ErrorMessage>{errors[`address_${index}`]}</ErrorMessage>}\n              </FormGroup>\n\n              <FormGroup>\n                <Label>Landmark</Label>\n                <Input\n                  type=\"text\"\n                  value={address.landmark}\n                  onChange={(e) => handleAddressChange(index, 'landmark', e.target.value)}\n                  placeholder=\"Enter nearby landmark (optional)\"\n                />\n              </FormGroup>\n            </AddressCard>\n          ))}\n\n          <AddAddressButton\n            type=\"button\"\n            variant=\"outline\"\n            onClick={addAddress}\n          >\n            + Add Another Address\n          </AddAddressButton>\n        </AddressSection>\n\n        <div style={{ marginTop: '30px', display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>\n          <Button type=\"button\" variant=\"outline\" onClick={handleBack}>\n            Cancel\n          </Button>\n          <Button type=\"submit\" disabled={loading}>\n            {loading ? 'Creating...' : 'Create Lead'}\n          </Button>\n        </div>\n      </form>\n    </Container>\n  );\n};\n\nexport default CreateLead;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,QAAwB,2BAA2B;AACvH,SAASC,UAAU,QAAyE,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxH,MAAMC,SAAS,GAAGX,MAAM,CAACY,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,SAAS;AAMf,MAAMG,MAAM,GAAGd,MAAM,CAACY,GAAG;AACzB;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACC,GAAA,GANIL,MAAM;AAQZ,MAAMM,KAAK,GAAGpB,MAAM,CAACqB,EAAE;AACvB;AACA;AACA,WAAWN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C;AACA,CAAC;AAACC,GAAA,GALIH,KAAK;AAOX,MAAMI,QAAQ,GAAGxB,MAAM,CAACY,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAJID,QAAQ;AAMd,MAAME,cAAc,GAAG1B,MAAM,CAACY,GAAG;AACjC;AACA,CAAC;AAACe,GAAA,GAFID,cAAc;AAIpB,MAAME,WAAW,GAAG5B,MAAM,CAACC,IAAI,CAAC;AAChC;AACA;AACA,CAAC;AAAC4B,GAAA,GAHID,WAAW;AAKjB,MAAME,YAAY,GAAG9B,MAAM,CAACE,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,GAAA,GANID,YAAY;AAQlB,MAAME,gBAAgB,GAAGhC,MAAM,CAACE,MAAM,CAAC;AACvC;AACA,CAAC;AAAC+B,GAAA,GAFID,gBAAgB;AAatB,MAAME,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAA4B,CAAC,CAAC,CAAC;EACnE,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAmB,EAAE,CAAC;EAChE,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAoC,CAAC,CAAC,CAAC;EAE3F,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC;IACvCsD,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAY,CACpD;IACE2D,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;EAEF/D,SAAS,CAAC,MAAM;IACdgE,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,KAAK,GAAG,MAAMvD,UAAU,CAACwD,gBAAgB,CAAC,CAAC;MACjDpB,gBAAgB,CAACmB,KAAK,CAAC;IACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrB,WAAW,CAACsB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAI7B,MAAM,CAAC4B,IAAI,CAAC,EAAE;MAChB3B,SAAS,CAAC8B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMI,mBAAmB,GAAGA,CAACC,KAAa,EAAEC,KAAa,EAAEL,KAAa,KAAK;IAC3Ef,YAAY,CAACiB,IAAI,IAAIA,IAAI,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KACpCA,CAAC,KAAKJ,KAAK,GAAG;MAAE,GAAGG,IAAI;MAAE,CAACF,KAAK,GAAGL;IAAM,CAAC,GAAGO,IAC9C,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvBxB,YAAY,CAACiB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC7BhB,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMmB,aAAa,GAAIN,KAAa,IAAK;IACvC,IAAIpB,SAAS,CAAC2B,MAAM,GAAG,CAAC,EAAE;MACxB1B,YAAY,CAACiB,IAAI,IAAIA,IAAI,CAACU,MAAM,CAAC,CAACC,CAAC,EAAEL,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAoC,GAAG,CAAC,CAAC;IAE/C,IAAI,CAACpC,QAAQ,CAACE,YAAY,CAACmC,IAAI,CAAC,CAAC,EAAE;MACjCD,SAAS,CAAClC,YAAY,GAAG,2BAA2B;IACtD;IAEA,IAAI,CAACF,QAAQ,CAACG,YAAY,CAACkC,IAAI,CAAC,CAAC,EAAE;MACjCD,SAAS,CAACjC,YAAY,GAAG,2BAA2B;IACtD,CAAC,MAAM,IAAI,CAAC,UAAU,CAACmC,IAAI,CAACtC,QAAQ,CAACG,YAAY,CAAC,EAAE;MAClDiC,SAAS,CAACjC,YAAY,GAAG,iCAAiC;IAC5D;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBgC,SAAS,CAAChC,QAAQ,GAAG,uBAAuB;IAC9C;;IAEA;IACAC,SAAS,CAACkC,OAAO,CAAC,CAACX,IAAI,EAAEH,KAAK,KAAK;MACjC,IAAI,CAACG,IAAI,CAACpB,OAAO,CAAC6B,IAAI,CAAC,CAAC,EAAE;QACxBD,SAAS,CAAC,WAAWX,KAAK,EAAE,CAAC,GAAG,qBAAqB;MACvD;MACA,IAAI,CAACG,IAAI,CAACnB,OAAO,CAAC4B,IAAI,CAAC,CAAC,EAAE;QACxBD,SAAS,CAAC,WAAWX,KAAK,EAAE,CAAC,GAAG,qBAAqB;MACvD,CAAC,MAAM,IAAI,CAAC,SAAS,CAACa,IAAI,CAACV,IAAI,CAACnB,OAAO,CAAC,EAAE;QACxC2B,SAAS,CAAC,WAAWX,KAAK,EAAE,CAAC,GAAG,0BAA0B;MAC5D;MACA,IAAI,CAACG,IAAI,CAAClB,KAAK,CAAC2B,IAAI,CAAC,CAAC,EAAE;QACtBD,SAAS,CAAC,SAASX,KAAK,EAAE,CAAC,GAAG,mBAAmB;MACnD;MACA,IAAI,CAACG,IAAI,CAACjB,QAAQ,CAAC0B,IAAI,CAAC,CAAC,EAAE;QACzBD,SAAS,CAAC,YAAYX,KAAK,EAAE,CAAC,GAAG,sBAAsB;MACzD;IACF,CAAC,CAAC;;IAEF;IACA,MAAMe,iBAAiB,GAAG5C,SAAS,CAACqC,MAAM,CAACQ,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC;IACjE,MAAMC,cAAc,GAAG/C,SAAS,CAACqC,MAAM,CAACQ,GAAG,IAAIA,GAAG,CAACG,MAAM,KAAK,OAAO,CAAC;IAEtE,IAAIhD,SAAS,CAACoC,MAAM,GAAG,CAAC,IAAIW,cAAc,CAACX,MAAM,KAAK,CAAC,EAAE;MACvDI,SAAS,CAACxC,SAAS,GAAG,yCAAyC;IACjE;;IAEA;IACA,MAAMiD,mBAAmB,GAAGjD,SAAS,CAACqC,MAAM,CAACQ,GAAG,IAAIA,GAAG,CAACG,MAAM,KAAK,OAAO,CAAC;IAC3E,IAAIC,mBAAmB,CAACb,MAAM,GAAG,CAAC,EAAE;MAClCI,SAAS,CAACxC,SAAS,GAAG,qDAAqD;IAC7E;IAEAH,SAAS,CAAC2C,SAAS,CAAC;IACpB,OAAOU,MAAM,CAACC,IAAI,CAACX,SAAS,CAAC,CAACJ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAO7B,CAAkB,IAAK;IACjDA,CAAC,CAAC8B,cAAc,CAAC,CAAC;IAElB,IAAI,CAACd,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA5C,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAM2D,QAA2B,GAAG;QAClChD,YAAY,EAAEF,QAAQ,CAACE,YAAY;QACnCC,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BC,SAAS,EAAEA;MACb,CAAC;MAED,IAAIT,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;QACxB;QACA,MAAMmB,MAAM,GAAG,MAAM5F,UAAU,CAAC6F,uBAAuB,CACrDF,QAAQ,EACRtD,SAAS,EACT,CAACyD,aAAa,EAAEC,QAAQ,KAAK;UAC3BvD,iBAAiB,CAACwB,IAAI,KAAK;YACzB,GAAGA,IAAI;YACP,CAAC8B,aAAa,GAAGC;UACnB,CAAC,CAAC,CAAC;;UAEH;UACAzD,YAAY,CAAC0B,IAAI,IAAIA,IAAI,CAACI,GAAG,CAAC,CAACc,GAAG,EAAEhB,KAAK,KACvCA,KAAK,KAAK4B,aAAa,GACnB;YAAE,GAAGZ,GAAG;YAAEG,MAAM,EAAE,WAAW;YAAE9C,cAAc,EAAEwD;UAAS,CAAC,GACzDb,GACN,CAAC,CAAC;QACJ,CACF,CAAC;;QAED;QACA5C,YAAY,CAAC0B,IAAI,IAAIA,IAAI,CAACI,GAAG,CAAC,CAACc,GAAG,EAAEhB,KAAK;UAAA,IAAA8B,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAAA,OAAM;YAC7C,GAAGhB,GAAG;YACNG,MAAM,EAAE,CAAAW,qBAAA,GAAAJ,MAAM,CAACO,aAAa,CAACjC,KAAK,CAAC,cAAA8B,qBAAA,eAA3BA,qBAAA,CAA6BI,OAAO,GAAG,WAAW,GAAG,OAAO;YACpE3C,KAAK,EAAE,CAAAwC,sBAAA,GAAAL,MAAM,CAACO,aAAa,CAACjC,KAAK,CAAC,cAAA+B,sBAAA,eAA3BA,sBAAA,CAA6BG,OAAO,GAAGC,SAAS,IAAAH,sBAAA,GAAGN,MAAM,CAACO,aAAa,CAACjC,KAAK,CAAC,cAAAgC,sBAAA,uBAA3BA,sBAAA,CAA6BI,OAAO;YAC9FC,YAAY,EAAEX,MAAM,CAACO,aAAa,CAACjC,KAAK;UAC1C,CAAC;QAAA,CAAC,CAAC,CAAC;QAEJ,MAAMsC,aAAa,GAAGZ,MAAM,CAACO,aAAa,CAACzB,MAAM,CAAC+B,CAAC,IAAI,CAACA,CAAC,CAACL,OAAO,CAAC;QAClE,IAAII,aAAa,CAAC/B,MAAM,GAAG,CAAC,EAAE;UAC5BiC,KAAK,CAAC,kCAAkCF,aAAa,CAAC/B,MAAM,oEAAoE,CAAC;QACnI,CAAC,MAAM;UACLiC,KAAK,CAAC,+CAA+C,CAAC;QACxD;MACF,CAAC,MAAM;QACL;QACA,MAAM1G,UAAU,CAAC2G,UAAU,CAAChB,QAAQ,CAAC;QACrCe,KAAK,CAAC,4BAA4B,CAAC;MACrC;MAEA9E,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CiD,KAAK,CAAC,0CAA0C,CAAC;IACnD,CAAC,SAAS;MACR1E,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM4E,UAAU,GAAGA,CAAA,KAAM;IACvBhF,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,oBACE1B,OAAA,CAACC,SAAS;IAAA0G,QAAA,gBACR3G,OAAA,CAACI,MAAM;MAAAuG,QAAA,gBACL3G,OAAA,CAACR,MAAM;QAACoH,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEH,UAAW;QAAAC,QAAA,EAAC;MAE/C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjH,OAAA,CAACU,KAAK;QAAAiG,QAAA,EAAC;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAETjH,OAAA;MAAMkH,QAAQ,EAAE3B,YAAa;MAAAoB,QAAA,gBAC3B3G,OAAA,CAACT,IAAI;QAAAoH,QAAA,gBACH3G,OAAA;UAAImH,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhFjH,OAAA,CAACc,QAAQ;UAAA6F,QAAA,gBACP3G,OAAA,CAACL,SAAS;YAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;cAAC0H,OAAO,EAAC,cAAc;cAAAX,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrDjH,OAAA,CAACP,KAAK;cACJqD,IAAI,EAAC,MAAM;cACXyE,EAAE,EAAC,cAAc;cACjB5D,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAErB,QAAQ,CAACE,YAAa;cAC7B+E,QAAQ,EAAE/D,iBAAkB;cAC5BgE,WAAW,EAAC,0BAA0B;cACtCC,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDlF,MAAM,CAACU,YAAY,iBAAIzC,OAAA,CAACH,YAAY;cAAA8G,QAAA,EAAE5E,MAAM,CAACU;YAAY;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEZjH,OAAA,CAACL,SAAS;YAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;cAAC0H,OAAO,EAAC,cAAc;cAAAX,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrDjH,OAAA,CAACP,KAAK;cACJqD,IAAI,EAAC,KAAK;cACVyE,EAAE,EAAC,cAAc;cACjB5D,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAErB,QAAQ,CAACG,YAAa;cAC7B8E,QAAQ,EAAE/D,iBAAkB;cAC5BgE,WAAW,EAAC,8BAA8B;cAC1CE,SAAS,EAAE,EAAG;cACdD,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDlF,MAAM,CAACW,YAAY,iBAAI1C,OAAA,CAACH,YAAY;cAAA8G,QAAA,EAAE5E,MAAM,CAACW;YAAY;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEZjH,OAAA,CAACL,SAAS;YAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;cAAC0H,OAAO,EAAC,UAAU;cAAAX,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CjH,OAAA,CAACN,MAAM;cACL6H,EAAE,EAAC,UAAU;cACb5D,IAAI,EAAC,UAAU;cACfC,KAAK,EAAErB,QAAQ,CAACI,QAAS;cACzB6E,QAAQ,EAAE/D,iBAAkB;cAC5BiE,QAAQ;cAAAf,QAAA,gBAER3G,OAAA;gBAAQ4D,KAAK,EAAC,EAAE;gBAAA+C,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CjH,OAAA;gBAAQ4D,KAAK,EAAC,eAAe;gBAAA+C,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpDjH,OAAA;gBAAQ4D,KAAK,EAAC,WAAW;gBAAA+C,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CjH,OAAA;gBAAQ4D,KAAK,EAAC,UAAU;gBAAA+C,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CjH,OAAA;gBAAQ4D,KAAK,EAAC,eAAe;gBAAA+C,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpDjH,OAAA;gBAAQ4D,KAAK,EAAC,gBAAgB;gBAAA+C,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDjH,OAAA;gBAAQ4D,KAAK,EAAC,WAAW;gBAAA+C,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EACRlF,MAAM,CAACY,QAAQ,iBAAI3C,OAAA,CAACH,YAAY;cAAA8G,QAAA,EAAE5E,MAAM,CAACY;YAAQ;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAEPjH,OAAA,CAACgB,cAAc;QAAA2F,QAAA,gBACb3G,OAAA;UAAImH,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EAAC;QAAmB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAE9ErE,SAAS,CAACsB,GAAG,CAAC,CAACnB,OAAO,EAAEiB,KAAK,kBAC5BhE,OAAA,CAACkB,WAAW;UAAAyF,QAAA,GACT/D,SAAS,CAAC2B,MAAM,GAAG,CAAC,iBACnBvE,OAAA,CAACoB,YAAY;YACX0B,IAAI,EAAC,QAAQ;YACb8D,OAAO,EAAC,QAAQ;YAChBgB,IAAI,EAAC,IAAI;YACTf,OAAO,EAAEA,CAAA,KAAMvC,aAAa,CAACN,KAAK,CAAE;YAAA2C,QAAA,EACrC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CACf,eAEDjH,OAAA;YAAImH,KAAK,EAAE;cAAEC,YAAY,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAV,QAAA,GAAC,UAC1C,EAAC3C,KAAK,GAAG,CAAC;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAELjH,OAAA,CAACc,QAAQ;YAAA6F,QAAA,gBACP3G,OAAA,CAACL,SAAS;cAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;gBAAA+G,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BjH,OAAA,CAACN,MAAM;gBACLkE,KAAK,EAAEb,OAAO,CAACD,IAAK;gBACpB0E,QAAQ,EAAG9D,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,MAAM,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAAA+C,QAAA,gBAEpE3G,OAAA;kBAAQ4D,KAAK,EAAC,aAAa;kBAAA+C,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDjH,OAAA;kBAAQ4D,KAAK,EAAC,QAAQ;kBAAA+C,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCjH,OAAA;kBAAQ4D,KAAK,EAAC,UAAU;kBAAA+C,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZjH,OAAA,CAACL,SAAS;cAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;gBAAA+G,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBjH,OAAA,CAACP,KAAK;gBACJqD,IAAI,EAAC,MAAM;gBACXc,KAAK,EAAEb,OAAO,CAACE,KAAM;gBACrBuE,QAAQ,EAAG9D,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,OAAO,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACrE6D,WAAW,EAAC,aAAa;gBACzBC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDlF,MAAM,CAAC,SAASiC,KAAK,EAAE,CAAC,iBAAIhE,OAAA,CAACH,YAAY;gBAAA8G,QAAA,EAAE5E,MAAM,CAAC,SAASiC,KAAK,EAAE;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEZjH,OAAA,CAACL,SAAS;cAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;gBAAA+G,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzBjH,OAAA,CAACP,KAAK;gBACJqD,IAAI,EAAC,MAAM;gBACXc,KAAK,EAAEb,OAAO,CAACG,QAAS;gBACxBsE,QAAQ,EAAG9D,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,UAAU,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACxE6D,WAAW,EAAC,gBAAgB;gBAC5BC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDlF,MAAM,CAAC,YAAYiC,KAAK,EAAE,CAAC,iBAAIhE,OAAA,CAACH,YAAY;gBAAA8G,QAAA,EAAE5E,MAAM,CAAC,YAAYiC,KAAK,EAAE;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eAEZjH,OAAA,CAACL,SAAS;cAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;gBAAA+G,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBjH,OAAA,CAACP,KAAK;gBACJqD,IAAI,EAAC,MAAM;gBACXc,KAAK,EAAEb,OAAO,CAACC,OAAQ;gBACvBwE,QAAQ,EAAG9D,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,SAAS,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACvE6D,WAAW,EAAC,uBAAuB;gBACnCE,SAAS,EAAE,CAAE;gBACbD,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDlF,MAAM,CAAC,WAAWiC,KAAK,EAAE,CAAC,iBAAIhE,OAAA,CAACH,YAAY;gBAAA8G,QAAA,EAAE5E,MAAM,CAAC,WAAWiC,KAAK,EAAE;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEXjH,OAAA,CAACL,SAAS;YAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;cAAA+G,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BjH,OAAA,CAACP,KAAK;cACJqD,IAAI,EAAC,MAAM;cACXc,KAAK,EAAEb,OAAO,CAACA,OAAQ;cACvByE,QAAQ,EAAG9D,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,SAAS,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACvE6D,WAAW,EAAC,wBAAwB;cACpCC,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDlF,MAAM,CAAC,WAAWiC,KAAK,EAAE,CAAC,iBAAIhE,OAAA,CAACH,YAAY;cAAA8G,QAAA,EAAE5E,MAAM,CAAC,WAAWiC,KAAK,EAAE;YAAC;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAEZjH,OAAA,CAACL,SAAS;YAAAgH,QAAA,gBACR3G,OAAA,CAACJ,KAAK;cAAA+G,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvBjH,OAAA,CAACP,KAAK;cACJqD,IAAI,EAAC,MAAM;cACXc,KAAK,EAAEb,OAAO,CAACI,QAAS;cACxBqE,QAAQ,EAAG9D,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,UAAU,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACxE6D,WAAW,EAAC;YAAkC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA,GAvFIjD,KAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwFV,CACd,CAAC,eAEFjH,OAAA,CAACsB,gBAAgB;UACfwB,IAAI,EAAC,QAAQ;UACb8D,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAExC,UAAW;UAAAsC,QAAA,EACrB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEjBjH,OAAA;QAAKmH,KAAK,EAAE;UAAEU,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAW,CAAE;QAAArB,QAAA,gBAC1F3G,OAAA,CAACR,MAAM;UAACsD,IAAI,EAAC,QAAQ;UAAC8D,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEH,UAAW;UAAAC,QAAA,EAAC;QAE7D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjH,OAAA,CAACR,MAAM;UAACsD,IAAI,EAAC,QAAQ;UAACmF,QAAQ,EAAEtG,OAAQ;UAAAgF,QAAA,EACrChF,OAAO,GAAG,aAAa,GAAG;QAAa;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACxF,EAAA,CA1XID,UAAoB;EAAA,QACPnC,WAAW;AAAA;AAAA6I,GAAA,GADxB1G,UAAoB;AA4X1B,eAAeA,UAAU;AAAC,IAAArB,EAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA2G,GAAA;AAAAC,YAAA,CAAAhI,EAAA;AAAAgI,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}