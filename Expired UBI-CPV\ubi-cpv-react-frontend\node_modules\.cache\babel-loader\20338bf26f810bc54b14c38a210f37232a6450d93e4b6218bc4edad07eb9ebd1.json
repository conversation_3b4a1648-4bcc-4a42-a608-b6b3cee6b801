{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{Button,Input,FormGroup,Label,ErrorMessage,LoadingSpinner}from'../../styles/GlobalStyles';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const LoginContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n\"])));const LoginCard=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  max-width: 400px;\\n  background: \",\";\\n  border-radius: \",\";\\n  box-shadow: \",\";\\n  padding: 30px;\\n  transition: \",\";\\n  border-top: 4px solid \",\";\\n\\n  &:hover {\\n    box-shadow: \",\";\\n  }\\n\"])),props=>props.theme.colors.white,props=>props.theme.borderRadius.md,props=>props.theme.shadows.md,props=>props.theme.transitions.default,props=>props.theme.colors.secondary,props=>props.theme.shadows.lg);const Header=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  margin-bottom: 30px;\\n\"])));const LogoContainer=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 15px;\\n\"])));const Logo=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 120px;\\n  height: 60px;\\n  position: relative;\\n\"])));const LogoULeft=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  width: 50px;\\n  height: 50px;\\n  background-color: \",\";\\n  border-radius: 25px 25px 0 0;\\n  position: absolute;\\n  left: 15px;\\n  transform: rotate(180deg);\\n  box-shadow: \",\";\\n\"])),props=>props.theme.colors.primary,props=>props.theme.shadows.sm);const LogoURight=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  width: 50px;\\n  height: 50px;\\n  background-color: \",\";\\n  border-radius: 25px 25px 0 0;\\n  position: absolute;\\n  right: 15px;\\n  box-shadow: \",\";\\n\"])),props=>props.theme.colors.secondary,props=>props.theme.shadows.sm);const Title=styled.h1(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  letter-spacing: 0.5px;\\n\"])),props=>props.theme.colors.secondary);const Subtitle=styled.p(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  font-size: 14px;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n\"])),props=>props.theme.colors.textLight);const RoleSelector=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 20px;\\n\"])));const RoleOption=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  text-align: center;\\n  padding: 10px;\\n  border: 1px solid \",\";\\n  background-color: \",\";\\n  color: \",\";\\n  border-color: \",\";\\n  cursor: pointer;\\n  transition: \",\";\\n  font-weight: 500;\\n  font-size: 13px;\\n\\n  &:first-child {\\n    border-radius: \",\" 0 0 \",\";\\n  }\\n\\n  &:last-child {\\n    border-radius: 0 \",\" \",\" 0;\\n  }\\n\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.active?props.theme.colors.secondary:props.theme.colors.offWhite,props=>props.active?props.theme.colors.white:props.theme.colors.textDark,props=>props.active?props.theme.colors.secondary:props.theme.colors.mediumGray,props=>props.theme.transitions.default,props=>props.theme.borderRadius.sm,props=>props.theme.borderRadius.sm,props=>props.theme.borderRadius.sm,props=>props.theme.borderRadius.sm,props=>props.active?props.theme.colors.secondaryDark:props.theme.colors.lightGray);const Login=()=>{const[username,setUsername]=useState('');const[password,setPassword]=useState('');const[selectedRole,setSelectedRole]=useState('Agent');const[error,setError]=useState('');const[isSubmitting,setIsSubmitting]=useState(false);const{login,isAuthenticated,user}=useAuth();const navigate=useNavigate();useEffect(()=>{if(isAuthenticated&&user){// Redirect based on user role\nswitch(user.role){case'Agent':navigate('/agent/dashboard');break;case'Supervisor':navigate('/supervisor/dashboard');break;case'Admin':navigate('/admin/dashboard');break;default:navigate('/');}}},[isAuthenticated,user,navigate]);const handleSubmit=async e=>{e.preventDefault();setError('');if(!username||!password){setError('Please enter both username and password');return;}setIsSubmitting(true);try{await login(username,password,selectedRole);}catch(err){setError(err.message||'Login failed. Please check your credentials.');}finally{setIsSubmitting(false);}};return/*#__PURE__*/_jsx(LoginContainer,{children:/*#__PURE__*/_jsxs(LoginCard,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(LogoContainer,{children:/*#__PURE__*/_jsxs(Logo,{children:[/*#__PURE__*/_jsx(LogoULeft,{}),/*#__PURE__*/_jsx(LogoURight,{})]})}),/*#__PURE__*/_jsx(Title,{children:\"Union Bank of India\"}),/*#__PURE__*/_jsx(Subtitle,{children:\"Office Verification System\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(RoleSelector,{children:[/*#__PURE__*/_jsx(RoleOption,{active:selectedRole==='Agent',onClick:()=>setSelectedRole('Agent'),children:\"Agent\"}),/*#__PURE__*/_jsx(RoleOption,{active:selectedRole==='Supervisor',onClick:()=>setSelectedRole('Supervisor'),children:\"Supervisor\"}),/*#__PURE__*/_jsx(RoleOption,{active:selectedRole==='Admin',onClick:()=>setSelectedRole('Admin'),children:\"Admin\"})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"username\",children:\"Username\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"username\",value:username,onChange:e=>setUsername(e.target.value),placeholder:\"Enter your username\",required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"password\",children:\"Password\"}),/*#__PURE__*/_jsx(Input,{type:\"password\",id:\"password\",value:password,onChange:e=>setPassword(e.target.value),placeholder:\"Enter your password\",required:true}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"primary\",fullWidth:true,disabled:isSubmitting,children:isSubmitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(LoadingSpinner,{}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:'8px'},children:\"Logging in...\"})]}):'Login'})]})]})});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "useAuth", "<PERSON><PERSON>", "Input", "FormGroup", "Label", "ErrorMessage", "LoadingSpinner", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "LoginContainer", "div", "_templateObject", "_taggedTemplateLiteral", "LoginCard", "_templateObject2", "props", "theme", "colors", "white", "borderRadius", "md", "shadows", "transitions", "default", "secondary", "lg", "Header", "_templateObject3", "LogoContainer", "_templateObject4", "Logo", "_templateObject5", "LogoULeft", "_templateObject6", "primary", "sm", "LogoURight", "_templateObject7", "Title", "h1", "_templateObject8", "Subtitle", "p", "_templateObject9", "textLight", "RoleSelector", "_templateObject0", "RoleOption", "_templateObject1", "mediumGray", "active", "offWhite", "textDark", "secondaryDark", "lightGray", "<PERSON><PERSON>", "username", "setUsername", "password", "setPassword", "selectedR<PERSON>", "setSelectedRole", "error", "setError", "isSubmitting", "setIsSubmitting", "login", "isAuthenticated", "user", "navigate", "role", "handleSubmit", "e", "preventDefault", "err", "message", "children", "onSubmit", "onClick", "htmlFor", "type", "id", "value", "onChange", "target", "placeholder", "required", "variant", "fullWidth", "disabled", "style", "marginLeft"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Auth/Login.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Button, Input, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';\n\nconst LoginContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n`;\n\nconst LoginCard = styled.div`\n  width: 100%;\n  max-width: 400px;\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  box-shadow: ${props => props.theme.shadows.md};\n  padding: 30px;\n  transition: ${props => props.theme.transitions.default};\n  border-top: 4px solid ${props => props.theme.colors.secondary};\n\n  &:hover {\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst LogoContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  margin-bottom: 15px;\n`;\n\nconst Logo = styled.div`\n  width: 120px;\n  height: 60px;\n  position: relative;\n`;\n\nconst LogoULeft = styled.div`\n  width: 50px;\n  height: 50px;\n  background-color: ${props => props.theme.colors.primary};\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  left: 15px;\n  transform: rotate(180deg);\n  box-shadow: ${props => props.theme.shadows.sm};\n`;\n\nconst LogoURight = styled.div`\n  width: 50px;\n  height: 50px;\n  background-color: ${props => props.theme.colors.secondary};\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  right: 15px;\n  box-shadow: ${props => props.theme.shadows.sm};\n`;\n\nconst Title = styled.h1`\n  color: ${props => props.theme.colors.secondary};\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 5px;\n  letter-spacing: 0.5px;\n`;\n\nconst Subtitle = styled.p`\n  color: ${props => props.theme.colors.textLight};\n  font-size: 14px;\n  margin-bottom: 5px;\n  font-weight: 500;\n`;\n\nconst RoleSelector = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n`;\n\nconst RoleOption = styled.div<{ active: boolean }>`\n  flex: 1;\n  text-align: center;\n  padding: 10px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  background-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.offWhite};\n  color: ${props => props.active ? props.theme.colors.white : props.theme.colors.textDark};\n  border-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.mediumGray};\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.default};\n  font-weight: 500;\n  font-size: 13px;\n\n  &:first-child {\n    border-radius: ${props => props.theme.borderRadius.sm} 0 0 ${props => props.theme.borderRadius.sm};\n  }\n\n  &:last-child {\n    border-radius: 0 ${props => props.theme.borderRadius.sm} ${props => props.theme.borderRadius.sm} 0;\n  }\n\n  &:hover {\n    background-color: ${props => props.active ? props.theme.colors.secondaryDark : props.theme.colors.lightGray};\n  }\n`;\n\nconst Login: React.FC = () => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [selectedRole, setSelectedRole] = useState<'Agent' | 'Supervisor' | 'Admin'>('Agent');\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const { login, isAuthenticated, user } = useAuth();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      // Redirect based on user role\n      switch (user.role) {\n        case 'Agent':\n          navigate('/agent/dashboard');\n          break;\n        case 'Supervisor':\n          navigate('/supervisor/dashboard');\n          break;\n        case 'Admin':\n          navigate('/admin/dashboard');\n          break;\n        default:\n          navigate('/');\n      }\n    }\n  }, [isAuthenticated, user, navigate]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    if (!username || !password) {\n      setError('Please enter both username and password');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      await login(username, password, selectedRole);\n    } catch (err: any) {\n      setError(err.message || 'Login failed. Please check your credentials.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <LoginContainer>\n      <LoginCard>\n        <Header>\n          <LogoContainer>\n            <Logo>\n              <LogoULeft />\n              <LogoURight />\n            </Logo>\n          </LogoContainer>\n          <Title>Union Bank of India</Title>\n          <Subtitle>Office Verification System</Subtitle>\n        </Header>\n\n        <form onSubmit={handleSubmit}>\n          <RoleSelector>\n            <RoleOption\n              active={selectedRole === 'Agent'}\n              onClick={() => setSelectedRole('Agent')}\n            >\n              Agent\n            </RoleOption>\n            <RoleOption\n              active={selectedRole === 'Supervisor'}\n              onClick={() => setSelectedRole('Supervisor')}\n            >\n              Supervisor\n            </RoleOption>\n            <RoleOption\n              active={selectedRole === 'Admin'}\n              onClick={() => setSelectedRole('Admin')}\n            >\n              Admin\n            </RoleOption>\n          </RoleSelector>\n\n          <FormGroup>\n            <Label htmlFor=\"username\">Username</Label>\n            <Input\n              type=\"text\"\n              id=\"username\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              placeholder=\"Enter your username\"\n              required\n            />\n          </FormGroup>\n\n          <FormGroup>\n            <Label htmlFor=\"password\">Password</Label>\n            <Input\n              type=\"password\"\n              id=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              placeholder=\"Enter your password\"\n              required\n            />\n            {error && <ErrorMessage>{error}</ErrorMessage>}\n          </FormGroup>\n\n          <Button\n            type=\"submit\"\n            variant=\"primary\"\n            fullWidth\n            disabled={isSubmitting}\n          >\n            {isSubmitting ? (\n              <>\n                <LoadingSpinner />\n                <span style={{ marginLeft: '8px' }}>Logging in...</span>\n              </>\n            ) : (\n              'Login'\n            )}\n          </Button>\n        </form>\n      </LoginCard>\n    </LoginContainer>\n  );\n};\n\nexport default Login;\n"], "mappings": "8WAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,MAAM,CAAEC,KAAK,CAAEC,SAAS,CAAEC,KAAK,CAAEC,YAAY,CAAEC,cAAc,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1G,KAAM,CAAAC,cAAc,CAAGd,MAAM,CAACe,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,wHAMhC,CAED,KAAM,CAAAC,SAAS,CAAGlB,MAAM,CAACe,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,4NAGZG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,KAAK,CAC9BH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CACvCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACD,EAAE,CAE/BL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACM,WAAW,CAACC,OAAO,CAC9BR,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACO,SAAS,CAG7CT,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACI,EAAE,CAEhD,CAED,KAAM,CAAAC,MAAM,CAAG/B,MAAM,CAACe,GAAG,CAAAiB,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,yDAGxB,CAED,KAAM,CAAAgB,aAAa,CAAGjC,MAAM,CAACe,GAAG,CAAAmB,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,gFAI/B,CAED,KAAM,CAAAkB,IAAI,CAAGnC,MAAM,CAACe,GAAG,CAAAqB,gBAAA,GAAAA,gBAAA,CAAAnB,sBAAA,mEAItB,CAED,KAAM,CAAAoB,SAAS,CAAGrC,MAAM,CAACe,GAAG,CAAAuB,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,8LAGNG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,OAAO,CAKzCnB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACc,EAAE,CAC9C,CAED,KAAM,CAAAC,UAAU,CAAGzC,MAAM,CAACe,GAAG,CAAA2B,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,iKAGPG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACO,SAAS,CAI3CT,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACc,EAAE,CAC9C,CAED,KAAM,CAAAG,KAAK,CAAG3C,MAAM,CAAC4C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA5B,sBAAA,mHACZG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACO,SAAS,CAK/C,CAED,KAAM,CAAAiB,QAAQ,CAAG9C,MAAM,CAAC+C,CAAC,CAAAC,gBAAA,GAAAA,gBAAA,CAAA/B,sBAAA,yFACdG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2B,SAAS,CAI/C,CAED,KAAM,CAAAC,YAAY,CAAGlD,MAAM,CAACe,GAAG,CAAAoC,gBAAA,GAAAA,gBAAA,CAAAlC,sBAAA,uFAI9B,CAED,KAAM,CAAAmC,UAAU,CAAGpD,MAAM,CAACe,GAAG,CAAAsC,gBAAA,GAAAA,gBAAA,CAAApC,sBAAA,8YAIPG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgC,UAAU,CACtClC,KAAK,EAAIA,KAAK,CAACmC,MAAM,CAAGnC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACO,SAAS,CAAGT,KAAK,CAACC,KAAK,CAACC,MAAM,CAACkC,QAAQ,CAC7FpC,KAAK,EAAIA,KAAK,CAACmC,MAAM,CAAGnC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAGH,KAAK,CAACC,KAAK,CAACC,MAAM,CAACmC,QAAQ,CACvErC,KAAK,EAAIA,KAAK,CAACmC,MAAM,CAAGnC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACO,SAAS,CAAGT,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgC,UAAU,CAEtFlC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACM,WAAW,CAACC,OAAO,CAKnCR,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACgB,EAAE,CAAQpB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACgB,EAAE,CAI9EpB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACgB,EAAE,CAAIpB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACgB,EAAE,CAI3EpB,KAAK,EAAIA,KAAK,CAACmC,MAAM,CAAGnC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACoC,aAAa,CAAGtC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACqC,SAAS,CAE9G,CAED,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkE,QAAQ,CAAEC,WAAW,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACoE,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAmC,OAAO,CAAC,CAC3F,KAAM,CAACsE,KAAK,CAAEC,QAAQ,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACwE,YAAY,CAAEC,eAAe,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CAEvD,KAAM,CAAE0E,KAAK,CAAEC,eAAe,CAAEC,IAAK,CAAC,CAAGxE,OAAO,CAAC,CAAC,CAClD,KAAM,CAAAyE,QAAQ,CAAG3E,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd,GAAI0E,eAAe,EAAIC,IAAI,CAAE,CAC3B;AACA,OAAQA,IAAI,CAACE,IAAI,EACf,IAAK,OAAO,CACVD,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,IAAK,YAAY,CACfA,QAAQ,CAAC,uBAAuB,CAAC,CACjC,MACF,IAAK,OAAO,CACVA,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,QACEA,QAAQ,CAAC,GAAG,CAAC,CACjB,CACF,CACF,CAAC,CAAE,CAACF,eAAe,CAAEC,IAAI,CAAEC,QAAQ,CAAC,CAAC,CAErC,KAAM,CAAAE,YAAY,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACjDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBV,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CAACP,QAAQ,EAAI,CAACE,QAAQ,CAAE,CAC1BK,QAAQ,CAAC,yCAAyC,CAAC,CACnD,OACF,CAEAE,eAAe,CAAC,IAAI,CAAC,CAErB,GAAI,CACF,KAAM,CAAAC,KAAK,CAACV,QAAQ,CAAEE,QAAQ,CAAEE,YAAY,CAAC,CAC/C,CAAE,MAAOc,GAAQ,CAAE,CACjBX,QAAQ,CAACW,GAAG,CAACC,OAAO,EAAI,8CAA8C,CAAC,CACzE,CAAC,OAAS,CACRV,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,mBACE7D,IAAA,CAACK,cAAc,EAAAmE,QAAA,cACbtE,KAAA,CAACO,SAAS,EAAA+D,QAAA,eACRtE,KAAA,CAACoB,MAAM,EAAAkD,QAAA,eACLxE,IAAA,CAACwB,aAAa,EAAAgD,QAAA,cACZtE,KAAA,CAACwB,IAAI,EAAA8C,QAAA,eACHxE,IAAA,CAAC4B,SAAS,GAAE,CAAC,cACb5B,IAAA,CAACgC,UAAU,GAAE,CAAC,EACV,CAAC,CACM,CAAC,cAChBhC,IAAA,CAACkC,KAAK,EAAAsC,QAAA,CAAC,qBAAmB,CAAO,CAAC,cAClCxE,IAAA,CAACqC,QAAQ,EAAAmC,QAAA,CAAC,4BAA0B,CAAU,CAAC,EACzC,CAAC,cAETtE,KAAA,SAAMuE,QAAQ,CAAEN,YAAa,CAAAK,QAAA,eAC3BtE,KAAA,CAACuC,YAAY,EAAA+B,QAAA,eACXxE,IAAA,CAAC2C,UAAU,EACTG,MAAM,CAAEU,YAAY,GAAK,OAAQ,CACjCkB,OAAO,CAAEA,CAAA,GAAMjB,eAAe,CAAC,OAAO,CAAE,CAAAe,QAAA,CACzC,OAED,CAAY,CAAC,cACbxE,IAAA,CAAC2C,UAAU,EACTG,MAAM,CAAEU,YAAY,GAAK,YAAa,CACtCkB,OAAO,CAAEA,CAAA,GAAMjB,eAAe,CAAC,YAAY,CAAE,CAAAe,QAAA,CAC9C,YAED,CAAY,CAAC,cACbxE,IAAA,CAAC2C,UAAU,EACTG,MAAM,CAAEU,YAAY,GAAK,OAAQ,CACjCkB,OAAO,CAAEA,CAAA,GAAMjB,eAAe,CAAC,OAAO,CAAE,CAAAe,QAAA,CACzC,OAED,CAAY,CAAC,EACD,CAAC,cAEftE,KAAA,CAACP,SAAS,EAAA6E,QAAA,eACRxE,IAAA,CAACJ,KAAK,EAAC+E,OAAO,CAAC,UAAU,CAAAH,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1CxE,IAAA,CAACN,KAAK,EACJkF,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,UAAU,CACbC,KAAK,CAAE1B,QAAS,CAChB2B,QAAQ,CAAGX,CAAC,EAAKf,WAAW,CAACe,CAAC,CAACY,MAAM,CAACF,KAAK,CAAE,CAC7CG,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACT,CAAC,EACO,CAAC,cAEZhF,KAAA,CAACP,SAAS,EAAA6E,QAAA,eACRxE,IAAA,CAACJ,KAAK,EAAC+E,OAAO,CAAC,UAAU,CAAAH,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1CxE,IAAA,CAACN,KAAK,EACJkF,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,UAAU,CACbC,KAAK,CAAExB,QAAS,CAChByB,QAAQ,CAAGX,CAAC,EAAKb,WAAW,CAACa,CAAC,CAACY,MAAM,CAACF,KAAK,CAAE,CAC7CG,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACT,CAAC,CACDxB,KAAK,eAAI1D,IAAA,CAACH,YAAY,EAAA2E,QAAA,CAAEd,KAAK,CAAe,CAAC,EACrC,CAAC,cAEZ1D,IAAA,CAACP,MAAM,EACLmF,IAAI,CAAC,QAAQ,CACbO,OAAO,CAAC,SAAS,CACjBC,SAAS,MACTC,QAAQ,CAAEzB,YAAa,CAAAY,QAAA,CAEtBZ,YAAY,cACX1D,KAAA,CAAAE,SAAA,EAAAoE,QAAA,eACExE,IAAA,CAACF,cAAc,GAAE,CAAC,cAClBE,IAAA,SAAMsF,KAAK,CAAE,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAf,QAAA,CAAC,eAAa,CAAM,CAAC,EACxD,CAAC,CAEH,OACD,CACK,CAAC,EACL,CAAC,EACE,CAAC,CACE,CAAC,CAErB,CAAC,CAED,cAAe,CAAArB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}