{"ast": null, "code": "'use strict';\n\nimport _objectSpread from \"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport utils from '../utils.js';\nimport AxiosHeaders from \"./AxiosHeaders.js\";\nconst headersToObject = thing => thing instanceof AxiosHeaders ? _objectSpread({}, thing) : thing;\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  const config = {};\n  function getMergedValue(target, source, prop, caseless) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge.call({\n        caseless\n      }, target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(a, b, prop, caseless) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(a, b, prop, caseless);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a, prop, caseless);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(a, b, prop) {\n    if (prop in config2) {\n      return getMergedValue(a, b);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, a);\n    }\n  }\n  const mergeMap = {\n    url: valueFromConfig2,\n    method: valueFromConfig2,\n    data: valueFromConfig2,\n    baseURL: defaultToConfig2,\n    transformRequest: defaultToConfig2,\n    transformResponse: defaultToConfig2,\n    paramsSerializer: defaultToConfig2,\n    timeout: defaultToConfig2,\n    timeoutMessage: defaultToConfig2,\n    withCredentials: defaultToConfig2,\n    withXSRFToken: defaultToConfig2,\n    adapter: defaultToConfig2,\n    responseType: defaultToConfig2,\n    xsrfCookieName: defaultToConfig2,\n    xsrfHeaderName: defaultToConfig2,\n    onUploadProgress: defaultToConfig2,\n    onDownloadProgress: defaultToConfig2,\n    decompress: defaultToConfig2,\n    maxContentLength: defaultToConfig2,\n    maxBodyLength: defaultToConfig2,\n    beforeRedirect: defaultToConfig2,\n    transport: defaultToConfig2,\n    httpAgent: defaultToConfig2,\n    httpsAgent: defaultToConfig2,\n    cancelToken: defaultToConfig2,\n    socketPath: defaultToConfig2,\n    responseEncoding: defaultToConfig2,\n    validateStatus: mergeDirectKeys,\n    headers: (a, b, prop) => mergeDeepProperties(headersToObject(a), headersToObject(b), prop, true)\n  };\n  utils.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {\n    const merge = mergeMap[prop] || mergeDeepProperties;\n    const configValue = merge(config1[prop], config2[prop], prop);\n    utils.isUndefined(configValue) && merge !== mergeDirectKeys || (config[prop] = configValue);\n  });\n  return config;\n}", "map": {"version": 3, "names": ["_objectSpread", "utils", "AxiosHeaders", "headersToObject", "thing", "mergeConfig", "config1", "config2", "config", "getMergedValue", "target", "source", "prop", "caseless", "isPlainObject", "merge", "call", "isArray", "slice", "mergeDeepProperties", "a", "b", "isUndefined", "undefined", "valueFromConfig2", "defaultToConfig2", "mergeDirectKeys", "mergeMap", "url", "method", "data", "baseURL", "transformRequest", "transformResponse", "paramsSerializer", "timeout", "timeoutMessage", "withCredentials", "withXSRFToken", "adapter", "responseType", "xsrfCookieName", "xsrfHeaderName", "onUploadProgress", "onDownloadProgress", "decompress", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "beforeRedirect", "transport", "httpAgent", "httpsAgent", "cancelToken", "socketPath", "responseEncoding", "validateStatus", "headers", "for<PERSON>ach", "Object", "keys", "assign", "computeConfigValue", "config<PERSON><PERSON><PERSON>"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/axios/lib/core/mergeConfig.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosHeaders from \"./AxiosHeaders.js\";\n\nconst headersToObject = (thing) => thing instanceof AxiosHeaders ? { ...thing } : thing;\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  const config = {};\n\n  function getMergedValue(target, source, prop, caseless) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge.call({caseless}, target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(a, b, prop , caseless) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(a, b, prop , caseless);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a, prop , caseless);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(a, b, prop) {\n    if (prop in config2) {\n      return getMergedValue(a, b);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  const mergeMap = {\n    url: valueFromConfig2,\n    method: valueFromConfig2,\n    data: valueFromConfig2,\n    baseURL: defaultToConfig2,\n    transformRequest: defaultToConfig2,\n    transformResponse: defaultToConfig2,\n    paramsSerializer: defaultToConfig2,\n    timeout: defaultToConfig2,\n    timeoutMessage: defaultToConfig2,\n    withCredentials: defaultToConfig2,\n    withXSRFToken: defaultToConfig2,\n    adapter: defaultToConfig2,\n    responseType: defaultToConfig2,\n    xsrfCookieName: defaultToConfig2,\n    xsrfHeaderName: defaultToConfig2,\n    onUploadProgress: defaultToConfig2,\n    onDownloadProgress: defaultToConfig2,\n    decompress: defaultToConfig2,\n    maxContentLength: defaultToConfig2,\n    maxBodyLength: defaultToConfig2,\n    beforeRedirect: defaultToConfig2,\n    transport: defaultToConfig2,\n    httpAgent: defaultToConfig2,\n    httpsAgent: defaultToConfig2,\n    cancelToken: defaultToConfig2,\n    socketPath: defaultToConfig2,\n    responseEncoding: defaultToConfig2,\n    validateStatus: mergeDirectKeys,\n    headers: (a, b , prop) => mergeDeepProperties(headersToObject(a), headersToObject(b),prop, true)\n  };\n\n  utils.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {\n    const merge = mergeMap[prop] || mergeDeepProperties;\n    const configValue = merge(config1[prop], config2[prop], prop);\n    (utils.isUndefined(configValue) && merge !== mergeDirectKeys) || (config[prop] = configValue);\n  });\n\n  return config;\n}\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAEb,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,YAAY,MAAM,mBAAmB;AAE5C,MAAMC,eAAe,GAAIC,KAAK,IAAKA,KAAK,YAAYF,YAAY,GAAAF,aAAA,KAAQI,KAAK,IAAKA,KAAK;;AAEvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACpD;EACAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMC,MAAM,GAAG,CAAC,CAAC;EAEjB,SAASC,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;IACtD,IAAIZ,KAAK,CAACa,aAAa,CAACJ,MAAM,CAAC,IAAIT,KAAK,CAACa,aAAa,CAACH,MAAM,CAAC,EAAE;MAC9D,OAAOV,KAAK,CAACc,KAAK,CAACC,IAAI,CAAC;QAACH;MAAQ,CAAC,EAAEH,MAAM,EAAEC,MAAM,CAAC;IACrD,CAAC,MAAM,IAAIV,KAAK,CAACa,aAAa,CAACH,MAAM,CAAC,EAAE;MACtC,OAAOV,KAAK,CAACc,KAAK,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC;IAChC,CAAC,MAAM,IAAIV,KAAK,CAACgB,OAAO,CAACN,MAAM,CAAC,EAAE;MAChC,OAAOA,MAAM,CAACO,KAAK,CAAC,CAAC;IACvB;IACA,OAAOP,MAAM;EACf;;EAEA;EACA,SAASQ,mBAAmBA,CAACC,CAAC,EAAEC,CAAC,EAAET,IAAI,EAAGC,QAAQ,EAAE;IAClD,IAAI,CAACZ,KAAK,CAACqB,WAAW,CAACD,CAAC,CAAC,EAAE;MACzB,OAAOZ,cAAc,CAACW,CAAC,EAAEC,CAAC,EAAET,IAAI,EAAGC,QAAQ,CAAC;IAC9C,CAAC,MAAM,IAAI,CAACZ,KAAK,CAACqB,WAAW,CAACF,CAAC,CAAC,EAAE;MAChC,OAAOX,cAAc,CAACc,SAAS,EAAEH,CAAC,EAAER,IAAI,EAAGC,QAAQ,CAAC;IACtD;EACF;;EAEA;EACA,SAASW,gBAAgBA,CAACJ,CAAC,EAAEC,CAAC,EAAE;IAC9B,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACD,CAAC,CAAC,EAAE;MACzB,OAAOZ,cAAc,CAACc,SAAS,EAAEF,CAAC,CAAC;IACrC;EACF;;EAEA;EACA,SAASI,gBAAgBA,CAACL,CAAC,EAAEC,CAAC,EAAE;IAC9B,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACD,CAAC,CAAC,EAAE;MACzB,OAAOZ,cAAc,CAACc,SAAS,EAAEF,CAAC,CAAC;IACrC,CAAC,MAAM,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACF,CAAC,CAAC,EAAE;MAChC,OAAOX,cAAc,CAACc,SAAS,EAAEH,CAAC,CAAC;IACrC;EACF;;EAEA;EACA,SAASM,eAAeA,CAACN,CAAC,EAAEC,CAAC,EAAET,IAAI,EAAE;IACnC,IAAIA,IAAI,IAAIL,OAAO,EAAE;MACnB,OAAOE,cAAc,CAACW,CAAC,EAAEC,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIT,IAAI,IAAIN,OAAO,EAAE;MAC1B,OAAOG,cAAc,CAACc,SAAS,EAAEH,CAAC,CAAC;IACrC;EACF;EAEA,MAAMO,QAAQ,GAAG;IACfC,GAAG,EAAEJ,gBAAgB;IACrBK,MAAM,EAAEL,gBAAgB;IACxBM,IAAI,EAAEN,gBAAgB;IACtBO,OAAO,EAAEN,gBAAgB;IACzBO,gBAAgB,EAAEP,gBAAgB;IAClCQ,iBAAiB,EAAER,gBAAgB;IACnCS,gBAAgB,EAAET,gBAAgB;IAClCU,OAAO,EAAEV,gBAAgB;IACzBW,cAAc,EAAEX,gBAAgB;IAChCY,eAAe,EAAEZ,gBAAgB;IACjCa,aAAa,EAAEb,gBAAgB;IAC/Bc,OAAO,EAAEd,gBAAgB;IACzBe,YAAY,EAAEf,gBAAgB;IAC9BgB,cAAc,EAAEhB,gBAAgB;IAChCiB,cAAc,EAAEjB,gBAAgB;IAChCkB,gBAAgB,EAAElB,gBAAgB;IAClCmB,kBAAkB,EAAEnB,gBAAgB;IACpCoB,UAAU,EAAEpB,gBAAgB;IAC5BqB,gBAAgB,EAAErB,gBAAgB;IAClCsB,aAAa,EAAEtB,gBAAgB;IAC/BuB,cAAc,EAAEvB,gBAAgB;IAChCwB,SAAS,EAAExB,gBAAgB;IAC3ByB,SAAS,EAAEzB,gBAAgB;IAC3B0B,UAAU,EAAE1B,gBAAgB;IAC5B2B,WAAW,EAAE3B,gBAAgB;IAC7B4B,UAAU,EAAE5B,gBAAgB;IAC5B6B,gBAAgB,EAAE7B,gBAAgB;IAClC8B,cAAc,EAAE7B,eAAe;IAC/B8B,OAAO,EAAEA,CAACpC,CAAC,EAAEC,CAAC,EAAGT,IAAI,KAAKO,mBAAmB,CAAChB,eAAe,CAACiB,CAAC,CAAC,EAAEjB,eAAe,CAACkB,CAAC,CAAC,EAACT,IAAI,EAAE,IAAI;EACjG,CAAC;EAEDX,KAAK,CAACwD,OAAO,CAACC,MAAM,CAACC,IAAI,CAACD,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEtD,OAAO,EAAEC,OAAO,CAAC,CAAC,EAAE,SAASsD,kBAAkBA,CAACjD,IAAI,EAAE;IAChG,MAAMG,KAAK,GAAGY,QAAQ,CAACf,IAAI,CAAC,IAAIO,mBAAmB;IACnD,MAAM2C,WAAW,GAAG/C,KAAK,CAACT,OAAO,CAACM,IAAI,CAAC,EAAEL,OAAO,CAACK,IAAI,CAAC,EAAEA,IAAI,CAAC;IAC5DX,KAAK,CAACqB,WAAW,CAACwC,WAAW,CAAC,IAAI/C,KAAK,KAAKW,eAAe,KAAMlB,MAAM,CAACI,IAAI,CAAC,GAAGkD,WAAW,CAAC;EAC/F,CAAC,CAAC;EAEF,OAAOtD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}