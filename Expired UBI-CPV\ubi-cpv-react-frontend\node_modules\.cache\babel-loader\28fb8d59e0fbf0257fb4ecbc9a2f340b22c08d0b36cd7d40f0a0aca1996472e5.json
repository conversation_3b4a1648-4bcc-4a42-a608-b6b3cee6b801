{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Admin\\\\UserManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n_c3 = Title;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c4 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c5 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c6 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c7 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c8 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ${props => props.active ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.active ? '#2e7d32' : '#c62828'};\n`;\n_c9 = StatusBadge;\nconst RoleBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.role) {\n    case 'Admin':\n      return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n    case 'Supervisor':\n      return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n    case 'Agent':\n      return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c0 = RoleBadge;\nconst Modal = styled.div`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n_c1 = Modal;\nconst ModalContent = styled.div`\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n`;\n_c10 = ModalContent;\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c11 = ModalHeader;\nconst ModalTitle = styled.h2`\n  color: ${props => props.theme.colors.primary};\n  margin: 0;\n`;\n_c12 = ModalTitle;\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ${props => props.theme.colors.textMedium};\n\n  &:hover {\n    color: ${props => props.theme.colors.textDark};\n  }\n`;\n\n// User interface is now imported from apiService\n_c13 = CloseButton;\nconst UserManagement = () => {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: 'Agent',\n    password: '',\n    confirmPassword: ''\n  });\n  const [errors, setErrors] = useState({});\n  useEffect(() => {\n    loadUsers();\n  }, []);\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const usersData = await apiService.getUsers();\n      setUsers(usersData);\n    } catch (error) {\n      console.error('Error loading users:', error);\n      // Show user-friendly error message\n      alert('Failed to load users. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!editingUser) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 6) {\n        newErrors.password = 'Password must be at least 6 characters';\n      }\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    try {\n      const userData = {\n        username: formData.username,\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        email: formData.email,\n        role: formData.role,\n        ...(formData.password && {\n          password: formData.password\n        })\n      };\n      if (editingUser) {\n        // Update existing user\n        const updatedUser = await apiService.updateUser(editingUser.userId, userData);\n        setUsers(prev => prev.map(user => user.userId === editingUser.userId ? updatedUser : user));\n        alert('User updated successfully!');\n      } else {\n        // Create new user\n        const newUser = await apiService.createUser(userData);\n        setUsers(prev => [...prev, newUser]);\n        alert('User created successfully!');\n      }\n      handleCloseModal();\n    } catch (error) {\n      console.error('Error saving user:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to save user. Please try again.';\n      alert(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n  const handleEditUser = user => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      email: user.email,\n      role: user.role,\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n  const handleToggleUserStatus = async userId => {\n    try {\n      await apiService.toggleUserStatus(userId);\n      // Update the local state to reflect the change\n      setUsers(prev => prev.map(user => user.userId === userId ? {\n        ...user,\n        isActive: !user.isActive\n      } : user));\n      alert('User status updated successfully!');\n    } catch (error) {\n      console.error('Error toggling user status:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update user status. Please try again.';\n      alert(errorMessage);\n    }\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n  };\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: handleBack,\n          children: \"\\u2190 Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          style: {\n            marginLeft: '20px'\n          },\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCreateUser,\n        children: \"+ Create User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Created Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Last Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: users.map(user => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(RoleBadge, {\n                  role: user.role,\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                  active: user.isActive,\n                  children: user.isActive ? 'Active' : 'Inactive'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(user.createdDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Never'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    onClick: () => handleEditUser(user),\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: user.isActive ? 'danger' : 'secondary',\n                    onClick: () => handleToggleUserStatus(user.userId),\n                    children: user.isActive ? 'Deactivate' : 'Activate'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, user.userId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), users.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#777'\n        },\n        children: \"No users found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n          children: [/*#__PURE__*/_jsxDEV(ModalTitle, {\n            children: editingUser ? 'Edit User' : 'Create New User'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CloseButton, {\n            onClick: handleCloseModal,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"username\",\n              children: \"Username *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"username\",\n              name: \"username\",\n              value: formData.username,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this), errors.username && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"firstName\",\n              children: \"First Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"firstName\",\n              name: \"firstName\",\n              value: formData.firstName,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), errors.firstName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.firstName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 36\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"lastName\",\n              children: \"Last Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"lastName\",\n              name: \"lastName\",\n              value: formData.lastName,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this), errors.lastName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.lastName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"email\",\n              children: \"Email *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"role\",\n              children: \"Role *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              id: \"role\",\n              name: \"role\",\n              value: formData.role,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Agent\",\n                children: \"Agent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Supervisor\",\n                children: \"Supervisor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Admin\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), !editingUser && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"password\",\n                children: \"Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"confirmPassword\",\n                children: \"Confirm Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"password\",\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 46\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '10px',\n              justifyContent: 'flex-end',\n              marginTop: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: handleCloseModal,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: loading,\n              children: loading ? 'Saving...' : editingUser ? 'Update User' : 'Create User'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagement, \"T4Go8mY5KjC1acl112uSL6fXCtk=\", false, function () {\n  return [useNavigate];\n});\n_c14 = UserManagement;\nexport default UserManagement;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"TableContainer\");\n$RefreshReg$(_c5, \"Table\");\n$RefreshReg$(_c6, \"TableHeader\");\n$RefreshReg$(_c7, \"TableCell\");\n$RefreshReg$(_c8, \"TableRow\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"RoleBadge\");\n$RefreshReg$(_c1, \"Modal\");\n$RefreshReg$(_c10, \"ModalContent\");\n$RefreshReg$(_c11, \"ModalHeader\");\n$RefreshReg$(_c12, \"ModalTitle\");\n$RefreshReg$(_c13, \"CloseButton\");\n$RefreshReg$(_c14, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "Input", "Select", "FormGroup", "Label", "ErrorMessage", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Container", "div", "_c", "Header", "props", "theme", "colors", "mediumGray", "_c2", "Title", "h1", "primary", "_c3", "TableContainer", "_c4", "Table", "table", "_c5", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c6", "TableCell", "td", "_c7", "TableRow", "tr", "_c8", "StatusBadge", "span", "active", "_c9", "RoleBadge", "role", "_c0", "Modal", "show", "_c1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "white", "borderRadius", "md", "_c10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c11", "ModalTitle", "h2", "_c12", "CloseButton", "button", "textDark", "_c13", "UserManagement", "_s", "navigate", "users", "setUsers", "showModal", "setShowModal", "editingUser", "setEditingUser", "loading", "setLoading", "formData", "setFormData", "username", "firstName", "lastName", "email", "password", "confirmPassword", "errors", "setErrors", "loadUsers", "usersData", "getUsers", "error", "console", "alert", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "trim", "test", "length", "Object", "keys", "handleSubmit", "preventDefault", "userData", "updatedUser", "updateUser", "userId", "map", "user", "newUser", "createUser", "handleCloseModal", "errorMessage", "Error", "message", "handleCreateUser", "handleEditUser", "handleToggleUserStatus", "toggleUserStatus", "isActive", "handleBack", "formatDate", "dateString", "Date", "toLocaleDateString", "children", "style", "display", "alignItems", "variant", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "createdDate", "lastLoginDate", "gap", "size", "textAlign", "padding", "color", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "justifyContent", "marginTop", "disabled", "_c14", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/UserManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, User } from '../../services/apiService';\n\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ active: boolean }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ${props => props.active ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.active ? '#2e7d32' : '#c62828'};\n`;\n\nconst RoleBadge = styled.span<{ role: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.role) {\n      case 'Admin':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'Supervisor':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'Agent':\n        return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst Modal = styled.div<{ show: boolean }>`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst ModalContent = styled.div`\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n`;\n\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst ModalTitle = styled.h2`\n  color: ${props => props.theme.colors.primary};\n  margin: 0;\n`;\n\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ${props => props.theme.colors.textMedium};\n\n  &:hover {\n    color: ${props => props.theme.colors.textDark};\n  }\n`;\n\n// User interface is now imported from apiService\n\nconst UserManagement: React.FC = () => {\n  const navigate = useNavigate();\n  const [users, setUsers] = useState<User[]>([]);\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: 'Agent' as 'Agent' | 'Supervisor' | 'Admin',\n    password: '',\n    confirmPassword: '',\n  });\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\n\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const usersData = await apiService.getUsers();\n      setUsers(usersData);\n    } catch (error) {\n      console.error('Error loading users:', error);\n      // Show user-friendly error message\n      alert('Failed to load users. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: { [key: string]: string } = {};\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!editingUser) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 6) {\n        newErrors.password = 'Password must be at least 6 characters';\n      }\n\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const userData = {\n        username: formData.username,\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        email: formData.email,\n        role: formData.role,\n        ...(formData.password && { password: formData.password }),\n      };\n\n      if (editingUser) {\n        // Update existing user\n        const updatedUser = await apiService.updateUser(editingUser.userId, userData);\n        setUsers(prev => prev.map(user =>\n          user.userId === editingUser.userId ? updatedUser : user\n        ));\n        alert('User updated successfully!');\n      } else {\n        // Create new user\n        const newUser = await apiService.createUser(userData);\n        setUsers(prev => [...prev, newUser]);\n        alert('User created successfully!');\n      }\n\n      handleCloseModal();\n    } catch (error) {\n      console.error('Error saving user:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to save user. Please try again.';\n      alert(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      email: user.email,\n      role: user.role,\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleToggleUserStatus = async (userId: number) => {\n    try {\n      await apiService.toggleUserStatus(userId);\n      // Update the local state to reflect the change\n      setUsers(prev => prev.map(user =>\n        user.userId === userId ? { ...user, isActive: !user.isActive } : user\n      ));\n      alert('User status updated successfully!');\n    } catch (error) {\n      console.error('Error toggling user status:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update user status. Please try again.';\n      alert(errorMessage);\n    }\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n  };\n\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  return (\n    <Container>\n      <Header>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <Button variant=\"outline\" onClick={handleBack}>\n            ← Back\n          </Button>\n          <Title style={{ marginLeft: '20px' }}>User Management</Title>\n        </div>\n        <Button onClick={handleCreateUser}>\n          + Create User\n        </Button>\n      </Header>\n\n      <Card>\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Name</TableHeader>\n                <TableHeader>Username</TableHeader>\n                <TableHeader>Email</TableHeader>\n                <TableHeader>Role</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Created Date</TableHeader>\n                <TableHeader>Last Login</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {users.map((user) => (\n                <TableRow key={user.userId}>\n                  <TableCell>{user.firstName} {user.lastName}</TableCell>\n                  <TableCell>{user.username}</TableCell>\n                  <TableCell>{user.email}</TableCell>\n                  <TableCell>\n                    <RoleBadge role={user.role}>{user.role}</RoleBadge>\n                  </TableCell>\n                  <TableCell>\n                    <StatusBadge active={user.isActive}>\n                      {user.isActive ? 'Active' : 'Inactive'}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>{formatDate(user.createdDate)}</TableCell>\n                  <TableCell>\n                    {user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Never'}\n                  </TableCell>\n                  <TableCell>\n                    <div style={{ display: 'flex', gap: '8px' }}>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleEditUser(user)}\n                      >\n                        Edit\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant={user.isActive ? 'danger' : 'secondary'}\n                        onClick={() => handleToggleUserStatus(user.userId)}\n                      >\n                        {user.isActive ? 'Deactivate' : 'Activate'}\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {users.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            No users found.\n          </div>\n        )}\n      </Card>\n\n      {/* Create/Edit User Modal */}\n      <Modal show={showModal}>\n        <ModalContent>\n          <ModalHeader>\n            <ModalTitle>\n              {editingUser ? 'Edit User' : 'Create New User'}\n            </ModalTitle>\n            <CloseButton onClick={handleCloseModal}>×</CloseButton>\n          </ModalHeader>\n\n          <form onSubmit={handleSubmit}>\n            <FormGroup>\n              <Label htmlFor=\"username\">Username *</Label>\n              <Input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                value={formData.username}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.username && <ErrorMessage>{errors.username}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"firstName\">First Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"firstName\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.firstName && <ErrorMessage>{errors.firstName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"lastName\">Last Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"lastName\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.lastName && <ErrorMessage>{errors.lastName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"email\">Email *</Label>\n              <Input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"role\">Role *</Label>\n              <Select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"Agent\">Agent</option>\n                <option value=\"Supervisor\">Supervisor</option>\n                <option value=\"Admin\">Admin</option>\n              </Select>\n            </FormGroup>\n\n            {!editingUser && (\n              <>\n                <FormGroup>\n                  <Label htmlFor=\"password\">Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.password && <ErrorMessage>{errors.password}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label htmlFor=\"confirmPassword\">Confirm Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    value={formData.confirmPassword}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.confirmPassword && <ErrorMessage>{errors.confirmPassword}</ErrorMessage>}\n                </FormGroup>\n              </>\n            )}\n\n            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}>\n              <Button type=\"button\" variant=\"outline\" onClick={handleCloseModal}>\n                Cancel\n              </Button>\n              <Button type=\"submit\" disabled={loading}>\n                {loading ? 'Saving...' : (editingUser ? 'Update User' : 'Create User')}\n              </Button>\n            </div>\n          </form>\n        </ModalContent>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,QAAwB,2BAA2B;AACvH,SAASC,UAAU,QAAc,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,SAAS,GAAGb,MAAM,CAACc,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,SAAS;AAMf,MAAMG,MAAM,GAAGhB,MAAM,CAACc,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACC,GAAA,GAPIL,MAAM;AASZ,MAAMM,KAAK,GAAGtB,MAAM,CAACuB,EAAE;AACvB;AACA;AACA,WAAWN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C,CAAC;AAACC,GAAA,GAJIH,KAAK;AAMX,MAAMI,cAAc,GAAG1B,MAAM,CAACc,GAAG;AACjC;AACA,CAAC;AAACa,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAG5B,MAAM,CAAC6B,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAG/B,MAAM,CAACgC,EAAE;AAC7B;AACA;AACA,6BAA6Bf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAClE,sBAAsBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACe,QAAQ;AAC1D;AACA,WAAWjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU;AACjD,CAAC;AAACC,GAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAGrC,MAAM,CAACsC,EAAE;AAC3B;AACA;AACA,6BAA6BrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAClE,CAAC;AAACM,GAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGxC,MAAM,CAACyC,EAAE;AAC1B;AACA,wBAAwBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAC7D;AACA,CAAC;AAACS,GAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAG3C,MAAM,CAAC4C,IAAyB;AACpD;AACA;AACA;AACA;AACA;AACA,sBAAsB3B,KAAK,IAAIA,KAAK,CAAC4B,MAAM,GAAG,SAAS,GAAG,SAAS;AACnE,WAAW5B,KAAK,IAAIA,KAAK,CAAC4B,MAAM,GAAG,SAAS,GAAG,SAAS;AACxD,CAAC;AAACC,GAAA,GARIH,WAAW;AAUjB,MAAMI,SAAS,GAAG/C,MAAM,CAAC4C,IAAsB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,IAAI3B,KAAK,IAAI;EACT,QAAQA,KAAK,CAAC+B,IAAI;IAChB,KAAK,OAAO;MACV,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,YAAY;MACf,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,OAAO;MACV,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA/BIF,SAAS;AAiCf,MAAMG,KAAK,GAAGlD,MAAM,CAACc,GAAsB;AAC3C,aAAaG,KAAK,IAAIA,KAAK,CAACkC,IAAI,GAAG,MAAM,GAAG,MAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,KAAK;AAaX,MAAMG,YAAY,GAAGrD,MAAM,CAACc,GAAG;AAC/B,gBAAgBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACmC,KAAK;AACjD,mBAAmBrC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqC,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GARIJ,YAAY;AAUlB,MAAMK,WAAW,GAAG1D,MAAM,CAACc,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACuC,IAAA,GAPID,WAAW;AASjB,MAAME,UAAU,GAAG5D,MAAM,CAAC6D,EAAE;AAC5B,WAAW5C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C;AACA,CAAC;AAACsC,IAAA,GAHIF,UAAU;AAKhB,MAAMG,WAAW,GAAG/D,MAAM,CAACgE,MAAM;AACjC;AACA;AACA;AACA;AACA,WAAW/C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU;AACjD;AACA;AACA,aAAalB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC8C,QAAQ;AACjD;AACA,CAAC;;AAED;AAAAC,IAAA,GAZMH,WAAW;AAcjB,MAAMI,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGtE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAG1E,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC+E,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiF,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAAC;IACvCmF,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTnC,IAAI,EAAE,OAA2C;IACjDoC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1F,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd0F,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMY,SAAS,GAAG,MAAMjF,UAAU,CAACkF,QAAQ,CAAC,CAAC;MAC7CnB,QAAQ,CAACkB,SAAS,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAE,KAAK,CAAC,yCAAyC,CAAC;IAClD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnB,WAAW,CAACoB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAIX,MAAM,CAACU,IAAI,CAAC,EAAE;MAChBT,SAAS,CAACY,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAoC,GAAG,CAAC,CAAC;IAE/C,IAAI,CAACvB,QAAQ,CAACE,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACrB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACF,QAAQ,CAACG,SAAS,CAACqB,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAACpB,SAAS,GAAG,wBAAwB;IAChD;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAACoB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACnB,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACJ,QAAQ,CAACK,KAAK,CAACmB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAClB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACoB,IAAI,CAACzB,QAAQ,CAACK,KAAK,CAAC,EAAE;MAC/CkB,SAAS,CAAClB,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACT,WAAW,EAAE;MAChB,IAAI,CAACI,QAAQ,CAACM,QAAQ,EAAE;QACtBiB,SAAS,CAACjB,QAAQ,GAAG,sBAAsB;MAC7C,CAAC,MAAM,IAAIN,QAAQ,CAACM,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;QACvCH,SAAS,CAACjB,QAAQ,GAAG,wCAAwC;MAC/D;MAEA,IAAIN,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,eAAe,EAAE;QAClDgB,SAAS,CAAChB,eAAe,GAAG,wBAAwB;MACtD;IACF;IAEAE,SAAS,CAACc,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOZ,CAAkB,IAAK;IACjDA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAvB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMgC,QAAQ,GAAG;QACf7B,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;QAC7BC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK;QACrBnC,IAAI,EAAE8B,QAAQ,CAAC9B,IAAI;QACnB,IAAI8B,QAAQ,CAACM,QAAQ,IAAI;UAAEA,QAAQ,EAAEN,QAAQ,CAACM;QAAS,CAAC;MAC1D,CAAC;MAED,IAAIV,WAAW,EAAE;QACf;QACA,MAAMoC,WAAW,GAAG,MAAMtG,UAAU,CAACuG,UAAU,CAACrC,WAAW,CAACsC,MAAM,EAAEH,QAAQ,CAAC;QAC7EtC,QAAQ,CAAC4B,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACF,MAAM,KAAKtC,WAAW,CAACsC,MAAM,GAAGF,WAAW,GAAGI,IACrD,CAAC,CAAC;QACFrB,KAAK,CAAC,4BAA4B,CAAC;MACrC,CAAC,MAAM;QACL;QACA,MAAMsB,OAAO,GAAG,MAAM3G,UAAU,CAAC4G,UAAU,CAACP,QAAQ,CAAC;QACrDtC,QAAQ,CAAC4B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEgB,OAAO,CAAC,CAAC;QACpCtB,KAAK,CAAC,4BAA4B,CAAC;MACrC;MAEAwB,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM2B,YAAY,GAAG3B,KAAK,YAAY4B,KAAK,GAAG5B,KAAK,CAAC6B,OAAO,GAAG,wCAAwC;MACtG3B,KAAK,CAACyB,YAAY,CAAC;IACrB,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9C,cAAc,CAAC,IAAI,CAAC;IACpBI,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTnC,IAAI,EAAE,OAAO;MACboC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiD,cAAc,GAAIR,IAAU,IAAK;IACrCvC,cAAc,CAACuC,IAAI,CAAC;IACpBnC,WAAW,CAAC;MACVC,QAAQ,EAAEkC,IAAI,CAAClC,QAAQ;MACvBC,SAAS,EAAEiC,IAAI,CAACjC,SAAS;MACzBC,QAAQ,EAAEgC,IAAI,CAAChC,QAAQ;MACvBC,KAAK,EAAE+B,IAAI,CAAC/B,KAAK;MACjBnC,IAAI,EAAEkE,IAAI,CAAClE,IAAI;MACfoC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkD,sBAAsB,GAAG,MAAOX,MAAc,IAAK;IACvD,IAAI;MACF,MAAMxG,UAAU,CAACoH,gBAAgB,CAACZ,MAAM,CAAC;MACzC;MACAzC,QAAQ,CAAC4B,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACF,MAAM,KAAKA,MAAM,GAAG;QAAE,GAAGE,IAAI;QAAEW,QAAQ,EAAE,CAACX,IAAI,CAACW;MAAS,CAAC,GAAGX,IACnE,CAAC,CAAC;MACFrB,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAM2B,YAAY,GAAG3B,KAAK,YAAY4B,KAAK,GAAG5B,KAAK,CAAC6B,OAAO,GAAG,iDAAiD;MAC/G3B,KAAK,CAACyB,YAAY,CAAC;IACrB;EACF,CAAC;EAED,MAAMD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5C,YAAY,CAAC,KAAK,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;IACpBI,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTnC,IAAI,EAAE,OAAO;MACboC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;EAED,MAAMuC,UAAU,GAAGA,CAAA,KAAM;IACvBzD,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,MAAM0D,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,oBACExH,OAAA,CAACG,SAAS;IAAAsH,QAAA,gBACRzH,OAAA,CAACM,MAAM;MAAAmH,QAAA,gBACLzH,OAAA;QAAK0H,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACpDzH,OAAA,CAACR,MAAM;UAACqI,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEV,UAAW;UAAAK,QAAA,EAAC;QAE/C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlI,OAAA,CAACY,KAAK;UAAC8G,KAAK,EAAE;YAAES,UAAU,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNlI,OAAA,CAACR,MAAM;QAACsI,OAAO,EAAEf,gBAAiB;QAAAU,QAAA,EAAC;MAEnC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAETlI,OAAA,CAACT,IAAI;MAAAkI,QAAA,gBACHzH,OAAA,CAACgB,cAAc;QAAAyG,QAAA,eACbzH,OAAA,CAACkB,KAAK;UAAAuG,QAAA,gBACJzH,OAAA;YAAAyH,QAAA,eACEzH,OAAA;cAAAyH,QAAA,gBACEzH,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/BlI,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnClI,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChClI,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/BlI,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjClI,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvClI,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrClI,OAAA,CAACqB,WAAW;gBAAAoG,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRlI,OAAA;YAAAyH,QAAA,EACG7D,KAAK,CAAC2C,GAAG,CAAEC,IAAI,iBACdxG,OAAA,CAAC8B,QAAQ;cAAA2F,QAAA,gBACPzH,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,GAAEjB,IAAI,CAACjC,SAAS,EAAC,GAAC,EAACiC,IAAI,CAAChC,QAAQ;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvDlI,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,EAAEjB,IAAI,CAAClC;cAAQ;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtClI,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,EAAEjB,IAAI,CAAC/B;cAAK;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnClI,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,eACRzH,OAAA,CAACqC,SAAS;kBAACC,IAAI,EAAEkE,IAAI,CAAClE,IAAK;kBAAAmF,QAAA,EAAEjB,IAAI,CAAClE;gBAAI;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACZlI,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,eACRzH,OAAA,CAACiC,WAAW;kBAACE,MAAM,EAAEqE,IAAI,CAACW,QAAS;kBAAAM,QAAA,EAChCjB,IAAI,CAACW,QAAQ,GAAG,QAAQ,GAAG;gBAAU;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACZlI,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,EAAEJ,UAAU,CAACb,IAAI,CAAC4B,WAAW;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrDlI,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,EACPjB,IAAI,CAAC6B,aAAa,GAAGhB,UAAU,CAACb,IAAI,CAAC6B,aAAa,CAAC,GAAG;cAAO;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACZlI,OAAA,CAAC2B,SAAS;gBAAA8F,QAAA,eACRzH,OAAA;kBAAK0H,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEW,GAAG,EAAE;kBAAM,CAAE;kBAAAb,QAAA,gBAC1CzH,OAAA,CAACR,MAAM;oBACL+I,IAAI,EAAC,IAAI;oBACTT,OAAO,EAAEA,CAAA,KAAMd,cAAc,CAACR,IAAI,CAAE;oBAAAiB,QAAA,EACrC;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlI,OAAA,CAACR,MAAM;oBACL+I,IAAI,EAAC,IAAI;oBACTV,OAAO,EAAErB,IAAI,CAACW,QAAQ,GAAG,QAAQ,GAAG,WAAY;oBAChDW,OAAO,EAAEA,CAAA,KAAMb,sBAAsB,CAACT,IAAI,CAACF,MAAM,CAAE;oBAAAmB,QAAA,EAElDjB,IAAI,CAACW,QAAQ,GAAG,YAAY,GAAG;kBAAU;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GAhCC1B,IAAI,CAACF,MAAM;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiChB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBtE,KAAK,CAACkC,MAAM,KAAK,CAAC,iBACjB9F,OAAA;QAAK0H,KAAK,EAAE;UAAEc,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAjB,QAAA,EAAC;MAErE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPlI,OAAA,CAACwC,KAAK;MAACC,IAAI,EAAEqB,SAAU;MAAA2D,QAAA,eACrBzH,OAAA,CAAC2C,YAAY;QAAA8E,QAAA,gBACXzH,OAAA,CAACgD,WAAW;UAAAyE,QAAA,gBACVzH,OAAA,CAACkD,UAAU;YAAAuE,QAAA,EACRzD,WAAW,GAAG,WAAW,GAAG;UAAiB;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACblI,OAAA,CAACqD,WAAW;YAACyE,OAAO,EAAEnB,gBAAiB;YAAAc,QAAA,EAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAEdlI,OAAA;UAAM2I,QAAQ,EAAE1C,YAAa;UAAAwB,QAAA,gBAC3BzH,OAAA,CAACL,SAAS;YAAA8H,QAAA,gBACRzH,OAAA,CAACJ,KAAK;cAACgJ,OAAO,EAAC,UAAU;cAAAnB,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5ClI,OAAA,CAACP,KAAK;cACJoJ,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACbxD,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEnB,QAAQ,CAACE,QAAS;cACzByE,QAAQ,EAAE3D,iBAAkB;cAC5B4D,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDtD,MAAM,CAACN,QAAQ,iBAAItE,OAAA,CAACH,YAAY;cAAA4H,QAAA,EAAE7C,MAAM,CAACN;YAAQ;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAEZlI,OAAA,CAACL,SAAS;YAAA8H,QAAA,gBACRzH,OAAA,CAACJ,KAAK;cAACgJ,OAAO,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAAY;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/ClI,OAAA,CAACP,KAAK;cACJoJ,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,WAAW;cACdxD,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEnB,QAAQ,CAACG,SAAU;cAC1BwE,QAAQ,EAAE3D,iBAAkB;cAC5B4D,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDtD,MAAM,CAACL,SAAS,iBAAIvE,OAAA,CAACH,YAAY;cAAA4H,QAAA,EAAE7C,MAAM,CAACL;YAAS;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEZlI,OAAA,CAACL,SAAS;YAAA8H,QAAA,gBACRzH,OAAA,CAACJ,KAAK;cAACgJ,OAAO,EAAC,UAAU;cAAAnB,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7ClI,OAAA,CAACP,KAAK;cACJoJ,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACbxD,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEnB,QAAQ,CAACI,QAAS;cACzBuE,QAAQ,EAAE3D,iBAAkB;cAC5B4D,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDtD,MAAM,CAACJ,QAAQ,iBAAIxE,OAAA,CAACH,YAAY;cAAA4H,QAAA,EAAE7C,MAAM,CAACJ;YAAQ;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAEZlI,OAAA,CAACL,SAAS;YAAA8H,QAAA,gBACRzH,OAAA,CAACJ,KAAK;cAACgJ,OAAO,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtClI,OAAA,CAACP,KAAK;cACJoJ,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACVxD,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEnB,QAAQ,CAACK,KAAM;cACtBsE,QAAQ,EAAE3D,iBAAkB;cAC5B4D,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDtD,MAAM,CAACH,KAAK,iBAAIzE,OAAA,CAACH,YAAY;cAAA4H,QAAA,EAAE7C,MAAM,CAACH;YAAK;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEZlI,OAAA,CAACL,SAAS;YAAA8H,QAAA,gBACRzH,OAAA,CAACJ,KAAK;cAACgJ,OAAO,EAAC,MAAM;cAAAnB,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpClI,OAAA,CAACN,MAAM;cACLoJ,EAAE,EAAC,MAAM;cACTxD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEnB,QAAQ,CAAC9B,IAAK;cACrByG,QAAQ,EAAE3D,iBAAkB;cAC5B4D,QAAQ;cAAAvB,QAAA,gBAERzH,OAAA;gBAAQuF,KAAK,EAAC,OAAO;gBAAAkC,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpClI,OAAA;gBAAQuF,KAAK,EAAC,YAAY;gBAAAkC,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9ClI,OAAA;gBAAQuF,KAAK,EAAC,OAAO;gBAAAkC,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAEX,CAAClE,WAAW,iBACXhE,OAAA,CAAAE,SAAA;YAAAuH,QAAA,gBACEzH,OAAA,CAACL,SAAS;cAAA8H,QAAA,gBACRzH,OAAA,CAACJ,KAAK;gBAACgJ,OAAO,EAAC,UAAU;gBAAAnB,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5ClI,OAAA,CAACP,KAAK;gBACJoJ,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,UAAU;gBACbxD,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEnB,QAAQ,CAACM,QAAS;gBACzBqE,QAAQ,EAAE3D,iBAAkB;gBAC5B4D,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDtD,MAAM,CAACF,QAAQ,iBAAI1E,OAAA,CAACH,YAAY;gBAAA4H,QAAA,EAAE7C,MAAM,CAACF;cAAQ;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eAEZlI,OAAA,CAACL,SAAS;cAAA8H,QAAA,gBACRzH,OAAA,CAACJ,KAAK;gBAACgJ,OAAO,EAAC,iBAAiB;gBAAAnB,QAAA,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DlI,OAAA,CAACP,KAAK;gBACJoJ,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,iBAAiB;gBACpBxD,IAAI,EAAC,iBAAiB;gBACtBC,KAAK,EAAEnB,QAAQ,CAACO,eAAgB;gBAChCoE,QAAQ,EAAE3D,iBAAkB;gBAC5B4D,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDtD,MAAM,CAACD,eAAe,iBAAI3E,OAAA,CAACH,YAAY;gBAAA4H,QAAA,EAAE7C,MAAM,CAACD;cAAe;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA,eACZ,CACH,eAEDlI,OAAA;YAAK0H,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEW,GAAG,EAAE,MAAM;cAAEW,cAAc,EAAE,UAAU;cAAEC,SAAS,EAAE;YAAO,CAAE;YAAAzB,QAAA,gBAC1FzH,OAAA,CAACR,MAAM;cAACqJ,IAAI,EAAC,QAAQ;cAAChB,OAAO,EAAC,SAAS;cAACC,OAAO,EAAEnB,gBAAiB;cAAAc,QAAA,EAAC;YAEnE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlI,OAAA,CAACR,MAAM;cAACqJ,IAAI,EAAC,QAAQ;cAACM,QAAQ,EAAEjF,OAAQ;cAAAuD,QAAA,EACrCvD,OAAO,GAAG,WAAW,GAAIF,WAAW,GAAG,aAAa,GAAG;YAAc;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACxE,EAAA,CAvYID,cAAwB;EAAA,QACXpE,WAAW;AAAA;AAAA+J,IAAA,GADxB3F,cAAwB;AAyY9B,eAAeA,cAAc;AAAC,IAAApD,EAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAA4F,IAAA;AAAAC,YAAA,CAAAhJ,EAAA;AAAAgJ,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAAtI,GAAA;AAAAsI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}