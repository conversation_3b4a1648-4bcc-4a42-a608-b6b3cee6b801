{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Common\\\\DocumentUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport styled from 'styled-components';\nimport { <PERSON><PERSON>, <PERSON>adingSpinner, Badge } from '../../styles/GlobalStyles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadContainer = styled.div`\n  border: 2px dashed ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.xl};\n  padding: ${props => props.theme.spacing.xl};\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n  background: ${props => props.theme.colors.backgroundSecondary};\n\n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    background: ${props => props.theme.colors.backgroundTertiary};\n  }\n\n  &.drag-over {\n    border-color: ${props => props.theme.colors.primary};\n    background: ${props => props.theme.colors.primaryLight}20;\n  }\n`;\n_c = UploadContainer;\nconst UploadIcon = styled.div`\n  font-size: 48px;\n  color: ${props => props.theme.colors.textTertiary};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n_c2 = UploadIcon;\nconst UploadText = styled.div`\n  color: ${props => props.theme.colors.textSecondary};\n  font-size: ${props => props.theme.typography.fontSize.lg};\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c3 = UploadText;\nconst UploadSubtext = styled.div`\n  color: ${props => props.theme.colors.textTertiary};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c4 = UploadSubtext;\nconst HiddenInput = styled.input`\n  display: none;\n`;\n_c5 = HiddenInput;\nconst DocumentList = styled.div`\n  margin-top: ${props => props.theme.spacing.lg};\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.md};\n`;\n_c6 = DocumentList;\nconst DocumentItem = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.theme.colors.white};\n  transition: ${props => props.theme.transitions.default};\n\n  ${props => props.status === 'error' && `\n    border-color: ${props.theme.colors.error};\n    background: ${props.theme.colors.errorLight};\n  `}\n\n  ${props => props.status === 'completed' && `\n    border-color: ${props.theme.colors.success};\n    background: ${props.theme.colors.successLight};\n  `}\n`;\n_c7 = DocumentItem;\nconst DocumentInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n  flex: 1;\n`;\n_c8 = DocumentInfo;\nconst DocumentIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: ${props => props.theme.borderRadius.md};\n  background: ${props => props.theme.colors.primaryGradient};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${props => props.theme.colors.white};\n  font-weight: ${props => props.theme.typography.fontWeight.bold};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n`;\n_c9 = DocumentIcon;\nconst DocumentDetails = styled.div`\n  flex: 1;\n`;\n_c0 = DocumentDetails;\nconst DocumentName = styled.div`\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n  margin-bottom: 2px;\n`;\n_c1 = DocumentName;\nconst DocumentMeta = styled.div`\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  color: ${props => props.theme.colors.textTertiary};\n`;\n_c10 = DocumentMeta;\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 4px;\n  background: ${props => props.theme.colors.backgroundTertiary};\n  border-radius: ${props => props.theme.borderRadius.full};\n  overflow: hidden;\n  margin-top: ${props => props.theme.spacing.xs};\n`;\n_c11 = ProgressBar;\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: ${props => props.theme.colors.primaryGradient};\n  width: ${props => props.percentage}%;\n  transition: width 0.3s ease;\n`;\n_c12 = ProgressFill;\nconst DocumentActions = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c13 = DocumentActions;\nconst ErrorMessage = styled.div`\n  color: ${props => props.theme.colors.error};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  margin-top: ${props => props.theme.spacing.xs};\n`;\n_c14 = ErrorMessage;\nconst DocumentUploadComponent = ({\n  documents,\n  onDocumentsChange,\n  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],\n  maxFileSize = 10,\n  // 10MB default\n  maxFiles = 10,\n  required = false\n}) => {\n  _s();\n  const [dragOver, setDragOver] = useState(false);\n  const fileInputRef = useRef(null);\n  const validateFile = file => {\n    var _file$name$split$pop;\n    // Check file size\n    if (file.size > maxFileSize * 1024 * 1024) {\n      return `File size must be less than ${maxFileSize}MB`;\n    }\n\n    // Check file type\n    const fileExtension = '.' + ((_file$name$split$pop = file.name.split('.').pop()) === null || _file$name$split$pop === void 0 ? void 0 : _file$name$split$pop.toLowerCase());\n    if (!acceptedTypes.includes(fileExtension)) {\n      return `File type not supported. Accepted types: ${acceptedTypes.join(', ')}`;\n    }\n    return null;\n  };\n  const handleFiles = useCallback(files => {\n    const newDocuments = [];\n    for (let i = 0; i < files.length && documents.length + newDocuments.length < maxFiles; i++) {\n      const file = files[i];\n      const error = validateFile(file);\n      const documentUpload = {\n        file,\n        documentType: getDocumentTypeFromFileName(file.name),\n        documentTypeId: 1,\n        // Default, should be set based on actual document type\n        category: 'General',\n        isRequired: required,\n        status: error ? 'error' : 'pending',\n        error: error || undefined\n      };\n      newDocuments.push(documentUpload);\n    }\n    onDocumentsChange([...documents, ...newDocuments]);\n  }, [documents, onDocumentsChange, maxFiles, maxFileSize, acceptedTypes, required]);\n  const getDocumentTypeFromFileName = fileName => {\n    const name = fileName.toLowerCase();\n    if (name.includes('id') || name.includes('identity')) return 'ID Proof';\n    if (name.includes('address') || name.includes('utility')) return 'Address Proof';\n    if (name.includes('income') || name.includes('salary')) return 'Income Proof';\n    if (name.includes('bank') || name.includes('statement')) return 'Bank Statement';\n    return 'Other Document';\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setDragOver(false);\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      handleFiles(files);\n    }\n  };\n  const handleFileSelect = e => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      handleFiles(files);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n  const removeDocument = index => {\n    const updatedDocuments = documents.filter((_, i) => i !== index);\n    onDocumentsChange(updatedDocuments);\n  };\n  const retryUpload = index => {\n    const updatedDocuments = [...documents];\n    updatedDocuments[index] = {\n      ...updatedDocuments[index],\n      status: 'pending',\n      error: undefined\n    };\n    onDocumentsChange(updatedDocuments);\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const getFileIcon = fileName => {\n    var _fileName$split$pop;\n    const extension = (_fileName$split$pop = fileName.split('.').pop()) === null || _fileName$split$pop === void 0 ? void 0 : _fileName$split$pop.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return '📄';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n        return '🖼️';\n      case 'doc':\n      case 'docx':\n        return '📝';\n      default:\n        return '📎';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(UploadContainer, {\n      className: dragOver ? 'drag-over' : '',\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      onClick: () => {\n        var _fileInputRef$current;\n        return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n      },\n      children: [/*#__PURE__*/_jsxDEV(UploadIcon, {\n        children: \"\\uD83D\\uDCC1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UploadText, {\n        children: \"Drag and drop files here, or click to select\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UploadSubtext, {\n        children: [\"Supported formats: \", acceptedTypes.join(', '), \" \\u2022 Max size: \", maxFileSize, \"MB each\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        type: \"button\",\n        children: \"Choose Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HiddenInput, {\n        ref: fileInputRef,\n        type: \"file\",\n        multiple: true,\n        accept: acceptedTypes.join(','),\n        onChange: handleFileSelect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), documents.length > 0 && /*#__PURE__*/_jsxDEV(DocumentList, {\n      children: documents.map((doc, index) => /*#__PURE__*/_jsxDEV(DocumentItem, {\n        status: doc.status,\n        children: [/*#__PURE__*/_jsxDEV(DocumentInfo, {\n          children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n            children: doc.status === 'uploading' ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 21\n            }, this) : getFileIcon(doc.file.name)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(DocumentDetails, {\n            children: [/*#__PURE__*/_jsxDEV(DocumentName, {\n              children: doc.file.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(DocumentMeta, {\n              children: [formatFileSize(doc.file.size), \" \\u2022 \", doc.documentType]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this), doc.status === 'uploading' && doc.uploadProgress && /*#__PURE__*/_jsxDEV(ProgressBar, {\n              children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n                percentage: doc.uploadProgress.percentage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 21\n            }, this), doc.error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: doc.error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(DocumentActions, {\n          children: [/*#__PURE__*/_jsxDEV(Badge, {\n            variant: doc.status === 'completed' ? 'success' : doc.status === 'error' ? 'error' : doc.status === 'uploading' ? 'info' : 'secondary',\n            size: \"sm\",\n            children: doc.status === 'uploading' ? 'Uploading...' : doc.status === 'completed' ? 'Uploaded' : doc.status === 'error' ? 'Failed' : 'Ready'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 17\n          }, this), doc.status === 'error' && /*#__PURE__*/_jsxDEV(Button, {\n            size: \"sm\",\n            variant: \"outline\",\n            onClick: () => retryUpload(index),\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"sm\",\n            variant: \"danger\",\n            onClick: () => removeDocument(index),\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentUploadComponent, \"7BmhI3OZqhuP4htmxmyWCJai/kQ=\");\n_c15 = DocumentUploadComponent;\nexport default DocumentUploadComponent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"UploadContainer\");\n$RefreshReg$(_c2, \"UploadIcon\");\n$RefreshReg$(_c3, \"UploadText\");\n$RefreshReg$(_c4, \"UploadSubtext\");\n$RefreshReg$(_c5, \"HiddenInput\");\n$RefreshReg$(_c6, \"DocumentList\");\n$RefreshReg$(_c7, \"DocumentItem\");\n$RefreshReg$(_c8, \"DocumentInfo\");\n$RefreshReg$(_c9, \"DocumentIcon\");\n$RefreshReg$(_c0, \"DocumentDetails\");\n$RefreshReg$(_c1, \"DocumentName\");\n$RefreshReg$(_c10, \"DocumentMeta\");\n$RefreshReg$(_c11, \"ProgressBar\");\n$RefreshReg$(_c12, \"ProgressFill\");\n$RefreshReg$(_c13, \"DocumentActions\");\n$RefreshReg$(_c14, \"ErrorMessage\");\n$RefreshReg$(_c15, \"DocumentUploadComponent\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "styled", "<PERSON><PERSON>", "LoadingSpinner", "Badge", "jsxDEV", "_jsxDEV", "UploadContainer", "div", "props", "theme", "colors", "border", "borderRadius", "xl", "spacing", "transitions", "default", "backgroundSecondary", "primary", "backgroundTertiary", "primaryLight", "_c", "UploadIcon", "textTertiary", "md", "_c2", "UploadText", "textSecondary", "typography", "fontSize", "lg", "fontWeight", "medium", "sm", "_c3", "UploadSubtext", "_c4", "HiddenInput", "input", "_c5", "DocumentList", "_c6", "DocumentItem", "white", "status", "error", "errorLight", "success", "successLight", "_c7", "DocumentInfo", "_c8", "DocumentIcon", "primaryGradient", "bold", "_c9", "DocumentDetails", "_c0", "DocumentName", "textPrimary", "_c1", "DocumentMeta", "_c10", "ProgressBar", "full", "xs", "_c11", "ProgressFill", "percentage", "_c12", "DocumentActions", "_c13", "ErrorMessage", "_c14", "DocumentUploadComponent", "documents", "onDocumentsChange", "acceptedTypes", "maxFileSize", "maxFiles", "required", "_s", "dragOver", "setDragOver", "fileInputRef", "validateFile", "file", "_file$name$split$pop", "size", "fileExtension", "name", "split", "pop", "toLowerCase", "includes", "join", "handleFiles", "files", "newDocuments", "i", "length", "documentUpload", "documentType", "getDocumentTypeFromFileName", "documentTypeId", "category", "isRequired", "undefined", "push", "fileName", "handleDragOver", "e", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleFileSelect", "target", "value", "removeDocument", "index", "updatedDocuments", "filter", "_", "retryUpload", "formatFileSize", "bytes", "k", "sizes", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "getFileIcon", "_fileName$split$pop", "extension", "children", "className", "onDragOver", "onDragLeave", "onDrop", "onClick", "_fileInputRef$current", "current", "click", "_jsxFileName", "lineNumber", "columnNumber", "variant", "type", "ref", "multiple", "accept", "onChange", "map", "doc", "uploadProgress", "_c15", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Common/DocumentUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport styled from 'styled-components';\nimport { <PERSON><PERSON>, <PERSON>ading<PERSON><PERSON><PERSON>, Badge } from '../../styles/GlobalStyles';\nimport { DocumentUpload, UploadProgress } from '../../services/apiService';\n\nconst UploadContainer = styled.div`\n  border: 2px dashed ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.xl};\n  padding: ${props => props.theme.spacing.xl};\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n  background: ${props => props.theme.colors.backgroundSecondary};\n\n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    background: ${props => props.theme.colors.backgroundTertiary};\n  }\n\n  &.drag-over {\n    border-color: ${props => props.theme.colors.primary};\n    background: ${props => props.theme.colors.primaryLight}20;\n  }\n`;\n\nconst UploadIcon = styled.div`\n  font-size: 48px;\n  color: ${props => props.theme.colors.textTertiary};\n  margin-bottom: ${props => props.theme.spacing.md};\n`;\n\nconst UploadText = styled.div`\n  color: ${props => props.theme.colors.textSecondary};\n  font-size: ${props => props.theme.typography.fontSize.lg};\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst UploadSubtext = styled.div`\n  color: ${props => props.theme.colors.textTertiary};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst HiddenInput = styled.input`\n  display: none;\n`;\n\nconst DocumentList = styled.div`\n  margin-top: ${props => props.theme.spacing.lg};\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.md};\n`;\n\nconst DocumentItem = styled.div<{ status: string }>`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  background: ${props => props.theme.colors.white};\n  transition: ${props => props.theme.transitions.default};\n\n  ${props => props.status === 'error' && `\n    border-color: ${props.theme.colors.error};\n    background: ${props.theme.colors.errorLight};\n  `}\n\n  ${props => props.status === 'completed' && `\n    border-color: ${props.theme.colors.success};\n    background: ${props.theme.colors.successLight};\n  `}\n`;\n\nconst DocumentInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n  flex: 1;\n`;\n\nconst DocumentIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: ${props => props.theme.borderRadius.md};\n  background: ${props => props.theme.colors.primaryGradient};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${props => props.theme.colors.white};\n  font-weight: ${props => props.theme.typography.fontWeight.bold};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n`;\n\nconst DocumentDetails = styled.div`\n  flex: 1;\n`;\n\nconst DocumentName = styled.div`\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n  margin-bottom: 2px;\n`;\n\nconst DocumentMeta = styled.div`\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  color: ${props => props.theme.colors.textTertiary};\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 4px;\n  background: ${props => props.theme.colors.backgroundTertiary};\n  border-radius: ${props => props.theme.borderRadius.full};\n  overflow: hidden;\n  margin-top: ${props => props.theme.spacing.xs};\n`;\n\nconst ProgressFill = styled.div<{ percentage: number }>`\n  height: 100%;\n  background: ${props => props.theme.colors.primaryGradient};\n  width: ${props => props.percentage}%;\n  transition: width 0.3s ease;\n`;\n\nconst DocumentActions = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst ErrorMessage = styled.div`\n  color: ${props => props.theme.colors.error};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  margin-top: ${props => props.theme.spacing.xs};\n`;\n\ninterface DocumentUploadComponentProps {\n  documents: DocumentUpload[];\n  onDocumentsChange: (documents: DocumentUpload[]) => void;\n  acceptedTypes?: string[];\n  maxFileSize?: number; // in MB\n  maxFiles?: number;\n  required?: boolean;\n}\n\nconst DocumentUploadComponent: React.FC<DocumentUploadComponentProps> = ({\n  documents,\n  onDocumentsChange,\n  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],\n  maxFileSize = 10, // 10MB default\n  maxFiles = 10,\n  required = false,\n}) => {\n  const [dragOver, setDragOver] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const validateFile = (file: File): string | null => {\n    // Check file size\n    if (file.size > maxFileSize * 1024 * 1024) {\n      return `File size must be less than ${maxFileSize}MB`;\n    }\n\n    // Check file type\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    if (!acceptedTypes.includes(fileExtension)) {\n      return `File type not supported. Accepted types: ${acceptedTypes.join(', ')}`;\n    }\n\n    return null;\n  };\n\n  const handleFiles = useCallback((files: FileList) => {\n    const newDocuments: DocumentUpload[] = [];\n\n    for (let i = 0; i < files.length && documents.length + newDocuments.length < maxFiles; i++) {\n      const file = files[i];\n      const error = validateFile(file);\n\n      const documentUpload: DocumentUpload = {\n        file,\n        documentType: getDocumentTypeFromFileName(file.name),\n        documentTypeId: 1, // Default, should be set based on actual document type\n        category: 'General',\n        isRequired: required,\n        status: error ? 'error' : 'pending',\n        error: error || undefined,\n      };\n\n      newDocuments.push(documentUpload);\n    }\n\n    onDocumentsChange([...documents, ...newDocuments]);\n  }, [documents, onDocumentsChange, maxFiles, maxFileSize, acceptedTypes, required]);\n\n  const getDocumentTypeFromFileName = (fileName: string): string => {\n    const name = fileName.toLowerCase();\n    if (name.includes('id') || name.includes('identity')) return 'ID Proof';\n    if (name.includes('address') || name.includes('utility')) return 'Address Proof';\n    if (name.includes('income') || name.includes('salary')) return 'Income Proof';\n    if (name.includes('bank') || name.includes('statement')) return 'Bank Statement';\n    return 'Other Document';\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(false);\n\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      handleFiles(files);\n    }\n  };\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      handleFiles(files);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  const removeDocument = (index: number) => {\n    const updatedDocuments = documents.filter((_, i) => i !== index);\n    onDocumentsChange(updatedDocuments);\n  };\n\n  const retryUpload = (index: number) => {\n    const updatedDocuments = [...documents];\n    updatedDocuments[index] = {\n      ...updatedDocuments[index],\n      status: 'pending',\n      error: undefined,\n    };\n    onDocumentsChange(updatedDocuments);\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const getFileIcon = (fileName: string): string => {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf': return '📄';\n      case 'jpg':\n      case 'jpeg':\n      case 'png': return '🖼️';\n      case 'doc':\n      case 'docx': return '📝';\n      default: return '📎';\n    }\n  };\n\n  return (\n    <div>\n      <UploadContainer\n        className={dragOver ? 'drag-over' : ''}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={() => fileInputRef.current?.click()}\n      >\n        <UploadIcon>📁</UploadIcon>\n        <UploadText>\n          Drag and drop files here, or click to select\n        </UploadText>\n        <UploadSubtext>\n          Supported formats: {acceptedTypes.join(', ')} • Max size: {maxFileSize}MB each\n        </UploadSubtext>\n        <Button variant=\"outline\" type=\"button\">\n          Choose Files\n        </Button>\n\n        <HiddenInput\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept={acceptedTypes.join(',')}\n          onChange={handleFileSelect}\n        />\n      </UploadContainer>\n\n      {documents.length > 0 && (\n        <DocumentList>\n          {documents.map((doc, index) => (\n            <DocumentItem key={index} status={doc.status}>\n              <DocumentInfo>\n                <DocumentIcon>\n                  {doc.status === 'uploading' ? (\n                    <LoadingSpinner size=\"sm\" />\n                  ) : (\n                    getFileIcon(doc.file.name)\n                  )}\n                </DocumentIcon>\n                <DocumentDetails>\n                  <DocumentName>{doc.file.name}</DocumentName>\n                  <DocumentMeta>\n                    {formatFileSize(doc.file.size)} • {doc.documentType}\n                  </DocumentMeta>\n                  {doc.status === 'uploading' && doc.uploadProgress && (\n                    <ProgressBar>\n                      <ProgressFill percentage={doc.uploadProgress.percentage} />\n                    </ProgressBar>\n                  )}\n                  {doc.error && <ErrorMessage>{doc.error}</ErrorMessage>}\n                </DocumentDetails>\n              </DocumentInfo>\n\n              <DocumentActions>\n                <Badge\n                  variant={\n                    doc.status === 'completed' ? 'success' :\n                    doc.status === 'error' ? 'error' :\n                    doc.status === 'uploading' ? 'info' : 'secondary'\n                  }\n                  size=\"sm\"\n                >\n                  {doc.status === 'uploading' ? 'Uploading...' :\n                   doc.status === 'completed' ? 'Uploaded' :\n                   doc.status === 'error' ? 'Failed' : 'Ready'}\n                </Badge>\n\n                {doc.status === 'error' && (\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={() => retryUpload(index)}\n                  >\n                    Retry\n                  </Button>\n                )}\n\n                <Button\n                  size=\"sm\"\n                  variant=\"danger\"\n                  onClick={() => removeDocument(index)}\n                >\n                  Remove\n                </Button>\n              </DocumentActions>\n            </DocumentItem>\n          ))}\n        </DocumentList>\n      )}\n    </div>\n  );\n};\n\nexport default DocumentUploadComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,cAAc,EAAEC,KAAK,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1E,MAAMC,eAAe,GAAGN,MAAM,CAACO,GAAG;AAClC,uBAAuBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM;AACzD,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD,aAAaL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACD,EAAE;AAC5C;AACA,gBAAgBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,WAAW,CAACC,OAAO;AACxD,gBAAgBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACO,mBAAmB;AAC/D;AACA;AACA,oBAAoBT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACQ,OAAO;AACvD,kBAAkBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACS,kBAAkB;AAChE;AACA;AACA;AACA,oBAAoBX,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACQ,OAAO;AACvD,kBAAkBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACU,YAAY;AAC1D;AACA,CAAC;AAACC,EAAA,GAjBIf,eAAe;AAmBrB,MAAMgB,UAAU,GAAGtB,MAAM,CAACO,GAAG;AAC7B;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,YAAY;AACnD,mBAAmBf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACU,EAAE;AAClD,CAAC;AAACC,GAAA,GAJIH,UAAU;AAMhB,MAAMI,UAAU,GAAG1B,MAAM,CAACO,GAAG;AAC7B,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,aAAa;AACpD,eAAenB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACC,QAAQ,CAACC,EAAE;AAC1D,iBAAiBtB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACG,UAAU,CAACC,MAAM;AAClE,mBAAmBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACmB,EAAE;AAClD,CAAC;AAACC,GAAA,GALIR,UAAU;AAOhB,MAAMS,aAAa,GAAGnC,MAAM,CAACO,GAAG;AAChC,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,YAAY;AACnD,eAAef,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACC,QAAQ,CAACI,EAAE;AAC1D,mBAAmBzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACgB,EAAE;AAClD,CAAC;AAACM,GAAA,GAJID,aAAa;AAMnB,MAAME,WAAW,GAAGrC,MAAM,CAACsC,KAAK;AAChC;AACA,CAAC;AAACC,GAAA,GAFIF,WAAW;AAIjB,MAAMG,YAAY,GAAGxC,MAAM,CAACO,GAAG;AAC/B,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACgB,EAAE;AAC/C;AACA;AACA,SAAStB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACU,EAAE;AACxC,CAAC;AAACiB,GAAA,GALID,YAAY;AAOlB,MAAME,YAAY,GAAG1C,MAAM,CAACO,GAAuB;AACnD;AACA;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACU,EAAE;AAC5C,sBAAsBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM;AACxD,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACkB,EAAE;AACvD,gBAAgBtB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiC,KAAK;AACjD,gBAAgBnC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,WAAW,CAACC,OAAO;AACxD;AACA,IAAIR,KAAK,IAAIA,KAAK,CAACoC,MAAM,KAAK,OAAO,IAAI;AACzC,oBAAoBpC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACmC,KAAK;AAC5C,kBAAkBrC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACoC,UAAU;AAC/C,GAAG;AACH;AACA,IAAItC,KAAK,IAAIA,KAAK,CAACoC,MAAM,KAAK,WAAW,IAAI;AAC7C,oBAAoBpC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACqC,OAAO;AAC9C,kBAAkBvC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACsC,YAAY;AACjD,GAAG;AACH,CAAC;AAACC,GAAA,GAnBIP,YAAY;AAqBlB,MAAMQ,YAAY,GAAGlD,MAAM,CAACO,GAAG;AAC/B;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACU,EAAE;AACxC;AACA,CAAC;AAAC2B,GAAA,GALID,YAAY;AAOlB,MAAME,YAAY,GAAGpD,MAAM,CAACO,GAAG;AAC/B;AACA;AACA,mBAAmBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACY,EAAE;AACvD,gBAAgBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2C,eAAe;AAC3D;AACA;AACA;AACA,WAAW7C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiC,KAAK;AAC5C,iBAAiBnC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACG,UAAU,CAACuB,IAAI;AAChE,eAAe9C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACC,QAAQ,CAACI,EAAE;AAC1D,CAAC;AAACsB,GAAA,GAXIH,YAAY;AAalB,MAAMI,eAAe,GAAGxD,MAAM,CAACO,GAAG;AAClC;AACA,CAAC;AAACkD,GAAA,GAFID,eAAe;AAIrB,MAAME,YAAY,GAAG1D,MAAM,CAACO,GAAG;AAC/B,iBAAiBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACG,UAAU,CAACC,MAAM;AAClE,WAAWxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiD,WAAW;AAClD;AACA,CAAC;AAACC,GAAA,GAJIF,YAAY;AAMlB,MAAMG,YAAY,GAAG7D,MAAM,CAACO,GAAG;AAC/B,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACC,QAAQ,CAACI,EAAE;AAC1D,WAAWzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,YAAY;AACnD,CAAC;AAACuC,IAAA,GAHID,YAAY;AAKlB,MAAME,WAAW,GAAG/D,MAAM,CAACO,GAAG;AAC9B;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACS,kBAAkB;AAC9D,mBAAmBX,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACoD,IAAI;AACzD;AACA,gBAAgBxD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACmD,EAAE;AAC/C,CAAC;AAACC,IAAA,GAPIH,WAAW;AASjB,MAAMI,YAAY,GAAGnE,MAAM,CAACO,GAA2B;AACvD;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2C,eAAe;AAC3D,WAAW7C,KAAK,IAAIA,KAAK,CAAC4D,UAAU;AACpC;AACA,CAAC;AAACC,IAAA,GALIF,YAAY;AAOlB,MAAMG,eAAe,GAAGtE,MAAM,CAACO,GAAG;AAClC;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACmB,EAAE;AACxC,CAAC;AAACsC,IAAA,GAJID,eAAe;AAMrB,MAAME,YAAY,GAAGxE,MAAM,CAACO,GAAG;AAC/B,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACmC,KAAK;AAC5C,eAAerC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACC,QAAQ,CAACI,EAAE;AAC1D,gBAAgBzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,OAAO,CAACmD,EAAE;AAC/C,CAAC;AAACQ,IAAA,GAJID,YAAY;AAelB,MAAME,uBAA+D,GAAGA,CAAC;EACvEC,SAAS;EACTC,iBAAiB;EACjBC,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;EAClEC,WAAW,GAAG,EAAE;EAAE;EAClBC,QAAQ,GAAG,EAAE;EACbC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMuF,YAAY,GAAGtF,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAMuF,YAAY,GAAIC,IAAU,IAAoB;IAAA,IAAAC,oBAAA;IAClD;IACA,IAAID,IAAI,CAACE,IAAI,GAAGV,WAAW,GAAG,IAAI,GAAG,IAAI,EAAE;MACzC,OAAO,+BAA+BA,WAAW,IAAI;IACvD;;IAEA;IACA,MAAMW,aAAa,GAAG,GAAG,KAAAF,oBAAA,GAAGD,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAL,oBAAA,uBAA1BA,oBAAA,CAA4BM,WAAW,CAAC,CAAC;IACrE,IAAI,CAAChB,aAAa,CAACiB,QAAQ,CAACL,aAAa,CAAC,EAAE;MAC1C,OAAO,4CAA4CZ,aAAa,CAACkB,IAAI,CAAC,IAAI,CAAC,EAAE;IAC/E;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,WAAW,GAAGjG,WAAW,CAAEkG,KAAe,IAAK;IACnD,MAAMC,YAA8B,GAAG,EAAE;IAEzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,IAAIzB,SAAS,CAACyB,MAAM,GAAGF,YAAY,CAACE,MAAM,GAAGrB,QAAQ,EAAEoB,CAAC,EAAE,EAAE;MAC1F,MAAMb,IAAI,GAAGW,KAAK,CAACE,CAAC,CAAC;MACrB,MAAMtD,KAAK,GAAGwC,YAAY,CAACC,IAAI,CAAC;MAEhC,MAAMe,cAA8B,GAAG;QACrCf,IAAI;QACJgB,YAAY,EAAEC,2BAA2B,CAACjB,IAAI,CAACI,IAAI,CAAC;QACpDc,cAAc,EAAE,CAAC;QAAE;QACnBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE1B,QAAQ;QACpBpC,MAAM,EAAEC,KAAK,GAAG,OAAO,GAAG,SAAS;QACnCA,KAAK,EAAEA,KAAK,IAAI8D;MAClB,CAAC;MAEDT,YAAY,CAACU,IAAI,CAACP,cAAc,CAAC;IACnC;IAEAzB,iBAAiB,CAAC,CAAC,GAAGD,SAAS,EAAE,GAAGuB,YAAY,CAAC,CAAC;EACpD,CAAC,EAAE,CAACvB,SAAS,EAAEC,iBAAiB,EAAEG,QAAQ,EAAED,WAAW,EAAED,aAAa,EAAEG,QAAQ,CAAC,CAAC;EAElF,MAAMuB,2BAA2B,GAAIM,QAAgB,IAAa;IAChE,MAAMnB,IAAI,GAAGmB,QAAQ,CAAChB,WAAW,CAAC,CAAC;IACnC,IAAIH,IAAI,CAACI,QAAQ,CAAC,IAAI,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU;IACvE,IAAIJ,IAAI,CAACI,QAAQ,CAAC,SAAS,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,eAAe;IAChF,IAAIJ,IAAI,CAACI,QAAQ,CAAC,QAAQ,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,cAAc;IAC7E,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,gBAAgB;IAChF,OAAO,gBAAgB;EACzB,CAAC;EAED,MAAMgB,cAAc,GAAIC,CAAkB,IAAK;IAC7CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB7B,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAM8B,eAAe,GAAIF,CAAkB,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB7B,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM+B,UAAU,GAAIH,CAAkB,IAAK;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB7B,WAAW,CAAC,KAAK,CAAC;IAElB,MAAMc,KAAK,GAAGc,CAAC,CAACI,YAAY,CAAClB,KAAK;IAClC,IAAIA,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACpBJ,WAAW,CAACC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAIL,CAAsC,IAAK;IACnE,MAAMd,KAAK,GAAGc,CAAC,CAACM,MAAM,CAACpB,KAAK;IAC5B,IAAIA,KAAK,IAAIA,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MAC7BJ,WAAW,CAACC,KAAK,CAAC;IACpB;IACA;IACAc,CAAC,CAACM,MAAM,CAACC,KAAK,GAAG,EAAE;EACrB,CAAC;EAED,MAAMC,cAAc,GAAIC,KAAa,IAAK;IACxC,MAAMC,gBAAgB,GAAG9C,SAAS,CAAC+C,MAAM,CAAC,CAACC,CAAC,EAAExB,CAAC,KAAKA,CAAC,KAAKqB,KAAK,CAAC;IAChE5C,iBAAiB,CAAC6C,gBAAgB,CAAC;EACrC,CAAC;EAED,MAAMG,WAAW,GAAIJ,KAAa,IAAK;IACrC,MAAMC,gBAAgB,GAAG,CAAC,GAAG9C,SAAS,CAAC;IACvC8C,gBAAgB,CAACD,KAAK,CAAC,GAAG;MACxB,GAAGC,gBAAgB,CAACD,KAAK,CAAC;MAC1B5E,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE8D;IACT,CAAC;IACD/B,iBAAiB,CAAC6C,gBAAgB,CAAC;EACrC,CAAC;EAED,MAAMI,cAAc,GAAIC,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAM7B,CAAC,GAAG8B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGG,IAAI,CAACI,GAAG,CAACN,CAAC,EAAE5B,CAAC,CAAC,EAAEmC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAAC7B,CAAC,CAAC;EACzE,CAAC;EAED,MAAMoC,WAAW,GAAI1B,QAAgB,IAAa;IAAA,IAAA2B,mBAAA;IAChD,MAAMC,SAAS,IAAAD,mBAAA,GAAG3B,QAAQ,CAAClB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAA4C,mBAAA,uBAAzBA,mBAAA,CAA2B3C,WAAW,CAAC,CAAC;IAC1D,QAAQ4C,SAAS;MACf,KAAK,KAAK;QAAE,OAAO,IAAI;MACvB,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;QAAE,OAAO,KAAK;MACxB,KAAK,KAAK;MACV,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,oBACEpI,OAAA;IAAAqI,QAAA,gBACErI,OAAA,CAACC,eAAe;MACdqI,SAAS,EAAEzD,QAAQ,GAAG,WAAW,GAAG,EAAG;MACvC0D,UAAU,EAAE9B,cAAe;MAC3B+B,WAAW,EAAE5B,eAAgB;MAC7B6B,MAAM,EAAE5B,UAAW;MACnB6B,OAAO,EAAEA,CAAA;QAAA,IAAAC,qBAAA;QAAA,QAAAA,qBAAA,GAAM5D,YAAY,CAAC6D,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;MAAA,CAAC;MAAAR,QAAA,gBAE7CrI,OAAA,CAACiB,UAAU;QAAAoH,QAAA,EAAC;MAAE;QAAA7B,QAAA,EAAAsC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3BhJ,OAAA,CAACqB,UAAU;QAAAgH,QAAA,EAAC;MAEZ;QAAA7B,QAAA,EAAAsC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhJ,OAAA,CAAC8B,aAAa;QAAAuG,QAAA,GAAC,qBACM,EAAC7D,aAAa,CAACkB,IAAI,CAAC,IAAI,CAAC,EAAC,oBAAa,EAACjB,WAAW,EAAC,SACzE;MAAA;QAAA+B,QAAA,EAAAsC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAChBhJ,OAAA,CAACJ,MAAM;QAACqJ,OAAO,EAAC,SAAS;QAACC,IAAI,EAAC,QAAQ;QAAAb,QAAA,EAAC;MAExC;QAAA7B,QAAA,EAAAsC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEThJ,OAAA,CAACgC,WAAW;QACVmH,GAAG,EAAEpE,YAAa;QAClBmE,IAAI,EAAC,MAAM;QACXE,QAAQ;QACRC,MAAM,EAAE7E,aAAa,CAACkB,IAAI,CAAC,GAAG,CAAE;QAChC4D,QAAQ,EAAEvC;MAAiB;QAAAP,QAAA,EAAAsC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAxC,QAAA,EAAAsC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa,CAAC,EAEjB1E,SAAS,CAACyB,MAAM,GAAG,CAAC,iBACnB/F,OAAA,CAACmC,YAAY;MAAAkG,QAAA,EACV/D,SAAS,CAACiF,GAAG,CAAC,CAACC,GAAG,EAAErC,KAAK,kBACxBnH,OAAA,CAACqC,YAAY;QAAaE,MAAM,EAAEiH,GAAG,CAACjH,MAAO;QAAA8F,QAAA,gBAC3CrI,OAAA,CAAC6C,YAAY;UAAAwF,QAAA,gBACXrI,OAAA,CAAC+C,YAAY;YAAAsF,QAAA,EACVmB,GAAG,CAACjH,MAAM,KAAK,WAAW,gBACzBvC,OAAA,CAACH,cAAc;cAACsF,IAAI,EAAC;YAAI;cAAAqB,QAAA,EAAAsC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAE5Bd,WAAW,CAACsB,GAAG,CAACvE,IAAI,CAACI,IAAI;UAC1B;YAAAmB,QAAA,EAAAsC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,eACfhJ,OAAA,CAACmD,eAAe;YAAAkF,QAAA,gBACdrI,OAAA,CAACqD,YAAY;cAAAgF,QAAA,EAAEmB,GAAG,CAACvE,IAAI,CAACI;YAAI;cAAAmB,QAAA,EAAAsC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAC5ChJ,OAAA,CAACwD,YAAY;cAAA6E,QAAA,GACVb,cAAc,CAACgC,GAAG,CAACvE,IAAI,CAACE,IAAI,CAAC,EAAC,UAAG,EAACqE,GAAG,CAACvD,YAAY;YAAA;cAAAO,QAAA,EAAAsC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,EACdQ,GAAG,CAACjH,MAAM,KAAK,WAAW,IAAIiH,GAAG,CAACC,cAAc,iBAC/CzJ,OAAA,CAAC0D,WAAW;cAAA2E,QAAA,eACVrI,OAAA,CAAC8D,YAAY;gBAACC,UAAU,EAAEyF,GAAG,CAACC,cAAc,CAAC1F;cAAW;gBAAAyC,QAAA,EAAAsC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAxC,QAAA,EAAAsC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACd,EACAQ,GAAG,CAAChH,KAAK,iBAAIxC,OAAA,CAACmE,YAAY;cAAAkE,QAAA,EAAEmB,GAAG,CAAChH;YAAK;cAAAgE,QAAA,EAAAsC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAxC,QAAA,EAAAsC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAxC,QAAA,EAAAsC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEfhJ,OAAA,CAACiE,eAAe;UAAAoE,QAAA,gBACdrI,OAAA,CAACF,KAAK;YACJmJ,OAAO,EACLO,GAAG,CAACjH,MAAM,KAAK,WAAW,GAAG,SAAS,GACtCiH,GAAG,CAACjH,MAAM,KAAK,OAAO,GAAG,OAAO,GAChCiH,GAAG,CAACjH,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,WACvC;YACD4C,IAAI,EAAC,IAAI;YAAAkD,QAAA,EAERmB,GAAG,CAACjH,MAAM,KAAK,WAAW,GAAG,cAAc,GAC3CiH,GAAG,CAACjH,MAAM,KAAK,WAAW,GAAG,UAAU,GACvCiH,GAAG,CAACjH,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG;UAAO;YAAAiE,QAAA,EAAAsC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,EAEPQ,GAAG,CAACjH,MAAM,KAAK,OAAO,iBACrBvC,OAAA,CAACJ,MAAM;YACLuF,IAAI,EAAC,IAAI;YACT8D,OAAO,EAAC,SAAS;YACjBP,OAAO,EAAEA,CAAA,KAAMnB,WAAW,CAACJ,KAAK,CAAE;YAAAkB,QAAA,EACnC;UAED;YAAA7B,QAAA,EAAAsC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAEDhJ,OAAA,CAACJ,MAAM;YACLuF,IAAI,EAAC,IAAI;YACT8D,OAAO,EAAC,QAAQ;YAChBP,OAAO,EAAEA,CAAA,KAAMxB,cAAc,CAACC,KAAK,CAAE;YAAAkB,QAAA,EACtC;UAED;YAAA7B,QAAA,EAAAsC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAxC,QAAA,EAAAsC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA,GAtDD7B,KAAK;QAAAX,QAAA,EAAAsC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuDV,CACf;IAAC;MAAAxC,QAAA,EAAAsC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CACf;EAAA;IAAAxC,QAAA,EAAAsC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpE,EAAA,CAxNIP,uBAA+D;AAAAqF,IAAA,GAA/DrF,uBAA+D;AA0NrE,eAAeA,uBAAuB;AAAC,IAAArD,EAAA,EAAAI,GAAA,EAAAS,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAsF,IAAA;AAAAC,YAAA,CAAA3I,EAAA;AAAA2I,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}