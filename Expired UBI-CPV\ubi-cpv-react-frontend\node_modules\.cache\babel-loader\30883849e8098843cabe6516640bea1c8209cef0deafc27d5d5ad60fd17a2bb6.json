{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{Card,Button,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FilterContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background: white;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const SearchInput=styled.input(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  min-width: 250px;\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const TableContainer=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  overflow-x: auto;\\n\"])));const Table=styled.table(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const StatusBadge=styled.span(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.status){case'assigned':return\"\\n          background-color: #fff3e0;\\n          color: #e65100;\\n        \";case'in-progress':return\"\\n          background-color: #fff8e1;\\n          color: #ff8f00;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const PriorityBadge=styled.span(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.priority){case'high':return\"\\n          background-color: #ffebee;\\n          color: #c62828;\\n        \";case'medium':return\"\\n          background-color: #fff8e1;\\n          color: #ff8f00;\\n        \";case'low':return\"\\n          background-color: #e8f5e9;\\n          color: #2e7d32;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const ActionButtons=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 8px;\\n\"])));const AgentTasks=()=>{const[tasks,setTasks]=useState([]);const[loading,setLoading]=useState(true);const[statusFilter,setStatusFilter]=useState('all');const[searchTerm,setSearchTerm]=useState('');const navigate=useNavigate();useEffect(()=>{loadTasks();},[statusFilter]);const loadTasks=async()=>{try{setLoading(true);const status=statusFilter==='all'?undefined:statusFilter;const response=await apiService.getLeads(1,100,status);setTasks(response.data||[]);}catch(error){console.error('Error loading tasks:',error);// Mock data for demo\nsetTasks([{leadId:1,customerName:'John Doe',mobileNumber:'9876543210',loanType:'Personal Loan',status:'assigned',createdDate:'2024-01-15T10:30:00Z',assignedDate:'2024-01-15T11:00:00Z',createdByName:'Admin User',assignedToName:'Current Agent',documentCount:0,croppedImageCount:0},{leadId:2,customerName:'Jane Smith',mobileNumber:'9876543211',loanType:'Home Loan',status:'in-progress',createdDate:'2024-01-14T09:15:00Z',assignedDate:'2024-01-14T10:00:00Z',createdByName:'Admin User',assignedToName:'Current Agent',documentCount:2,croppedImageCount:1}]);}finally{setLoading(false);}};const filteredTasks=tasks.filter(task=>task.customerName.toLowerCase().includes(searchTerm.toLowerCase())||task.mobileNumber.includes(searchTerm)||task.loanType.toLowerCase().includes(searchTerm.toLowerCase()));const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>navigate('/agent/dashboard')},{icon:'📋',label:'My Tasks',active:true},{icon:'✅',label:'Completed',onClick:()=>navigate('/agent/completed')},{icon:'📊',label:'Reports',onClick:()=>navigate('/agent/reports')}];const handleTaskAction=async(leadId,action)=>{try{if(action==='start'){await apiService.updateLeadStatus(leadId,'in-progress','Started verification process');}else if(action==='submit'){await apiService.updateLeadStatus(leadId,'pending-review','Submitted for review');}loadTasks();// Reload tasks after action\n}catch(error){console.error('Error updating task:',error);alert('Failed to update task');}};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};const getPriority=createdDate=>{const daysDiff=Math.floor((Date.now()-new Date(createdDate).getTime())/(1000*60*60*24));if(daysDiff>3)return'high';if(daysDiff>1)return'medium';return'low';};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"My Tasks\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsx(DashboardLayout,{title:\"My Tasks\",navigationItems:navigationItems,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Assigned Tasks\"}),/*#__PURE__*/_jsxs(FilterContainer,{children:[/*#__PURE__*/_jsxs(FilterSelect,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Tasks\"}),/*#__PURE__*/_jsx(\"option\",{value:\"assigned\",children:\"Assigned\"}),/*#__PURE__*/_jsx(\"option\",{value:\"in-progress\",children:\"In Progress\"})]}),/*#__PURE__*/_jsx(SearchInput,{type:\"text\",placeholder:\"Search by customer name, mobile, or loan type...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Customer\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Mobile\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Loan Type\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Status\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Priority\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Assigned Date\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredTasks.map(task=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:task.customerName}),/*#__PURE__*/_jsx(TableCell,{children:task.mobileNumber}),/*#__PURE__*/_jsx(TableCell,{children:task.loanType}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(StatusBadge,{status:task.status,children:task.status.replace('-',' ').toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(PriorityBadge,{priority:getPriority(task.createdDate),children:getPriority(task.createdDate).toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:task.assignedDate?formatDate(task.assignedDate):'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(ActionButtons,{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>navigate(\"/lead/\".concat(task.leadId)),children:\"View\"}),task.status==='assigned'&&/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleTaskAction(task.leadId,'start'),children:\"Start\"}),task.status==='in-progress'&&/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline\",onClick:()=>handleTaskAction(task.leadId,'submit'),children:\"Submit\"})]})})]},task.leadId))})]})}),filteredTasks.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px',color:'#777'},children:searchTerm?'No tasks found matching your search.':'No tasks assigned yet.'})]})});};export default AgentTasks;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_templateObject", "_taggedTemplateLiteral", "FilterSelect", "select", "_templateObject2", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "SearchInput", "input", "_templateObject3", "TableContainer", "_templateObject4", "Table", "table", "_templateObject5", "TableHeader", "th", "_templateObject6", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject7", "TableRow", "tr", "_templateObject8", "StatusBadge", "span", "_templateObject9", "status", "PriorityBadge", "_templateObject0", "priority", "ActionButtons", "_templateObject1", "AgentTasks", "tasks", "setTasks", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "navigate", "loadTasks", "undefined", "response", "getLeads", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedDate", "createdByName", "assignedToName", "documentCount", "croppedImageCount", "filteredTasks", "filter", "task", "toLowerCase", "includes", "navigationItems", "icon", "label", "onClick", "active", "handleTaskAction", "action", "updateLeadStatus", "alert", "formatDate", "dateString", "Date", "toLocaleDateString", "getPriority", "daysDiff", "Math", "floor", "now", "getTime", "title", "children", "style", "marginBottom", "color", "value", "onChange", "e", "target", "type", "placeholder", "map", "replace", "toUpperCase", "size", "concat", "variant", "length", "textAlign", "padding"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Agent/AgentTasks.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem } from '../../services/apiService';\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'assigned':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'in-progress':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst PriorityBadge = styled.span<{ priority: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.priority) {\n      case 'high':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      case 'medium':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      case 'low':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n\nconst AgentTasks: React.FC = () => {\n  const [tasks, setTasks] = useState<LeadListItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadTasks();\n  }, [statusFilter]);\n\n  const loadTasks = async () => {\n    try {\n      setLoading(true);\n      const status = statusFilter === 'all' ? undefined : statusFilter;\n      const response = await apiService.getLeads(1, 100, status);\n      setTasks(response.data || []);\n    } catch (error) {\n      console.error('Error loading tasks:', error);\n      // Mock data for demo\n      setTasks([\n        {\n          leadId: 1,\n          customerName: 'John Doe',\n          mobileNumber: '9876543210',\n          loanType: 'Personal Loan',\n          status: 'assigned',\n          createdDate: '2024-01-15T10:30:00Z',\n          assignedDate: '2024-01-15T11:00:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 0,\n          croppedImageCount: 0,\n        },\n        {\n          leadId: 2,\n          customerName: 'Jane Smith',\n          mobileNumber: '9876543211',\n          loanType: 'Home Loan',\n          status: 'in-progress',\n          createdDate: '2024-01-14T09:15:00Z',\n          assignedDate: '2024-01-14T10:00:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 2,\n          croppedImageCount: 1,\n        },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredTasks = tasks.filter(task =>\n    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    task.mobileNumber.includes(searchTerm) ||\n    task.loanType.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },\n    { icon: '📋', label: 'My Tasks', active: true },\n    { icon: '✅', label: 'Completed', onClick: () => navigate('/agent/completed') },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/agent/reports') },\n  ];\n\n  const handleTaskAction = async (leadId: number, action: string) => {\n    try {\n      if (action === 'start') {\n        await apiService.updateLeadStatus(leadId, 'in-progress', 'Started verification process');\n      } else if (action === 'submit') {\n        await apiService.updateLeadStatus(leadId, 'pending-review', 'Submitted for review');\n      }\n      loadTasks(); // Reload tasks after action\n    } catch (error) {\n      console.error('Error updating task:', error);\n      alert('Failed to update task');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getPriority = (createdDate: string) => {\n    const daysDiff = Math.floor((Date.now() - new Date(createdDate).getTime()) / (1000 * 60 * 60 * 24));\n    if (daysDiff > 3) return 'high';\n    if (daysDiff > 1) return 'medium';\n    return 'low';\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"My Tasks\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"My Tasks\" navigationItems={navigationItems}>\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Assigned Tasks</h2>\n\n        <FilterContainer>\n          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>\n            <option value=\"all\">All Tasks</option>\n            <option value=\"assigned\">Assigned</option>\n            <option value=\"in-progress\">In Progress</option>\n          </FilterSelect>\n          \n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search by customer name, mobile, or loan type...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Priority</TableHeader>\n                <TableHeader>Assigned Date</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredTasks.map((task) => (\n                <TableRow key={task.leadId}>\n                  <TableCell>{task.customerName}</TableCell>\n                  <TableCell>{task.mobileNumber}</TableCell>\n                  <TableCell>{task.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={task.status}>\n                      {task.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    <PriorityBadge priority={getPriority(task.createdDate)}>\n                      {getPriority(task.createdDate).toUpperCase()}\n                    </PriorityBadge>\n                  </TableCell>\n                  <TableCell>\n                    {task.assignedDate ? formatDate(task.assignedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    <ActionButtons>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => navigate(`/lead/${task.leadId}`)}\n                      >\n                        View\n                      </Button>\n                      {task.status === 'assigned' && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"secondary\"\n                          onClick={() => handleTaskAction(task.leadId, 'start')}\n                        >\n                          Start\n                        </Button>\n                      )}\n                      {task.status === 'in-progress' && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleTaskAction(task.leadId, 'submit')}\n                        >\n                          Submit\n                        </Button>\n                      )}\n                    </ActionButtons>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {filteredTasks.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            {searchTerm ? 'No tasks found matching your search.' : 'No tasks assigned yet.'}\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default AgentTasks;\n"], "mappings": "8WAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAAsB,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErE,KAAM,CAAAC,eAAe,CAAGV,MAAM,CAACW,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,sFAKjC,CAED,KAAM,CAAAC,YAAY,CAAGd,MAAM,CAACe,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAH,sBAAA,6LAEZI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAKnCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAC,WAAW,CAAGxB,MAAM,CAACyB,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,wMAIVI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAInCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAI,cAAc,CAAG3B,MAAM,CAACW,GAAG,CAAAiB,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,+BAEhC,CAED,KAAM,CAAAgB,KAAK,CAAG7B,MAAM,CAAC8B,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,wDAGzB,CAED,KAAM,CAAAmB,WAAW,CAAGhC,MAAM,CAACiC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,qJAGAI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAC5ClB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,QAAQ,CAE/CnB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACkB,UAAU,CAChD,CAED,KAAM,CAAAC,SAAS,CAAGtC,MAAM,CAACuC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA3B,sBAAA,uFAGEI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAGzC,MAAM,CAAC0C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA9B,sBAAA,wDAEFI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAE5D,CAED,KAAM,CAAAS,WAAW,CAAG5C,MAAM,CAAC6C,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjC,sBAAA,mIAO3BI,KAAK,EAAI,CACT,OAAQA,KAAK,CAAC8B,MAAM,EAClB,IAAK,UAAU,CACb,oFAIF,IAAK,aAAa,CAChB,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAGhD,MAAM,CAAC6C,IAAI,CAAAI,gBAAA,GAAAA,gBAAA,CAAApC,sBAAA,mIAO7BI,KAAK,EAAI,CACT,OAAQA,KAAK,CAACiC,QAAQ,EACpB,IAAK,MAAM,CACT,oFAIF,IAAK,QAAQ,CACX,oFAIF,IAAK,KAAK,CACR,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAGnD,MAAM,CAACW,GAAG,CAAAyC,gBAAA,GAAAA,gBAAA,CAAAvC,sBAAA,yCAG/B,CAED,KAAM,CAAAwC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAG1D,QAAQ,CAAiB,EAAE,CAAC,CACtD,KAAM,CAAC2D,OAAO,CAAEC,UAAU,CAAC,CAAG5D,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6D,YAAY,CAAEC,eAAe,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC+D,UAAU,CAAEC,aAAa,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAAiE,QAAQ,CAAG/D,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACdiE,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACL,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAK,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFN,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAV,MAAM,CAAGW,YAAY,GAAK,KAAK,CAAGM,SAAS,CAAGN,YAAY,CAChE,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAA5D,UAAU,CAAC6D,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAEnB,MAAM,CAAC,CAC1DQ,QAAQ,CAACU,QAAQ,CAACE,IAAI,EAAI,EAAE,CAAC,CAC/B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C;AACAb,QAAQ,CAAC,CACP,CACEe,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,UAAU,CACxBC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,eAAe,CACzB1B,MAAM,CAAE,UAAU,CAClB2B,WAAW,CAAE,sBAAsB,CACnCC,YAAY,CAAE,sBAAsB,CACpCC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,eAAe,CAC/BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACD,CACET,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,WAAW,CACrB1B,MAAM,CAAE,aAAa,CACrB2B,WAAW,CAAE,sBAAsB,CACnCC,YAAY,CAAE,sBAAsB,CACpCC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,eAAe,CAC/BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACF,CAAC,CACJ,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAuB,aAAa,CAAG1B,KAAK,CAAC2B,MAAM,CAACC,IAAI,EACrCA,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,EAClED,IAAI,CAACV,YAAY,CAACY,QAAQ,CAACxB,UAAU,CAAC,EACtCsB,IAAI,CAACT,QAAQ,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAC/D,CAAC,CAED,KAAM,CAAAE,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC/E,CAAEwB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAU,CAAEE,MAAM,CAAE,IAAK,CAAC,CAC/C,CAAEH,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC9E,CAAEwB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,gBAAgB,CAAE,CAAC,CAC5E,CAED,KAAM,CAAA4B,gBAAgB,CAAG,KAAAA,CAAOpB,MAAc,CAAEqB,MAAc,GAAK,CACjE,GAAI,CACF,GAAIA,MAAM,GAAK,OAAO,CAAE,CACtB,KAAM,CAAAtF,UAAU,CAACuF,gBAAgB,CAACtB,MAAM,CAAE,aAAa,CAAE,8BAA8B,CAAC,CAC1F,CAAC,IAAM,IAAIqB,MAAM,GAAK,QAAQ,CAAE,CAC9B,KAAM,CAAAtF,UAAU,CAACuF,gBAAgB,CAACtB,MAAM,CAAE,gBAAgB,CAAE,sBAAsB,CAAC,CACrF,CACAP,SAAS,CAAC,CAAC,CAAE;AACf,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CyB,KAAK,CAAC,uBAAuB,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIxB,WAAmB,EAAK,CAC3C,KAAM,CAAAyB,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,CAAG,GAAI,CAAAN,IAAI,CAACtB,WAAW,CAAC,CAAC6B,OAAO,CAAC,CAAC,GAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CACnG,GAAIJ,QAAQ,CAAG,CAAC,CAAE,MAAO,MAAM,CAC/B,GAAIA,QAAQ,CAAG,CAAC,CAAE,MAAO,QAAQ,CACjC,MAAO,KAAK,CACd,CAAC,CAED,GAAI3C,OAAO,CAAE,CACX,mBACEjD,IAAA,CAACN,eAAe,EAACuG,KAAK,CAAC,UAAU,CAACnB,eAAe,CAAEA,eAAgB,CAAAoB,QAAA,cACjElG,IAAA,CAACH,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,mBACEG,IAAA,CAACN,eAAe,EAACuG,KAAK,CAAC,UAAU,CAACnB,eAAe,CAAEA,eAAgB,CAAAoB,QAAA,cACjEhG,KAAA,CAACP,IAAI,EAAAuG,QAAA,eACHlG,IAAA,OAAImG,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,CAAC,gBAAc,CAAI,CAAC,cAE1EhG,KAAA,CAACC,eAAe,EAAA+F,QAAA,eACdhG,KAAA,CAACK,YAAY,EAAC+F,KAAK,CAAEnD,YAAa,CAACoD,QAAQ,CAAGC,CAAC,EAAKpD,eAAe,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAJ,QAAA,eAClFlG,IAAA,WAAQsG,KAAK,CAAC,KAAK,CAAAJ,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtClG,IAAA,WAAQsG,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1ClG,IAAA,WAAQsG,KAAK,CAAC,aAAa,CAAAJ,QAAA,CAAC,aAAW,CAAQ,CAAC,EACpC,CAAC,cAEflG,IAAA,CAACiB,WAAW,EACVyF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kDAAkD,CAC9DL,KAAK,CAAEjD,UAAW,CAClBkD,QAAQ,CAAGC,CAAC,EAAKlD,aAAa,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,EACa,CAAC,cAElBtG,IAAA,CAACoB,cAAc,EAAA8E,QAAA,cACbhG,KAAA,CAACoB,KAAK,EAAA4E,QAAA,eACJlG,IAAA,UAAAkG,QAAA,cACEhG,KAAA,OAAAgG,QAAA,eACElG,IAAA,CAACyB,WAAW,EAAAyE,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnClG,IAAA,CAACyB,WAAW,EAAAyE,QAAA,CAAC,QAAM,CAAa,CAAC,cACjClG,IAAA,CAACyB,WAAW,EAAAyE,QAAA,CAAC,WAAS,CAAa,CAAC,cACpClG,IAAA,CAACyB,WAAW,EAAAyE,QAAA,CAAC,QAAM,CAAa,CAAC,cACjClG,IAAA,CAACyB,WAAW,EAAAyE,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnClG,IAAA,CAACyB,WAAW,EAAAyE,QAAA,CAAC,eAAa,CAAa,CAAC,cACxClG,IAAA,CAACyB,WAAW,EAAAyE,QAAA,CAAC,SAAO,CAAa,CAAC,EAChC,CAAC,CACA,CAAC,cACRlG,IAAA,UAAAkG,QAAA,CACGzB,aAAa,CAACmC,GAAG,CAAEjC,IAAI,eACtBzE,KAAA,CAACgC,QAAQ,EAAAgE,QAAA,eACPlG,IAAA,CAAC+B,SAAS,EAAAmE,QAAA,CAAEvB,IAAI,CAACX,YAAY,CAAY,CAAC,cAC1ChE,IAAA,CAAC+B,SAAS,EAAAmE,QAAA,CAAEvB,IAAI,CAACV,YAAY,CAAY,CAAC,cAC1CjE,IAAA,CAAC+B,SAAS,EAAAmE,QAAA,CAAEvB,IAAI,CAACT,QAAQ,CAAY,CAAC,cACtClE,IAAA,CAAC+B,SAAS,EAAAmE,QAAA,cACRlG,IAAA,CAACqC,WAAW,EAACG,MAAM,CAAEmC,IAAI,CAACnC,MAAO,CAAA0D,QAAA,CAC9BvB,IAAI,CAACnC,MAAM,CAACqE,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CACjC,CAAC,CACL,CAAC,cACZ9G,IAAA,CAAC+B,SAAS,EAAAmE,QAAA,cACRlG,IAAA,CAACyC,aAAa,EAACE,QAAQ,CAAEgD,WAAW,CAAChB,IAAI,CAACR,WAAW,CAAE,CAAA+B,QAAA,CACpDP,WAAW,CAAChB,IAAI,CAACR,WAAW,CAAC,CAAC2C,WAAW,CAAC,CAAC,CAC/B,CAAC,CACP,CAAC,cACZ9G,IAAA,CAAC+B,SAAS,EAAAmE,QAAA,CACPvB,IAAI,CAACP,YAAY,CAAGmB,UAAU,CAACZ,IAAI,CAACP,YAAY,CAAC,CAAG,GAAG,CAC/C,CAAC,cACZpE,IAAA,CAAC+B,SAAS,EAAAmE,QAAA,cACRhG,KAAA,CAAC0C,aAAa,EAAAsD,QAAA,eACZlG,IAAA,CAACJ,MAAM,EACLmH,IAAI,CAAC,IAAI,CACT9B,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,UAAAyD,MAAA,CAAUrC,IAAI,CAACZ,MAAM,CAAE,CAAE,CAAAmC,QAAA,CACjD,MAED,CAAQ,CAAC,CACRvB,IAAI,CAACnC,MAAM,GAAK,UAAU,eACzBxC,IAAA,CAACJ,MAAM,EACLmH,IAAI,CAAC,IAAI,CACTE,OAAO,CAAC,WAAW,CACnBhC,OAAO,CAAEA,CAAA,GAAME,gBAAgB,CAACR,IAAI,CAACZ,MAAM,CAAE,OAAO,CAAE,CAAAmC,QAAA,CACvD,OAED,CAAQ,CACT,CACAvB,IAAI,CAACnC,MAAM,GAAK,aAAa,eAC5BxC,IAAA,CAACJ,MAAM,EACLmH,IAAI,CAAC,IAAI,CACTE,OAAO,CAAC,SAAS,CACjBhC,OAAO,CAAEA,CAAA,GAAME,gBAAgB,CAACR,IAAI,CAACZ,MAAM,CAAE,QAAQ,CAAE,CAAAmC,QAAA,CACxD,QAED,CAAQ,CACT,EACY,CAAC,CACP,CAAC,GA5CCvB,IAAI,CAACZ,MA6CV,CACX,CAAC,CACG,CAAC,EACH,CAAC,CACM,CAAC,CAEhBU,aAAa,CAACyC,MAAM,GAAK,CAAC,eACzBlH,IAAA,QAAKmG,KAAK,CAAE,CAAEgB,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEf,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CACjE7C,UAAU,CAAG,sCAAsC,CAAG,wBAAwB,CAC5E,CACN,EACG,CAAC,CACQ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}