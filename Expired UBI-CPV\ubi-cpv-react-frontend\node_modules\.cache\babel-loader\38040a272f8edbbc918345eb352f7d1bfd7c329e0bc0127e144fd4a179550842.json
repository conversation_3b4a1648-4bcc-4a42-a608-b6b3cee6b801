{"ast": null, "code": "import _objectSpread from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{<PERSON>,But<PERSON>,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProfileContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 1fr 2fr;\\n  gap: 20px;\\n  \\n  @media (max-width: 768px) {\\n    grid-template-columns: 1fr;\\n  }\\n\"])));const ProfileCard=styled(Card)(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  padding: 30px;\\n\"])));const Avatar=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #007E3A, #005a2a);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 36px;\\n  color: white;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n\"])));const UserName=styled.h2(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  margin-bottom: 8px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.textDark);const UserRole=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  font-size: 16px;\\n  margin-bottom: 20px;\\n\"])),props=>props.theme.colors.textMedium);const StatusBadge=styled.span(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 6px 12px;\\n  border-radius: 16px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  background-color: \",\";\\n  color: \",\";\\n\"])),props=>props.isActive?'#e8f5e9':'#ffebee',props=>props.isActive?'#2e7d32':'#c62828');const FormContainer=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  display: grid;\\n  gap: 20px;\\n\"])));const FormSection=styled(Card)(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  padding: 20px;\\n\"])));const SectionTitle=styled.h3(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n  color: #007E3A;\\n  border-bottom: 1px solid \",\";\\n  padding-bottom: 10px;\\n\"])),props=>props.theme.colors.lightGray);const FormGroup=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n\"])));const Label=styled.label(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n  color: \",\";\\n\"])),props=>props.theme.colors.textMedium);const Input=styled.input(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\\n  }\\n  \\n  &:disabled {\\n    background-color: \",\";\\n    cursor: not-allowed;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary,props=>props.theme.colors.lightGray);const TextArea=styled.textarea(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  min-height: 80px;\\n  padding: 10px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  resize: vertical;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const ButtonGroup=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 10px;\\n  justify-content: flex-end;\\n  margin-top: 20px;\\n\"])));const StatsGrid=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 15px;\\n  margin-top: 20px;\\n\"])));const StatItem=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  padding: 15px;\\n  background: \",\";\\n  border-radius: \",\";\\n\"])),props=>props.theme.colors.offWhite,props=>props.theme.borderRadius.sm);const StatValue=styled.div(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  font-size: 20px;\\n  font-weight: bold;\\n  color: #007E3A;\\n  margin-bottom: 5px;\\n\"])));const StatLabel=styled.div(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  font-size: 12px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.textLight);const UserProfile=()=>{const{user:currentUser}=useAuth();const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[editing,setEditing]=useState(false);const[formData,setFormData]=useState({firstName:'',lastName:'',email:'',phoneNumber:'',bio:''});const[passwordData,setPasswordData]=useState({currentPassword:'',newPassword:'',confirmPassword:''});const[userStats,setUserStats]=useState({totalTasks:0,completedTasks:0,approvalRate:0,avgTime:0});const navigate=useNavigate();useEffect(()=>{loadUserProfile();loadUserStats();},[]);const loadUserProfile=async()=>{try{setLoading(true);const userData=await apiService.getCurrentUser();setUser(userData);setFormData({firstName:userData.firstName||'',lastName:userData.lastName||'',email:userData.email||'',phoneNumber:userData.phoneNumber||'',bio:''// This would come from user profile if available\n});}catch(error){console.error('Error loading user profile:',error);// Use current user from context as fallback\nif(currentUser){setUser(currentUser);setFormData({firstName:currentUser.firstName||'',lastName:currentUser.lastName||'',email:currentUser.email||'',phoneNumber:currentUser.phoneNumber||'',bio:''});}}finally{setLoading(false);}};const loadUserStats=async()=>{try{if((currentUser===null||currentUser===void 0?void 0:currentUser.role)==='Agent'){const stats=await apiService.getAgentDashboardStats();setUserStats({totalTasks:stats.totalAssigned||0,completedTasks:stats.completedLeads||0,approvalRate:85,// Mock data\navgTime:2.5// Mock data\n});}}catch(error){console.error('Error loading user stats:',error);}};const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>{switch(currentUser===null||currentUser===void 0?void 0:currentUser.role){case'Agent':navigate('/agent/dashboard');break;case'Supervisor':navigate('/supervisor/dashboard');break;case'Admin':navigate('/admin/dashboard');break;default:navigate('/');}}},{icon:'👤',label:'Profile',active:true},{icon:'⚙️',label:'Settings',onClick:()=>navigate('/settings')}];const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));};const handlePasswordChange=e=>{const{name,value}=e.target;setPasswordData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));};const handleSaveProfile=async()=>{try{if(!user)return;await apiService.updateUser(user.userId,formData);// Update local user state\nsetUser(prev=>prev?_objectSpread(_objectSpread({},prev),formData):null);setEditing(false);alert('Profile updated successfully!');}catch(error){console.error('Error updating profile:',error);alert('Failed to update profile');}};const handleChangePassword=async()=>{if(passwordData.newPassword!==passwordData.confirmPassword){alert('New passwords do not match');return;}if(passwordData.newPassword.length<6){alert('Password must be at least 6 characters long');return;}try{// This would be a dedicated change password endpoint\nalert('Password change functionality would be implemented here');setPasswordData({currentPassword:'',newPassword:'',confirmPassword:''});}catch(error){console.error('Error changing password:',error);alert('Failed to change password');}};const getInitials=(firstName,lastName)=>{return\"\".concat(firstName.charAt(0)).concat(lastName.charAt(0)).toUpperCase();};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"User Profile\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}if(!user){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"User Profile\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px'},children:\"User profile not found\"})})});}return/*#__PURE__*/_jsx(DashboardLayout,{title:\"User Profile\",navigationItems:navigationItems,children:/*#__PURE__*/_jsxs(ProfileContainer,{children:[/*#__PURE__*/_jsxs(ProfileCard,{children:[/*#__PURE__*/_jsx(Avatar,{children:getInitials(user.firstName,user.lastName)}),/*#__PURE__*/_jsxs(UserName,{children:[user.firstName,\" \",user.lastName]}),/*#__PURE__*/_jsx(UserRole,{children:user.role}),/*#__PURE__*/_jsx(StatusBadge,{isActive:user.isActive,children:user.isActive?'Active':'Inactive'}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'20px',width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'10px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Username:\"}),\" \",user.username]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'10px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Email:\"}),\" \",user.email]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'10px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Joined:\"}),\" \",formatDate(user.createdDate)]}),user.lastLoginDate&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Last Login:\"}),\" \",formatDate(user.lastLoginDate)]})]}),user.role==='Agent'&&/*#__PURE__*/_jsxs(StatsGrid,{children:[/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatValue,{children:userStats.totalTasks}),/*#__PURE__*/_jsx(StatLabel,{children:\"Total Tasks\"})]}),/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatValue,{children:userStats.completedTasks}),/*#__PURE__*/_jsx(StatLabel,{children:\"Completed\"})]}),/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsxs(StatValue,{children:[userStats.approvalRate,\"%\"]}),/*#__PURE__*/_jsx(StatLabel,{children:\"Approval Rate\"})]}),/*#__PURE__*/_jsxs(StatItem,{children:[/*#__PURE__*/_jsx(StatValue,{children:userStats.avgTime}),/*#__PURE__*/_jsx(StatLabel,{children:\"Avg. Days\"})]})]})]}),/*#__PURE__*/_jsxs(FormContainer,{children:[/*#__PURE__*/_jsxs(FormSection,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'20px'},children:[/*#__PURE__*/_jsx(SectionTitle,{style:{marginBottom:0},children:\"Personal Information\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",size:\"sm\",onClick:()=>setEditing(!editing),children:editing?'Cancel':'Edit'})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'15px'},children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"First Name\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",name:\"firstName\",value:formData.firstName,onChange:handleInputChange,disabled:!editing})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"Last Name\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",name:\"lastName\",value:formData.lastName,onChange:handleInputChange,disabled:!editing})]})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"Email\"}),/*#__PURE__*/_jsx(Input,{type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,disabled:!editing})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"Phone Number\"}),/*#__PURE__*/_jsx(Input,{type:\"tel\",name:\"phoneNumber\",value:formData.phoneNumber,onChange:handleInputChange,disabled:!editing})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"Bio\"}),/*#__PURE__*/_jsx(TextArea,{name:\"bio\",value:formData.bio,onChange:handleInputChange,disabled:!editing,placeholder:\"Tell us about yourself...\"})]}),editing&&/*#__PURE__*/_jsxs(ButtonGroup,{children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>setEditing(false),children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleSaveProfile,children:\"Save Changes\"})]})]}),/*#__PURE__*/_jsxs(FormSection,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"Change Password\"}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"Current Password\"}),/*#__PURE__*/_jsx(Input,{type:\"password\",name:\"currentPassword\",value:passwordData.currentPassword,onChange:handlePasswordChange})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"New Password\"}),/*#__PURE__*/_jsx(Input,{type:\"password\",name:\"newPassword\",value:passwordData.newPassword,onChange:handlePasswordChange})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{children:\"Confirm New Password\"}),/*#__PURE__*/_jsx(Input,{type:\"password\",name:\"confirmPassword\",value:passwordData.confirmPassword,onChange:handlePasswordChange})]}),/*#__PURE__*/_jsx(ButtonGroup,{children:/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:handleChangePassword,disabled:!passwordData.currentPassword||!passwordData.newPassword||!passwordData.confirmPassword,children:\"Change Password\"})})]})]})]})});};export default UserProfile;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "ProfileContainer", "div", "_templateObject", "_taggedTemplateLiteral", "ProfileCard", "_templateObject2", "Avatar", "_templateObject3", "UserName", "h2", "_templateObject4", "props", "theme", "colors", "textDark", "UserRole", "_templateObject5", "textMedium", "StatusBadge", "span", "_templateObject6", "isActive", "FormContainer", "_templateObject7", "FormSection", "_templateObject8", "SectionTitle", "h3", "_templateObject9", "lightGray", "FormGroup", "_templateObject0", "Label", "label", "_templateObject1", "Input", "input", "_templateObject10", "mediumGray", "borderRadius", "sm", "primary", "TextArea", "textarea", "_templateObject11", "ButtonGroup", "_templateObject12", "StatsGrid", "_templateObject13", "StatItem", "_templateObject14", "offWhite", "StatValue", "_templateObject15", "StatLabel", "_templateObject16", "textLight", "UserProfile", "user", "currentUser", "setUser", "loading", "setLoading", "editing", "setEditing", "formData", "setFormData", "firstName", "lastName", "email", "phoneNumber", "bio", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "userStats", "setUserStats", "totalTasks", "completedTasks", "approvalRate", "avgTime", "navigate", "loadUserProfile", "loadUserStats", "userData", "getCurrentUser", "error", "console", "role", "stats", "getAgentDashboardStats", "totalAssigned", "completedLeads", "navigationItems", "icon", "onClick", "active", "handleInputChange", "e", "name", "value", "target", "prev", "_objectSpread", "handlePasswordChange", "handleSaveProfile", "updateUser", "userId", "alert", "handleChangePassword", "length", "getInitials", "concat", "char<PERSON>t", "toUpperCase", "formatDate", "dateString", "Date", "toLocaleDateString", "title", "children", "style", "textAlign", "padding", "marginTop", "width", "marginBottom", "username", "createdDate", "lastLoginDate", "display", "justifyContent", "alignItems", "variant", "size", "gridTemplateColumns", "gap", "type", "onChange", "disabled", "placeholder"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Profile/UserProfile.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, User } from '../../services/apiService';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst ProfileContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 20px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ProfileCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 30px;\n`;\n\nconst Avatar = styled.div`\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 36px;\n  color: white;\n  font-weight: bold;\n  margin-bottom: 20px;\n`;\n\nconst UserName = styled.h2`\n  margin-bottom: 8px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst UserRole = styled.div`\n  color: ${props => props.theme.colors.textMedium};\n  font-size: 16px;\n  margin-bottom: 20px;\n`;\n\nconst StatusBadge = styled.span<{ isActive: boolean }>`\n  display: inline-block;\n  padding: 6px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n  background-color: ${props => props.isActive ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.isActive ? '#2e7d32' : '#c62828'};\n`;\n\nconst FormContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst FormSection = styled(Card)`\n  padding: 20px;\n`;\n\nconst SectionTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  padding-bottom: 10px;\n`;\n\nconst FormGroup = styled.div`\n  margin-bottom: 20px;\n`;\n\nconst Label = styled.label`\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 500;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 10px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n  \n  &:disabled {\n    background-color: ${props => props.theme.colors.lightGray};\n    cursor: not-allowed;\n  }\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 80px;\n  padding: 10px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 15px;\n  margin-top: 20px;\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n  padding: 15px;\n  background: ${props => props.theme.colors.offWhite};\n  border-radius: ${props => props.theme.borderRadius.sm};\n`;\n\nconst StatValue = styled.div`\n  font-size: 20px;\n  font-weight: bold;\n  color: #007E3A;\n  margin-bottom: 5px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 12px;\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst UserProfile: React.FC = () => {\n  const { user: currentUser } = useAuth();\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [editing, setEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phoneNumber: '',\n    bio: '',\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n  const [userStats, setUserStats] = useState({\n    totalTasks: 0,\n    completedTasks: 0,\n    approvalRate: 0,\n    avgTime: 0,\n  });\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadUserProfile();\n    loadUserStats();\n  }, []);\n\n  const loadUserProfile = async () => {\n    try {\n      setLoading(true);\n      const userData = await apiService.getCurrentUser();\n      setUser(userData);\n      setFormData({\n        firstName: userData.firstName || '',\n        lastName: userData.lastName || '',\n        email: userData.email || '',\n        phoneNumber: userData.phoneNumber || '',\n        bio: '', // This would come from user profile if available\n      });\n    } catch (error) {\n      console.error('Error loading user profile:', error);\n      // Use current user from context as fallback\n      if (currentUser) {\n        setUser(currentUser);\n        setFormData({\n          firstName: currentUser.firstName || '',\n          lastName: currentUser.lastName || '',\n          email: currentUser.email || '',\n          phoneNumber: currentUser.phoneNumber || '',\n          bio: '',\n        });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadUserStats = async () => {\n    try {\n      if (currentUser?.role === 'Agent') {\n        const stats = await apiService.getAgentDashboardStats();\n        setUserStats({\n          totalTasks: stats.totalAssigned || 0,\n          completedTasks: stats.completedLeads || 0,\n          approvalRate: 85, // Mock data\n          avgTime: 2.5, // Mock data\n        });\n      }\n    } catch (error) {\n      console.error('Error loading user stats:', error);\n    }\n  };\n\n  const navigationItems = [\n    { \n      icon: '🏠', \n      label: 'Dashboard', \n      onClick: () => {\n        switch (currentUser?.role) {\n          case 'Agent':\n            navigate('/agent/dashboard');\n            break;\n          case 'Supervisor':\n            navigate('/supervisor/dashboard');\n            break;\n          case 'Admin':\n            navigate('/admin/dashboard');\n            break;\n          default:\n            navigate('/');\n        }\n      }\n    },\n    { icon: '👤', label: 'Profile', active: true },\n    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/settings') },\n  ];\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setPasswordData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSaveProfile = async () => {\n    try {\n      if (!user) return;\n      \n      await apiService.updateUser(user.userId, formData);\n      \n      // Update local user state\n      setUser(prev => prev ? { ...prev, ...formData } : null);\n      setEditing(false);\n      \n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile');\n    }\n  };\n\n  const handleChangePassword = async () => {\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      alert('New passwords do not match');\n      return;\n    }\n    \n    if (passwordData.newPassword.length < 6) {\n      alert('Password must be at least 6 characters long');\n      return;\n    }\n\n    try {\n      // This would be a dedicated change password endpoint\n      alert('Password change functionality would be implemented here');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n      });\n    } catch (error) {\n      console.error('Error changing password:', error);\n      alert('Failed to change password');\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"User Profile\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  if (!user) {\n    return (\n      <DashboardLayout title=\"User Profile\" navigationItems={navigationItems}>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            User profile not found\n          </div>\n        </Card>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"User Profile\" navigationItems={navigationItems}>\n      <ProfileContainer>\n        {/* Profile Summary */}\n        <ProfileCard>\n          <Avatar>\n            {getInitials(user.firstName, user.lastName)}\n          </Avatar>\n          <UserName>{user.firstName} {user.lastName}</UserName>\n          <UserRole>{user.role}</UserRole>\n          <StatusBadge isActive={user.isActive}>\n            {user.isActive ? 'Active' : 'Inactive'}\n          </StatusBadge>\n          \n          <div style={{ marginTop: '20px', width: '100%' }}>\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Username:</strong> {user.username}\n            </div>\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Email:</strong> {user.email}\n            </div>\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Joined:</strong> {formatDate(user.createdDate)}\n            </div>\n            {user.lastLoginDate && (\n              <div>\n                <strong>Last Login:</strong> {formatDate(user.lastLoginDate)}\n              </div>\n            )}\n          </div>\n\n          {/* User Stats for Agents */}\n          {user.role === 'Agent' && (\n            <StatsGrid>\n              <StatItem>\n                <StatValue>{userStats.totalTasks}</StatValue>\n                <StatLabel>Total Tasks</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{userStats.completedTasks}</StatValue>\n                <StatLabel>Completed</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{userStats.approvalRate}%</StatValue>\n                <StatLabel>Approval Rate</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{userStats.avgTime}</StatValue>\n                <StatLabel>Avg. Days</StatLabel>\n              </StatItem>\n            </StatsGrid>\n          )}\n        </ProfileCard>\n\n        {/* Profile Form */}\n        <FormContainer>\n          {/* Personal Information */}\n          <FormSection>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>\n              <SectionTitle style={{ marginBottom: 0 }}>Personal Information</SectionTitle>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setEditing(!editing)}\n              >\n                {editing ? 'Cancel' : 'Edit'}\n              </Button>\n            </div>\n\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>\n              <FormGroup>\n                <Label>First Name</Label>\n                <Input\n                  type=\"text\"\n                  name=\"firstName\"\n                  value={formData.firstName}\n                  onChange={handleInputChange}\n                  disabled={!editing}\n                />\n              </FormGroup>\n              <FormGroup>\n                <Label>Last Name</Label>\n                <Input\n                  type=\"text\"\n                  name=\"lastName\"\n                  value={formData.lastName}\n                  onChange={handleInputChange}\n                  disabled={!editing}\n                />\n              </FormGroup>\n            </div>\n\n            <FormGroup>\n              <Label>Email</Label>\n              <Input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                disabled={!editing}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>Phone Number</Label>\n              <Input\n                type=\"tel\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                disabled={!editing}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>Bio</Label>\n              <TextArea\n                name=\"bio\"\n                value={formData.bio}\n                onChange={handleInputChange}\n                disabled={!editing}\n                placeholder=\"Tell us about yourself...\"\n              />\n            </FormGroup>\n\n            {editing && (\n              <ButtonGroup>\n                <Button variant=\"outline\" onClick={() => setEditing(false)}>\n                  Cancel\n                </Button>\n                <Button onClick={handleSaveProfile}>\n                  Save Changes\n                </Button>\n              </ButtonGroup>\n            )}\n          </FormSection>\n\n          {/* Change Password */}\n          <FormSection>\n            <SectionTitle>Change Password</SectionTitle>\n\n            <FormGroup>\n              <Label>Current Password</Label>\n              <Input\n                type=\"password\"\n                name=\"currentPassword\"\n                value={passwordData.currentPassword}\n                onChange={handlePasswordChange}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>New Password</Label>\n              <Input\n                type=\"password\"\n                name=\"newPassword\"\n                value={passwordData.newPassword}\n                onChange={handlePasswordChange}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>Confirm New Password</Label>\n              <Input\n                type=\"password\"\n                name=\"confirmPassword\"\n                value={passwordData.confirmPassword}\n                onChange={handlePasswordChange}\n              />\n            </FormGroup>\n\n            <ButtonGroup>\n              <Button\n                variant=\"secondary\"\n                onClick={handleChangePassword}\n                disabled={!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}\n              >\n                Change Password\n              </Button>\n            </ButtonGroup>\n          </FormSection>\n        </FormContainer>\n      </ProfileContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": "2oBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAAc,2BAA2B,CAC5D,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,gBAAgB,CAAGX,MAAM,CAACY,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,sJAQlC,CAED,KAAM,CAAAC,WAAW,CAAGf,MAAM,CAACE,IAAI,CAAC,CAAAc,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,wHAM/B,CAED,KAAM,CAAAG,MAAM,CAAGjB,MAAM,CAACY,GAAG,CAAAM,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,uRAYxB,CAED,KAAM,CAAAK,QAAQ,CAAGnB,MAAM,CAACoB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAP,sBAAA,gDAEfQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,QAAQ,CAC9C,CAED,KAAM,CAAAC,QAAQ,CAAG1B,MAAM,CAACY,GAAG,CAAAe,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,qEAChBQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,UAAU,CAGhD,CAED,KAAM,CAAAC,WAAW,CAAG7B,MAAM,CAAC8B,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,oKAMTQ,KAAK,EAAIA,KAAK,CAACU,QAAQ,CAAG,SAAS,CAAG,SAAS,CAC1DV,KAAK,EAAIA,KAAK,CAACU,QAAQ,CAAG,SAAS,CAAG,SAAS,CACzD,CAED,KAAM,CAAAC,aAAa,CAAGjC,MAAM,CAACY,GAAG,CAAAsB,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,0CAG/B,CAED,KAAM,CAAAqB,WAAW,CAAGnC,MAAM,CAACE,IAAI,CAAC,CAAAkC,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,4BAE/B,CAED,KAAM,CAAAuB,YAAY,CAAGrC,MAAM,CAACsC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,+GAGDQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAEjE,CAED,KAAM,CAAAC,SAAS,CAAGzC,MAAM,CAACY,GAAG,CAAA8B,gBAAA,GAAAA,gBAAA,CAAA5B,sBAAA,kCAE3B,CAED,KAAM,CAAA6B,KAAK,CAAG3C,MAAM,CAAC4C,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAA/B,sBAAA,wFAIfQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,UAAU,CAChD,CAED,KAAM,CAAAkB,KAAK,CAAG9C,MAAM,CAAC+C,KAAK,CAAAC,iBAAA,GAAAA,iBAAA,CAAAlC,sBAAA,yTAGJQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,UAAU,CACzC3B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAC2B,YAAY,CAACC,EAAE,CAInC7B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC4B,OAAO,CAM/B9B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAG5D,CAED,KAAM,CAAAa,QAAQ,CAAGrD,MAAM,CAACsD,QAAQ,CAAAC,iBAAA,GAAAA,iBAAA,CAAAzC,sBAAA,oRAIVQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,UAAU,CACzC3B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAC2B,YAAY,CAACC,EAAE,CAKnC7B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC4B,OAAO,CAItD,CAED,KAAM,CAAAI,WAAW,CAAGxD,MAAM,CAACY,GAAG,CAAA6C,iBAAA,GAAAA,iBAAA,CAAA3C,sBAAA,6FAK7B,CAED,KAAM,CAAA4C,SAAS,CAAG1D,MAAM,CAACY,GAAG,CAAA+C,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,+HAK3B,CAED,KAAM,CAAA8C,QAAQ,CAAG5D,MAAM,CAACY,GAAG,CAAAiD,iBAAA,GAAAA,iBAAA,CAAA/C,sBAAA,8FAGXQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACsC,QAAQ,CACjCxC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAC2B,YAAY,CAACC,EAAE,CACtD,CAED,KAAM,CAAAY,SAAS,CAAG/D,MAAM,CAACY,GAAG,CAAAoD,iBAAA,GAAAA,iBAAA,CAAAlD,sBAAA,8FAK3B,CAED,KAAM,CAAAmD,SAAS,CAAGjE,MAAM,CAACY,GAAG,CAAAsD,iBAAA,GAAAA,iBAAA,CAAApD,sBAAA,6CAEjBQ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2C,SAAS,CAC/C,CAED,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAEC,IAAI,CAAEC,WAAY,CAAC,CAAGhE,OAAO,CAAC,CAAC,CACvC,KAAM,CAAC+D,IAAI,CAAEE,OAAO,CAAC,CAAG1E,QAAQ,CAAc,IAAI,CAAC,CACnD,KAAM,CAAC2E,OAAO,CAAEC,UAAU,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6E,OAAO,CAAEC,UAAU,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC+E,QAAQ,CAAEC,WAAW,CAAC,CAAGhF,QAAQ,CAAC,CACvCiF,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,WAAW,CAAE,EAAE,CACfC,GAAG,CAAE,EACP,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGvF,QAAQ,CAAC,CAC/CwF,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CACF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG5F,QAAQ,CAAC,CACzC6F,UAAU,CAAE,CAAC,CACbC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,CACX,CAAC,CAAC,CACF,KAAM,CAAAC,QAAQ,CAAG/F,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACdiG,eAAe,CAAC,CAAC,CACjBC,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFtB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAA5F,UAAU,CAAC6F,cAAc,CAAC,CAAC,CAClD3B,OAAO,CAAC0B,QAAQ,CAAC,CACjBpB,WAAW,CAAC,CACVC,SAAS,CAAEmB,QAAQ,CAACnB,SAAS,EAAI,EAAE,CACnCC,QAAQ,CAAEkB,QAAQ,CAAClB,QAAQ,EAAI,EAAE,CACjCC,KAAK,CAAEiB,QAAQ,CAACjB,KAAK,EAAI,EAAE,CAC3BC,WAAW,CAAEgB,QAAQ,CAAChB,WAAW,EAAI,EAAE,CACvCC,GAAG,CAAE,EAAI;AACX,CAAC,CAAC,CACJ,CAAE,MAAOiB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD;AACA,GAAI7B,WAAW,CAAE,CACfC,OAAO,CAACD,WAAW,CAAC,CACpBO,WAAW,CAAC,CACVC,SAAS,CAAER,WAAW,CAACQ,SAAS,EAAI,EAAE,CACtCC,QAAQ,CAAET,WAAW,CAACS,QAAQ,EAAI,EAAE,CACpCC,KAAK,CAAEV,WAAW,CAACU,KAAK,EAAI,EAAE,CAC9BC,WAAW,CAAEX,WAAW,CAACW,WAAW,EAAI,EAAE,CAC1CC,GAAG,CAAE,EACP,CAAC,CAAC,CACJ,CACF,CAAC,OAAS,CACRT,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAuB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,GAAI,CAAA1B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE+B,IAAI,IAAK,OAAO,CAAE,CACjC,KAAM,CAAAC,KAAK,CAAG,KAAM,CAAAjG,UAAU,CAACkG,sBAAsB,CAAC,CAAC,CACvDd,YAAY,CAAC,CACXC,UAAU,CAAEY,KAAK,CAACE,aAAa,EAAI,CAAC,CACpCb,cAAc,CAAEW,KAAK,CAACG,cAAc,EAAI,CAAC,CACzCb,YAAY,CAAE,EAAE,CAAE;AAClBC,OAAO,CAAE,GAAK;AAChB,CAAC,CAAC,CACJ,CACF,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAO,eAAe,CAAG,CACtB,CACEC,IAAI,CAAE,IAAI,CACV/D,KAAK,CAAE,WAAW,CAClBgE,OAAO,CAAEA,CAAA,GAAM,CACb,OAAQtC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE+B,IAAI,EACvB,IAAK,OAAO,CACVP,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,IAAK,YAAY,CACfA,QAAQ,CAAC,uBAAuB,CAAC,CACjC,MACF,IAAK,OAAO,CACVA,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,QACEA,QAAQ,CAAC,GAAG,CAAC,CACjB,CACF,CACF,CAAC,CACD,CAAEa,IAAI,CAAE,IAAI,CAAE/D,KAAK,CAAE,SAAS,CAAEiE,MAAM,CAAE,IAAK,CAAC,CAC9C,CAAEF,IAAI,CAAE,IAAI,CAAE/D,KAAK,CAAE,UAAU,CAAEgE,OAAO,CAAEA,CAAA,GAAMd,QAAQ,CAAC,WAAW,CAAE,CAAC,CACxE,CAED,KAAM,CAAAgB,iBAAiB,CAAIC,CAA4D,EAAK,CAC1F,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChCrC,WAAW,CAACsC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACH,IAAI,EAAGC,KAAK,EACb,CAAC,CACL,CAAC,CAED,KAAM,CAAAI,oBAAoB,CAAIN,CAAsC,EAAK,CACvE,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChC9B,eAAe,CAAC+B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACfD,IAAI,MACP,CAACH,IAAI,EAAGC,KAAK,EACb,CAAC,CACL,CAAC,CAED,KAAM,CAAAK,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,GAAI,CAACjD,IAAI,CAAE,OAEX,KAAM,CAAAhE,UAAU,CAACkH,UAAU,CAAClD,IAAI,CAACmD,MAAM,CAAE5C,QAAQ,CAAC,CAElD;AACAL,OAAO,CAAC4C,IAAI,EAAIA,IAAI,CAAAC,aAAA,CAAAA,aAAA,IAAQD,IAAI,EAAKvC,QAAQ,EAAK,IAAI,CAAC,CACvDD,UAAU,CAAC,KAAK,CAAC,CAEjB8C,KAAK,CAAC,+BAA+B,CAAC,CACxC,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CsB,KAAK,CAAC,0BAA0B,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAIvC,YAAY,CAACG,WAAW,GAAKH,YAAY,CAACI,eAAe,CAAE,CAC7DkC,KAAK,CAAC,4BAA4B,CAAC,CACnC,OACF,CAEA,GAAItC,YAAY,CAACG,WAAW,CAACqC,MAAM,CAAG,CAAC,CAAE,CACvCF,KAAK,CAAC,6CAA6C,CAAC,CACpD,OACF,CAEA,GAAI,CACF;AACAA,KAAK,CAAC,yDAAyD,CAAC,CAChErC,eAAe,CAAC,CACdC,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CACJ,CAAE,MAAOY,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDsB,KAAK,CAAC,2BAA2B,CAAC,CACpC,CACF,CAAC,CAED,KAAM,CAAAG,WAAW,CAAGA,CAAC9C,SAAiB,CAAEC,QAAgB,GAAK,CAC3D,MAAO,GAAA8C,MAAA,CAAG/C,SAAS,CAACgD,MAAM,CAAC,CAAC,CAAC,EAAAD,MAAA,CAAG9C,QAAQ,CAAC+C,MAAM,CAAC,CAAC,CAAC,EAAGC,WAAW,CAAC,CAAC,CACpE,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,GAAI3D,OAAO,CAAE,CACX,mBACEhE,IAAA,CAACP,eAAe,EAACmI,KAAK,CAAC,cAAc,CAAC1B,eAAe,CAAEA,eAAgB,CAAA2B,QAAA,cACrE7H,IAAA,CAACJ,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,GAAI,CAACiE,IAAI,CAAE,CACT,mBACE7D,IAAA,CAACP,eAAe,EAACmI,KAAK,CAAC,cAAc,CAAC1B,eAAe,CAAEA,eAAgB,CAAA2B,QAAA,cACrE7H,IAAA,CAACN,IAAI,EAAAmI,QAAA,cACH7H,IAAA,QAAK8H,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAC,wBAEtD,CAAK,CAAC,CACF,CAAC,CACQ,CAAC,CAEtB,CAEA,mBACE7H,IAAA,CAACP,eAAe,EAACmI,KAAK,CAAC,cAAc,CAAC1B,eAAe,CAAEA,eAAgB,CAAA2B,QAAA,cACrE3H,KAAA,CAACC,gBAAgB,EAAA0H,QAAA,eAEf3H,KAAA,CAACK,WAAW,EAAAsH,QAAA,eACV7H,IAAA,CAACS,MAAM,EAAAoH,QAAA,CACJT,WAAW,CAACvD,IAAI,CAACS,SAAS,CAAET,IAAI,CAACU,QAAQ,CAAC,CACrC,CAAC,cACTrE,KAAA,CAACS,QAAQ,EAAAkH,QAAA,EAAEhE,IAAI,CAACS,SAAS,CAAC,GAAC,CAACT,IAAI,CAACU,QAAQ,EAAW,CAAC,cACrDvE,IAAA,CAACkB,QAAQ,EAAA2G,QAAA,CAAEhE,IAAI,CAACgC,IAAI,CAAW,CAAC,cAChC7F,IAAA,CAACqB,WAAW,EAACG,QAAQ,CAAEqC,IAAI,CAACrC,QAAS,CAAAqG,QAAA,CAClChE,IAAI,CAACrC,QAAQ,CAAG,QAAQ,CAAG,UAAU,CAC3B,CAAC,cAEdtB,KAAA,QAAK4H,KAAK,CAAE,CAAEG,SAAS,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,eAC/C3H,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAN,QAAA,eACnC7H,IAAA,WAAA6H,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAChE,IAAI,CAACuE,QAAQ,EACtC,CAAC,cACNlI,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAN,QAAA,eACnC7H,IAAA,WAAA6H,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAAChE,IAAI,CAACW,KAAK,EAChC,CAAC,cACNtE,KAAA,QAAK4H,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAN,QAAA,eACnC7H,IAAA,WAAA6H,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACL,UAAU,CAAC3D,IAAI,CAACwE,WAAW,CAAC,EACnD,CAAC,CACLxE,IAAI,CAACyE,aAAa,eACjBpI,KAAA,QAAA2H,QAAA,eACE7H,IAAA,WAAA6H,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAACL,UAAU,CAAC3D,IAAI,CAACyE,aAAa,CAAC,EACzD,CACN,EACE,CAAC,CAGLzE,IAAI,CAACgC,IAAI,GAAK,OAAO,eACpB3F,KAAA,CAACgD,SAAS,EAAA2E,QAAA,eACR3H,KAAA,CAACkD,QAAQ,EAAAyE,QAAA,eACP7H,IAAA,CAACuD,SAAS,EAAAsE,QAAA,CAAE7C,SAAS,CAACE,UAAU,CAAY,CAAC,cAC7ClF,IAAA,CAACyD,SAAS,EAAAoE,QAAA,CAAC,aAAW,CAAW,CAAC,EAC1B,CAAC,cACX3H,KAAA,CAACkD,QAAQ,EAAAyE,QAAA,eACP7H,IAAA,CAACuD,SAAS,EAAAsE,QAAA,CAAE7C,SAAS,CAACG,cAAc,CAAY,CAAC,cACjDnF,IAAA,CAACyD,SAAS,EAAAoE,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,cACX3H,KAAA,CAACkD,QAAQ,EAAAyE,QAAA,eACP3H,KAAA,CAACqD,SAAS,EAAAsE,QAAA,EAAE7C,SAAS,CAACI,YAAY,CAAC,GAAC,EAAW,CAAC,cAChDpF,IAAA,CAACyD,SAAS,EAAAoE,QAAA,CAAC,eAAa,CAAW,CAAC,EAC5B,CAAC,cACX3H,KAAA,CAACkD,QAAQ,EAAAyE,QAAA,eACP7H,IAAA,CAACuD,SAAS,EAAAsE,QAAA,CAAE7C,SAAS,CAACK,OAAO,CAAY,CAAC,cAC1CrF,IAAA,CAACyD,SAAS,EAAAoE,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,EACF,CACZ,EACU,CAAC,cAGd3H,KAAA,CAACuB,aAAa,EAAAoG,QAAA,eAEZ3H,KAAA,CAACyB,WAAW,EAAAkG,QAAA,eACV3H,KAAA,QAAK4H,KAAK,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEN,YAAY,CAAE,MAAO,CAAE,CAAAN,QAAA,eAC3G7H,IAAA,CAAC6B,YAAY,EAACiG,KAAK,CAAE,CAAEK,YAAY,CAAE,CAAE,CAAE,CAAAN,QAAA,CAAC,sBAAoB,CAAc,CAAC,cAC7E7H,IAAA,CAACL,MAAM,EACL+I,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,IAAI,CACTvC,OAAO,CAAEA,CAAA,GAAMjC,UAAU,CAAC,CAACD,OAAO,CAAE,CAAA2D,QAAA,CAEnC3D,OAAO,CAAG,QAAQ,CAAG,MAAM,CACtB,CAAC,EACN,CAAC,cAENhE,KAAA,QAAK4H,KAAK,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEK,mBAAmB,CAAE,SAAS,CAAEC,GAAG,CAAE,MAAO,CAAE,CAAAhB,QAAA,eAC3E3H,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,YAAU,CAAO,CAAC,cACzB7H,IAAA,CAACsC,KAAK,EACJwG,IAAI,CAAC,MAAM,CACXtC,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAErC,QAAQ,CAACE,SAAU,CAC1ByE,QAAQ,CAAEzC,iBAAkB,CAC5B0C,QAAQ,CAAE,CAAC9E,OAAQ,CACpB,CAAC,EACO,CAAC,cACZhE,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,WAAS,CAAO,CAAC,cACxB7H,IAAA,CAACsC,KAAK,EACJwG,IAAI,CAAC,MAAM,CACXtC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAErC,QAAQ,CAACG,QAAS,CACzBwE,QAAQ,CAAEzC,iBAAkB,CAC5B0C,QAAQ,CAAE,CAAC9E,OAAQ,CACpB,CAAC,EACO,CAAC,EACT,CAAC,cAENhE,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,OAAK,CAAO,CAAC,cACpB7H,IAAA,CAACsC,KAAK,EACJwG,IAAI,CAAC,OAAO,CACZtC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAErC,QAAQ,CAACI,KAAM,CACtBuE,QAAQ,CAAEzC,iBAAkB,CAC5B0C,QAAQ,CAAE,CAAC9E,OAAQ,CACpB,CAAC,EACO,CAAC,cAEZhE,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,cAAY,CAAO,CAAC,cAC3B7H,IAAA,CAACsC,KAAK,EACJwG,IAAI,CAAC,KAAK,CACVtC,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAErC,QAAQ,CAACK,WAAY,CAC5BsE,QAAQ,CAAEzC,iBAAkB,CAC5B0C,QAAQ,CAAE,CAAC9E,OAAQ,CACpB,CAAC,EACO,CAAC,cAEZhE,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,KAAG,CAAO,CAAC,cAClB7H,IAAA,CAAC6C,QAAQ,EACP2D,IAAI,CAAC,KAAK,CACVC,KAAK,CAAErC,QAAQ,CAACM,GAAI,CACpBqE,QAAQ,CAAEzC,iBAAkB,CAC5B0C,QAAQ,CAAE,CAAC9E,OAAQ,CACnB+E,WAAW,CAAC,2BAA2B,CACxC,CAAC,EACO,CAAC,CAEX/E,OAAO,eACNhE,KAAA,CAAC8C,WAAW,EAAA6E,QAAA,eACV7H,IAAA,CAACL,MAAM,EAAC+I,OAAO,CAAC,SAAS,CAACtC,OAAO,CAAEA,CAAA,GAAMjC,UAAU,CAAC,KAAK,CAAE,CAAA0D,QAAA,CAAC,QAE5D,CAAQ,CAAC,cACT7H,IAAA,CAACL,MAAM,EAACyG,OAAO,CAAEU,iBAAkB,CAAAe,QAAA,CAAC,cAEpC,CAAQ,CAAC,EACE,CACd,EACU,CAAC,cAGd3H,KAAA,CAACyB,WAAW,EAAAkG,QAAA,eACV7H,IAAA,CAAC6B,YAAY,EAAAgG,QAAA,CAAC,iBAAe,CAAc,CAAC,cAE5C3H,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,kBAAgB,CAAO,CAAC,cAC/B7H,IAAA,CAACsC,KAAK,EACJwG,IAAI,CAAC,UAAU,CACftC,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAE9B,YAAY,CAACE,eAAgB,CACpCkE,QAAQ,CAAElC,oBAAqB,CAChC,CAAC,EACO,CAAC,cAEZ3G,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,cAAY,CAAO,CAAC,cAC3B7H,IAAA,CAACsC,KAAK,EACJwG,IAAI,CAAC,UAAU,CACftC,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAE9B,YAAY,CAACG,WAAY,CAChCiE,QAAQ,CAAElC,oBAAqB,CAChC,CAAC,EACO,CAAC,cAEZ3G,KAAA,CAAC+B,SAAS,EAAA4F,QAAA,eACR7H,IAAA,CAACmC,KAAK,EAAA0F,QAAA,CAAC,sBAAoB,CAAO,CAAC,cACnC7H,IAAA,CAACsC,KAAK,EACJwG,IAAI,CAAC,UAAU,CACftC,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAE9B,YAAY,CAACI,eAAgB,CACpCgE,QAAQ,CAAElC,oBAAqB,CAChC,CAAC,EACO,CAAC,cAEZ7G,IAAA,CAACgD,WAAW,EAAA6E,QAAA,cACV7H,IAAA,CAACL,MAAM,EACL+I,OAAO,CAAC,WAAW,CACnBtC,OAAO,CAAEc,oBAAqB,CAC9B8B,QAAQ,CAAE,CAACrE,YAAY,CAACE,eAAe,EAAI,CAACF,YAAY,CAACG,WAAW,EAAI,CAACH,YAAY,CAACI,eAAgB,CAAA8C,QAAA,CACvG,iBAED,CAAQ,CAAC,CACE,CAAC,EACH,CAAC,EACD,CAAC,EACA,CAAC,CACJ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAjE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}