{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{Card,Button,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatsContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\"])));const StatCard=styled(Card)(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  padding: 20px;\\n\"])));const StatValue=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin-bottom: 5px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.textDark);const StatLabel=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  color: \",\";\\n  font-weight: 500;\\n\"])),props=>props.theme.colors.textLight);const FilterContainer=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background: white;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const SearchInput=styled.input(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  min-width: 250px;\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const TableContainer=styled.div(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  overflow-x: auto;\\n\"])));const Table=styled.table(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const StatusBadge=styled.span(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.status){case'approved':case'completed':return\"\\n          background-color: #e8f5e9;\\n          color: #2e7d32;\\n        \";case'rejected':return\"\\n          background-color: #ffebee;\\n          color: #c62828;\\n        \";case'pending-review':return\"\\n          background-color: #f3e5f5;\\n          color: #4a148c;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const AgentCompleted=()=>{const[completedTasks,setCompletedTasks]=useState([]);const[loading,setLoading]=useState(true);const[statusFilter,setStatusFilter]=useState('all');const[searchTerm,setSearchTerm]=useState('');const[stats,setStats]=useState({totalCompleted:0,approved:0,rejected:0,pendingReview:0});const navigate=useNavigate();useEffect(()=>{loadCompletedTasks();},[statusFilter]);const loadCompletedTasks=async()=>{try{setLoading(true);// Load completed tasks based on filter\nconst statuses=statusFilter==='all'?['approved','rejected','pending-review']:[statusFilter];const allTasks=[];for(const status of statuses){try{const response=await apiService.getLeads(1,100,status);allTasks.push(...(response.data||[]));}catch(error){console.error(\"Error loading \".concat(status,\" tasks:\"),error);}}setCompletedTasks(allTasks);// Calculate stats\nconst approved=allTasks.filter(task=>task.status==='approved').length;const rejected=allTasks.filter(task=>task.status==='rejected').length;const pendingReview=allTasks.filter(task=>task.status==='pending-review').length;setStats({totalCompleted:allTasks.length,approved,rejected,pendingReview});}catch(error){console.error('Error loading completed tasks:',error);// Mock data for demo\nconst mockTasks=[{leadId:3,customerName:'Alice Johnson',mobileNumber:'9876543212',loanType:'Car Loan',status:'approved',createdDate:'2024-01-10T08:00:00Z',assignedDate:'2024-01-10T09:00:00Z',submittedDate:'2024-01-12T16:30:00Z',createdByName:'Admin User',assignedToName:'Current Agent',documentCount:3,croppedImageCount:2},{leadId:4,customerName:'Bob Wilson',mobileNumber:'9876543213',loanType:'Personal Loan',status:'rejected',createdDate:'2024-01-08T10:15:00Z',assignedDate:'2024-01-08T11:00:00Z',submittedDate:'2024-01-09T14:20:00Z',createdByName:'Admin User',assignedToName:'Current Agent',documentCount:2,croppedImageCount:1}];setCompletedTasks(mockTasks);setStats({totalCompleted:2,approved:1,rejected:1,pendingReview:0});}finally{setLoading(false);}};const filteredTasks=completedTasks.filter(task=>task.customerName.toLowerCase().includes(searchTerm.toLowerCase())||task.mobileNumber.includes(searchTerm)||task.loanType.toLowerCase().includes(searchTerm.toLowerCase()));const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>navigate('/agent/dashboard')},{icon:'📋',label:'My Tasks',onClick:()=>navigate('/agent/tasks')},{icon:'✅',label:'Completed',active:true},{icon:'📊',label:'Reports',onClick:()=>navigate('/agent/reports')}];const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};const calculateDuration=(startDate,endDate)=>{const start=new Date(startDate);const end=new Date(endDate);const diffTime=Math.abs(end.getTime()-start.getTime());const diffDays=Math.ceil(diffTime/(1000*60*60*24));return\"\".concat(diffDays,\" day\").concat(diffDays!==1?'s':'');};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Completed Tasks\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsxs(DashboardLayout,{title:\"Completed Tasks\",navigationItems:navigationItems,children:[/*#__PURE__*/_jsxs(StatsContainer,{children:[/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:stats.totalCompleted}),/*#__PURE__*/_jsx(StatLabel,{children:\"Total Completed\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{style:{color:'#2e7d32'},children:stats.approved}),/*#__PURE__*/_jsx(StatLabel,{children:\"Approved\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{style:{color:'#c62828'},children:stats.rejected}),/*#__PURE__*/_jsx(StatLabel,{children:\"Rejected\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{style:{color:'#4a148c'},children:stats.pendingReview}),/*#__PURE__*/_jsx(StatLabel,{children:\"Pending Review\"})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Completed Verifications\"}),/*#__PURE__*/_jsxs(FilterContainer,{children:[/*#__PURE__*/_jsxs(FilterSelect,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Completed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:\"Rejected\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-review\",children:\"Pending Review\"})]}),/*#__PURE__*/_jsx(SearchInput,{type:\"text\",placeholder:\"Search by customer name, mobile, or loan type...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Customer\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Mobile\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Loan Type\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Status\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Submitted Date\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Duration\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredTasks.map(task=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:task.customerName}),/*#__PURE__*/_jsx(TableCell,{children:task.mobileNumber}),/*#__PURE__*/_jsx(TableCell,{children:task.loanType}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(StatusBadge,{status:task.status,children:task.status.replace('-',' ').toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:task.submittedDate?formatDate(task.submittedDate):'-'}),/*#__PURE__*/_jsx(TableCell,{children:task.assignedDate&&task.submittedDate?calculateDuration(task.assignedDate,task.submittedDate):'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>navigate(\"/lead/\".concat(task.leadId)),children:\"View Details\"})})]},task.leadId))})]})}),filteredTasks.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px',color:'#777'},children:searchTerm?'No completed tasks found matching your search.':'No completed tasks yet.'})]})]});};export default AgentCompleted;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "StatsContainer", "div", "_templateObject", "_taggedTemplateLiteral", "StatCard", "_templateObject2", "StatValue", "_templateObject3", "props", "theme", "colors", "textDark", "StatLabel", "_templateObject4", "textLight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject5", "FilterSelect", "select", "_templateObject6", "mediumGray", "borderRadius", "sm", "primary", "SearchInput", "input", "_templateObject7", "TableContainer", "_templateObject8", "Table", "table", "_templateObject9", "TableHeader", "th", "_templateObject0", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject1", "TableRow", "tr", "_templateObject10", "StatusBadge", "span", "_templateObject11", "status", "AgentCompleted", "completedTasks", "setCompletedTasks", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "stats", "setStats", "totalCompleted", "approved", "rejected", "pendingReview", "navigate", "loadCompletedTasks", "statuses", "allTasks", "response", "getLeads", "push", "data", "error", "console", "concat", "filter", "task", "length", "mockTasks", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedDate", "submittedDate", "createdByName", "assignedToName", "documentCount", "croppedImageCount", "filteredTasks", "toLowerCase", "includes", "navigationItems", "icon", "label", "onClick", "active", "formatDate", "dateString", "Date", "toLocaleDateString", "calculateDuration", "startDate", "endDate", "start", "end", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "title", "children", "style", "color", "marginBottom", "value", "onChange", "e", "target", "type", "placeholder", "map", "replace", "toUpperCase", "size", "textAlign", "padding"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Agent/AgentCompleted.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem } from '../../services/apiService';\n\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'approved':\n      case 'completed':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst AgentCompleted: React.FC = () => {\n  const [completedTasks, setCompletedTasks] = useState<LeadListItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [stats, setStats] = useState({\n    totalCompleted: 0,\n    approved: 0,\n    rejected: 0,\n    pendingReview: 0,\n  });\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadCompletedTasks();\n  }, [statusFilter]);\n\n  const loadCompletedTasks = async () => {\n    try {\n      setLoading(true);\n      \n      // Load completed tasks based on filter\n      const statuses = statusFilter === 'all' \n        ? ['approved', 'rejected', 'pending-review'] \n        : [statusFilter];\n      \n      const allTasks: LeadListItem[] = [];\n      \n      for (const status of statuses) {\n        try {\n          const response = await apiService.getLeads(1, 100, status);\n          allTasks.push(...(response.data || []));\n        } catch (error) {\n          console.error(`Error loading ${status} tasks:`, error);\n        }\n      }\n      \n      setCompletedTasks(allTasks);\n      \n      // Calculate stats\n      const approved = allTasks.filter(task => task.status === 'approved').length;\n      const rejected = allTasks.filter(task => task.status === 'rejected').length;\n      const pendingReview = allTasks.filter(task => task.status === 'pending-review').length;\n      \n      setStats({\n        totalCompleted: allTasks.length,\n        approved,\n        rejected,\n        pendingReview,\n      });\n      \n    } catch (error) {\n      console.error('Error loading completed tasks:', error);\n      // Mock data for demo\n      const mockTasks = [\n        {\n          leadId: 3,\n          customerName: 'Alice Johnson',\n          mobileNumber: '9876543212',\n          loanType: 'Car Loan',\n          status: 'approved',\n          createdDate: '2024-01-10T08:00:00Z',\n          assignedDate: '2024-01-10T09:00:00Z',\n          submittedDate: '2024-01-12T16:30:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 3,\n          croppedImageCount: 2,\n        },\n        {\n          leadId: 4,\n          customerName: 'Bob Wilson',\n          mobileNumber: '9876543213',\n          loanType: 'Personal Loan',\n          status: 'rejected',\n          createdDate: '2024-01-08T10:15:00Z',\n          assignedDate: '2024-01-08T11:00:00Z',\n          submittedDate: '2024-01-09T14:20:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 2,\n          croppedImageCount: 1,\n        },\n      ];\n      \n      setCompletedTasks(mockTasks);\n      setStats({\n        totalCompleted: 2,\n        approved: 1,\n        rejected: 1,\n        pendingReview: 0,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredTasks = completedTasks.filter(task =>\n    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    task.mobileNumber.includes(searchTerm) ||\n    task.loanType.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },\n    { icon: '📋', label: 'My Tasks', onClick: () => navigate('/agent/tasks') },\n    { icon: '✅', label: 'Completed', active: true },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/agent/reports') },\n  ];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const calculateDuration = (startDate: string, endDate: string) => {\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Completed Tasks\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Completed Tasks\" navigationItems={navigationItems}>\n      {/* Stats Cards */}\n      <StatsContainer>\n        <StatCard>\n          <StatValue>{stats.totalCompleted}</StatValue>\n          <StatLabel>Total Completed</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue style={{ color: '#2e7d32' }}>{stats.approved}</StatValue>\n          <StatLabel>Approved</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue style={{ color: '#c62828' }}>{stats.rejected}</StatValue>\n          <StatLabel>Rejected</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue style={{ color: '#4a148c' }}>{stats.pendingReview}</StatValue>\n          <StatLabel>Pending Review</StatLabel>\n        </StatCard>\n      </StatsContainer>\n\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Completed Verifications</h2>\n\n        <FilterContainer>\n          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>\n            <option value=\"all\">All Completed</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"rejected\">Rejected</option>\n            <option value=\"pending-review\">Pending Review</option>\n          </FilterSelect>\n          \n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search by customer name, mobile, or loan type...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Submitted Date</TableHeader>\n                <TableHeader>Duration</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredTasks.map((task) => (\n                <TableRow key={task.leadId}>\n                  <TableCell>{task.customerName}</TableCell>\n                  <TableCell>{task.mobileNumber}</TableCell>\n                  <TableCell>{task.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={task.status}>\n                      {task.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate ? formatDate(task.submittedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    {task.assignedDate && task.submittedDate \n                      ? calculateDuration(task.assignedDate, task.submittedDate)\n                      : '-'\n                    }\n                  </TableCell>\n                  <TableCell>\n                    <Button\n                      size=\"sm\"\n                      onClick={() => navigate(`/lead/${task.leadId}`)}\n                    >\n                      View Details\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {filteredTasks.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            {searchTerm ? 'No completed tasks found matching your search.' : 'No completed tasks yet.'}\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default AgentCompleted;\n"], "mappings": "kZAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAAsB,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErE,KAAM,CAAAC,cAAc,CAAGV,MAAM,CAACW,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,kIAKhC,CAED,KAAM,CAAAC,QAAQ,CAAGd,MAAM,CAACE,IAAI,CAAC,CAAAa,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,wHAM5B,CAED,KAAM,CAAAG,SAAS,CAAGhB,MAAM,CAACW,GAAG,CAAAM,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,yFAIjBK,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,QAAQ,CAC9C,CAED,KAAM,CAAAC,SAAS,CAAGtB,MAAM,CAACW,GAAG,CAAAY,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,kEAEjBK,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,SAAS,CAE/C,CAED,KAAM,CAAAC,eAAe,CAAGzB,MAAM,CAACW,GAAG,CAAAe,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,sFAKjC,CAED,KAAM,CAAAc,YAAY,CAAG3B,MAAM,CAAC4B,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,6LAEZK,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACU,UAAU,CACzCZ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACY,YAAY,CAACC,EAAE,CAKnCd,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,OAAO,CAGtD,CAED,KAAM,CAAAC,WAAW,CAAGlC,MAAM,CAACmC,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAvB,sBAAA,wMAIVK,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACU,UAAU,CACzCZ,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACY,YAAY,CAACC,EAAE,CAInCd,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,OAAO,CAGtD,CAED,KAAM,CAAAI,cAAc,CAAGrC,MAAM,CAACW,GAAG,CAAA2B,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,+BAEhC,CAED,KAAM,CAAA0B,KAAK,CAAGvC,MAAM,CAACwC,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAA5B,sBAAA,wDAGzB,CAED,KAAM,CAAA6B,WAAW,CAAG1C,MAAM,CAAC2C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA/B,sBAAA,qJAGAK,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,SAAS,CAC5C3B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC0B,QAAQ,CAE/C5B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2B,UAAU,CAChD,CAED,KAAM,CAAAC,SAAS,CAAGhD,MAAM,CAACiD,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAArC,sBAAA,uFAGEK,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAGnD,MAAM,CAACoD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAxC,sBAAA,wDAEFK,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,SAAS,CAE5D,CAED,KAAM,CAAAS,WAAW,CAAGtD,MAAM,CAACuD,IAAI,CAAAC,iBAAA,GAAAA,iBAAA,CAAA3C,sBAAA,mIAO3BK,KAAK,EAAI,CACT,OAAQA,KAAK,CAACuC,MAAM,EAClB,IAAK,UAAU,CACf,IAAK,WAAW,CACd,oFAIF,IAAK,UAAU,CACb,oFAIF,IAAK,gBAAgB,CACnB,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAG/D,QAAQ,CAAiB,EAAE,CAAC,CACxE,KAAM,CAACgE,OAAO,CAAEC,UAAU,CAAC,CAAGjE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACoE,UAAU,CAAEC,aAAa,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsE,KAAK,CAAEC,QAAQ,CAAC,CAAGvE,QAAQ,CAAC,CACjCwE,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,CAAC,CACXC,aAAa,CAAE,CACjB,CAAC,CAAC,CACF,KAAM,CAAAC,QAAQ,CAAG1E,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd4E,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,CAACX,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAW,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACFZ,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAAa,QAAQ,CAAGZ,YAAY,GAAK,KAAK,CACnC,CAAC,UAAU,CAAE,UAAU,CAAE,gBAAgB,CAAC,CAC1C,CAACA,YAAY,CAAC,CAElB,KAAM,CAAAa,QAAwB,CAAG,EAAE,CAEnC,IAAK,KAAM,CAAAnB,MAAM,GAAI,CAAAkB,QAAQ,CAAE,CAC7B,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAxE,UAAU,CAACyE,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAErB,MAAM,CAAC,CAC1DmB,QAAQ,CAACG,IAAI,CAAC,IAAIF,QAAQ,CAACG,IAAI,EAAI,EAAE,CAAC,CAAC,CACzC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,kBAAAE,MAAA,CAAkB1B,MAAM,YAAWwB,KAAK,CAAC,CACxD,CACF,CAEArB,iBAAiB,CAACgB,QAAQ,CAAC,CAE3B;AACA,KAAM,CAAAN,QAAQ,CAAGM,QAAQ,CAACQ,MAAM,CAACC,IAAI,EAAIA,IAAI,CAAC5B,MAAM,GAAK,UAAU,CAAC,CAAC6B,MAAM,CAC3E,KAAM,CAAAf,QAAQ,CAAGK,QAAQ,CAACQ,MAAM,CAACC,IAAI,EAAIA,IAAI,CAAC5B,MAAM,GAAK,UAAU,CAAC,CAAC6B,MAAM,CAC3E,KAAM,CAAAd,aAAa,CAAGI,QAAQ,CAACQ,MAAM,CAACC,IAAI,EAAIA,IAAI,CAAC5B,MAAM,GAAK,gBAAgB,CAAC,CAAC6B,MAAM,CAEtFlB,QAAQ,CAAC,CACPC,cAAc,CAAEO,QAAQ,CAACU,MAAM,CAC/BhB,QAAQ,CACRC,QAAQ,CACRC,aACF,CAAC,CAAC,CAEJ,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD;AACA,KAAM,CAAAM,SAAS,CAAG,CAChB,CACEC,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,eAAe,CAC7BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,UAAU,CACpBlC,MAAM,CAAE,UAAU,CAClBmC,WAAW,CAAE,sBAAsB,CACnCC,YAAY,CAAE,sBAAsB,CACpCC,aAAa,CAAE,sBAAsB,CACrCC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,eAAe,CAC/BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACD,CACEV,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,eAAe,CACzBlC,MAAM,CAAE,UAAU,CAClBmC,WAAW,CAAE,sBAAsB,CACnCC,YAAY,CAAE,sBAAsB,CACpCC,aAAa,CAAE,sBAAsB,CACrCC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,eAAe,CAC/BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACF,CAEDtC,iBAAiB,CAAC2B,SAAS,CAAC,CAC5BnB,QAAQ,CAAC,CACPC,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,CAAC,CACXC,aAAa,CAAE,CACjB,CAAC,CAAC,CACJ,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAqC,aAAa,CAAGxC,cAAc,CAACyB,MAAM,CAACC,IAAI,EAC9CA,IAAI,CAACI,YAAY,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpC,UAAU,CAACmC,WAAW,CAAC,CAAC,CAAC,EAClEf,IAAI,CAACK,YAAY,CAACW,QAAQ,CAACpC,UAAU,CAAC,EACtCoB,IAAI,CAACM,QAAQ,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpC,UAAU,CAACmC,WAAW,CAAC,CAAC,CAC/D,CAAC,CAED,KAAM,CAAAE,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAMhC,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC/E,CAAE8B,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAU,CAAEC,OAAO,CAAEA,CAAA,GAAMhC,QAAQ,CAAC,cAAc,CAAE,CAAC,CAC1E,CAAE8B,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,WAAW,CAAEE,MAAM,CAAE,IAAK,CAAC,CAC/C,CAAEH,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEC,OAAO,CAAEA,CAAA,GAAMhC,QAAQ,CAAC,gBAAgB,CAAE,CAAC,CAC5E,CAED,KAAM,CAAAkC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAACC,SAAiB,CAAEC,OAAe,GAAK,CAChE,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAL,IAAI,CAACG,SAAS,CAAC,CACjC,KAAM,CAAAG,GAAG,CAAG,GAAI,CAAAN,IAAI,CAACI,OAAO,CAAC,CAC7B,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAGL,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC,CAC1D,KAAM,CAAAC,QAAQ,CAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAC5D,SAAAjC,MAAA,CAAUqC,QAAQ,SAAArC,MAAA,CAAOqC,QAAQ,GAAK,CAAC,CAAG,GAAG,CAAG,EAAE,EACpD,CAAC,CAED,GAAI3D,OAAO,CAAE,CACX,mBACEtD,IAAA,CAACN,eAAe,EAACyH,KAAK,CAAC,iBAAiB,CAACpB,eAAe,CAAEA,eAAgB,CAAAqB,QAAA,cACxEpH,IAAA,CAACH,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,mBACEK,KAAA,CAACR,eAAe,EAACyH,KAAK,CAAC,iBAAiB,CAACpB,eAAe,CAAEA,eAAgB,CAAAqB,QAAA,eAExElH,KAAA,CAACC,cAAc,EAAAiH,QAAA,eACblH,KAAA,CAACK,QAAQ,EAAA6G,QAAA,eACPpH,IAAA,CAACS,SAAS,EAAA2G,QAAA,CAAExD,KAAK,CAACE,cAAc,CAAY,CAAC,cAC7C9D,IAAA,CAACe,SAAS,EAAAqG,QAAA,CAAC,iBAAe,CAAW,CAAC,EAC9B,CAAC,cACXlH,KAAA,CAACK,QAAQ,EAAA6G,QAAA,eACPpH,IAAA,CAACS,SAAS,EAAC4G,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAF,QAAA,CAAExD,KAAK,CAACG,QAAQ,CAAY,CAAC,cACpE/D,IAAA,CAACe,SAAS,EAAAqG,QAAA,CAAC,UAAQ,CAAW,CAAC,EACvB,CAAC,cACXlH,KAAA,CAACK,QAAQ,EAAA6G,QAAA,eACPpH,IAAA,CAACS,SAAS,EAAC4G,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAF,QAAA,CAAExD,KAAK,CAACI,QAAQ,CAAY,CAAC,cACpEhE,IAAA,CAACe,SAAS,EAAAqG,QAAA,CAAC,UAAQ,CAAW,CAAC,EACvB,CAAC,cACXlH,KAAA,CAACK,QAAQ,EAAA6G,QAAA,eACPpH,IAAA,CAACS,SAAS,EAAC4G,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAF,QAAA,CAAExD,KAAK,CAACK,aAAa,CAAY,CAAC,cACzEjE,IAAA,CAACe,SAAS,EAAAqG,QAAA,CAAC,gBAAc,CAAW,CAAC,EAC7B,CAAC,EACG,CAAC,cAEjBlH,KAAA,CAACP,IAAI,EAAAyH,QAAA,eACHpH,IAAA,OAAIqH,KAAK,CAAE,CAAEE,YAAY,CAAE,MAAM,CAAED,KAAK,CAAE,SAAU,CAAE,CAAAF,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAEnFlH,KAAA,CAACgB,eAAe,EAAAkG,QAAA,eACdlH,KAAA,CAACkB,YAAY,EAACoG,KAAK,CAAEhE,YAAa,CAACiE,QAAQ,CAAGC,CAAC,EAAKjE,eAAe,CAACiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAJ,QAAA,eAClFpH,IAAA,WAAQwH,KAAK,CAAC,KAAK,CAAAJ,QAAA,CAAC,eAAa,CAAQ,CAAC,cAC1CpH,IAAA,WAAQwH,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CpH,IAAA,WAAQwH,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CpH,IAAA,WAAQwH,KAAK,CAAC,gBAAgB,CAAAJ,QAAA,CAAC,gBAAc,CAAQ,CAAC,EAC1C,CAAC,cAEfpH,IAAA,CAAC2B,WAAW,EACViG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kDAAkD,CAC9DL,KAAK,CAAE9D,UAAW,CAClB+D,QAAQ,CAAGC,CAAC,EAAK/D,aAAa,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,EACa,CAAC,cAElBxH,IAAA,CAAC8B,cAAc,EAAAsF,QAAA,cACblH,KAAA,CAAC8B,KAAK,EAAAoF,QAAA,eACJpH,IAAA,UAAAoH,QAAA,cACElH,KAAA,OAAAkH,QAAA,eACEpH,IAAA,CAACmC,WAAW,EAAAiF,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnCpH,IAAA,CAACmC,WAAW,EAAAiF,QAAA,CAAC,QAAM,CAAa,CAAC,cACjCpH,IAAA,CAACmC,WAAW,EAAAiF,QAAA,CAAC,WAAS,CAAa,CAAC,cACpCpH,IAAA,CAACmC,WAAW,EAAAiF,QAAA,CAAC,QAAM,CAAa,CAAC,cACjCpH,IAAA,CAACmC,WAAW,EAAAiF,QAAA,CAAC,gBAAc,CAAa,CAAC,cACzCpH,IAAA,CAACmC,WAAW,EAAAiF,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnCpH,IAAA,CAACmC,WAAW,EAAAiF,QAAA,CAAC,SAAO,CAAa,CAAC,EAChC,CAAC,CACA,CAAC,cACRpH,IAAA,UAAAoH,QAAA,CACGxB,aAAa,CAACkC,GAAG,CAAEhD,IAAI,eACtB5E,KAAA,CAAC0C,QAAQ,EAAAwE,QAAA,eACPpH,IAAA,CAACyC,SAAS,EAAA2E,QAAA,CAAEtC,IAAI,CAACI,YAAY,CAAY,CAAC,cAC1ClF,IAAA,CAACyC,SAAS,EAAA2E,QAAA,CAAEtC,IAAI,CAACK,YAAY,CAAY,CAAC,cAC1CnF,IAAA,CAACyC,SAAS,EAAA2E,QAAA,CAAEtC,IAAI,CAACM,QAAQ,CAAY,CAAC,cACtCpF,IAAA,CAACyC,SAAS,EAAA2E,QAAA,cACRpH,IAAA,CAAC+C,WAAW,EAACG,MAAM,CAAE4B,IAAI,CAAC5B,MAAO,CAAAkE,QAAA,CAC9BtC,IAAI,CAAC5B,MAAM,CAAC6E,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CACjC,CAAC,CACL,CAAC,cACZhI,IAAA,CAACyC,SAAS,EAAA2E,QAAA,CACPtC,IAAI,CAACS,aAAa,CAAGa,UAAU,CAACtB,IAAI,CAACS,aAAa,CAAC,CAAG,GAAG,CACjD,CAAC,cACZvF,IAAA,CAACyC,SAAS,EAAA2E,QAAA,CACPtC,IAAI,CAACQ,YAAY,EAAIR,IAAI,CAACS,aAAa,CACpCiB,iBAAiB,CAAC1B,IAAI,CAACQ,YAAY,CAAER,IAAI,CAACS,aAAa,CAAC,CACxD,GAAG,CAEE,CAAC,cACZvF,IAAA,CAACyC,SAAS,EAAA2E,QAAA,cACRpH,IAAA,CAACJ,MAAM,EACLqI,IAAI,CAAC,IAAI,CACT/B,OAAO,CAAEA,CAAA,GAAMhC,QAAQ,UAAAU,MAAA,CAAUE,IAAI,CAACG,MAAM,CAAE,CAAE,CAAAmC,QAAA,CACjD,cAED,CAAQ,CAAC,CACA,CAAC,GAzBCtC,IAAI,CAACG,MA0BV,CACX,CAAC,CACG,CAAC,EACH,CAAC,CACM,CAAC,CAEhBW,aAAa,CAACb,MAAM,GAAK,CAAC,eACzB/E,IAAA,QAAKqH,KAAK,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEb,KAAK,CAAE,MAAO,CAAE,CAAAF,QAAA,CACjE1D,UAAU,CAAG,gDAAgD,CAAG,yBAAyB,CACvF,CACN,EACG,CAAC,EACQ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}