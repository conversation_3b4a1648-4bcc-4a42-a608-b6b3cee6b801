{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Common\\\\DocumentViewer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { <PERSON><PERSON>, Badge } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DocumentsContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.lg};\n`;\n_c = DocumentsContainer;\nconst DocumentSection = styled.div`\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  background: ${props => props.theme.colors.white};\n`;\n_c2 = DocumentSection;\nconst SectionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.md};\n  padding-bottom: ${props => props.theme.spacing.sm};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n`;\n_c3 = SectionHeader;\nconst SectionTitle = styled.h4`\n  margin: 0;\n  color: ${props => props.theme.colors.primary};\n  font-size: ${props => props.theme.typography.fontSize.lg};\n  font-weight: ${props => props.theme.typography.fontWeight.semibold};\n`;\n_c4 = SectionTitle;\nconst DocumentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: ${props => props.theme.spacing.md};\n`;\n_c5 = DocumentGrid;\nconst DocumentCard = styled.div`\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md};\n  background: ${props => props.theme.colors.backgroundSecondary};\n  transition: ${props => props.theme.transitions.default};\n  \n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: ${props => props.theme.shadows.sm};\n  }\n`;\n_c6 = DocumentCard;\nconst DocumentHeader = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c7 = DocumentHeader;\nconst DocumentIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: ${props => props.theme.borderRadius.md};\n  background: ${props => props.theme.colors.primaryGradient};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${props => props.theme.colors.white};\n  font-size: ${props => props.theme.typography.fontSize.lg};\n  font-weight: ${props => props.theme.typography.fontWeight.bold};\n`;\n_c8 = DocumentIcon;\nconst DocumentInfo = styled.div`\n  flex: 1;\n`;\n_c9 = DocumentInfo;\nconst DocumentName = styled.div`\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n  margin-bottom: 2px;\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  word-break: break-word;\n`;\n_c0 = DocumentName;\nconst DocumentType = styled.div`\n  font-size: ${props => props.theme.typography.fontSize.xs};\n  color: ${props => props.theme.colors.textTertiary};\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n`;\n_c1 = DocumentType;\nconst DocumentMeta = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.xs};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n_c10 = DocumentMeta;\nconst MetaRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: ${props => props.theme.typography.fontSize.xs};\n  color: ${props => props.theme.colors.textTertiary};\n`;\n_c11 = MetaRow;\nconst DocumentActions = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.xs};\n  flex-wrap: wrap;\n`;\n_c12 = DocumentActions;\nconst PreviewContainer = styled.div`\n  margin-top: ${props => props.theme.spacing.sm};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  overflow: hidden;\n  background: ${props => props.theme.colors.backgroundTertiary};\n`;\n_c13 = PreviewContainer;\nconst ImagePreview = styled.img`\n  width: 100%;\n  height: 120px;\n  object-fit: cover;\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.default};\n  \n  &:hover {\n    opacity: 0.8;\n  }\n`;\n_c14 = ImagePreview;\nconst NoPreview = styled.div`\n  width: 100%;\n  height: 120px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${props => props.theme.colors.textTertiary};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  background: ${props => props.theme.colors.backgroundTertiary};\n`;\n_c15 = NoPreview;\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: ${props => props.theme.spacing.xl};\n  color: ${props => props.theme.colors.textTertiary};\n`;\n_c16 = EmptyState;\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${props => props.theme.spacing.md};\n  opacity: 0.5;\n`;\n_c17 = EmptyIcon;\nconst DocumentViewer = ({\n  leadId,\n  documents,\n  verificationDocuments,\n  croppedImages,\n  onRefresh,\n  showUploadButton = false,\n  onUploadClick\n}) => {\n  _s();\n  const [downloading, setDownloading] = useState(null);\n  const getFileIcon = (fileName, mimeType) => {\n    var _fileName$split$pop;\n    if (mimeType !== null && mimeType !== void 0 && mimeType.startsWith('image/')) return '🖼️';\n    const extension = (_fileName$split$pop = fileName.split('.').pop()) === null || _fileName$split$pop === void 0 ? void 0 : _fileName$split$pop.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return '📄';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n        return '🖼️';\n      case 'doc':\n      case 'docx':\n        return '📝';\n      case 'xls':\n      case 'xlsx':\n        return '📊';\n      case 'txt':\n        return '📄';\n      default:\n        return '📎';\n    }\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleDownload = async (documentId, fileName) => {\n    try {\n      setDownloading(documentId);\n      const blob = await apiService.downloadDocument(documentId);\n\n      // Create download link\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading document:', error);\n      alert('Failed to download document. Please try again.');\n    } finally {\n      setDownloading(null);\n    }\n  };\n  const handlePreview = (filePath, fileName) => {\n    const fileUrl = apiService.getFileUrl(filePath);\n    window.open(fileUrl, '_blank');\n  };\n  const isImageFile = (fileName, mimeType) => {\n    var _fileName$split$pop2;\n    if (mimeType !== null && mimeType !== void 0 && mimeType.startsWith('image/')) return true;\n    const extension = (_fileName$split$pop2 = fileName.split('.').pop()) === null || _fileName$split$pop2 === void 0 ? void 0 : _fileName$split$pop2.toLowerCase();\n    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '');\n  };\n  const renderDocumentCard = (doc, type) => /*#__PURE__*/_jsxDEV(DocumentCard, {\n    children: [/*#__PURE__*/_jsxDEV(DocumentHeader, {\n      children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n        children: getFileIcon(doc.fileName, doc.mimeType)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DocumentInfo, {\n        children: [/*#__PURE__*/_jsxDEV(DocumentName, {\n          title: doc.originalFileName,\n          children: doc.originalFileName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DocumentType, {\n          children: doc.documentTypeName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DocumentMeta, {\n      children: [/*#__PURE__*/_jsxDEV(MetaRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Size:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatFileSize(doc.fileSize)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetaRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Uploaded:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatDate(doc.uploadedDate)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), doc.uploadedByName && /*#__PURE__*/_jsxDEV(MetaRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"By:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: doc.uploadedByName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), isImageFile(doc.fileName, doc.mimeType) && /*#__PURE__*/_jsxDEV(PreviewContainer, {\n      children: [/*#__PURE__*/_jsxDEV(ImagePreview, {\n        src: apiService.getFileUrl(doc.filePath),\n        alt: doc.originalFileName,\n        onClick: () => handlePreview(doc.filePath, doc.originalFileName),\n        onError: e => {\n          var _target$nextElementSi;\n          const target = e.target;\n          target.style.display = 'none';\n          (_target$nextElementSi = target.nextElementSibling) === null || _target$nextElementSi === void 0 ? void 0 : _target$nextElementSi.setAttribute('style', 'display: flex');\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(NoPreview, {\n        style: {\n          display: 'none'\n        },\n        children: \"Preview not available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(DocumentActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        size: \"sm\",\n        variant: \"outline\",\n        onClick: () => handlePreview(doc.filePath, doc.originalFileName),\n        children: \"\\uD83D\\uDC41\\uFE0F View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        size: \"sm\",\n        variant: \"secondary\",\n        onClick: () => handleDownload(doc.documentId, doc.originalFileName),\n        disabled: downloading === doc.documentId,\n        loading: downloading === doc.documentId,\n        children: downloading === doc.documentId ? 'Downloading...' : '📥 Download'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this)]\n  }, `${type}-${doc.documentId}`, true, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 5\n  }, this);\n  const renderCroppedImageCard = image => /*#__PURE__*/_jsxDEV(DocumentCard, {\n    children: [/*#__PURE__*/_jsxDEV(DocumentHeader, {\n      children: [/*#__PURE__*/_jsxDEV(DocumentIcon, {\n        children: \"\\uD83D\\uDDBC\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DocumentInfo, {\n        children: [/*#__PURE__*/_jsxDEV(DocumentName, {\n          title: image.originalFileName,\n          children: image.originalFileName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DocumentType, {\n          children: \"Cropped Image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DocumentMeta, {\n      children: [/*#__PURE__*/_jsxDEV(MetaRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Size:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatFileSize(image.fileSize)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetaRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Created:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatDate(image.createdDate)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), image.pageNumber && /*#__PURE__*/_jsxDEV(MetaRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Page:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: image.pageNumber\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this), image.createdByName && /*#__PURE__*/_jsxDEV(MetaRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"By:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: image.createdByName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PreviewContainer, {\n      children: [/*#__PURE__*/_jsxDEV(ImagePreview, {\n        src: apiService.getFileUrl(image.filePath),\n        alt: image.originalFileName,\n        onClick: () => handlePreview(image.filePath, image.originalFileName),\n        onError: e => {\n          var _target$nextElementSi2;\n          const target = e.target;\n          target.style.display = 'none';\n          (_target$nextElementSi2 = target.nextElementSibling) === null || _target$nextElementSi2 === void 0 ? void 0 : _target$nextElementSi2.setAttribute('style', 'display: flex');\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NoPreview, {\n        style: {\n          display: 'none'\n        },\n        children: \"Preview not available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DocumentActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        size: \"sm\",\n        variant: \"outline\",\n        onClick: () => handlePreview(image.filePath, image.originalFileName),\n        children: \"\\uD83D\\uDC41\\uFE0F View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        size: \"sm\",\n        variant: \"secondary\",\n        onClick: () => handleDownload(image.imageId, image.originalFileName),\n        disabled: downloading === image.imageId,\n        loading: downloading === image.imageId,\n        children: downloading === image.imageId ? 'Downloading...' : '📥 Download'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)]\n  }, `cropped-${image.imageId}`, true, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n  const hasAnyDocuments = documents.length > 0 || verificationDocuments.length > 0 || croppedImages.length > 0;\n  if (!hasAnyDocuments) {\n    return /*#__PURE__*/_jsxDEV(DocumentSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), showUploadButton && /*#__PURE__*/_jsxDEV(Button, {\n          size: \"sm\",\n          onClick: onUploadClick,\n          children: \"\\uD83D\\uDCCE Upload Documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EmptyState, {\n        children: [/*#__PURE__*/_jsxDEV(EmptyIcon, {\n          children: \"\\uD83D\\uDCC1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '18px',\n            fontWeight: 500,\n            marginBottom: '8px'\n          },\n          children: \"No documents uploaded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px'\n          },\n          children: \"Documents will appear here once they are uploaded for this lead.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DocumentsContainer, {\n    children: [documents.length > 0 && /*#__PURE__*/_jsxDEV(DocumentSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: [\"Lead Documents\", /*#__PURE__*/_jsxDEV(Badge, {\n            variant: \"info\",\n            size: \"sm\",\n            style: {\n              marginLeft: '8px'\n            },\n            children: documents.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), onRefresh && /*#__PURE__*/_jsxDEV(Button, {\n          size: \"sm\",\n          variant: \"outline\",\n          onClick: onRefresh,\n          children: \"\\uD83D\\uDD04 Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DocumentGrid, {\n        children: documents.map(doc => renderDocumentCard(doc, 'document'))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 9\n    }, this), verificationDocuments.length > 0 && /*#__PURE__*/_jsxDEV(DocumentSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        children: /*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: [\"Verification Documents\", /*#__PURE__*/_jsxDEV(Badge, {\n            variant: \"success\",\n            size: \"sm\",\n            style: {\n              marginLeft: '8px'\n            },\n            children: verificationDocuments.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DocumentGrid, {\n        children: verificationDocuments.map(doc => renderDocumentCard(doc, 'verification'))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 9\n    }, this), croppedImages.length > 0 && /*#__PURE__*/_jsxDEV(DocumentSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        children: /*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: [\"Cropped Images\", /*#__PURE__*/_jsxDEV(Badge, {\n            variant: \"secondary\",\n            size: \"sm\",\n            style: {\n              marginLeft: '8px'\n            },\n            children: croppedImages.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DocumentGrid, {\n        children: croppedImages.map(image => renderCroppedImageCard(image))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 9\n    }, this), showUploadButton && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginTop: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onUploadClick,\n        children: \"\\uD83D\\uDCCE Upload More Documents\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 416,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentViewer, \"lpgS5HToWykuNLMEOr6RvoH8LFc=\");\n_c18 = DocumentViewer;\nexport default DocumentViewer;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"DocumentsContainer\");\n$RefreshReg$(_c2, \"DocumentSection\");\n$RefreshReg$(_c3, \"SectionHeader\");\n$RefreshReg$(_c4, \"SectionTitle\");\n$RefreshReg$(_c5, \"DocumentGrid\");\n$RefreshReg$(_c6, \"DocumentCard\");\n$RefreshReg$(_c7, \"DocumentHeader\");\n$RefreshReg$(_c8, \"DocumentIcon\");\n$RefreshReg$(_c9, \"DocumentInfo\");\n$RefreshReg$(_c0, \"DocumentName\");\n$RefreshReg$(_c1, \"DocumentType\");\n$RefreshReg$(_c10, \"DocumentMeta\");\n$RefreshReg$(_c11, \"MetaRow\");\n$RefreshReg$(_c12, \"DocumentActions\");\n$RefreshReg$(_c13, \"PreviewContainer\");\n$RefreshReg$(_c14, \"ImagePreview\");\n$RefreshReg$(_c15, \"NoPreview\");\n$RefreshReg$(_c16, \"EmptyState\");\n$RefreshReg$(_c17, \"EmptyIcon\");\n$RefreshReg$(_c18, \"DocumentViewer\");", "map": {"version": 3, "names": ["React", "useState", "styled", "<PERSON><PERSON>", "Badge", "apiService", "jsxDEV", "_jsxDEV", "DocumentsContainer", "div", "props", "theme", "spacing", "lg", "_c", "DocumentSection", "colors", "border", "borderRadius", "white", "_c2", "SectionHeader", "md", "sm", "_c3", "SectionTitle", "h4", "primary", "typography", "fontSize", "fontWeight", "semibold", "_c4", "DocumentGrid", "_c5", "DocumentCard", "backgroundSecondary", "transitions", "default", "shadows", "_c6", "DocumentHeader", "_c7", "DocumentIcon", "primaryGradient", "bold", "_c8", "DocumentInfo", "_c9", "DocumentName", "medium", "textPrimary", "_c0", "DocumentType", "xs", "textTertiary", "_c1", "DocumentMeta", "_c10", "MetaRow", "_c11", "DocumentActions", "_c12", "PreviewContainer", "backgroundTertiary", "_c13", "ImagePreview", "img", "_c14", "NoPreview", "_c15", "EmptyState", "xl", "_c16", "EmptyIcon", "_c17", "DocumentViewer", "leadId", "documents", "verificationDocuments", "croppedImages", "onRefresh", "showUploadButton", "onUploadClick", "_s", "downloading", "setDownloading", "getFileIcon", "fileName", "mimeType", "_fileName$split$pop", "startsWith", "extension", "split", "pop", "toLowerCase", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "handleDownload", "documentId", "blob", "downloadDocument", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "error", "console", "alert", "handlePreview", "filePath", "fileUrl", "getFileUrl", "open", "isImageFile", "_fileName$split$pop2", "includes", "renderDocumentCard", "doc", "type", "children", "_jsxFileName", "lineNumber", "columnNumber", "title", "originalFileName", "documentTypeName", "fileSize", "uploadedDate", "uploadedByName", "src", "alt", "onClick", "onError", "e", "_target$nextElementSi", "target", "style", "display", "nextElement<PERSON><PERSON>ling", "setAttribute", "size", "variant", "disabled", "loading", "renderCroppedImageCard", "image", "createdDate", "pageNumber", "createdByName", "_target$nextElementSi2", "imageId", "hasAnyDocuments", "length", "marginBottom", "marginLeft", "map", "textAlign", "marginTop", "_c18", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Common/DocumentViewer.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '../../styles/GlobalStyles';\nimport { Document, CroppedImage, apiService } from '../../services/apiService';\n\nconst DocumentsContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.lg};\n`;\n\nconst DocumentSection = styled.div`\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  padding: ${props => props.theme.spacing.lg};\n  background: ${props => props.theme.colors.white};\n`;\n\nconst SectionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${props => props.theme.spacing.md};\n  padding-bottom: ${props => props.theme.spacing.sm};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n`;\n\nconst SectionTitle = styled.h4`\n  margin: 0;\n  color: ${props => props.theme.colors.primary};\n  font-size: ${props => props.theme.typography.fontSize.lg};\n  font-weight: ${props => props.theme.typography.fontWeight.semibold};\n`;\n\nconst DocumentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: ${props => props.theme.spacing.md};\n`;\n\nconst DocumentCard = styled.div`\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.md};\n  background: ${props => props.theme.colors.backgroundSecondary};\n  transition: ${props => props.theme.transitions.default};\n  \n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: ${props => props.theme.shadows.sm};\n  }\n`;\n\nconst DocumentHeader = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst DocumentIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: ${props => props.theme.borderRadius.md};\n  background: ${props => props.theme.colors.primaryGradient};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${props => props.theme.colors.white};\n  font-size: ${props => props.theme.typography.fontSize.lg};\n  font-weight: ${props => props.theme.typography.fontWeight.bold};\n`;\n\nconst DocumentInfo = styled.div`\n  flex: 1;\n`;\n\nconst DocumentName = styled.div`\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n  margin-bottom: 2px;\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  word-break: break-word;\n`;\n\nconst DocumentType = styled.div`\n  font-size: ${props => props.theme.typography.fontSize.xs};\n  color: ${props => props.theme.colors.textTertiary};\n  font-weight: ${props => props.theme.typography.fontWeight.medium};\n`;\n\nconst DocumentMeta = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.xs};\n  margin-bottom: ${props => props.theme.spacing.sm};\n`;\n\nconst MetaRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: ${props => props.theme.typography.fontSize.xs};\n  color: ${props => props.theme.colors.textTertiary};\n`;\n\nconst DocumentActions = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.xs};\n  flex-wrap: wrap;\n`;\n\nconst PreviewContainer = styled.div`\n  margin-top: ${props => props.theme.spacing.sm};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  overflow: hidden;\n  background: ${props => props.theme.colors.backgroundTertiary};\n`;\n\nconst ImagePreview = styled.img`\n  width: 100%;\n  height: 120px;\n  object-fit: cover;\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.default};\n  \n  &:hover {\n    opacity: 0.8;\n  }\n`;\n\nconst NoPreview = styled.div`\n  width: 100%;\n  height: 120px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${props => props.theme.colors.textTertiary};\n  font-size: ${props => props.theme.typography.fontSize.sm};\n  background: ${props => props.theme.colors.backgroundTertiary};\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: ${props => props.theme.spacing.xl};\n  color: ${props => props.theme.colors.textTertiary};\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${props => props.theme.spacing.md};\n  opacity: 0.5;\n`;\n\ninterface DocumentViewerProps {\n  leadId: number;\n  documents: Document[];\n  verificationDocuments: Document[];\n  croppedImages: CroppedImage[];\n  onRefresh?: () => void;\n  showUploadButton?: boolean;\n  onUploadClick?: () => void;\n}\n\nconst DocumentViewer: React.FC<DocumentViewerProps> = ({\n  leadId,\n  documents,\n  verificationDocuments,\n  croppedImages,\n  onRefresh,\n  showUploadButton = false,\n  onUploadClick\n}) => {\n  const [downloading, setDownloading] = useState<number | null>(null);\n\n  const getFileIcon = (fileName: string, mimeType?: string): string => {\n    if (mimeType?.startsWith('image/')) return '🖼️';\n    \n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf': return '📄';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif': return '🖼️';\n      case 'doc':\n      case 'docx': return '📝';\n      case 'xls':\n      case 'xlsx': return '📊';\n      case 'txt': return '📄';\n      default: return '📎';\n    }\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const formatDate = (dateString: string): string => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const handleDownload = async (documentId: number, fileName: string) => {\n    try {\n      setDownloading(documentId);\n      const blob = await apiService.downloadDocument(documentId);\n      \n      // Create download link\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading document:', error);\n      alert('Failed to download document. Please try again.');\n    } finally {\n      setDownloading(null);\n    }\n  };\n\n  const handlePreview = (filePath: string, fileName: string) => {\n    const fileUrl = apiService.getFileUrl(filePath);\n    window.open(fileUrl, '_blank');\n  };\n\n  const isImageFile = (fileName: string, mimeType?: string): boolean => {\n    if (mimeType?.startsWith('image/')) return true;\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '');\n  };\n\n  const renderDocumentCard = (doc: Document, type: 'document' | 'verification') => (\n    <DocumentCard key={`${type}-${doc.documentId}`}>\n      <DocumentHeader>\n        <DocumentIcon>\n          {getFileIcon(doc.fileName, doc.mimeType)}\n        </DocumentIcon>\n        <DocumentInfo>\n          <DocumentName title={doc.originalFileName}>\n            {doc.originalFileName}\n          </DocumentName>\n          <DocumentType>{doc.documentTypeName}</DocumentType>\n        </DocumentInfo>\n      </DocumentHeader>\n\n      <DocumentMeta>\n        <MetaRow>\n          <span>Size:</span>\n          <span>{formatFileSize(doc.fileSize)}</span>\n        </MetaRow>\n        <MetaRow>\n          <span>Uploaded:</span>\n          <span>{formatDate(doc.uploadedDate)}</span>\n        </MetaRow>\n        {doc.uploadedByName && (\n          <MetaRow>\n            <span>By:</span>\n            <span>{doc.uploadedByName}</span>\n          </MetaRow>\n        )}\n      </DocumentMeta>\n\n      {isImageFile(doc.fileName, doc.mimeType) && (\n        <PreviewContainer>\n          <ImagePreview\n            src={apiService.getFileUrl(doc.filePath)}\n            alt={doc.originalFileName}\n            onClick={() => handlePreview(doc.filePath, doc.originalFileName)}\n            onError={(e) => {\n              const target = e.target as HTMLImageElement;\n              target.style.display = 'none';\n              target.nextElementSibling?.setAttribute('style', 'display: flex');\n            }}\n          />\n          <NoPreview style={{ display: 'none' }}>\n            Preview not available\n          </NoPreview>\n        </PreviewContainer>\n      )}\n\n      <DocumentActions>\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          onClick={() => handlePreview(doc.filePath, doc.originalFileName)}\n        >\n          👁️ View\n        </Button>\n        <Button\n          size=\"sm\"\n          variant=\"secondary\"\n          onClick={() => handleDownload(doc.documentId, doc.originalFileName)}\n          disabled={downloading === doc.documentId}\n          loading={downloading === doc.documentId}\n        >\n          {downloading === doc.documentId ? 'Downloading...' : '📥 Download'}\n        </Button>\n      </DocumentActions>\n    </DocumentCard>\n  );\n\n  const renderCroppedImageCard = (image: CroppedImage) => (\n    <DocumentCard key={`cropped-${image.imageId}`}>\n      <DocumentHeader>\n        <DocumentIcon>🖼️</DocumentIcon>\n        <DocumentInfo>\n          <DocumentName title={image.originalFileName}>\n            {image.originalFileName}\n          </DocumentName>\n          <DocumentType>Cropped Image</DocumentType>\n        </DocumentInfo>\n      </DocumentHeader>\n\n      <DocumentMeta>\n        <MetaRow>\n          <span>Size:</span>\n          <span>{formatFileSize(image.fileSize)}</span>\n        </MetaRow>\n        <MetaRow>\n          <span>Created:</span>\n          <span>{formatDate(image.createdDate)}</span>\n        </MetaRow>\n        {image.pageNumber && (\n          <MetaRow>\n            <span>Page:</span>\n            <span>{image.pageNumber}</span>\n          </MetaRow>\n        )}\n        {image.createdByName && (\n          <MetaRow>\n            <span>By:</span>\n            <span>{image.createdByName}</span>\n          </MetaRow>\n        )}\n      </DocumentMeta>\n\n      <PreviewContainer>\n        <ImagePreview\n          src={apiService.getFileUrl(image.filePath)}\n          alt={image.originalFileName}\n          onClick={() => handlePreview(image.filePath, image.originalFileName)}\n          onError={(e) => {\n            const target = e.target as HTMLImageElement;\n            target.style.display = 'none';\n            target.nextElementSibling?.setAttribute('style', 'display: flex');\n          }}\n        />\n        <NoPreview style={{ display: 'none' }}>\n          Preview not available\n        </NoPreview>\n      </PreviewContainer>\n\n      <DocumentActions>\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          onClick={() => handlePreview(image.filePath, image.originalFileName)}\n        >\n          👁️ View\n        </Button>\n        <Button\n          size=\"sm\"\n          variant=\"secondary\"\n          onClick={() => handleDownload(image.imageId, image.originalFileName)}\n          disabled={downloading === image.imageId}\n          loading={downloading === image.imageId}\n        >\n          {downloading === image.imageId ? 'Downloading...' : '📥 Download'}\n        </Button>\n      </DocumentActions>\n    </DocumentCard>\n  );\n\n  const hasAnyDocuments = documents.length > 0 || verificationDocuments.length > 0 || croppedImages.length > 0;\n\n  if (!hasAnyDocuments) {\n    return (\n      <DocumentSection>\n        <SectionHeader>\n          <SectionTitle>Documents</SectionTitle>\n          {showUploadButton && (\n            <Button size=\"sm\" onClick={onUploadClick}>\n              📎 Upload Documents\n            </Button>\n          )}\n        </SectionHeader>\n        <EmptyState>\n          <EmptyIcon>📁</EmptyIcon>\n          <div style={{ fontSize: '18px', fontWeight: 500, marginBottom: '8px' }}>\n            No documents uploaded\n          </div>\n          <div style={{ fontSize: '14px' }}>\n            Documents will appear here once they are uploaded for this lead.\n          </div>\n        </EmptyState>\n      </DocumentSection>\n    );\n  }\n\n  return (\n    <DocumentsContainer>\n      {/* Lead Documents */}\n      {documents.length > 0 && (\n        <DocumentSection>\n          <SectionHeader>\n            <SectionTitle>\n              Lead Documents\n              <Badge variant=\"info\" size=\"sm\" style={{ marginLeft: '8px' }}>\n                {documents.length}\n              </Badge>\n            </SectionTitle>\n            {onRefresh && (\n              <Button size=\"sm\" variant=\"outline\" onClick={onRefresh}>\n                🔄 Refresh\n              </Button>\n            )}\n          </SectionHeader>\n          <DocumentGrid>\n            {documents.map(doc => renderDocumentCard(doc, 'document'))}\n          </DocumentGrid>\n        </DocumentSection>\n      )}\n\n      {/* Verification Documents */}\n      {verificationDocuments.length > 0 && (\n        <DocumentSection>\n          <SectionHeader>\n            <SectionTitle>\n              Verification Documents\n              <Badge variant=\"success\" size=\"sm\" style={{ marginLeft: '8px' }}>\n                {verificationDocuments.length}\n              </Badge>\n            </SectionTitle>\n          </SectionHeader>\n          <DocumentGrid>\n            {verificationDocuments.map(doc => renderDocumentCard(doc, 'verification'))}\n          </DocumentGrid>\n        </DocumentSection>\n      )}\n\n      {/* Cropped Images */}\n      {croppedImages.length > 0 && (\n        <DocumentSection>\n          <SectionHeader>\n            <SectionTitle>\n              Cropped Images\n              <Badge variant=\"secondary\" size=\"sm\" style={{ marginLeft: '8px' }}>\n                {croppedImages.length}\n              </Badge>\n            </SectionTitle>\n          </SectionHeader>\n          <DocumentGrid>\n            {croppedImages.map(image => renderCroppedImageCard(image))}\n          </DocumentGrid>\n        </DocumentSection>\n      )}\n\n      {showUploadButton && (\n        <div style={{ textAlign: 'center', marginTop: '16px' }}>\n          <Button onClick={onUploadClick}>\n            📎 Upload More Documents\n          </Button>\n        </div>\n      )}\n    </DocumentsContainer>\n  );\n};\n\nexport default DocumentViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAkBC,KAAK,QAAQ,2BAA2B;AACzE,SAAiCC,UAAU,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,kBAAkB,GAAGN,MAAM,CAACO,GAAG;AACrC;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AACxC,CAAC;AAACC,EAAA,GAJIN,kBAAkB;AAMxB,MAAMO,eAAe,GAAGb,MAAM,CAACO,GAAG;AAClC,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACC,MAAM;AACxD,mBAAmBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,YAAY,CAACL,EAAE;AACvD,aAAaH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C,gBAAgBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACG,KAAK;AACjD,CAAC;AAACC,GAAA,GALIL,eAAe;AAOrB,MAAMM,aAAa,GAAGnB,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA,mBAAmBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE;AAClD,oBAAoBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACW,EAAE;AACnD,6BAA6Bb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACC,MAAM;AAC/D,CAAC;AAACO,GAAA,GAPIH,aAAa;AASnB,MAAMI,YAAY,GAAGvB,MAAM,CAACwB,EAAE;AAC9B;AACA,WAAWhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACW,OAAO;AAC9C,eAAejB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAAChB,EAAE;AAC1D,iBAAiBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACE,UAAU,CAACC,QAAQ;AACpE,CAAC;AAACC,GAAA,GALIP,YAAY;AAOlB,MAAMQ,YAAY,GAAG/B,MAAM,CAACO,GAAG;AAC/B;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE;AACxC,CAAC;AAACY,GAAA,GAJID,YAAY;AAMlB,MAAME,YAAY,GAAGjC,MAAM,CAACO,GAAG;AAC/B,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACC,MAAM;AACxD,mBAAmBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,YAAY,CAACI,EAAE;AACvD,aAAaZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE;AAC5C,gBAAgBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACoB,mBAAmB;AAC/D,gBAAgB1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC0B,WAAW,CAACC,OAAO;AACxD;AACA;AACA,oBAAoB5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACW,OAAO;AACvD,kBAAkBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC4B,OAAO,CAAChB,EAAE;AACjD;AACA,CAAC;AAACiB,GAAA,GAXIL,YAAY;AAalB,MAAMM,cAAc,GAAGvC,MAAM,CAACO,GAAG;AACjC;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACW,EAAE;AACxC,mBAAmBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACW,EAAE;AAClD,CAAC;AAACmB,GAAA,GALID,cAAc;AAOpB,MAAME,YAAY,GAAGzC,MAAM,CAACO,GAAG;AAC/B;AACA;AACA,mBAAmBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,YAAY,CAACI,EAAE;AACvD,gBAAgBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAAC4B,eAAe;AAC3D;AACA;AACA;AACA,WAAWlC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACG,KAAK;AAC5C,eAAeT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAAChB,EAAE;AAC1D,iBAAiBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACE,UAAU,CAACe,IAAI;AAChE,CAAC;AAACC,GAAA,GAXIH,YAAY;AAalB,MAAMI,YAAY,GAAG7C,MAAM,CAACO,GAAG;AAC/B;AACA,CAAC;AAACuC,GAAA,GAFID,YAAY;AAIlB,MAAME,YAAY,GAAG/C,MAAM,CAACO,GAAG;AAC/B,iBAAiBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACE,UAAU,CAACoB,MAAM;AAClE,WAAWxC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACmC,WAAW;AAClD;AACA,eAAezC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACN,EAAE;AAC1D;AACA,CAAC;AAAC6B,GAAA,GANIH,YAAY;AAQlB,MAAMI,YAAY,GAAGnD,MAAM,CAACO,GAAG;AAC/B,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACyB,EAAE;AAC1D,WAAW5C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACuC,YAAY;AACnD,iBAAiB7C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACE,UAAU,CAACoB,MAAM;AAClE,CAAC;AAACM,GAAA,GAJIH,YAAY;AAMlB,MAAMI,YAAY,GAAGvD,MAAM,CAACO,GAAG;AAC/B;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC0C,EAAE;AACxC,mBAAmB5C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACW,EAAE;AAClD,CAAC;AAACmC,IAAA,GALID,YAAY;AAOlB,MAAME,OAAO,GAAGzD,MAAM,CAACO,GAAG;AAC1B;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACyB,EAAE;AAC1D,WAAW5C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACuC,YAAY;AACnD,CAAC;AAACK,IAAA,GANID,OAAO;AAQb,MAAME,eAAe,GAAG3D,MAAM,CAACO,GAAG;AAClC;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC0C,EAAE;AACxC;AACA,CAAC;AAACQ,IAAA,GAJID,eAAe;AAMrB,MAAME,gBAAgB,GAAG7D,MAAM,CAACO,GAAG;AACnC,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACW,EAAE;AAC/C,sBAAsBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACC,MAAM;AACxD,mBAAmBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,YAAY,CAACK,EAAE;AACvD;AACA,gBAAgBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACgD,kBAAkB;AAC9D,CAAC;AAACC,IAAA,GANIF,gBAAgB;AAQtB,MAAMG,YAAY,GAAGhE,MAAM,CAACiE,GAAG;AAC/B;AACA;AACA;AACA;AACA,gBAAgBzD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC0B,WAAW,CAACC,OAAO;AACxD;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GAVIF,YAAY;AAYlB,MAAMG,SAAS,GAAGnE,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACuC,YAAY;AACnD,eAAe7C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACN,EAAE;AAC1D,gBAAgBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACgD,kBAAkB;AAC9D,CAAC;AAACM,IAAA,GATID,SAAS;AAWf,MAAME,UAAU,GAAGrE,MAAM,CAACO,GAAG;AAC7B;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC4D,EAAE;AAC5C,WAAW9D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACuC,YAAY;AACnD,CAAC;AAACkB,IAAA,GAJIF,UAAU;AAMhB,MAAMG,SAAS,GAAGxE,MAAM,CAACO,GAAG;AAC5B;AACA,mBAAmBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE;AAClD;AACA,CAAC;AAACqD,IAAA,GAJID,SAAS;AAgBf,MAAME,cAA6C,GAAGA,CAAC;EACrDC,MAAM;EACNC,SAAS;EACTC,qBAAqB;EACrBC,aAAa;EACbC,SAAS;EACTC,gBAAgB,GAAG,KAAK;EACxBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAgB,IAAI,CAAC;EAEnE,MAAMsF,WAAW,GAAGA,CAACC,QAAgB,EAAEC,QAAiB,KAAa;IAAA,IAAAC,mBAAA;IACnE,IAAID,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,KAAK;IAEhD,MAAMC,SAAS,IAAAF,mBAAA,GAAGF,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAJ,mBAAA,uBAAzBA,mBAAA,CAA2BK,WAAW,CAAC,CAAC;IAC1D,QAAQH,SAAS;MACf,KAAK,KAAK;QAAE,OAAO,IAAI;MACvB,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;QAAE,OAAO,KAAK;MACxB,KAAK,KAAK;MACV,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,KAAK;MACV,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,KAAK;QAAE,OAAO,IAAI;MACvB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMI,cAAc,GAAIC,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAkB,IAAa;IACjD,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAOC,UAAkB,EAAE7B,QAAgB,KAAK;IACrE,IAAI;MACFF,cAAc,CAAC+B,UAAU,CAAC;MAC1B,MAAMC,IAAI,GAAG,MAAMjH,UAAU,CAACkH,gBAAgB,CAACF,UAAU,CAAC;;MAE1D;MACA,MAAMG,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAGxC,QAAQ;MACxBqC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDE,KAAK,CAAC,gDAAgD,CAAC;IACzD,CAAC,SAAS;MACRlD,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC;EAED,MAAMmD,aAAa,GAAGA,CAACC,QAAgB,EAAElD,QAAgB,KAAK;IAC5D,MAAMmD,OAAO,GAAGtI,UAAU,CAACuI,UAAU,CAACF,QAAQ,CAAC;IAC/CjB,MAAM,CAACoB,IAAI,CAACF,OAAO,EAAE,QAAQ,CAAC;EAChC,CAAC;EAED,MAAMG,WAAW,GAAGA,CAACtD,QAAgB,EAAEC,QAAiB,KAAc;IAAA,IAAAsD,oBAAA;IACpE,IAAItD,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEE,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC/C,MAAMC,SAAS,IAAAmD,oBAAA,GAAGvD,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAiD,oBAAA,uBAAzBA,oBAAA,CAA2BhD,WAAW,CAAC,CAAC;IAC1D,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAACiD,QAAQ,CAACpD,SAAS,IAAI,EAAE,CAAC;EAC/E,CAAC;EAED,MAAMqD,kBAAkB,GAAGA,CAACC,GAAa,EAAEC,IAAiC,kBAC1E5I,OAAA,CAAC4B,YAAY;IAAAiH,QAAA,gBACX7I,OAAA,CAACkC,cAAc;MAAA2G,QAAA,gBACb7I,OAAA,CAACoC,YAAY;QAAAyG,QAAA,EACV7D,WAAW,CAAC2D,GAAG,CAAC1D,QAAQ,EAAE0D,GAAG,CAACzD,QAAQ;MAAC;QAAAD,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACfhJ,OAAA,CAACwC,YAAY;QAAAqG,QAAA,gBACX7I,OAAA,CAAC0C,YAAY;UAACuG,KAAK,EAAEN,GAAG,CAACO,gBAAiB;UAAAL,QAAA,EACvCF,GAAG,CAACO;QAAgB;UAAAjE,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACfhJ,OAAA,CAAC8C,YAAY;UAAA+F,QAAA,EAAEF,GAAG,CAACQ;QAAgB;UAAAlE,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEjBhJ,OAAA,CAACkD,YAAY;MAAA2F,QAAA,gBACX7I,OAAA,CAACoD,OAAO;QAAAyF,QAAA,gBACN7I,OAAA;UAAA6I,QAAA,EAAM;QAAK;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBhJ,OAAA;UAAA6I,QAAA,EAAOpD,cAAc,CAACkD,GAAG,CAACS,QAAQ;QAAC;UAAAnE,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACVhJ,OAAA,CAACoD,OAAO;QAAAyF,QAAA,gBACN7I,OAAA;UAAA6I,QAAA,EAAM;QAAS;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtBhJ,OAAA;UAAA6I,QAAA,EAAOzC,UAAU,CAACuC,GAAG,CAACU,YAAY;QAAC;UAAApE,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EACTL,GAAG,CAACW,cAAc,iBACjBtJ,OAAA,CAACoD,OAAO;QAAAyF,QAAA,gBACN7I,OAAA;UAAA6I,QAAA,EAAM;QAAG;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChBhJ,OAAA;UAAA6I,QAAA,EAAOF,GAAG,CAACW;QAAc;UAAArE,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACV;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,EAEdT,WAAW,CAACI,GAAG,CAAC1D,QAAQ,EAAE0D,GAAG,CAACzD,QAAQ,CAAC,iBACtClF,OAAA,CAACwD,gBAAgB;MAAAqF,QAAA,gBACf7I,OAAA,CAAC2D,YAAY;QACX4F,GAAG,EAAEzJ,UAAU,CAACuI,UAAU,CAACM,GAAG,CAACR,QAAQ,CAAE;QACzCqB,GAAG,EAAEb,GAAG,CAACO,gBAAiB;QAC1BO,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAACS,GAAG,CAACR,QAAQ,EAAEQ,GAAG,CAACO,gBAAgB,CAAE;QACjEQ,OAAO,EAAGC,CAAC,IAAK;UAAA,IAAAC,qBAAA;UACd,MAAMC,MAAM,GAAGF,CAAC,CAACE,MAA0B;UAC3CA,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7B,CAAAH,qBAAA,GAAAC,MAAM,CAACG,kBAAkB,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BK,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACnE;MAAE;QAAAhF,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFhJ,OAAA,CAAC8D,SAAS;QAACgG,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAlB,QAAA,EAAC;MAEvC;QAAA5D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACnB,eAEDhJ,OAAA,CAACsD,eAAe;MAAAuF,QAAA,gBACd7I,OAAA,CAACJ,MAAM;QACLsK,IAAI,EAAC,IAAI;QACTC,OAAO,EAAC,SAAS;QACjBV,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAACS,GAAG,CAACR,QAAQ,EAAEQ,GAAG,CAACO,gBAAgB,CAAE;QAAAL,QAAA,EAClE;MAED;QAAA5D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThJ,OAAA,CAACJ,MAAM;QACLsK,IAAI,EAAC,IAAI;QACTC,OAAO,EAAC,WAAW;QACnBV,OAAO,EAAEA,CAAA,KAAM5C,cAAc,CAAC8B,GAAG,CAAC7B,UAAU,EAAE6B,GAAG,CAACO,gBAAgB,CAAE;QACpEkB,QAAQ,EAAEtF,WAAW,KAAK6D,GAAG,CAAC7B,UAAW;QACzCuD,OAAO,EAAEvF,WAAW,KAAK6D,GAAG,CAAC7B,UAAW;QAAA+B,QAAA,EAEvC/D,WAAW,KAAK6D,GAAG,CAAC7B,UAAU,GAAG,gBAAgB,GAAG;MAAa;QAAA7B,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA,GAjED,GAAGJ,IAAI,IAAID,GAAG,CAAC7B,UAAU,EAAE;IAAA7B,QAAA,EAAA6D,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAkEhC,CACf;EAED,MAAMsB,sBAAsB,GAAIC,KAAmB,iBACjDvK,OAAA,CAAC4B,YAAY;IAAAiH,QAAA,gBACX7I,OAAA,CAACkC,cAAc;MAAA2G,QAAA,gBACb7I,OAAA,CAACoC,YAAY;QAAAyG,QAAA,EAAC;MAAG;QAAA5D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAChChJ,OAAA,CAACwC,YAAY;QAAAqG,QAAA,gBACX7I,OAAA,CAAC0C,YAAY;UAACuG,KAAK,EAAEsB,KAAK,CAACrB,gBAAiB;UAAAL,QAAA,EACzC0B,KAAK,CAACrB;QAAgB;UAAAjE,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACfhJ,OAAA,CAAC8C,YAAY;UAAA+F,QAAA,EAAC;QAAa;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEjBhJ,OAAA,CAACkD,YAAY;MAAA2F,QAAA,gBACX7I,OAAA,CAACoD,OAAO;QAAAyF,QAAA,gBACN7I,OAAA;UAAA6I,QAAA,EAAM;QAAK;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBhJ,OAAA;UAAA6I,QAAA,EAAOpD,cAAc,CAAC8E,KAAK,CAACnB,QAAQ;QAAC;UAAAnE,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACVhJ,OAAA,CAACoD,OAAO;QAAAyF,QAAA,gBACN7I,OAAA;UAAA6I,QAAA,EAAM;QAAQ;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrBhJ,OAAA;UAAA6I,QAAA,EAAOzC,UAAU,CAACmE,KAAK,CAACC,WAAW;QAAC;UAAAvF,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,EACTuB,KAAK,CAACE,UAAU,iBACfzK,OAAA,CAACoD,OAAO;QAAAyF,QAAA,gBACN7I,OAAA;UAAA6I,QAAA,EAAM;QAAK;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBhJ,OAAA;UAAA6I,QAAA,EAAO0B,KAAK,CAACE;QAAU;UAAAxF,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACV,EACAuB,KAAK,CAACG,aAAa,iBAClB1K,OAAA,CAACoD,OAAO;QAAAyF,QAAA,gBACN7I,OAAA;UAAA6I,QAAA,EAAM;QAAG;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChBhJ,OAAA;UAAA6I,QAAA,EAAO0B,KAAK,CAACG;QAAa;UAAAzF,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACV;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,eAEfhJ,OAAA,CAACwD,gBAAgB;MAAAqF,QAAA,gBACf7I,OAAA,CAAC2D,YAAY;QACX4F,GAAG,EAAEzJ,UAAU,CAACuI,UAAU,CAACkC,KAAK,CAACpC,QAAQ,CAAE;QAC3CqB,GAAG,EAAEe,KAAK,CAACrB,gBAAiB;QAC5BO,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAACqC,KAAK,CAACpC,QAAQ,EAAEoC,KAAK,CAACrB,gBAAgB,CAAE;QACrEQ,OAAO,EAAGC,CAAC,IAAK;UAAA,IAAAgB,sBAAA;UACd,MAAMd,MAAM,GAAGF,CAAC,CAACE,MAA0B;UAC3CA,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7B,CAAAY,sBAAA,GAAAd,MAAM,CAACG,kBAAkB,cAAAW,sBAAA,uBAAzBA,sBAAA,CAA2BV,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACnE;MAAE;QAAAhF,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFhJ,OAAA,CAAC8D,SAAS;QAACgG,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAlB,QAAA,EAAC;MAEvC;QAAA5D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEnBhJ,OAAA,CAACsD,eAAe;MAAAuF,QAAA,gBACd7I,OAAA,CAACJ,MAAM;QACLsK,IAAI,EAAC,IAAI;QACTC,OAAO,EAAC,SAAS;QACjBV,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAACqC,KAAK,CAACpC,QAAQ,EAAEoC,KAAK,CAACrB,gBAAgB,CAAE;QAAAL,QAAA,EACtE;MAED;QAAA5D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThJ,OAAA,CAACJ,MAAM;QACLsK,IAAI,EAAC,IAAI;QACTC,OAAO,EAAC,WAAW;QACnBV,OAAO,EAAEA,CAAA,KAAM5C,cAAc,CAAC0D,KAAK,CAACK,OAAO,EAAEL,KAAK,CAACrB,gBAAgB,CAAE;QACrEkB,QAAQ,EAAEtF,WAAW,KAAKyF,KAAK,CAACK,OAAQ;QACxCP,OAAO,EAAEvF,WAAW,KAAKyF,KAAK,CAACK,OAAQ;QAAA/B,QAAA,EAEtC/D,WAAW,KAAKyF,KAAK,CAACK,OAAO,GAAG,gBAAgB,GAAG;MAAa;QAAA3F,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA,GAnED,WAAWuB,KAAK,CAACK,OAAO,EAAE;IAAA3F,QAAA,EAAA6D,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAoE/B,CACf;EAED,MAAM6B,eAAe,GAAGtG,SAAS,CAACuG,MAAM,GAAG,CAAC,IAAItG,qBAAqB,CAACsG,MAAM,GAAG,CAAC,IAAIrG,aAAa,CAACqG,MAAM,GAAG,CAAC;EAE5G,IAAI,CAACD,eAAe,EAAE;IACpB,oBACE7K,OAAA,CAACQ,eAAe;MAAAqI,QAAA,gBACd7I,OAAA,CAACc,aAAa;QAAA+H,QAAA,gBACZ7I,OAAA,CAACkB,YAAY;UAAA2H,QAAA,EAAC;QAAS;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,EACrCrE,gBAAgB,iBACf3E,OAAA,CAACJ,MAAM;UAACsK,IAAI,EAAC,IAAI;UAACT,OAAO,EAAE7E,aAAc;UAAAiE,QAAA,EAAC;QAE1C;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBhJ,OAAA,CAACgE,UAAU;QAAA6E,QAAA,gBACT7I,OAAA,CAACmE,SAAS;UAAA0E,QAAA,EAAC;QAAE;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACzBhJ,OAAA;UAAK8J,KAAK,EAAE;YAAExI,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,GAAG;YAAEwJ,YAAY,EAAE;UAAM,CAAE;UAAAlC,QAAA,EAAC;QAExE;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhJ,OAAA;UAAK8J,KAAK,EAAE;YAAExI,QAAQ,EAAE;UAAO,CAAE;UAAAuH,QAAA,EAAC;QAElC;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEtB;EAEA,oBACEhJ,OAAA,CAACC,kBAAkB;IAAA4I,QAAA,GAEhBtE,SAAS,CAACuG,MAAM,GAAG,CAAC,iBACnB9K,OAAA,CAACQ,eAAe;MAAAqI,QAAA,gBACd7I,OAAA,CAACc,aAAa;QAAA+H,QAAA,gBACZ7I,OAAA,CAACkB,YAAY;UAAA2H,QAAA,GAAC,gBAEZ,eAAA7I,OAAA,CAACH,KAAK;YAACsK,OAAO,EAAC,MAAM;YAACD,IAAI,EAAC,IAAI;YAACJ,KAAK,EAAE;cAAEkB,UAAU,EAAE;YAAM,CAAE;YAAAnC,QAAA,EAC1DtE,SAAS,CAACuG;UAAM;YAAA7F,QAAA,EAAA6D,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAA/D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EACdtE,SAAS,iBACR1E,OAAA,CAACJ,MAAM;UAACsK,IAAI,EAAC,IAAI;UAACC,OAAO,EAAC,SAAS;UAACV,OAAO,EAAE/E,SAAU;UAAAmE,QAAA,EAAC;QAExD;UAAA5D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBhJ,OAAA,CAAC0B,YAAY;QAAAmH,QAAA,EACVtE,SAAS,CAAC0G,GAAG,CAACtC,GAAG,IAAID,kBAAkB,CAACC,GAAG,EAAE,UAAU,CAAC;MAAC;QAAA1D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAClB,EAGAxE,qBAAqB,CAACsG,MAAM,GAAG,CAAC,iBAC/B9K,OAAA,CAACQ,eAAe;MAAAqI,QAAA,gBACd7I,OAAA,CAACc,aAAa;QAAA+H,QAAA,eACZ7I,OAAA,CAACkB,YAAY;UAAA2H,QAAA,GAAC,wBAEZ,eAAA7I,OAAA,CAACH,KAAK;YAACsK,OAAO,EAAC,SAAS;YAACD,IAAI,EAAC,IAAI;YAACJ,KAAK,EAAE;cAAEkB,UAAU,EAAE;YAAM,CAAE;YAAAnC,QAAA,EAC7DrE,qBAAqB,CAACsG;UAAM;YAAA7F,QAAA,EAAA6D,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAA/D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAChBhJ,OAAA,CAAC0B,YAAY;QAAAmH,QAAA,EACVrE,qBAAqB,CAACyG,GAAG,CAACtC,GAAG,IAAID,kBAAkB,CAACC,GAAG,EAAE,cAAc,CAAC;MAAC;QAAA1D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAClB,EAGAvE,aAAa,CAACqG,MAAM,GAAG,CAAC,iBACvB9K,OAAA,CAACQ,eAAe;MAAAqI,QAAA,gBACd7I,OAAA,CAACc,aAAa;QAAA+H,QAAA,eACZ7I,OAAA,CAACkB,YAAY;UAAA2H,QAAA,GAAC,gBAEZ,eAAA7I,OAAA,CAACH,KAAK;YAACsK,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,IAAI;YAACJ,KAAK,EAAE;cAAEkB,UAAU,EAAE;YAAM,CAAE;YAAAnC,QAAA,EAC/DpE,aAAa,CAACqG;UAAM;YAAA7F,QAAA,EAAA6D,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAA/D,QAAA,EAAA6D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAA/D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAChBhJ,OAAA,CAAC0B,YAAY;QAAAmH,QAAA,EACVpE,aAAa,CAACwG,GAAG,CAACV,KAAK,IAAID,sBAAsB,CAACC,KAAK,CAAC;MAAC;QAAAtF,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAClB,EAEArE,gBAAgB,iBACf3E,OAAA;MAAK8J,KAAK,EAAE;QAAEoB,SAAS,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAtC,QAAA,eACrD7I,OAAA,CAACJ,MAAM;QAAC6J,OAAO,EAAE7E,aAAc;QAAAiE,QAAA,EAAC;MAEhC;QAAA5D,QAAA,EAAA6D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAA/D,QAAA,EAAA6D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAA/D,QAAA,EAAA6D,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB,CAAC;AAEzB,CAAC;AAACnE,EAAA,CA5TIR,cAA6C;AAAA+G,IAAA,GAA7C/G,cAA6C;AA8TnD,eAAeA,cAAc;AAAC,IAAA9D,EAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAgH,IAAA;AAAAC,YAAA,CAAA9K,EAAA;AAAA8K,YAAA,CAAAxK,GAAA;AAAAwK,YAAA,CAAApK,GAAA;AAAAoK,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAAhI,IAAA;AAAAgI,YAAA,CAAA9H,IAAA;AAAA8H,YAAA,CAAA3H,IAAA;AAAA2H,YAAA,CAAAxH,IAAA;AAAAwH,YAAA,CAAAtH,IAAA;AAAAsH,YAAA,CAAAnH,IAAA;AAAAmH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}