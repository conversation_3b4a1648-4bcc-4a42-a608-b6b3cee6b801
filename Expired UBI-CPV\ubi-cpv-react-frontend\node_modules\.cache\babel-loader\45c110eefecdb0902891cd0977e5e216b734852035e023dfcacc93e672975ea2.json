{"ast": null, "code": "import _objectSpread from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{<PERSON>,<PERSON><PERSON>,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const FilterContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background: white;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const SearchInput=styled.input(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  min-width: 250px;\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const TableContainer=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  overflow-x: auto;\\n\"])));const Table=styled.table(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const StatusBadge=styled.span(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.status){case'pending-review':return\"\\n          background-color: #f3e5f5;\\n          color: #4a148c;\\n        \";case'approved':return\"\\n          background-color: #e8f5e9;\\n          color: #2e7d32;\\n        \";case'rejected':return\"\\n          background-color: #ffebee;\\n          color: #c62828;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const PriorityBadge=styled.span(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.priority){case'high':return\"\\n          background-color: #ffebee;\\n          color: #c62828;\\n        \";case'medium':return\"\\n          background-color: #fff8e1;\\n          color: #ff8f00;\\n        \";case'low':return\"\\n          background-color: #e8f5e9;\\n          color: #2e7d32;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const ActionButtons=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 8px;\\n\"])));const ReviewModal=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  display: \",\";\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 1000;\\n\"])),props=>props.isOpen?'flex':'none');const ModalContent=styled.div(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  background: white;\\n  padding: 30px;\\n  border-radius: 8px;\\n  width: 90%;\\n  max-width: 500px;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n\"])));const ModalTitle=styled.h3(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n  color: #007E3A;\\n\"])));const TextArea=styled.textarea(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  min-height: 100px;\\n  padding: 10px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  resize: vertical;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const ModalActions=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 10px;\\n  justify-content: flex-end;\\n  margin-top: 20px;\\n\"])));const SupervisorReview=()=>{const[reviewTasks,setReviewTasks]=useState([]);const[loading,setLoading]=useState(true);const[statusFilter,setStatusFilter]=useState('pending-review');const[searchTerm,setSearchTerm]=useState('');const[selectedLead,setSelectedLead]=useState(null);const[reviewAction,setReviewAction]=useState(null);const[reviewComments,setReviewComments]=useState('');const[rejectionReason,setRejectionReason]=useState('');const navigate=useNavigate();useEffect(()=>{loadReviewTasks();},[statusFilter]);const loadReviewTasks=async()=>{try{setLoading(true);const response=await apiService.getLeads(1,100,statusFilter==='all'?undefined:statusFilter);setReviewTasks(response.data||[]);}catch(error){console.error('Error loading review tasks:',error);// Mock data for demo\nsetReviewTasks([{leadId:5,customerName:'Charlie Brown',mobileNumber:'9876543214',loanType:'Home Loan',status:'pending-review',createdDate:'2024-01-12T09:00:00Z',assignedDate:'2024-01-12T10:00:00Z',submittedDate:'2024-01-15T17:30:00Z',createdByName:'Admin User',assignedToName:'Agent Smith',documentCount:4,croppedImageCount:3},{leadId:6,customerName:'Diana Prince',mobileNumber:'9876543215',loanType:'Car Loan',status:'pending-review',createdDate:'2024-01-13T11:15:00Z',assignedDate:'2024-01-13T12:00:00Z',submittedDate:'2024-01-16T09:45:00Z',createdByName:'Admin User',assignedToName:'Agent Johnson',documentCount:3,croppedImageCount:2}]);}finally{setLoading(false);}};const filteredTasks=reviewTasks.filter(task=>task.customerName.toLowerCase().includes(searchTerm.toLowerCase())||task.mobileNumber.includes(searchTerm)||task.loanType.toLowerCase().includes(searchTerm.toLowerCase())||task.assignedToName&&task.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()));const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>navigate('/supervisor/dashboard')},{icon:'👁️',label:'Review Queue',active:true},{icon:'📊',label:'Reports',onClick:()=>navigate('/supervisor/reports')},{icon:'👥',label:'Team',onClick:()=>navigate('/supervisor/team')}];const handleReviewAction=(lead,action)=>{setSelectedLead(lead);setReviewAction(action);setReviewComments('');setRejectionReason('');};const submitReview=async()=>{if(!selectedLead||!reviewAction)return;try{const newStatus=reviewAction==='approve'?'approved':'rejected';await apiService.updateLeadStatus(selectedLead.leadId,newStatus,reviewComments,reviewAction==='reject'?rejectionReason:undefined);// Update local state\nsetReviewTasks(tasks=>tasks.map(task=>task.leadId===selectedLead.leadId?_objectSpread(_objectSpread({},task),{},{status:newStatus}):task));// Close modal\nsetSelectedLead(null);setReviewAction(null);// Reload if filtering by status\nif(statusFilter!=='all'){loadReviewTasks();}}catch(error){console.error('Error submitting review:',error);alert('Failed to submit review');}};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};const getPriority=submittedDate=>{const daysDiff=Math.floor((Date.now()-new Date(submittedDate).getTime())/(1000*60*60*24));if(daysDiff>2)return'high';if(daysDiff>1)return'medium';return'low';};const calculateWaitTime=submittedDate=>{const daysDiff=Math.floor((Date.now()-new Date(submittedDate).getTime())/(1000*60*60*24));return\"\".concat(daysDiff,\" day\").concat(daysDiff!==1?'s':'');};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Review Queue\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsxs(DashboardLayout,{title:\"Review Queue\",navigationItems:navigationItems,children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Pending Reviews\"}),/*#__PURE__*/_jsxs(FilterContainer,{children:[/*#__PURE__*/_jsxs(FilterSelect,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"pending-review\",children:\"Pending Review\"}),/*#__PURE__*/_jsx(\"option\",{value:\"approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:\"Rejected\"}),/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Reviews\"})]}),/*#__PURE__*/_jsx(SearchInput,{type:\"text\",placeholder:\"Search by customer, mobile, loan type, or agent...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Customer\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Mobile\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Loan Type\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Agent\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Status\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Priority\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Submitted\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Wait Time\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredTasks.map(task=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:task.customerName}),/*#__PURE__*/_jsx(TableCell,{children:task.mobileNumber}),/*#__PURE__*/_jsx(TableCell,{children:task.loanType}),/*#__PURE__*/_jsx(TableCell,{children:task.assignedToName||'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(StatusBadge,{status:task.status,children:task.status.replace('-',' ').toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:task.submittedDate&&/*#__PURE__*/_jsx(PriorityBadge,{priority:getPriority(task.submittedDate),children:getPriority(task.submittedDate).toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:task.submittedDate?formatDate(task.submittedDate):'-'}),/*#__PURE__*/_jsx(TableCell,{children:task.submittedDate?calculateWaitTime(task.submittedDate):'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(ActionButtons,{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>navigate(\"/lead/\".concat(task.leadId)),children:\"View\"}),task.status==='pending-review'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleReviewAction(task,'approve'),children:\"Approve\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"danger\",onClick:()=>handleReviewAction(task,'reject'),children:\"Reject\"})]})]})})]},task.leadId))})]})}),filteredTasks.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px',color:'#777'},children:searchTerm?'No tasks found matching your search.':'No tasks pending review.'})]}),/*#__PURE__*/_jsx(ReviewModal,{isOpen:!!selectedLead&&!!reviewAction,children:/*#__PURE__*/_jsxs(ModalContent,{children:[/*#__PURE__*/_jsxs(ModalTitle,{children:[reviewAction==='approve'?'Approve':'Reject',\" Lead - \",selectedLead===null||selectedLead===void 0?void 0:selectedLead.customerName]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'15px'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'5px',fontWeight:'500'},children:\"Comments:\"}),/*#__PURE__*/_jsx(TextArea,{value:reviewComments,onChange:e=>setReviewComments(e.target.value),placeholder:\"Add your review comments...\"})]}),reviewAction==='reject'&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'15px'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'5px',fontWeight:'500'},children:\"Rejection Reason:\"}),/*#__PURE__*/_jsxs(FilterSelect,{value:rejectionReason,onChange:e=>setRejectionReason(e.target.value),style:{width:'100%'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select reason...\"}),/*#__PURE__*/_jsx(\"option\",{value:\"incomplete-documents\",children:\"Incomplete Documents\"}),/*#__PURE__*/_jsx(\"option\",{value:\"poor-quality\",children:\"Poor Quality Images\"}),/*#__PURE__*/_jsx(\"option\",{value:\"verification-failed\",children:\"Verification Failed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"incorrect-information\",children:\"Incorrect Information\"}),/*#__PURE__*/_jsx(\"option\",{value:\"other\",children:\"Other\"})]})]}),/*#__PURE__*/_jsxs(ModalActions,{children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>{setSelectedLead(null);setReviewAction(null);},children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{variant:reviewAction==='approve'?'secondary':'danger',onClick:submitReview,disabled:!reviewComments||reviewAction==='reject'&&!rejectionReason,children:reviewAction==='approve'?'Approve':'Reject'})]})]})})]});};export default SupervisorReview;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_templateObject", "_taggedTemplateLiteral", "FilterSelect", "select", "_templateObject2", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "SearchInput", "input", "_templateObject3", "TableContainer", "_templateObject4", "Table", "table", "_templateObject5", "TableHeader", "th", "_templateObject6", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject7", "TableRow", "tr", "_templateObject8", "StatusBadge", "span", "_templateObject9", "status", "PriorityBadge", "_templateObject0", "priority", "ActionButtons", "_templateObject1", "ReviewModal", "_templateObject10", "isOpen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject11", "ModalTitle", "h3", "_templateObject12", "TextArea", "textarea", "_templateObject13", "ModalActions", "_templateObject14", "SupervisorReview", "reviewTasks", "setReviewTasks", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>", "setSelectedLead", "reviewAction", "setReviewAction", "reviewComments", "setReviewComments", "rejectionReason", "setRejectionReason", "navigate", "loadReviewTasks", "response", "getLeads", "undefined", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedDate", "submittedDate", "createdByName", "assignedToName", "documentCount", "croppedImageCount", "filteredTasks", "filter", "task", "toLowerCase", "includes", "navigationItems", "icon", "label", "onClick", "active", "handleReviewAction", "lead", "action", "submitReview", "newStatus", "updateLeadStatus", "tasks", "map", "_objectSpread", "alert", "formatDate", "dateString", "Date", "toLocaleDateString", "getPriority", "daysDiff", "Math", "floor", "now", "getTime", "calculateWaitTime", "concat", "title", "children", "style", "marginBottom", "color", "value", "onChange", "e", "target", "type", "placeholder", "replace", "toUpperCase", "size", "variant", "length", "textAlign", "padding", "display", "fontWeight", "width", "disabled"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Supervisor/SupervisorReview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem } from '../../services/apiService';\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'approved':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst PriorityBadge = styled.span<{ priority: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.priority) {\n      case 'high':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      case 'medium':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      case 'low':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n\nconst ReviewModal = styled.div<{ isOpen: boolean }>`\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n`;\n\nconst ModalContent = styled.div`\n  background: white;\n  padding: 30px;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 80vh;\n  overflow-y: auto;\n`;\n\nconst ModalTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 100px;\n  padding: 10px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst ModalActions = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n\nconst SupervisorReview: React.FC = () => {\n  const [reviewTasks, setReviewTasks] = useState<LeadListItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('pending-review');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLead, setSelectedLead] = useState<LeadListItem | null>(null);\n  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);\n  const [reviewComments, setReviewComments] = useState('');\n  const [rejectionReason, setRejectionReason] = useState('');\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReviewTasks();\n  }, [statusFilter]);\n\n  const loadReviewTasks = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLeads(1, 100, statusFilter === 'all' ? undefined : statusFilter);\n      setReviewTasks(response.data || []);\n    } catch (error) {\n      console.error('Error loading review tasks:', error);\n      // Mock data for demo\n      setReviewTasks([\n        {\n          leadId: 5,\n          customerName: 'Charlie Brown',\n          mobileNumber: '9876543214',\n          loanType: 'Home Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-12T09:00:00Z',\n          assignedDate: '2024-01-12T10:00:00Z',\n          submittedDate: '2024-01-15T17:30:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Agent Smith',\n          documentCount: 4,\n          croppedImageCount: 3,\n        },\n        {\n          leadId: 6,\n          customerName: 'Diana Prince',\n          mobileNumber: '9876543215',\n          loanType: 'Car Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-13T11:15:00Z',\n          assignedDate: '2024-01-13T12:00:00Z',\n          submittedDate: '2024-01-16T09:45:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Agent Johnson',\n          documentCount: 3,\n          croppedImageCount: 2,\n        },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredTasks = reviewTasks.filter(task =>\n    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    task.mobileNumber.includes(searchTerm) ||\n    task.loanType.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (task.assignedToName && task.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/supervisor/dashboard') },\n    { icon: '👁️', label: 'Review Queue', active: true },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/supervisor/reports') },\n    { icon: '👥', label: 'Team', onClick: () => navigate('/supervisor/team') },\n  ];\n\n  const handleReviewAction = (lead: LeadListItem, action: 'approve' | 'reject') => {\n    setSelectedLead(lead);\n    setReviewAction(action);\n    setReviewComments('');\n    setRejectionReason('');\n  };\n\n  const submitReview = async () => {\n    if (!selectedLead || !reviewAction) return;\n\n    try {\n      const newStatus = reviewAction === 'approve' ? 'approved' : 'rejected';\n      await apiService.updateLeadStatus(\n        selectedLead.leadId,\n        newStatus,\n        reviewComments,\n        reviewAction === 'reject' ? rejectionReason : undefined\n      );\n\n      // Update local state\n      setReviewTasks(tasks => \n        tasks.map(task => \n          task.leadId === selectedLead.leadId \n            ? { ...task, status: newStatus }\n            : task\n        )\n      );\n\n      // Close modal\n      setSelectedLead(null);\n      setReviewAction(null);\n      \n      // Reload if filtering by status\n      if (statusFilter !== 'all') {\n        loadReviewTasks();\n      }\n\n    } catch (error) {\n      console.error('Error submitting review:', error);\n      alert('Failed to submit review');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getPriority = (submittedDate: string) => {\n    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));\n    if (daysDiff > 2) return 'high';\n    if (daysDiff > 1) return 'medium';\n    return 'low';\n  };\n\n  const calculateWaitTime = (submittedDate: string) => {\n    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));\n    return `${daysDiff} day${daysDiff !== 1 ? 's' : ''}`;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Review Queue\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Review Queue\" navigationItems={navigationItems}>\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Pending Reviews</h2>\n\n        <FilterContainer>\n          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>\n            <option value=\"pending-review\">Pending Review</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"rejected\">Rejected</option>\n            <option value=\"all\">All Reviews</option>\n          </FilterSelect>\n          \n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search by customer, mobile, loan type, or agent...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Agent</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Priority</TableHeader>\n                <TableHeader>Submitted</TableHeader>\n                <TableHeader>Wait Time</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredTasks.map((task) => (\n                <TableRow key={task.leadId}>\n                  <TableCell>{task.customerName}</TableCell>\n                  <TableCell>{task.mobileNumber}</TableCell>\n                  <TableCell>{task.loanType}</TableCell>\n                  <TableCell>{task.assignedToName || '-'}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={task.status}>\n                      {task.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate && (\n                      <PriorityBadge priority={getPriority(task.submittedDate)}>\n                        {getPriority(task.submittedDate).toUpperCase()}\n                      </PriorityBadge>\n                    )}\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate ? formatDate(task.submittedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate ? calculateWaitTime(task.submittedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    <ActionButtons>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => navigate(`/lead/${task.leadId}`)}\n                      >\n                        View\n                      </Button>\n                      {task.status === 'pending-review' && (\n                        <>\n                          <Button\n                            size=\"sm\"\n                            variant=\"secondary\"\n                            onClick={() => handleReviewAction(task, 'approve')}\n                          >\n                            Approve\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"danger\"\n                            onClick={() => handleReviewAction(task, 'reject')}\n                          >\n                            Reject\n                          </Button>\n                        </>\n                      )}\n                    </ActionButtons>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {filteredTasks.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            {searchTerm ? 'No tasks found matching your search.' : 'No tasks pending review.'}\n          </div>\n        )}\n      </Card>\n\n      {/* Review Modal */}\n      <ReviewModal isOpen={!!selectedLead && !!reviewAction}>\n        <ModalContent>\n          <ModalTitle>\n            {reviewAction === 'approve' ? 'Approve' : 'Reject'} Lead - {selectedLead?.customerName}\n          </ModalTitle>\n          \n          <div style={{ marginBottom: '15px' }}>\n            <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>\n              Comments:\n            </label>\n            <TextArea\n              value={reviewComments}\n              onChange={(e) => setReviewComments(e.target.value)}\n              placeholder=\"Add your review comments...\"\n            />\n          </div>\n\n          {reviewAction === 'reject' && (\n            <div style={{ marginBottom: '15px' }}>\n              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>\n                Rejection Reason:\n              </label>\n              <FilterSelect\n                value={rejectionReason}\n                onChange={(e) => setRejectionReason(e.target.value)}\n                style={{ width: '100%' }}\n              >\n                <option value=\"\">Select reason...</option>\n                <option value=\"incomplete-documents\">Incomplete Documents</option>\n                <option value=\"poor-quality\">Poor Quality Images</option>\n                <option value=\"verification-failed\">Verification Failed</option>\n                <option value=\"incorrect-information\">Incorrect Information</option>\n                <option value=\"other\">Other</option>\n              </FilterSelect>\n            </div>\n          )}\n\n          <ModalActions>\n            <Button\n              variant=\"outline\"\n              onClick={() => {\n                setSelectedLead(null);\n                setReviewAction(null);\n              }}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant={reviewAction === 'approve' ? 'secondary' : 'danger'}\n              onClick={submitReview}\n              disabled={!reviewComments || (reviewAction === 'reject' && !rejectionReason)}\n            >\n              {reviewAction === 'approve' ? 'Approve' : 'Reject'}\n            </Button>\n          </ModalActions>\n        </ModalContent>\n      </ReviewModal>\n    </DashboardLayout>\n  );\n};\n\nexport default SupervisorReview;\n"], "mappings": "umBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAAsB,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErE,KAAM,CAAAC,eAAe,CAAGZ,MAAM,CAACa,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,sFAKjC,CAED,KAAM,CAAAC,YAAY,CAAGhB,MAAM,CAACiB,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAH,sBAAA,6LAEZI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAKnCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAC,WAAW,CAAG1B,MAAM,CAAC2B,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,wMAIVI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAInCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAI,cAAc,CAAG7B,MAAM,CAACa,GAAG,CAAAiB,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,+BAEhC,CAED,KAAM,CAAAgB,KAAK,CAAG/B,MAAM,CAACgC,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,wDAGzB,CAED,KAAM,CAAAmB,WAAW,CAAGlC,MAAM,CAACmC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,qJAGAI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAC5ClB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,QAAQ,CAE/CnB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACkB,UAAU,CAChD,CAED,KAAM,CAAAC,SAAS,CAAGxC,MAAM,CAACyC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA3B,sBAAA,uFAGEI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAG3C,MAAM,CAAC4C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA9B,sBAAA,wDAEFI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAE5D,CAED,KAAM,CAAAS,WAAW,CAAG9C,MAAM,CAAC+C,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjC,sBAAA,mIAO3BI,KAAK,EAAI,CACT,OAAQA,KAAK,CAAC8B,MAAM,EAClB,IAAK,gBAAgB,CACnB,oFAIF,IAAK,UAAU,CACb,oFAIF,IAAK,UAAU,CACb,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAGlD,MAAM,CAAC+C,IAAI,CAAAI,gBAAA,GAAAA,gBAAA,CAAApC,sBAAA,mIAO7BI,KAAK,EAAI,CACT,OAAQA,KAAK,CAACiC,QAAQ,EACpB,IAAK,MAAM,CACT,oFAIF,IAAK,QAAQ,CACX,oFAIF,IAAK,KAAK,CACR,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAGrD,MAAM,CAACa,GAAG,CAAAyC,gBAAA,GAAAA,gBAAA,CAAAvC,sBAAA,yCAG/B,CAED,KAAM,CAAAwC,WAAW,CAAGvD,MAAM,CAACa,GAAG,CAAA2C,iBAAA,GAAAA,iBAAA,CAAAzC,sBAAA,gNACjBI,KAAK,EAAIA,KAAK,CAACsC,MAAM,CAAG,MAAM,CAAG,MAAM,CAUnD,CAED,KAAM,CAAAC,YAAY,CAAG1D,MAAM,CAACa,GAAG,CAAA8C,iBAAA,GAAAA,iBAAA,CAAA5C,sBAAA,uJAQ9B,CAED,KAAM,CAAA6C,UAAU,CAAG5D,MAAM,CAAC6D,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA/C,sBAAA,qDAG3B,CAED,KAAM,CAAAgD,QAAQ,CAAG/D,MAAM,CAACgE,QAAQ,CAAAC,iBAAA,GAAAA,iBAAA,CAAAlD,sBAAA,8NAIVI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAKnCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAyC,YAAY,CAAGlE,MAAM,CAACa,GAAG,CAAAsD,iBAAA,GAAAA,iBAAA,CAAApD,sBAAA,6FAK9B,CAED,KAAM,CAAAqD,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGzE,QAAQ,CAAiB,EAAE,CAAC,CAClE,KAAM,CAAC0E,OAAO,CAAEC,UAAU,CAAC,CAAG3E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4E,YAAY,CAAEC,eAAe,CAAC,CAAG7E,QAAQ,CAAC,gBAAgB,CAAC,CAClE,KAAM,CAAC8E,UAAU,CAAEC,aAAa,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACgF,YAAY,CAAEC,eAAe,CAAC,CAAGjF,QAAQ,CAAsB,IAAI,CAAC,CAC3E,KAAM,CAACkF,YAAY,CAAEC,eAAe,CAAC,CAAGnF,QAAQ,CAA8B,IAAI,CAAC,CACnF,KAAM,CAACoF,cAAc,CAAEC,iBAAiB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACsF,eAAe,CAAEC,kBAAkB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAAwF,QAAQ,CAAGtF,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACdwF,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAACb,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAa,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFd,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAAlF,UAAU,CAACmF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAEf,YAAY,GAAK,KAAK,CAAGgB,SAAS,CAAGhB,YAAY,CAAC,CACrGH,cAAc,CAACiB,QAAQ,CAACG,IAAI,EAAI,EAAE,CAAC,CACrC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD;AACArB,cAAc,CAAC,CACb,CACEuB,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,eAAe,CAC7BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,WAAW,CACrB/C,MAAM,CAAE,gBAAgB,CACxBgD,WAAW,CAAE,sBAAsB,CACnCC,YAAY,CAAE,sBAAsB,CACpCC,aAAa,CAAE,sBAAsB,CACrCC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,aAAa,CAC7BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACD,CACEV,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,UAAU,CACpB/C,MAAM,CAAE,gBAAgB,CACxBgD,WAAW,CAAE,sBAAsB,CACnCC,YAAY,CAAE,sBAAsB,CACpCC,aAAa,CAAE,sBAAsB,CACrCC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,eAAe,CAC/BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACF,CAAC,CACJ,CAAC,OAAS,CACR/B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgC,aAAa,CAAGnC,WAAW,CAACoC,MAAM,CAACC,IAAI,EAC3CA,IAAI,CAACZ,YAAY,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAC,EAClED,IAAI,CAACX,YAAY,CAACa,QAAQ,CAACjC,UAAU,CAAC,EACtC+B,IAAI,CAACV,QAAQ,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAC,EAC7DD,IAAI,CAACL,cAAc,EAAIK,IAAI,CAACL,cAAc,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAC7F,CAAC,CAED,KAAM,CAAAE,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAM3B,QAAQ,CAAC,uBAAuB,CAAE,CAAC,CACpF,CAAEyB,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,cAAc,CAAEE,MAAM,CAAE,IAAK,CAAC,CACpD,CAAEH,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEC,OAAO,CAAEA,CAAA,GAAM3B,QAAQ,CAAC,qBAAqB,CAAE,CAAC,CAChF,CAAEyB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,MAAM,CAAEC,OAAO,CAAEA,CAAA,GAAM3B,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC3E,CAED,KAAM,CAAA6B,kBAAkB,CAAGA,CAACC,IAAkB,CAAEC,MAA4B,GAAK,CAC/EtC,eAAe,CAACqC,IAAI,CAAC,CACrBnC,eAAe,CAACoC,MAAM,CAAC,CACvBlC,iBAAiB,CAAC,EAAE,CAAC,CACrBE,kBAAkB,CAAC,EAAE,CAAC,CACxB,CAAC,CAED,KAAM,CAAAiC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAACxC,YAAY,EAAI,CAACE,YAAY,CAAE,OAEpC,GAAI,CACF,KAAM,CAAAuC,SAAS,CAAGvC,YAAY,GAAK,SAAS,CAAG,UAAU,CAAG,UAAU,CACtE,KAAM,CAAA1E,UAAU,CAACkH,gBAAgB,CAC/B1C,YAAY,CAACgB,MAAM,CACnByB,SAAS,CACTrC,cAAc,CACdF,YAAY,GAAK,QAAQ,CAAGI,eAAe,CAAGM,SAChD,CAAC,CAED;AACAnB,cAAc,CAACkD,KAAK,EAClBA,KAAK,CAACC,GAAG,CAACf,IAAI,EACZA,IAAI,CAACb,MAAM,GAAKhB,YAAY,CAACgB,MAAM,CAAA6B,aAAA,CAAAA,aAAA,IAC1BhB,IAAI,MAAEzD,MAAM,CAAEqE,SAAS,GAC5BZ,IACN,CACF,CAAC,CAED;AACA5B,eAAe,CAAC,IAAI,CAAC,CACrBE,eAAe,CAAC,IAAI,CAAC,CAErB;AACA,GAAIP,YAAY,GAAK,KAAK,CAAE,CAC1Ba,eAAe,CAAC,CAAC,CACnB,CAEF,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDgC,KAAK,CAAC,yBAAyB,CAAC,CAClC,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,WAAW,CAAI7B,aAAqB,EAAK,CAC7C,KAAM,CAAA8B,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,CAAG,GAAI,CAAAN,IAAI,CAAC3B,aAAa,CAAC,CAACkC,OAAO,CAAC,CAAC,GAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CACrG,GAAIJ,QAAQ,CAAG,CAAC,CAAE,MAAO,MAAM,CAC/B,GAAIA,QAAQ,CAAG,CAAC,CAAE,MAAO,QAAQ,CACjC,MAAO,KAAK,CACd,CAAC,CAED,KAAM,CAAAK,iBAAiB,CAAInC,aAAqB,EAAK,CACnD,KAAM,CAAA8B,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,CAAG,GAAI,CAAAN,IAAI,CAAC3B,aAAa,CAAC,CAACkC,OAAO,CAAC,CAAC,GAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CACrG,SAAAE,MAAA,CAAUN,QAAQ,SAAAM,MAAA,CAAON,QAAQ,GAAK,CAAC,CAAG,GAAG,CAAG,EAAE,EACpD,CAAC,CAED,GAAI1D,OAAO,CAAE,CACX,mBACEhE,IAAA,CAACN,eAAe,EAACuI,KAAK,CAAC,cAAc,CAAC3B,eAAe,CAAEA,eAAgB,CAAA4B,QAAA,cACrElI,IAAA,CAACH,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,mBACEK,KAAA,CAACR,eAAe,EAACuI,KAAK,CAAC,cAAc,CAAC3B,eAAe,CAAEA,eAAgB,CAAA4B,QAAA,eACrEhI,KAAA,CAACP,IAAI,EAAAuI,QAAA,eACHlI,IAAA,OAAImI,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,CAAC,iBAAe,CAAI,CAAC,cAE3EhI,KAAA,CAACG,eAAe,EAAA6H,QAAA,eACdhI,KAAA,CAACO,YAAY,EAAC6H,KAAK,CAAEpE,YAAa,CAACqE,QAAQ,CAAGC,CAAC,EAAKrE,eAAe,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAJ,QAAA,eAClFlI,IAAA,WAAQsI,KAAK,CAAC,gBAAgB,CAAAJ,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACtDlI,IAAA,WAAQsI,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1ClI,IAAA,WAAQsI,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1ClI,IAAA,WAAQsI,KAAK,CAAC,KAAK,CAAAJ,QAAA,CAAC,aAAW,CAAQ,CAAC,EAC5B,CAAC,cAEflI,IAAA,CAACmB,WAAW,EACVuH,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oDAAoD,CAChEL,KAAK,CAAElE,UAAW,CAClBmE,QAAQ,CAAGC,CAAC,EAAKnE,aAAa,CAACmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,EACa,CAAC,cAElBtI,IAAA,CAACsB,cAAc,EAAA4G,QAAA,cACbhI,KAAA,CAACsB,KAAK,EAAA0G,QAAA,eACJlI,IAAA,UAAAkI,QAAA,cACEhI,KAAA,OAAAgI,QAAA,eACElI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,QAAM,CAAa,CAAC,cACjClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,WAAS,CAAa,CAAC,cACpClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,OAAK,CAAa,CAAC,cAChClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,QAAM,CAAa,CAAC,cACjClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,WAAS,CAAa,CAAC,cACpClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,WAAS,CAAa,CAAC,cACpClI,IAAA,CAAC2B,WAAW,EAAAuG,QAAA,CAAC,SAAO,CAAa,CAAC,EAChC,CAAC,CACA,CAAC,cACRlI,IAAA,UAAAkI,QAAA,CACGjC,aAAa,CAACiB,GAAG,CAAEf,IAAI,eACtBjG,KAAA,CAACkC,QAAQ,EAAA8F,QAAA,eACPlI,IAAA,CAACiC,SAAS,EAAAiG,QAAA,CAAE/B,IAAI,CAACZ,YAAY,CAAY,CAAC,cAC1CvF,IAAA,CAACiC,SAAS,EAAAiG,QAAA,CAAE/B,IAAI,CAACX,YAAY,CAAY,CAAC,cAC1CxF,IAAA,CAACiC,SAAS,EAAAiG,QAAA,CAAE/B,IAAI,CAACV,QAAQ,CAAY,CAAC,cACtCzF,IAAA,CAACiC,SAAS,EAAAiG,QAAA,CAAE/B,IAAI,CAACL,cAAc,EAAI,GAAG,CAAY,CAAC,cACnD9F,IAAA,CAACiC,SAAS,EAAAiG,QAAA,cACRlI,IAAA,CAACuC,WAAW,EAACG,MAAM,CAAEyD,IAAI,CAACzD,MAAO,CAAAwF,QAAA,CAC9B/B,IAAI,CAACzD,MAAM,CAACkG,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CACjC,CAAC,CACL,CAAC,cACZ7I,IAAA,CAACiC,SAAS,EAAAiG,QAAA,CACP/B,IAAI,CAACP,aAAa,eACjB5F,IAAA,CAAC2C,aAAa,EAACE,QAAQ,CAAE4E,WAAW,CAACtB,IAAI,CAACP,aAAa,CAAE,CAAAsC,QAAA,CACtDT,WAAW,CAACtB,IAAI,CAACP,aAAa,CAAC,CAACiD,WAAW,CAAC,CAAC,CACjC,CAChB,CACQ,CAAC,cACZ7I,IAAA,CAACiC,SAAS,EAAAiG,QAAA,CACP/B,IAAI,CAACP,aAAa,CAAGyB,UAAU,CAAClB,IAAI,CAACP,aAAa,CAAC,CAAG,GAAG,CACjD,CAAC,cACZ5F,IAAA,CAACiC,SAAS,EAAAiG,QAAA,CACP/B,IAAI,CAACP,aAAa,CAAGmC,iBAAiB,CAAC5B,IAAI,CAACP,aAAa,CAAC,CAAG,GAAG,CACxD,CAAC,cACZ5F,IAAA,CAACiC,SAAS,EAAAiG,QAAA,cACRhI,KAAA,CAAC4C,aAAa,EAAAoF,QAAA,eACZlI,IAAA,CAACJ,MAAM,EACLkJ,IAAI,CAAC,IAAI,CACTrC,OAAO,CAAEA,CAAA,GAAM3B,QAAQ,UAAAkD,MAAA,CAAU7B,IAAI,CAACb,MAAM,CAAE,CAAE,CAAA4C,QAAA,CACjD,MAED,CAAQ,CAAC,CACR/B,IAAI,CAACzD,MAAM,GAAK,gBAAgB,eAC/BxC,KAAA,CAAAE,SAAA,EAAA8H,QAAA,eACElI,IAAA,CAACJ,MAAM,EACLkJ,IAAI,CAAC,IAAI,CACTC,OAAO,CAAC,WAAW,CACnBtC,OAAO,CAAEA,CAAA,GAAME,kBAAkB,CAACR,IAAI,CAAE,SAAS,CAAE,CAAA+B,QAAA,CACpD,SAED,CAAQ,CAAC,cACTlI,IAAA,CAACJ,MAAM,EACLkJ,IAAI,CAAC,IAAI,CACTC,OAAO,CAAC,QAAQ,CAChBtC,OAAO,CAAEA,CAAA,GAAME,kBAAkB,CAACR,IAAI,CAAE,QAAQ,CAAE,CAAA+B,QAAA,CACnD,QAED,CAAQ,CAAC,EACT,CACH,EACY,CAAC,CACP,CAAC,GAlDC/B,IAAI,CAACb,MAmDV,CACX,CAAC,CACG,CAAC,EACH,CAAC,CACM,CAAC,CAEhBW,aAAa,CAAC+C,MAAM,GAAK,CAAC,eACzBhJ,IAAA,QAAKmI,KAAK,CAAE,CAAEc,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEb,KAAK,CAAE,MAAO,CAAE,CAAAH,QAAA,CACjE9D,UAAU,CAAG,sCAAsC,CAAG,0BAA0B,CAC9E,CACN,EACG,CAAC,cAGPpE,IAAA,CAACgD,WAAW,EAACE,MAAM,CAAE,CAAC,CAACoB,YAAY,EAAI,CAAC,CAACE,YAAa,CAAA0D,QAAA,cACpDhI,KAAA,CAACiD,YAAY,EAAA+E,QAAA,eACXhI,KAAA,CAACmD,UAAU,EAAA6E,QAAA,EACR1D,YAAY,GAAK,SAAS,CAAG,SAAS,CAAG,QAAQ,CAAC,UAAQ,CAACF,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiB,YAAY,EAC5E,CAAC,cAEbrF,KAAA,QAAKiI,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAF,QAAA,eACnClI,IAAA,UAAOmI,KAAK,CAAE,CAAEgB,OAAO,CAAE,OAAO,CAAEf,YAAY,CAAE,KAAK,CAAEgB,UAAU,CAAE,KAAM,CAAE,CAAAlB,QAAA,CAAC,WAE5E,CAAO,CAAC,cACRlI,IAAA,CAACwD,QAAQ,EACP8E,KAAK,CAAE5D,cAAe,CACtB6D,QAAQ,CAAGC,CAAC,EAAK7D,iBAAiB,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnDK,WAAW,CAAC,6BAA6B,CAC1C,CAAC,EACC,CAAC,CAELnE,YAAY,GAAK,QAAQ,eACxBtE,KAAA,QAAKiI,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAF,QAAA,eACnClI,IAAA,UAAOmI,KAAK,CAAE,CAAEgB,OAAO,CAAE,OAAO,CAAEf,YAAY,CAAE,KAAK,CAAEgB,UAAU,CAAE,KAAM,CAAE,CAAAlB,QAAA,CAAC,mBAE5E,CAAO,CAAC,cACRhI,KAAA,CAACO,YAAY,EACX6H,KAAK,CAAE1D,eAAgB,CACvB2D,QAAQ,CAAGC,CAAC,EAAK3D,kBAAkB,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDH,KAAK,CAAE,CAAEkB,KAAK,CAAE,MAAO,CAAE,CAAAnB,QAAA,eAEzBlI,IAAA,WAAQsI,KAAK,CAAC,EAAE,CAAAJ,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC1ClI,IAAA,WAAQsI,KAAK,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,sBAAoB,CAAQ,CAAC,cAClElI,IAAA,WAAQsI,KAAK,CAAC,cAAc,CAAAJ,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cACzDlI,IAAA,WAAQsI,KAAK,CAAC,qBAAqB,CAAAJ,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cAChElI,IAAA,WAAQsI,KAAK,CAAC,uBAAuB,CAAAJ,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cACpElI,IAAA,WAAQsI,KAAK,CAAC,OAAO,CAAAJ,QAAA,CAAC,OAAK,CAAQ,CAAC,EACxB,CAAC,EACZ,CACN,cAEDhI,KAAA,CAACyD,YAAY,EAAAuE,QAAA,eACXlI,IAAA,CAACJ,MAAM,EACLmJ,OAAO,CAAC,SAAS,CACjBtC,OAAO,CAAEA,CAAA,GAAM,CACblC,eAAe,CAAC,IAAI,CAAC,CACrBE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CAAAyD,QAAA,CACH,QAED,CAAQ,CAAC,cACTlI,IAAA,CAACJ,MAAM,EACLmJ,OAAO,CAAEvE,YAAY,GAAK,SAAS,CAAG,WAAW,CAAG,QAAS,CAC7DiC,OAAO,CAAEK,YAAa,CACtBwC,QAAQ,CAAE,CAAC5E,cAAc,EAAKF,YAAY,GAAK,QAAQ,EAAI,CAACI,eAAiB,CAAAsD,QAAA,CAE5E1D,YAAY,GAAK,SAAS,CAAG,SAAS,CAAG,QAAQ,CAC5C,CAAC,EACG,CAAC,EACH,CAAC,CACJ,CAAC,EACC,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}