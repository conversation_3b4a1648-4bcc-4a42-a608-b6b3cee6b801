{"ast": null, "code": "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs;\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode;\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign;\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash(value, length) {\n  return charat(value, 0) ^ 45 ? (((length << 2 ^ charat(value, 0)) << 2 ^ charat(value, 1)) << 2 ^ charat(value, 2)) << 2 ^ charat(value, 3) : 0;\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim(value) {\n  return value.trim();\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match(value, pattern) {\n  return (value = pattern.exec(value)) ? value[0] : value;\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace(value, pattern, replacement) {\n  return value.replace(pattern, replacement);\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof(value, search, position) {\n  return value.indexOf(search, position);\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat(value, index) {\n  return value.charCodeAt(index) | 0;\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr(value, begin, end) {\n  return value.slice(begin, end);\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen(value) {\n  return value.length;\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof(value) {\n  return value.length;\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append(value, array) {\n  return array.push(value), value;\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine(array, callback) {\n  return array.map(callback).join('');\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter(array, pattern) {\n  return array.filter(function (value) {\n    return !match(value, pattern);\n  });\n}", "map": {"version": 3, "names": ["abs", "Math", "from", "String", "fromCharCode", "assign", "Object", "hash", "value", "length", "charat", "trim", "match", "pattern", "exec", "replace", "replacement", "indexof", "search", "position", "indexOf", "index", "charCodeAt", "substr", "begin", "end", "slice", "strlen", "sizeof", "append", "array", "push", "combine", "callback", "map", "join", "filter"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/stylis/src/Utility.js"], "sourcesContent": ["/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,GAAG,GAAGC,IAAI,CAACD,GAAG;;AAEzB;AACA;AACA;AACA;AACA,OAAO,IAAIE,IAAI,GAAGC,MAAM,CAACC,YAAY;;AAErC;AACA;AACA;AACA;AACA,OAAO,IAAIC,MAAM,GAAGC,MAAM,CAACD,MAAM;;AAEjC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,IAAIA,CAAEC,KAAK,EAAEC,MAAM,EAAE;EACpC,OAAOC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAI,CAAE,CAAE,CAAEC,MAAM,IAAI,CAAC,GAAIC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,GAAIE,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,GAAIE,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,GAAIE,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC;AACxJ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,IAAIA,CAAEH,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACG,IAAI,CAAC,CAAC;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAAEJ,KAAK,EAAEK,OAAO,EAAE;EACtC,OAAO,CAACL,KAAK,GAAGK,OAAO,CAACC,IAAI,CAACN,KAAK,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,OAAOA,CAAEP,KAAK,EAAEK,OAAO,EAAEG,WAAW,EAAE;EACrD,OAAOR,KAAK,CAACO,OAAO,CAACF,OAAO,EAAEG,WAAW,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAAET,KAAK,EAAEU,MAAM,EAAEC,QAAQ,EAAE;EACjD,OAAOX,KAAK,CAACY,OAAO,CAACF,MAAM,EAAEC,QAAQ,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAST,MAAMA,CAAEF,KAAK,EAAEa,KAAK,EAAE;EACrC,OAAOb,KAAK,CAACc,UAAU,CAACD,KAAK,CAAC,GAAG,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,MAAMA,CAAEf,KAAK,EAAEgB,KAAK,EAAEC,GAAG,EAAE;EAC1C,OAAOjB,KAAK,CAACkB,KAAK,CAACF,KAAK,EAAEC,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,MAAMA,CAAEnB,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACC,MAAM;AACpB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASmB,MAAMA,CAAEpB,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACC,MAAM;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,MAAMA,CAAErB,KAAK,EAAEsB,KAAK,EAAE;EACrC,OAAOA,KAAK,CAACC,IAAI,CAACvB,KAAK,CAAC,EAAEA,KAAK;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwB,OAAOA,CAAEF,KAAK,EAAEG,QAAQ,EAAE;EACzC,OAAOH,KAAK,CAACI,GAAG,CAACD,QAAQ,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAAEN,KAAK,EAAEjB,OAAO,EAAE;EACvC,OAAOiB,KAAK,CAACM,MAAM,CAAC,UAAU5B,KAAK,EAAE;IAAE,OAAO,CAACI,KAAK,CAACJ,KAAK,EAAEK,OAAO,CAAC;EAAC,CAAC,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}