{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Admin\\\\CreateLead.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n  margin-left: 20px;\n`;\n_c3 = Title;\nconst FormGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n_c4 = FormGrid;\nconst AddressSection = styled.div`\n  margin-top: 20px;\n`;\n_c5 = AddressSection;\nconst AddressCard = styled(Card)`\n  margin-bottom: 15px;\n  position: relative;\n`;\n_c6 = AddressCard;\nconst RemoveButton = styled(Button)`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  padding: 5px 10px;\n  font-size: 12px;\n`;\n_c7 = RemoveButton;\nconst AddAddressButton = styled(Button)`\n  margin-top: 10px;\n`;\n_c8 = AddAddressButton;\nconst CreateLead = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formData, setFormData] = useState({\n    customerName: '',\n    mobileNumber: '',\n    loanType: ''\n  });\n  const [addresses, setAddresses] = useState([{\n    type: 'Residential',\n    address: '',\n    pincode: '',\n    state: '',\n    district: '',\n    landmark: ''\n  }]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleAddressChange = (index, field, value) => {\n    setAddresses(prev => prev.map((addr, i) => i === index ? {\n      ...addr,\n      [field]: value\n    } : addr));\n  };\n  const addAddress = () => {\n    setAddresses(prev => [...prev, {\n      type: 'Residential',\n      address: '',\n      pincode: '',\n      state: '',\n      district: '',\n      landmark: ''\n    }]);\n  };\n  const removeAddress = index => {\n    if (addresses.length > 1) {\n      setAddresses(prev => prev.filter((_, i) => i !== index));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.customerName.trim()) {\n      newErrors.customerName = 'Customer name is required';\n    }\n    if (!formData.mobileNumber.trim()) {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else if (!/^\\d{10}$/.test(formData.mobileNumber)) {\n      newErrors.mobileNumber = 'Mobile number must be 10 digits';\n    }\n    if (!formData.loanType) {\n      newErrors.loanType = 'Loan type is required';\n    }\n\n    // Validate addresses\n    addresses.forEach((addr, index) => {\n      if (!addr.address.trim()) {\n        newErrors[`address_${index}`] = 'Address is required';\n      }\n      if (!addr.pincode.trim()) {\n        newErrors[`pincode_${index}`] = 'Pincode is required';\n      } else if (!/^\\d{6}$/.test(addr.pincode)) {\n        newErrors[`pincode_${index}`] = 'Pincode must be 6 digits';\n      }\n      if (!addr.state.trim()) {\n        newErrors[`state_${index}`] = 'State is required';\n      }\n      if (!addr.district.trim()) {\n        newErrors[`district_${index}`] = 'District is required';\n      }\n    });\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    try {\n      const leadData = {\n        customerName: formData.customerName,\n        mobileNumber: formData.mobileNumber,\n        loanType: formData.loanType,\n        addresses: addresses\n      };\n      await apiService.createLead(leadData);\n      alert('Lead created successfully!');\n      navigate('/admin/dashboard');\n    } catch (error) {\n      console.error('Error creating lead:', error);\n      alert('Failed to create lead. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: handleBack,\n        children: \"\\u2190 Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: \"Create New Lead\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Customer Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGrid, {\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"customerName\",\n              children: \"Customer Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"customerName\",\n              name: \"customerName\",\n              value: formData.customerName,\n              onChange: handleInputChange,\n              placeholder: \"Enter customer full name\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), errors.customerName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.customerName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"mobileNumber\",\n              children: \"Mobile Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"tel\",\n              id: \"mobileNumber\",\n              name: \"mobileNumber\",\n              value: formData.mobileNumber,\n              onChange: handleInputChange,\n              placeholder: \"Enter 10-digit mobile number\",\n              maxLength: 10,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), errors.mobileNumber && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.mobileNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"loanType\",\n              children: \"Loan Type *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              id: \"loanType\",\n              name: \"loanType\",\n              value: formData.loanType,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Loan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Personal Loan\",\n                children: \"Personal Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Home Loan\",\n                children: \"Home Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Car Loan\",\n                children: \"Car Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Business Loan\",\n                children: \"Business Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Education Loan\",\n                children: \"Education Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Gold Loan\",\n                children: \"Gold Loan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), errors.loanType && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.loanType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AddressSection, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Address Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), addresses.map((address, index) => /*#__PURE__*/_jsxDEV(AddressCard, {\n          children: [addresses.length > 1 && /*#__PURE__*/_jsxDEV(RemoveButton, {\n            type: \"button\",\n            variant: \"danger\",\n            size: \"sm\",\n            onClick: () => removeAddress(index),\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '15px',\n              color: '#555'\n            },\n            children: [\"Address \", index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGrid, {\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Address Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: address.type,\n                onChange: e => handleAddressChange(index, 'type', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Residential\",\n                  children: \"Residential\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Office\",\n                  children: \"Office\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Business\",\n                  children: \"Business\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"State *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                value: address.state,\n                onChange: e => handleAddressChange(index, 'state', e.target.value),\n                placeholder: \"Enter state\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), errors[`state_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors[`state_${index}`]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 48\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"District *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                value: address.district,\n                onChange: e => handleAddressChange(index, 'district', e.target.value),\n                placeholder: \"Enter district\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), errors[`district_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors[`district_${index}`]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Pincode *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                value: address.pincode,\n                onChange: e => handleAddressChange(index, 'pincode', e.target.value),\n                placeholder: \"Enter 6-digit pincode\",\n                maxLength: 6,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), errors[`pincode_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors[`pincode_${index}`]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 50\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Full Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              value: address.address,\n              onChange: e => handleAddressChange(index, 'address', e.target.value),\n              placeholder: \"Enter complete address\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), errors[`address_${index}`] && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors[`address_${index}`]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 48\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Landmark\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              value: address.landmark,\n              onChange: e => handleAddressChange(index, 'landmark', e.target.value),\n              placeholder: \"Enter nearby landmark (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(AddAddressButton, {\n          type: \"button\",\n          variant: \"outline\",\n          onClick: addAddress,\n          children: \"+ Add Another Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '30px',\n          display: 'flex',\n          gap: '10px',\n          justifyContent: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          variant: \"outline\",\n          onClick: handleBack,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? 'Creating...' : 'Create Lead'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateLead, \"02gR1rxtTf/FxPpUa/lonMlBBG0=\", false, function () {\n  return [useNavigate];\n});\n_c9 = CreateLead;\nexport default CreateLead;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"FormGrid\");\n$RefreshReg$(_c5, \"AddressSection\");\n$RefreshReg$(_c6, \"AddressCard\");\n$RefreshReg$(_c7, \"RemoveButton\");\n$RefreshReg$(_c8, \"AddAddressButton\");\n$RefreshReg$(_c9, \"CreateLead\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "Input", "Select", "FormGroup", "Label", "ErrorMessage", "apiService", "jsxDEV", "_jsxDEV", "Container", "div", "_c", "Header", "props", "theme", "colors", "mediumGray", "_c2", "Title", "h1", "primary", "_c3", "FormGrid", "_c4", "AddressSection", "_c5", "AddressCard", "_c6", "RemoveButton", "_c7", "AddAddressButton", "_c8", "CreateLead", "_s", "navigate", "loading", "setLoading", "errors", "setErrors", "formData", "setFormData", "customerName", "mobileNumber", "loanType", "addresses", "setAdd<PERSON>", "type", "address", "pincode", "state", "district", "landmark", "handleInputChange", "e", "name", "value", "target", "prev", "handleAddressChange", "index", "field", "map", "addr", "i", "addAddress", "removeAddress", "length", "filter", "_", "validateForm", "newErrors", "trim", "test", "for<PERSON>ach", "Object", "keys", "handleSubmit", "preventDefault", "leadData", "createLead", "alert", "error", "console", "handleBack", "children", "variant", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "style", "marginBottom", "color", "htmlFor", "id", "onChange", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "size", "marginTop", "display", "gap", "justifyContent", "disabled", "_c9", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/CreateLead.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, CreateLeadRequest, DocumentUpload, DocumentType, UploadProgress } from '../../services/apiService';\nimport DocumentUploadComponent from '../Common/DocumentUpload';\n\nconst Container = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n  margin-left: 20px;\n`;\n\nconst FormGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n\nconst AddressSection = styled.div`\n  margin-top: 20px;\n`;\n\nconst AddressCard = styled(Card)`\n  margin-bottom: 15px;\n  position: relative;\n`;\n\nconst RemoveButton = styled(Button)`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  padding: 5px 10px;\n  font-size: 12px;\n`;\n\nconst AddAddressButton = styled(Button)`\n  margin-top: 10px;\n`;\n\ninterface Address {\n  type: string;\n  address: string;\n  pincode: string;\n  state: string;\n  district: string;\n  landmark?: string;\n}\n\nconst CreateLead: React.FC = () => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\n\n  const [formData, setFormData] = useState({\n    customerName: '',\n    mobileNumber: '',\n    loanType: '',\n  });\n\n  const [addresses, setAddresses] = useState<Address[]>([\n    {\n      type: 'Residential',\n      address: '',\n      pincode: '',\n      state: '',\n      district: '',\n      landmark: '',\n    }\n  ]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const handleAddressChange = (index: number, field: string, value: string) => {\n    setAddresses(prev => prev.map((addr, i) =>\n      i === index ? { ...addr, [field]: value } : addr\n    ));\n  };\n\n  const addAddress = () => {\n    setAddresses(prev => [...prev, {\n      type: 'Residential',\n      address: '',\n      pincode: '',\n      state: '',\n      district: '',\n      landmark: '',\n    }]);\n  };\n\n  const removeAddress = (index: number) => {\n    if (addresses.length > 1) {\n      setAddresses(prev => prev.filter((_, i) => i !== index));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: { [key: string]: string } = {};\n\n    if (!formData.customerName.trim()) {\n      newErrors.customerName = 'Customer name is required';\n    }\n\n    if (!formData.mobileNumber.trim()) {\n      newErrors.mobileNumber = 'Mobile number is required';\n    } else if (!/^\\d{10}$/.test(formData.mobileNumber)) {\n      newErrors.mobileNumber = 'Mobile number must be 10 digits';\n    }\n\n    if (!formData.loanType) {\n      newErrors.loanType = 'Loan type is required';\n    }\n\n    // Validate addresses\n    addresses.forEach((addr, index) => {\n      if (!addr.address.trim()) {\n        newErrors[`address_${index}`] = 'Address is required';\n      }\n      if (!addr.pincode.trim()) {\n        newErrors[`pincode_${index}`] = 'Pincode is required';\n      } else if (!/^\\d{6}$/.test(addr.pincode)) {\n        newErrors[`pincode_${index}`] = 'Pincode must be 6 digits';\n      }\n      if (!addr.state.trim()) {\n        newErrors[`state_${index}`] = 'State is required';\n      }\n      if (!addr.district.trim()) {\n        newErrors[`district_${index}`] = 'District is required';\n      }\n    });\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const leadData: CreateLeadRequest = {\n        customerName: formData.customerName,\n        mobileNumber: formData.mobileNumber,\n        loanType: formData.loanType,\n        addresses: addresses,\n      };\n\n      await apiService.createLead(leadData);\n\n      alert('Lead created successfully!');\n      navigate('/admin/dashboard');\n    } catch (error) {\n      console.error('Error creating lead:', error);\n      alert('Failed to create lead. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n\n  return (\n    <Container>\n      <Header>\n        <Button variant=\"outline\" onClick={handleBack}>\n          ← Back\n        </Button>\n        <Title>Create New Lead</Title>\n      </Header>\n\n      <form onSubmit={handleSubmit}>\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Customer Information</h3>\n\n          <FormGrid>\n            <FormGroup>\n              <Label htmlFor=\"customerName\">Customer Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"customerName\"\n                name=\"customerName\"\n                value={formData.customerName}\n                onChange={handleInputChange}\n                placeholder=\"Enter customer full name\"\n                required\n              />\n              {errors.customerName && <ErrorMessage>{errors.customerName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"mobileNumber\">Mobile Number *</Label>\n              <Input\n                type=\"tel\"\n                id=\"mobileNumber\"\n                name=\"mobileNumber\"\n                value={formData.mobileNumber}\n                onChange={handleInputChange}\n                placeholder=\"Enter 10-digit mobile number\"\n                maxLength={10}\n                required\n              />\n              {errors.mobileNumber && <ErrorMessage>{errors.mobileNumber}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"loanType\">Loan Type *</Label>\n              <Select\n                id=\"loanType\"\n                name=\"loanType\"\n                value={formData.loanType}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Select Loan Type</option>\n                <option value=\"Personal Loan\">Personal Loan</option>\n                <option value=\"Home Loan\">Home Loan</option>\n                <option value=\"Car Loan\">Car Loan</option>\n                <option value=\"Business Loan\">Business Loan</option>\n                <option value=\"Education Loan\">Education Loan</option>\n                <option value=\"Gold Loan\">Gold Loan</option>\n              </Select>\n              {errors.loanType && <ErrorMessage>{errors.loanType}</ErrorMessage>}\n            </FormGroup>\n          </FormGrid>\n        </Card>\n\n        <AddressSection>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Address Information</h3>\n\n          {addresses.map((address, index) => (\n            <AddressCard key={index}>\n              {addresses.length > 1 && (\n                <RemoveButton\n                  type=\"button\"\n                  variant=\"danger\"\n                  size=\"sm\"\n                  onClick={() => removeAddress(index)}\n                >\n                  Remove\n                </RemoveButton>\n              )}\n\n              <h4 style={{ marginBottom: '15px', color: '#555' }}>\n                Address {index + 1}\n              </h4>\n\n              <FormGrid>\n                <FormGroup>\n                  <Label>Address Type</Label>\n                  <Select\n                    value={address.type}\n                    onChange={(e) => handleAddressChange(index, 'type', e.target.value)}\n                  >\n                    <option value=\"Residential\">Residential</option>\n                    <option value=\"Office\">Office</option>\n                    <option value=\"Business\">Business</option>\n                  </Select>\n                </FormGroup>\n\n                <FormGroup>\n                  <Label>State *</Label>\n                  <Input\n                    type=\"text\"\n                    value={address.state}\n                    onChange={(e) => handleAddressChange(index, 'state', e.target.value)}\n                    placeholder=\"Enter state\"\n                    required\n                  />\n                  {errors[`state_${index}`] && <ErrorMessage>{errors[`state_${index}`]}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label>District *</Label>\n                  <Input\n                    type=\"text\"\n                    value={address.district}\n                    onChange={(e) => handleAddressChange(index, 'district', e.target.value)}\n                    placeholder=\"Enter district\"\n                    required\n                  />\n                  {errors[`district_${index}`] && <ErrorMessage>{errors[`district_${index}`]}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label>Pincode *</Label>\n                  <Input\n                    type=\"text\"\n                    value={address.pincode}\n                    onChange={(e) => handleAddressChange(index, 'pincode', e.target.value)}\n                    placeholder=\"Enter 6-digit pincode\"\n                    maxLength={6}\n                    required\n                  />\n                  {errors[`pincode_${index}`] && <ErrorMessage>{errors[`pincode_${index}`]}</ErrorMessage>}\n                </FormGroup>\n              </FormGrid>\n\n              <FormGroup>\n                <Label>Full Address *</Label>\n                <Input\n                  type=\"text\"\n                  value={address.address}\n                  onChange={(e) => handleAddressChange(index, 'address', e.target.value)}\n                  placeholder=\"Enter complete address\"\n                  required\n                />\n                {errors[`address_${index}`] && <ErrorMessage>{errors[`address_${index}`]}</ErrorMessage>}\n              </FormGroup>\n\n              <FormGroup>\n                <Label>Landmark</Label>\n                <Input\n                  type=\"text\"\n                  value={address.landmark}\n                  onChange={(e) => handleAddressChange(index, 'landmark', e.target.value)}\n                  placeholder=\"Enter nearby landmark (optional)\"\n                />\n              </FormGroup>\n            </AddressCard>\n          ))}\n\n          <AddAddressButton\n            type=\"button\"\n            variant=\"outline\"\n            onClick={addAddress}\n          >\n            + Add Another Address\n          </AddAddressButton>\n        </AddressSection>\n\n        <div style={{ marginTop: '30px', display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>\n          <Button type=\"button\" variant=\"outline\" onClick={handleBack}>\n            Cancel\n          </Button>\n          <Button type=\"submit\" disabled={loading}>\n            {loading ? 'Creating...' : 'Create Lead'}\n          </Button>\n        </div>\n      </form>\n    </Container>\n  );\n};\n\nexport default CreateLead;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,QAAwB,2BAA2B;AACvH,SAASC,UAAU,QAAyE,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxH,MAAMC,SAAS,GAAGX,MAAM,CAACY,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,SAAS;AAMf,MAAMG,MAAM,GAAGd,MAAM,CAACY,GAAG;AACzB;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACC,GAAA,GANIL,MAAM;AAQZ,MAAMM,KAAK,GAAGpB,MAAM,CAACqB,EAAE;AACvB;AACA;AACA,WAAWN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C;AACA,CAAC;AAACC,GAAA,GALIH,KAAK;AAOX,MAAMI,QAAQ,GAAGxB,MAAM,CAACY,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAJID,QAAQ;AAMd,MAAME,cAAc,GAAG1B,MAAM,CAACY,GAAG;AACjC;AACA,CAAC;AAACe,GAAA,GAFID,cAAc;AAIpB,MAAME,WAAW,GAAG5B,MAAM,CAACC,IAAI,CAAC;AAChC;AACA;AACA,CAAC;AAAC4B,GAAA,GAHID,WAAW;AAKjB,MAAME,YAAY,GAAG9B,MAAM,CAACE,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,GAAA,GANID,YAAY;AAQlB,MAAME,gBAAgB,GAAGhC,MAAM,CAACE,MAAM,CAAC;AACvC;AACA,CAAC;AAAC+B,GAAA,GAFID,gBAAgB;AAatB,MAAME,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAEnE,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC;IACvC6C,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAY,CACpD;IACEkD,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;EAEF,MAAMC,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChB,WAAW,CAACiB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAIlB,MAAM,CAACiB,IAAI,CAAC,EAAE;MAChBhB,SAAS,CAACmB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMI,mBAAmB,GAAGA,CAACC,KAAa,EAAEC,KAAa,EAAEL,KAAa,KAAK;IAC3EV,YAAY,CAACY,IAAI,IAAIA,IAAI,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KACpCA,CAAC,KAAKJ,KAAK,GAAG;MAAE,GAAGG,IAAI;MAAE,CAACF,KAAK,GAAGL;IAAM,CAAC,GAAGO,IAC9C,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvBnB,YAAY,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC7BX,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMc,aAAa,GAAIN,KAAa,IAAK;IACvC,IAAIf,SAAS,CAACsB,MAAM,GAAG,CAAC,EAAE;MACxBrB,YAAY,CAACY,IAAI,IAAIA,IAAI,CAACU,MAAM,CAAC,CAACC,CAAC,EAAEL,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAoC,GAAG,CAAC,CAAC;IAE/C,IAAI,CAAC/B,QAAQ,CAACE,YAAY,CAAC8B,IAAI,CAAC,CAAC,EAAE;MACjCD,SAAS,CAAC7B,YAAY,GAAG,2BAA2B;IACtD;IAEA,IAAI,CAACF,QAAQ,CAACG,YAAY,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACjCD,SAAS,CAAC5B,YAAY,GAAG,2BAA2B;IACtD,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC8B,IAAI,CAACjC,QAAQ,CAACG,YAAY,CAAC,EAAE;MAClD4B,SAAS,CAAC5B,YAAY,GAAG,iCAAiC;IAC5D;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtB2B,SAAS,CAAC3B,QAAQ,GAAG,uBAAuB;IAC9C;;IAEA;IACAC,SAAS,CAAC6B,OAAO,CAAC,CAACX,IAAI,EAAEH,KAAK,KAAK;MACjC,IAAI,CAACG,IAAI,CAACf,OAAO,CAACwB,IAAI,CAAC,CAAC,EAAE;QACxBD,SAAS,CAAC,WAAWX,KAAK,EAAE,CAAC,GAAG,qBAAqB;MACvD;MACA,IAAI,CAACG,IAAI,CAACd,OAAO,CAACuB,IAAI,CAAC,CAAC,EAAE;QACxBD,SAAS,CAAC,WAAWX,KAAK,EAAE,CAAC,GAAG,qBAAqB;MACvD,CAAC,MAAM,IAAI,CAAC,SAAS,CAACa,IAAI,CAACV,IAAI,CAACd,OAAO,CAAC,EAAE;QACxCsB,SAAS,CAAC,WAAWX,KAAK,EAAE,CAAC,GAAG,0BAA0B;MAC5D;MACA,IAAI,CAACG,IAAI,CAACb,KAAK,CAACsB,IAAI,CAAC,CAAC,EAAE;QACtBD,SAAS,CAAC,SAASX,KAAK,EAAE,CAAC,GAAG,mBAAmB;MACnD;MACA,IAAI,CAACG,IAAI,CAACZ,QAAQ,CAACqB,IAAI,CAAC,CAAC,EAAE;QACzBD,SAAS,CAAC,YAAYX,KAAK,EAAE,CAAC,GAAG,sBAAsB;MACzD;IACF,CAAC,CAAC;IAEFrB,SAAS,CAACgC,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACJ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOvB,CAAkB,IAAK;IACjDA,CAAC,CAACwB,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAjC,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM0C,QAA2B,GAAG;QAClCrC,YAAY,EAAEF,QAAQ,CAACE,YAAY;QACnCC,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BC,SAAS,EAAEA;MACb,CAAC;MAED,MAAMtC,UAAU,CAACyE,UAAU,CAACD,QAAQ,CAAC;MAErCE,KAAK,CAAC,4BAA4B,CAAC;MACnC9C,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CD,KAAK,CAAC,0CAA0C,CAAC;IACnD,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,UAAU,GAAGA,CAAA,KAAM;IACvBjD,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,oBACE1B,OAAA,CAACC,SAAS;IAAA2E,QAAA,gBACR5E,OAAA,CAACI,MAAM;MAAAwE,QAAA,gBACL5E,OAAA,CAACR,MAAM;QAACqF,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEH,UAAW;QAAAC,QAAA,EAAC;MAE/C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlF,OAAA,CAACU,KAAK;QAAAkE,QAAA,EAAC;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAETlF,OAAA;MAAMmF,QAAQ,EAAEf,YAAa;MAAAQ,QAAA,gBAC3B5E,OAAA,CAACT,IAAI;QAAAqF,QAAA,gBACH5E,OAAA;UAAIoF,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhFlF,OAAA,CAACc,QAAQ;UAAA8D,QAAA,gBACP5E,OAAA,CAACL,SAAS;YAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;cAAC2F,OAAO,EAAC,cAAc;cAAAX,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrDlF,OAAA,CAACP,KAAK;cACJ6C,IAAI,EAAC,MAAM;cACXkD,EAAE,EAAC,cAAc;cACjB1C,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAEhB,QAAQ,CAACE,YAAa;cAC7BwD,QAAQ,EAAE7C,iBAAkB;cAC5B8C,WAAW,EAAC,0BAA0B;cACtCC,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDrD,MAAM,CAACI,YAAY,iBAAIjC,OAAA,CAACH,YAAY;cAAA+E,QAAA,EAAE/C,MAAM,CAACI;YAAY;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEZlF,OAAA,CAACL,SAAS;YAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;cAAC2F,OAAO,EAAC,cAAc;cAAAX,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrDlF,OAAA,CAACP,KAAK;cACJ6C,IAAI,EAAC,KAAK;cACVkD,EAAE,EAAC,cAAc;cACjB1C,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAEhB,QAAQ,CAACG,YAAa;cAC7BuD,QAAQ,EAAE7C,iBAAkB;cAC5B8C,WAAW,EAAC,8BAA8B;cAC1CE,SAAS,EAAE,EAAG;cACdD,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDrD,MAAM,CAACK,YAAY,iBAAIlC,OAAA,CAACH,YAAY;cAAA+E,QAAA,EAAE/C,MAAM,CAACK;YAAY;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEZlF,OAAA,CAACL,SAAS;YAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;cAAC2F,OAAO,EAAC,UAAU;cAAAX,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7ClF,OAAA,CAACN,MAAM;cACL8F,EAAE,EAAC,UAAU;cACb1C,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEhB,QAAQ,CAACI,QAAS;cACzBsD,QAAQ,EAAE7C,iBAAkB;cAC5B+C,QAAQ;cAAAf,QAAA,gBAER5E,OAAA;gBAAQ+C,KAAK,EAAC,EAAE;gBAAA6B,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ClF,OAAA;gBAAQ+C,KAAK,EAAC,eAAe;gBAAA6B,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpDlF,OAAA;gBAAQ+C,KAAK,EAAC,WAAW;gBAAA6B,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ClF,OAAA;gBAAQ+C,KAAK,EAAC,UAAU;gBAAA6B,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ClF,OAAA;gBAAQ+C,KAAK,EAAC,eAAe;gBAAA6B,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpDlF,OAAA;gBAAQ+C,KAAK,EAAC,gBAAgB;gBAAA6B,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDlF,OAAA;gBAAQ+C,KAAK,EAAC,WAAW;gBAAA6B,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EACRrD,MAAM,CAACM,QAAQ,iBAAInC,OAAA,CAACH,YAAY;cAAA+E,QAAA,EAAE/C,MAAM,CAACM;YAAQ;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAEPlF,OAAA,CAACgB,cAAc;QAAA4D,QAAA,gBACb5E,OAAA;UAAIoF,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EAAC;QAAmB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAE9E9C,SAAS,CAACiB,GAAG,CAAC,CAACd,OAAO,EAAEY,KAAK,kBAC5BnD,OAAA,CAACkB,WAAW;UAAA0D,QAAA,GACTxC,SAAS,CAACsB,MAAM,GAAG,CAAC,iBACnB1D,OAAA,CAACoB,YAAY;YACXkB,IAAI,EAAC,QAAQ;YACbuC,OAAO,EAAC,QAAQ;YAChBgB,IAAI,EAAC,IAAI;YACTf,OAAO,EAAEA,CAAA,KAAMrB,aAAa,CAACN,KAAK,CAAE;YAAAyB,QAAA,EACrC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CACf,eAEDlF,OAAA;YAAIoF,KAAK,EAAE;cAAEC,YAAY,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAV,QAAA,GAAC,UAC1C,EAACzB,KAAK,GAAG,CAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAELlF,OAAA,CAACc,QAAQ;YAAA8D,QAAA,gBACP5E,OAAA,CAACL,SAAS;cAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;gBAAAgF,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BlF,OAAA,CAACN,MAAM;gBACLqD,KAAK,EAAER,OAAO,CAACD,IAAK;gBACpBmD,QAAQ,EAAG5C,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,MAAM,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAAA6B,QAAA,gBAEpE5E,OAAA;kBAAQ+C,KAAK,EAAC,aAAa;kBAAA6B,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDlF,OAAA;kBAAQ+C,KAAK,EAAC,QAAQ;kBAAA6B,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtClF,OAAA;kBAAQ+C,KAAK,EAAC,UAAU;kBAAA6B,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZlF,OAAA,CAACL,SAAS;cAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;gBAAAgF,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBlF,OAAA,CAACP,KAAK;gBACJ6C,IAAI,EAAC,MAAM;gBACXS,KAAK,EAAER,OAAO,CAACE,KAAM;gBACrBgD,QAAQ,EAAG5C,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,OAAO,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACrE2C,WAAW,EAAC,aAAa;gBACzBC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDrD,MAAM,CAAC,SAASsB,KAAK,EAAE,CAAC,iBAAInD,OAAA,CAACH,YAAY;gBAAA+E,QAAA,EAAE/C,MAAM,CAAC,SAASsB,KAAK,EAAE;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEZlF,OAAA,CAACL,SAAS;cAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;gBAAAgF,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzBlF,OAAA,CAACP,KAAK;gBACJ6C,IAAI,EAAC,MAAM;gBACXS,KAAK,EAAER,OAAO,CAACG,QAAS;gBACxB+C,QAAQ,EAAG5C,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,UAAU,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACxE2C,WAAW,EAAC,gBAAgB;gBAC5BC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDrD,MAAM,CAAC,YAAYsB,KAAK,EAAE,CAAC,iBAAInD,OAAA,CAACH,YAAY;gBAAA+E,QAAA,EAAE/C,MAAM,CAAC,YAAYsB,KAAK,EAAE;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eAEZlF,OAAA,CAACL,SAAS;cAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;gBAAAgF,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBlF,OAAA,CAACP,KAAK;gBACJ6C,IAAI,EAAC,MAAM;gBACXS,KAAK,EAAER,OAAO,CAACC,OAAQ;gBACvBiD,QAAQ,EAAG5C,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,SAAS,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACvE2C,WAAW,EAAC,uBAAuB;gBACnCE,SAAS,EAAE,CAAE;gBACbD,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDrD,MAAM,CAAC,WAAWsB,KAAK,EAAE,CAAC,iBAAInD,OAAA,CAACH,YAAY;gBAAA+E,QAAA,EAAE/C,MAAM,CAAC,WAAWsB,KAAK,EAAE;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEXlF,OAAA,CAACL,SAAS;YAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;cAAAgF,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BlF,OAAA,CAACP,KAAK;cACJ6C,IAAI,EAAC,MAAM;cACXS,KAAK,EAAER,OAAO,CAACA,OAAQ;cACvBkD,QAAQ,EAAG5C,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,SAAS,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACvE2C,WAAW,EAAC,wBAAwB;cACpCC,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDrD,MAAM,CAAC,WAAWsB,KAAK,EAAE,CAAC,iBAAInD,OAAA,CAACH,YAAY;cAAA+E,QAAA,EAAE/C,MAAM,CAAC,WAAWsB,KAAK,EAAE;YAAC;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAEZlF,OAAA,CAACL,SAAS;YAAAiF,QAAA,gBACR5E,OAAA,CAACJ,KAAK;cAAAgF,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvBlF,OAAA,CAACP,KAAK;cACJ6C,IAAI,EAAC,MAAM;cACXS,KAAK,EAAER,OAAO,CAACI,QAAS;cACxB8C,QAAQ,EAAG5C,CAAC,IAAKK,mBAAmB,CAACC,KAAK,EAAE,UAAU,EAAEN,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACxE2C,WAAW,EAAC;YAAkC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA,GAvFI/B,KAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwFV,CACd,CAAC,eAEFlF,OAAA,CAACsB,gBAAgB;UACfgB,IAAI,EAAC,QAAQ;UACbuC,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEtB,UAAW;UAAAoB,QAAA,EACrB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEjBlF,OAAA;QAAKoF,KAAK,EAAE;UAAEU,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAW,CAAE;QAAArB,QAAA,gBAC1F5E,OAAA,CAACR,MAAM;UAAC8C,IAAI,EAAC,QAAQ;UAACuC,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEH,UAAW;UAAAC,QAAA,EAAC;QAE7D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlF,OAAA,CAACR,MAAM;UAAC8C,IAAI,EAAC,QAAQ;UAAC4D,QAAQ,EAAEvE,OAAQ;UAAAiD,QAAA,EACrCjD,OAAO,GAAG,aAAa,GAAG;QAAa;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACzD,EAAA,CAnTID,UAAoB;EAAA,QACPnC,WAAW;AAAA;AAAA8G,GAAA,GADxB3E,UAAoB;AAqT1B,eAAeA,UAAU;AAAC,IAAArB,EAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA4E,GAAA;AAAAC,YAAA,CAAAjG,EAAA;AAAAiG,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}