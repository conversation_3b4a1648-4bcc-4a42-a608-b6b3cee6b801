{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16,_templateObject17,_templateObject18;import React,{useState}from'react';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DashboardContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  min-height: 100vh;\\n\"])));const Sidebar=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  width: \",\";\\n  background-color: \",\";\\n  color: \",\";\\n  padding: 20px;\\n  position: fixed;\\n  height: 100vh;\\n  overflow-y: auto;\\n  transition: \",\";\\n  z-index: 1000;\\n\\n  @media (max-width: \",\") {\\n    width: \",\";\\n    padding: 15px 10px;\\n  }\\n\"])),props=>props.collapsed?'70px':'250px',props=>props.theme.colors.primary,props=>props.theme.colors.white,props=>props.theme.transitions.default,props=>props.theme.breakpoints.tablet,props=>props.collapsed?'70px':'250px');const LogoContainer=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  justify-content: \",\";\\n\"])),props=>props.collapsed?'center':'flex-start');const Logo=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  width: 40px;\\n  height: 40px;\\n  position: relative;\\n  margin-right: 10px;\\n\"])));const LogoULeft=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 20px;\\n  height: 20px;\\n  background-color: \",\";\\n  border-radius: 10px 10px 0 0;\\n  position: absolute;\\n  left: 5px;\\n  transform: rotate(180deg);\\n\"])),props=>props.theme.colors.secondary);const LogoURight=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  width: 20px;\\n  height: 20px;\\n  background-color: \",\";\\n  border-radius: 10px 10px 0 0;\\n  position: absolute;\\n  right: 5px;\\n\"])),props=>props.theme.colors.secondary);const LogoText=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  font-size: 18px;\\n  font-weight: 600;\\n  display: \",\";\\n\"])),props=>props.collapsed?'none':'block');const NavItem=styled.div(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  border-radius: \",\";\\n  margin-bottom: 5px;\\n  cursor: pointer;\\n  transition: \",\";\\n  display: flex;\\n  align-items: center;\\n  background-color: \",\";\\n\\n  &:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n  }\\n\"])),props=>props.theme.borderRadius.sm,props=>props.theme.transitions.default,props=>props.active?'rgba(255, 255, 255, 0.2)':'transparent');const NavIcon=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  margin-right: \",\";\\n  width: 20px;\\n  text-align: center;\\n  font-size: 16px;\\n\"])),props=>props.collapsed?'0':'10px');const NavText=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: \",\";\\n\"])),props=>props.collapsed?'none':'block');const UserInfo=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  margin-top: auto;\\n  padding: 15px;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  display: flex;\\n  align-items: center;\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  background-color: \",\";\\n  justify-content: \",\";\\n\"])),props=>props.theme.colors.primary,props=>props.collapsed?'center':'flex-start');const UserAvatar=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: \",\";\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 10px;\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.secondary,props=>props.theme.colors.textDark);const UserDetails=styled.div(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  display: \",\";\\n\"])),props=>props.collapsed?'none':'block');const UserName=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  font-weight: 500;\\n\"])));const UserRole=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  font-size: 12px;\\n  opacity: 0.8;\\n\"])));const MainContent=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  margin-left: \",\";\\n  padding: 20px;\\n  transition: \",\";\\n\"])),props=>props.sidebarCollapsed?'70px':'250px',props=>props.theme.transitions.default);const PageHeader=styled.div(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.mediumGray);const PageTitle=styled.h1(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.primary);const HeaderActions=styled.div(_templateObject17||(_templateObject17=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n\"])));const CollapseButton=styled.button(_templateObject18||(_templateObject18=_taggedTemplateLiteral([\"\\n  background: none;\\n  border: none;\\n  color: \",\";\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 4px;\\n  transition: \",\";\\n\\n  &:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n  }\\n\"])),props=>props.theme.colors.white,props=>props.theme.transitions.default);const DashboardLayout=_ref=>{var _user$firstName;let{children,title,headerActions,navigationItems}=_ref;const[sidebarCollapsed,setSidebarCollapsed]=useState(false);const{user,logout}=useAuth();const toggleSidebar=()=>{setSidebarCollapsed(!sidebarCollapsed);};return/*#__PURE__*/_jsxs(DashboardContainer,{children:[/*#__PURE__*/_jsxs(Sidebar,{collapsed:sidebarCollapsed,children:[/*#__PURE__*/_jsxs(LogoContainer,{collapsed:sidebarCollapsed,children:[/*#__PURE__*/_jsxs(Logo,{children:[/*#__PURE__*/_jsx(LogoULeft,{}),/*#__PURE__*/_jsx(LogoURight,{})]}),/*#__PURE__*/_jsx(LogoText,{collapsed:sidebarCollapsed,children:\"UBI Verify\"})]}),/*#__PURE__*/_jsx(CollapseButton,{onClick:toggleSidebar,children:sidebarCollapsed?'☰':'←'}),navigationItems.map((item,index)=>/*#__PURE__*/_jsxs(NavItem,{active:item.active,onClick:item.onClick,children:[/*#__PURE__*/_jsx(NavIcon,{collapsed:sidebarCollapsed,children:item.icon}),/*#__PURE__*/_jsx(NavText,{collapsed:sidebarCollapsed,children:item.label})]},index)),/*#__PURE__*/_jsxs(NavItem,{onClick:logout,children:[/*#__PURE__*/_jsx(NavIcon,{collapsed:sidebarCollapsed,children:\"\\uD83D\\uDEAA\"}),/*#__PURE__*/_jsx(NavText,{collapsed:sidebarCollapsed,children:\"Logout\"})]}),/*#__PURE__*/_jsxs(UserInfo,{collapsed:sidebarCollapsed,children:[/*#__PURE__*/_jsx(UserAvatar,{children:(user===null||user===void 0?void 0:(_user$firstName=user.firstName)===null||_user$firstName===void 0?void 0:_user$firstName.charAt(0))||'U'}),/*#__PURE__*/_jsxs(UserDetails,{collapsed:sidebarCollapsed,children:[/*#__PURE__*/_jsx(UserName,{children:(user===null||user===void 0?void 0:user.firstName)||'User'}),/*#__PURE__*/_jsx(UserRole,{children:(user===null||user===void 0?void 0:user.role)||'Role'})]})]})]}),/*#__PURE__*/_jsxs(MainContent,{sidebarCollapsed:sidebarCollapsed,children:[/*#__PURE__*/_jsxs(PageHeader,{children:[/*#__PURE__*/_jsx(PageTitle,{children:title}),/*#__PURE__*/_jsxs(HeaderActions,{children:[headerActions,/*#__PURE__*/_jsx(\"span\",{children:new Date().toLocaleDateString()})]})]}),children]})]});};export default DashboardLayout;", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "DashboardContainer", "div", "_templateObject", "_taggedTemplateLiteral", "Sidebar", "_templateObject2", "props", "collapsed", "theme", "colors", "primary", "white", "transitions", "default", "breakpoints", "tablet", "LogoContainer", "_templateObject3", "Logo", "_templateObject4", "LogoULeft", "_templateObject5", "secondary", "LogoURight", "_templateObject6", "LogoText", "_templateObject7", "NavItem", "_templateObject8", "borderRadius", "sm", "active", "NavIcon", "_templateObject9", "NavText", "_templateObject0", "UserInfo", "_templateObject1", "UserAvatar", "_templateObject10", "textDark", "UserDetails", "_templateObject11", "UserName", "_templateObject12", "UserRole", "_templateObject13", "MainContent", "_templateObject14", "sidebarCollapsed", "<PERSON><PERSON><PERSON><PERSON>", "_templateObject15", "mediumGray", "Page<PERSON><PERSON>le", "h1", "_templateObject16", "HeaderActions", "_templateObject17", "CollapseButton", "button", "_templateObject18", "DashboardLayout", "_ref", "_user$firstName", "children", "title", "headerActions", "navigationItems", "setSidebarCollapsed", "user", "logout", "toggleSidebar", "onClick", "map", "item", "index", "icon", "label", "firstName", "char<PERSON>t", "role", "Date", "toLocaleDateString"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Layout/DashboardLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst DashboardContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n`;\n\nconst Sidebar = styled.div<{ collapsed: boolean }>`\n  width: ${props => props.collapsed ? '70px' : '250px'};\n  background-color: ${props => props.theme.colors.primary};\n  color: ${props => props.theme.colors.white};\n  padding: 20px;\n  position: fixed;\n  height: 100vh;\n  overflow-y: auto;\n  transition: ${props => props.theme.transitions.default};\n  z-index: 1000;\n\n  @media (max-width: ${props => props.theme.breakpoints.tablet}) {\n    width: ${props => props.collapsed ? '70px' : '250px'};\n    padding: 15px 10px;\n  }\n`;\n\nconst LogoContainer = styled.div<{ collapsed: boolean }>`\n  display: flex;\n  align-items: center;\n  margin-bottom: 30px;\n  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};\n`;\n\nconst Logo = styled.div`\n  width: 40px;\n  height: 40px;\n  position: relative;\n  margin-right: 10px;\n`;\n\nconst LogoULeft = styled.div`\n  width: 20px;\n  height: 20px;\n  background-color: ${props => props.theme.colors.secondary};\n  border-radius: 10px 10px 0 0;\n  position: absolute;\n  left: 5px;\n  transform: rotate(180deg);\n`;\n\nconst LogoURight = styled.div`\n  width: 20px;\n  height: 20px;\n  background-color: ${props => props.theme.colors.secondary};\n  border-radius: 10px 10px 0 0;\n  position: absolute;\n  right: 5px;\n`;\n\nconst LogoText = styled.div<{ collapsed: boolean }>`\n  font-size: 18px;\n  font-weight: 600;\n  display: ${props => props.collapsed ? 'none' : 'block'};\n`;\n\nconst NavItem = styled.div<{ active?: boolean }>`\n  padding: 12px 15px;\n  border-radius: ${props => props.theme.borderRadius.sm};\n  margin-bottom: 5px;\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.default};\n  display: flex;\n  align-items: center;\n  background-color: ${props => props.active ? 'rgba(255, 255, 255, 0.2)' : 'transparent'};\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst NavIcon = styled.div<{ collapsed: boolean }>`\n  margin-right: ${props => props.collapsed ? '0' : '10px'};\n  width: 20px;\n  text-align: center;\n  font-size: 16px;\n`;\n\nconst NavText = styled.div<{ collapsed: boolean }>`\n  display: ${props => props.collapsed ? 'none' : 'block'};\n`;\n\nconst UserInfo = styled.div<{ collapsed: boolean }>`\n  margin-top: auto;\n  padding: 15px;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  align-items: center;\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  background-color: ${props => props.theme.colors.primary};\n  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};\n`;\n\nconst UserAvatar = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: ${props => props.theme.colors.secondary};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst UserDetails = styled.div<{ collapsed: boolean }>`\n  display: ${props => props.collapsed ? 'none' : 'block'};\n`;\n\nconst UserName = styled.div`\n  font-size: 14px;\n  font-weight: 500;\n`;\n\nconst UserRole = styled.div`\n  font-size: 12px;\n  opacity: 0.8;\n`;\n\nconst MainContent = styled.div<{ sidebarCollapsed: boolean }>`\n  flex: 1;\n  margin-left: ${props => props.sidebarCollapsed ? '70px' : '250px'};\n  padding: 20px;\n  transition: ${props => props.theme.transitions.default};\n`;\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst PageTitle = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst HeaderActions = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n\nconst CollapseButton = styled.button`\n  background: none;\n  border: none;\n  color: ${props => props.theme.colors.white};\n  font-size: 18px;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title: string;\n  headerActions?: React.ReactNode;\n  navigationItems: Array<{\n    icon: string;\n    label: string;\n    active?: boolean;\n    onClick?: () => void;\n  }>;\n}\n\nconst DashboardLayout: React.FC<DashboardLayoutProps> = ({\n  children,\n  title,\n  headerActions,\n  navigationItems,\n}) => {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const { user, logout } = useAuth();\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  return (\n    <DashboardContainer>\n      <Sidebar collapsed={sidebarCollapsed}>\n        <LogoContainer collapsed={sidebarCollapsed}>\n          <Logo>\n            <LogoULeft />\n            <LogoURight />\n          </Logo>\n          <LogoText collapsed={sidebarCollapsed}>UBI Verify</LogoText>\n        </LogoContainer>\n\n        <CollapseButton onClick={toggleSidebar}>\n          {sidebarCollapsed ? '☰' : '←'}\n        </CollapseButton>\n\n        {navigationItems.map((item, index) => (\n          <NavItem key={index} active={item.active} onClick={item.onClick}>\n            <NavIcon collapsed={sidebarCollapsed}>{item.icon}</NavIcon>\n            <NavText collapsed={sidebarCollapsed}>{item.label}</NavText>\n          </NavItem>\n        ))}\n\n        <NavItem onClick={logout}>\n          <NavIcon collapsed={sidebarCollapsed}>🚪</NavIcon>\n          <NavText collapsed={sidebarCollapsed}>Logout</NavText>\n        </NavItem>\n\n        <UserInfo collapsed={sidebarCollapsed}>\n          <UserAvatar>\n            {user?.firstName?.charAt(0) || 'U'}\n          </UserAvatar>\n          <UserDetails collapsed={sidebarCollapsed}>\n            <UserName>{user?.firstName || 'User'}</UserName>\n            <UserRole>{user?.role || 'Role'}</UserRole>\n          </UserDetails>\n        </UserInfo>\n      </Sidebar>\n\n      <MainContent sidebarCollapsed={sidebarCollapsed}>\n        <PageHeader>\n          <PageTitle>{title}</PageTitle>\n          <HeaderActions>\n            {headerActions}\n            <span>{new Date().toLocaleDateString()}</span>\n          </HeaderActions>\n        </PageHeader>\n        {children}\n      </MainContent>\n    </DashboardContainer>\n  );\n};\n\nexport default DashboardLayout;\n"], "mappings": "ghBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,kBAAkB,CAAGN,MAAM,CAACO,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,kDAGpC,CAED,KAAM,CAAAC,OAAO,CAAGV,MAAM,CAACO,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,mQACfG,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,MAAM,CAAG,OAAO,CAChCD,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACC,OAAO,CAC9CJ,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACE,KAAK,CAK5BL,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACI,WAAW,CAACC,OAAO,CAGjCP,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACM,WAAW,CAACC,MAAM,CACjDT,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,MAAM,CAAG,OAAO,CAGvD,CAED,KAAM,CAAAS,aAAa,CAAGtB,MAAM,CAACO,GAAG,CAAAgB,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,qGAIXG,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,QAAQ,CAAG,YAAY,CACtE,CAED,KAAM,CAAAW,IAAI,CAAGxB,MAAM,CAACO,GAAG,CAAAkB,gBAAA,GAAAA,gBAAA,CAAAhB,sBAAA,yFAKtB,CAED,KAAM,CAAAiB,SAAS,CAAG1B,MAAM,CAACO,GAAG,CAAAoB,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,yKAGNG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACa,SAAS,CAK1D,CAED,KAAM,CAAAC,UAAU,CAAG7B,MAAM,CAACO,GAAG,CAAAuB,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,4IAGPG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACa,SAAS,CAI1D,CAED,KAAM,CAAAG,QAAQ,CAAG/B,MAAM,CAACO,GAAG,CAAAyB,gBAAA,GAAAA,gBAAA,CAAAvB,sBAAA,oEAGdG,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,MAAM,CAAG,OAAO,CACvD,CAED,KAAM,CAAAoB,OAAO,CAAGjC,MAAM,CAACO,GAAG,CAAA2B,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,gQAEPG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACqB,YAAY,CAACC,EAAE,CAGvCxB,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACI,WAAW,CAACC,OAAO,CAGlCP,KAAK,EAAIA,KAAK,CAACyB,MAAM,CAAG,0BAA0B,CAAG,aAAa,CAKvF,CAED,KAAM,CAAAC,OAAO,CAAGtC,MAAM,CAACO,GAAG,CAAAgC,gBAAA,GAAAA,gBAAA,CAAA9B,sBAAA,2FACRG,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,GAAG,CAAG,MAAM,CAIxD,CAED,KAAM,CAAA2B,OAAO,CAAGxC,MAAM,CAACO,GAAG,CAAAkC,gBAAA,GAAAA,gBAAA,CAAAhC,sBAAA,2BACbG,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,MAAM,CAAG,OAAO,CACvD,CAED,KAAM,CAAA6B,QAAQ,CAAG1C,MAAM,CAACO,GAAG,CAAAoC,gBAAA,GAAAA,gBAAA,CAAAlC,sBAAA,kQAULG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACC,OAAO,CACpCJ,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,QAAQ,CAAG,YAAY,CACtE,CAED,KAAM,CAAA+B,UAAU,CAAG5C,MAAM,CAACO,GAAG,CAAAsC,iBAAA,GAAAA,iBAAA,CAAApC,sBAAA,6NAIPG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACa,SAAS,CAMhDhB,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAAC+B,QAAQ,CAC9C,CAED,KAAM,CAAAC,WAAW,CAAG/C,MAAM,CAACO,GAAG,CAAAyC,iBAAA,GAAAA,iBAAA,CAAAvC,sBAAA,2BACjBG,KAAK,EAAIA,KAAK,CAACC,SAAS,CAAG,MAAM,CAAG,OAAO,CACvD,CAED,KAAM,CAAAoC,QAAQ,CAAGjD,MAAM,CAACO,GAAG,CAAA2C,iBAAA,GAAAA,iBAAA,CAAAzC,sBAAA,mDAG1B,CAED,KAAM,CAAA0C,QAAQ,CAAGnD,MAAM,CAACO,GAAG,CAAA6C,iBAAA,GAAAA,iBAAA,CAAA3C,sBAAA,+CAG1B,CAED,KAAM,CAAA4C,WAAW,CAAGrD,MAAM,CAACO,GAAG,CAAA+C,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,iFAEbG,KAAK,EAAIA,KAAK,CAAC2C,gBAAgB,CAAG,MAAM,CAAG,OAAO,CAEnD3C,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACI,WAAW,CAACC,OAAO,CACvD,CAED,KAAM,CAAAqC,UAAU,CAAGxD,MAAM,CAACO,GAAG,CAAAkD,iBAAA,GAAAA,iBAAA,CAAAhD,sBAAA,yKAMAG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAAC2C,UAAU,CAClE,CAED,KAAM,CAAAC,SAAS,CAAG3D,MAAM,CAAC4D,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAApD,sBAAA,kEAGhBG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACC,OAAO,CAC7C,CAED,KAAM,CAAA8C,aAAa,CAAG9D,MAAM,CAACO,GAAG,CAAAwD,iBAAA,GAAAA,iBAAA,CAAAtD,sBAAA,kEAI/B,CAED,KAAM,CAAAuD,cAAc,CAAGhE,MAAM,CAACiE,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAAzD,sBAAA,wOAGzBG,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,CAACE,KAAK,CAK5BL,KAAK,EAAIA,KAAK,CAACE,KAAK,CAACI,WAAW,CAACC,OAAO,CAKvD,CAcD,KAAM,CAAAgD,eAA+C,CAAGC,IAAA,EAKlD,KAAAC,eAAA,IALmD,CACvDC,QAAQ,CACRC,KAAK,CACLC,aAAa,CACbC,eACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAACb,gBAAgB,CAAEmB,mBAAmB,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAE4E,IAAI,CAAEC,MAAO,CAAC,CAAG3E,OAAO,CAAC,CAAC,CAElC,KAAM,CAAA4E,aAAa,CAAGA,CAAA,GAAM,CAC1BH,mBAAmB,CAAC,CAACnB,gBAAgB,CAAC,CACxC,CAAC,CAED,mBACElD,KAAA,CAACC,kBAAkB,EAAAgE,QAAA,eACjBjE,KAAA,CAACK,OAAO,EAACG,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,eACnCjE,KAAA,CAACiB,aAAa,EAACT,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,eACzCjE,KAAA,CAACmB,IAAI,EAAA8C,QAAA,eACHnE,IAAA,CAACuB,SAAS,GAAE,CAAC,cACbvB,IAAA,CAAC0B,UAAU,GAAE,CAAC,EACV,CAAC,cACP1B,IAAA,CAAC4B,QAAQ,EAAClB,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,CAAC,YAAU,CAAU,CAAC,EAC/C,CAAC,cAEhBnE,IAAA,CAAC6D,cAAc,EAACc,OAAO,CAAED,aAAc,CAAAP,QAAA,CACpCf,gBAAgB,CAAG,GAAG,CAAG,GAAG,CACf,CAAC,CAEhBkB,eAAe,CAACM,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC/B5E,KAAA,CAAC4B,OAAO,EAAaI,MAAM,CAAE2C,IAAI,CAAC3C,MAAO,CAACyC,OAAO,CAAEE,IAAI,CAACF,OAAQ,CAAAR,QAAA,eAC9DnE,IAAA,CAACmC,OAAO,EAACzB,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,CAAEU,IAAI,CAACE,IAAI,CAAU,CAAC,cAC3D/E,IAAA,CAACqC,OAAO,EAAC3B,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,CAAEU,IAAI,CAACG,KAAK,CAAU,CAAC,GAFhDF,KAGL,CACV,CAAC,cAEF5E,KAAA,CAAC4B,OAAO,EAAC6C,OAAO,CAAEF,MAAO,CAAAN,QAAA,eACvBnE,IAAA,CAACmC,OAAO,EAACzB,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,CAAC,cAAE,CAAS,CAAC,cAClDnE,IAAA,CAACqC,OAAO,EAAC3B,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,CAAC,QAAM,CAAS,CAAC,EAC/C,CAAC,cAEVjE,KAAA,CAACqC,QAAQ,EAAC7B,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,eACpCnE,IAAA,CAACyC,UAAU,EAAA0B,QAAA,CACR,CAAAK,IAAI,SAAJA,IAAI,kBAAAN,eAAA,CAAJM,IAAI,CAAES,SAAS,UAAAf,eAAA,iBAAfA,eAAA,CAAiBgB,MAAM,CAAC,CAAC,CAAC,GAAI,GAAG,CACxB,CAAC,cACbhF,KAAA,CAAC0C,WAAW,EAAClC,SAAS,CAAE0C,gBAAiB,CAAAe,QAAA,eACvCnE,IAAA,CAAC8C,QAAQ,EAAAqB,QAAA,CAAE,CAAAK,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,GAAI,MAAM,CAAW,CAAC,cAChDjF,IAAA,CAACgD,QAAQ,EAAAmB,QAAA,CAAE,CAAAK,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEW,IAAI,GAAI,MAAM,CAAW,CAAC,EAChC,CAAC,EACN,CAAC,EACJ,CAAC,cAEVjF,KAAA,CAACgD,WAAW,EAACE,gBAAgB,CAAEA,gBAAiB,CAAAe,QAAA,eAC9CjE,KAAA,CAACmD,UAAU,EAAAc,QAAA,eACTnE,IAAA,CAACwD,SAAS,EAAAW,QAAA,CAAEC,KAAK,CAAY,CAAC,cAC9BlE,KAAA,CAACyD,aAAa,EAAAQ,QAAA,EACXE,aAAa,cACdrE,IAAA,SAAAmE,QAAA,CAAO,GAAI,CAAAiB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAO,CAAC,EACjC,CAAC,EACN,CAAC,CACZlB,QAAQ,EACE,CAAC,EACI,CAAC,CAEzB,CAAC,CAED,cAAe,CAAAH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}