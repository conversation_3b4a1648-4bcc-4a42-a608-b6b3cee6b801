{"ast": null, "code": "import _objectSpread from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_URL||'https://localhost:59358/api';// Types matching the API DTOs\nclass ApiService{constructor(){this.api=void 0;this.baseURL=void 0;this.baseURL=API_BASE_URL;this.api=axios.create({baseURL:this.baseURL,timeout:30000,headers:{'Content-Type':'application/json'}});// Request interceptor to add auth token\nthis.api.interceptors.request.use(config=>{const token=localStorage.getItem('authToken');if(token){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// Response interceptor for error handling and token refresh\nthis.api.interceptors.response.use(response=>response,async error=>{var _error$response;const originalRequest=error.config;if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401&&!originalRequest._retry){originalRequest._retry=true;const refreshToken=localStorage.getItem('refreshToken');const token=localStorage.getItem('authToken');if(refreshToken&&token){try{const response=await axios.post(\"\".concat(this.baseURL,\"/auth/refresh-token\"),{token,refreshToken});if(response.data.success){localStorage.setItem('authToken',response.data.token);localStorage.setItem('refreshToken',response.data.refreshToken);localStorage.setItem('user',JSON.stringify(response.data.user));// Retry original request with new token\noriginalRequest.headers.Authorization=\"Bearer \".concat(response.data.token);return this.api(originalRequest);}}catch(refreshError){console.error('Token refresh failed:',refreshError);}}// If refresh fails, logout user\nthis.logoutUser();}return Promise.reject(error);});}logoutUser(){localStorage.removeItem('authToken');localStorage.removeItem('refreshToken');localStorage.removeItem('user');window.location.href='/login';}// Authentication\nasync login(username,password,role){try{const response=await this.api.post('/auth/login',{username,password,role});return response.data;}catch(error){var _error$response2,_error$response2$data;console.error('Login API error:',error);throw new Error(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||'Login failed');}}async logout(){try{await this.api.post('/auth/logout');}catch(error){console.error('Logout API error:',error);}finally{this.logoutUser();}}async getCurrentUser(){try{const response=await this.api.get('/auth/me');return response.data;}catch(error){var _error$response3,_error$response3$data;console.error('Get current user API error:',error);throw new Error(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.message)||'Failed to get user information');}}// Leads\nasync getLeads(){let pageNumber=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;let pageSize=arguments.length>1&&arguments[1]!==undefined?arguments[1]:10;let status=arguments.length>2?arguments[2]:undefined;let assignedTo=arguments.length>3?arguments[3]:undefined;try{const params=new URLSearchParams({pageNumber:pageNumber.toString(),pageSize:pageSize.toString()});if(status)params.append('status',status);if(assignedTo)params.append('assignedTo',assignedTo.toString());const response=await this.api.get(\"/leads?\".concat(params.toString()));const data=response.data;// Ensure totalCount is available for backward compatibility\nreturn _objectSpread(_objectSpread({},data),{},{totalCount:data.totalRecords||data.totalCount||0});}catch(error){var _error$response4,_error$response4$data;console.error('Get leads API error:',error);throw new Error(((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.message)||'Failed to fetch leads');}}async getLead(id){try{const response=await this.api.get(\"/leads/\".concat(id));return response.data;}catch(error){var _error$response5,_error$response5$data;console.error('Get lead API error:',error);throw new Error(((_error$response5=error.response)===null||_error$response5===void 0?void 0:(_error$response5$data=_error$response5.data)===null||_error$response5$data===void 0?void 0:_error$response5$data.message)||'Failed to fetch lead');}}async createLead(leadData){try{const response=await this.api.post('/leads',leadData);return response.data;}catch(error){var _error$response6,_error$response6$data;console.error('Create lead API error:',error);throw new Error(((_error$response6=error.response)===null||_error$response6===void 0?void 0:(_error$response6$data=_error$response6.data)===null||_error$response6$data===void 0?void 0:_error$response6$data.message)||'Failed to create lead');}}async updateLeadStatus(id,status,comments,rejectionReason){try{await this.api.put(\"/leads/\".concat(id,\"/status\"),{status,comments,rejectionReason});}catch(error){var _error$response7,_error$response7$data;console.error('Update lead status API error:',error);throw new Error(((_error$response7=error.response)===null||_error$response7===void 0?void 0:(_error$response7$data=_error$response7.data)===null||_error$response7$data===void 0?void 0:_error$response7$data.message)||'Failed to update lead status');}}async assignLead(id,agentId,comments){try{await this.api.put(\"/leads/\".concat(id,\"/assign\"),{agentId,comments});}catch(error){var _error$response8,_error$response8$data;console.error('Assign lead API error:',error);throw new Error(((_error$response8=error.response)===null||_error$response8===void 0?void 0:(_error$response8$data=_error$response8.data)===null||_error$response8$data===void 0?void 0:_error$response8$data.message)||'Failed to assign lead');}}async getDashboardStats(){try{const response=await this.api.get('/leads/dashboard-stats');return response.data;}catch(error){var _error$response9,_error$response9$data;console.error('Get dashboard stats API error:',error);throw new Error(((_error$response9=error.response)===null||_error$response9===void 0?void 0:(_error$response9$data=_error$response9.data)===null||_error$response9$data===void 0?void 0:_error$response9$data.message)||'Failed to fetch dashboard statistics');}}// Verification\nasync getAgentDashboardStats(){try{const response=await this.api.get('/verification/agent/dashboard-stats');return response.data;}catch(error){var _error$response0,_error$response0$data;console.error('Get agent dashboard stats API error:',error);throw new Error(((_error$response0=error.response)===null||_error$response0===void 0?void 0:(_error$response0$data=_error$response0.data)===null||_error$response0$data===void 0?void 0:_error$response0$data.message)||'Failed to fetch agent dashboard statistics');}}async getSupervisorDashboardStats(){try{const response=await this.api.get('/verification/supervisor/dashboard-stats');return response.data;}catch(error){var _error$response1,_error$response1$data;console.error('Get supervisor dashboard stats API error:',error);throw new Error(((_error$response1=error.response)===null||_error$response1===void 0?void 0:(_error$response1$data=_error$response1.data)===null||_error$response1$data===void 0?void 0:_error$response1$data.message)||'Failed to fetch supervisor dashboard statistics');}}async saveVerificationData(leadId,verificationData){try{const response=await this.api.post(\"/verification/leads/\".concat(leadId),verificationData);return response.data;}catch(error){var _error$response10,_error$response10$dat;console.error('Save verification data API error:',error);throw new Error(((_error$response10=error.response)===null||_error$response10===void 0?void 0:(_error$response10$dat=_error$response10.data)===null||_error$response10$dat===void 0?void 0:_error$response10$dat.message)||'Failed to save verification data');}}async updateVerificationData(leadId,verificationData){try{const response=await this.api.put(\"/verification/leads/\".concat(leadId),verificationData);return response.data;}catch(error){var _error$response11,_error$response11$dat;console.error('Update verification data API error:',error);throw new Error(((_error$response11=error.response)===null||_error$response11===void 0?void 0:(_error$response11$dat=_error$response11.data)===null||_error$response11$dat===void 0?void 0:_error$response11$dat.message)||'Failed to update verification data');}}async getVerificationData(leadId){try{const response=await this.api.get(\"/verification/leads/\".concat(leadId));return response.data;}catch(error){var _error$response12,_error$response12$dat;console.error('Get verification data API error:',error);throw new Error(((_error$response12=error.response)===null||_error$response12===void 0?void 0:(_error$response12$dat=_error$response12.data)===null||_error$response12$dat===void 0?void 0:_error$response12$dat.message)||'Failed to fetch verification data');}}// Documents\nasync getDocumentTypes(){try{const response=await this.api.get('/documents/types');return response.data;}catch(error){var _error$response13,_error$response13$dat;console.error('Get document types API error:',error);throw new Error(((_error$response13=error.response)===null||_error$response13===void 0?void 0:(_error$response13$dat=_error$response13.data)===null||_error$response13$dat===void 0?void 0:_error$response13$dat.message)||'Failed to fetch document types');}}async uploadDocument(leadId,documentType,file){try{const formData=new FormData();formData.append('file',file);formData.append('documentTypeId','1');// Default document type ID\nformData.append('documentCategory',documentType);const response=await this.api.post(\"/documents/leads/\".concat(leadId,\"/verification-documents\"),formData,{headers:{'Content-Type':'multipart/form-data'}});return response.data;}catch(error){var _error$response14,_error$response14$dat;console.error('Upload document API error:',error);throw new Error(((_error$response14=error.response)===null||_error$response14===void 0?void 0:(_error$response14$dat=_error$response14.data)===null||_error$response14$dat===void 0?void 0:_error$response14$dat.message)||'Failed to upload document');}}async getVerificationDocuments(leadId){try{const response=await this.api.get(\"/documents/leads/\".concat(leadId,\"/verification-documents\"));return response.data;}catch(error){var _error$response15,_error$response15$dat;console.error('Get verification documents API error:',error);throw new Error(((_error$response15=error.response)===null||_error$response15===void 0?void 0:(_error$response15$dat=_error$response15.data)===null||_error$response15$dat===void 0?void 0:_error$response15$dat.message)||'Failed to fetch verification documents');}}// Users (for Admin)\nasync getUsers(){try{const response=await this.api.get('/users');return response.data;}catch(error){console.error('Get users API error:',error);// Return mock data for now since UsersController doesn't exist yet\nreturn[{userId:1,username:'agent1',firstName:'John',lastName:'Agent',email:'<EMAIL>',role:'Agent',isActive:true,createdDate:'2024-01-01T00:00:00Z',lastLoginDate:'2024-01-16T08:30:00Z'},{userId:2,username:'supervisor1',firstName:'Jane',lastName:'Supervisor',email:'<EMAIL>',role:'Supervisor',isActive:true,createdDate:'2024-01-01T00:00:00Z',lastLoginDate:'2024-01-16T09:15:00Z'},{userId:3,username:'admin1',firstName:'Admin',lastName:'User',email:'<EMAIL>',role:'Admin',isActive:true,createdDate:'2024-01-01T00:00:00Z',lastLoginDate:'2024-01-16T10:00:00Z'}];}}async createUser(userData){try{const response=await this.api.post('/users',userData);return response.data;}catch(error){var _error$response16,_error$response16$dat;console.error('Create user API error:',error);throw new Error(((_error$response16=error.response)===null||_error$response16===void 0?void 0:(_error$response16$dat=_error$response16.data)===null||_error$response16$dat===void 0?void 0:_error$response16$dat.message)||'Failed to create user');}}async updateUser(userId,userData){try{const response=await this.api.put(\"/users/\".concat(userId),userData);return response.data;}catch(error){var _error$response17,_error$response17$dat;console.error('Update user API error:',error);throw new Error(((_error$response17=error.response)===null||_error$response17===void 0?void 0:(_error$response17$dat=_error$response17.data)===null||_error$response17$dat===void 0?void 0:_error$response17$dat.message)||'Failed to update user');}}async toggleUserStatus(userId){try{await this.api.put(\"/users/\".concat(userId,\"/toggle-status\"));}catch(error){var _error$response18,_error$response18$dat;console.error('Toggle user status API error:',error);throw new Error(((_error$response18=error.response)===null||_error$response18===void 0?void 0:(_error$response18$dat=_error$response18.data)===null||_error$response18$dat===void 0?void 0:_error$response18$dat.message)||'Failed to toggle user status');}}}// Export singleton instance\nexport const apiService=new ApiService();export default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "api", "baseURL", "create", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "data", "success", "setItem", "JSON", "stringify", "user", "refreshError", "console", "logoutUser", "removeItem", "window", "location", "href", "login", "username", "password", "role", "_error$response2", "_error$response2$data", "Error", "message", "logout", "getCurrentUser", "get", "_error$response3", "_error$response3$data", "getLeads", "pageNumber", "arguments", "length", "undefined", "pageSize", "assignedTo", "params", "URLSearchParams", "toString", "append", "_objectSpread", "totalCount", "totalRecords", "_error$response4", "_error$response4$data", "getLead", "id", "_error$response5", "_error$response5$data", "createLead", "leadData", "_error$response6", "_error$response6$data", "updateLeadStatus", "comments", "rejectionReason", "put", "_error$response7", "_error$response7$data", "assignLead", "agentId", "_error$response8", "_error$response8$data", "getDashboardStats", "_error$response9", "_error$response9$data", "getAgentDashboardStats", "_error$response0", "_error$response0$data", "getSupervisorDashboardStats", "_error$response1", "_error$response1$data", "saveVerificationData", "leadId", "verificationData", "_error$response10", "_error$response10$dat", "updateVerificationData", "_error$response11", "_error$response11$dat", "getVerificationData", "_error$response12", "_error$response12$dat", "getDocumentTypes", "_error$response13", "_error$response13$dat", "uploadDocument", "documentType", "file", "formData", "FormData", "_error$response14", "_error$response14$dat", "getVerificationDocuments", "_error$response15", "_error$response15$dat", "getUsers", "userId", "firstName", "lastName", "email", "isActive", "createdDate", "lastLoginDate", "createUser", "userData", "_error$response16", "_error$response16$dat", "updateUser", "_error$response17", "_error$response17$dat", "toggleUserStatus", "_error$response18", "_error$response18$dat", "apiService"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/services/apiService.ts"], "sourcesContent": ["import axios, { AxiosInstance } from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:59358/api';\n\n// Types matching the API DTOs\nexport interface User {\n  userId: number;\n  username: string;\n  email: string;\n  role: 'Agent' | 'Supervisor' | 'Admin';\n  firstName: string;\n  lastName: string;\n  phoneNumber?: string;\n  isActive: boolean;\n  createdDate: string;\n  lastLoginDate?: string;\n}\n\nexport interface LoginResponse {\n  success: boolean;\n  message: string;\n  token?: string;\n  refreshToken?: string;\n  user?: User;\n  expiresAt?: string;\n}\n\nexport interface Lead {\n  leadId: number;\n  customerName: string;\n  mobileNumber: string;\n  loanType: string;\n  status: string;\n  createdDate: string;\n  assignedDate?: string;\n  startedDate?: string;\n  submittedDate?: string;\n  reviewedDate?: string;\n  approvedDate?: string;\n  rejectedDate?: string;\n  rejectionReason?: string;\n  reviewComments?: string;\n  creator?: User;\n  assignedAgent?: User;\n  reviewer?: User;\n  addresses: Address[];\n  statusHistory: StatusHistory[];\n  documents: Document[];\n  croppedImages: CroppedImage[];\n  verificationData?: VerificationData;\n}\n\nexport interface LeadListItem {\n  leadId: number;\n  customerName: string;\n  mobileNumber: string;\n  loanType: string;\n  status: string;\n  createdDate: string;\n  assignedDate?: string;\n  submittedDate?: string;\n  createdByName?: string;\n  assignedToName?: string;\n  reviewedByName?: string;\n  documentCount: number;\n  croppedImageCount: number;\n}\n\nexport interface PagedResult<T> {\n  data: T[];\n  totalRecords: number;\n  totalCount: number; // Alias for totalRecords for backward compatibility\n  pageNumber: number;\n  pageSize: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nexport interface Address {\n  addressId: number;\n  addressType: string;\n  address: string;\n  pincode: string;\n  state: string;\n  district: string;\n  landmark?: string;\n  createdDate: string;\n}\n\nexport interface StatusHistory {\n  historyId: number;\n  status: string;\n  timestamp: string;\n  comments?: string;\n  updatedByUser?: User;\n}\n\nexport interface Document {\n  documentId: number;\n  leadId: number;\n  documentTypeName: string;\n  fileName: string;\n  originalFileName: string;\n  filePath: string;\n  fileSize: number;\n  mimeType: string;\n  uploadedDate: string;\n  uploadedByName?: string;\n  isActive: boolean;\n}\n\nexport interface CroppedImage {\n  imageId: number;\n  leadId: number;\n  fileName: string;\n  originalFileName: string;\n  filePath: string;\n  fileSize: number;\n  mimeType: string;\n  cropData?: string;\n  pageNumber?: number;\n  createdDate: string;\n  createdByName?: string;\n}\n\nexport interface VerificationData {\n  verificationId?: number;\n  leadId?: number;\n  agentName: string;\n  agentContact: string;\n  addressConfirmed?: string;\n  personMet?: string;\n  relationship?: string;\n  officeAddress?: string;\n  officeState?: string;\n  officeDistrict?: string;\n  officePincode?: string;\n  landmark?: string;\n  companyType?: string;\n  businessNature?: string;\n  establishmentYear?: number;\n  employeesCount?: string;\n  grossSalary?: number;\n  netSalary?: number;\n  proofType?: string;\n  verificationDate?: string;\n  additionalNotes?: string;\n}\n\nexport interface DashboardStats {\n  totalLeads: number;\n  newLeads: number;\n  assignedLeads: number;\n  inProgressLeads: number;\n  pendingReviewLeads: number;\n  approvedLeads: number;\n  rejectedLeads: number;\n  completedLeads: number;\n  pendingLeads: number;\n}\n\nexport interface AgentDashboardStats {\n  pendingLeads: number;\n  inProgressLeads: number;\n  completedLeads: number;\n  rejectedLeads: number;\n  totalAssigned: number;\n}\n\nexport interface SupervisorDashboardStats {\n  pendingReviews: number;\n  approvedToday: number;\n  rejectedToday: number;\n  totalReviewed: number;\n  approvalRate: number;\n  agentPerformance: AgentPerformance[];\n  totalLeads: number;\n  completedLeads: number;\n}\n\nexport interface AgentPerformance {\n  agentId: number;\n  agentName: string;\n  assignedCount: number;\n  completedCount: number;\n  approvedCount: number;\n  rejectedCount: number;\n  completionRate: number;\n  approvalRate: number;\n}\n\nexport interface CreateLeadRequest {\n  customerName: string;\n  mobileNumber: string;\n  loanType: string;\n  addresses: {\n    type: string;\n    address: string;\n    pincode: string;\n    state: string;\n    district: string;\n    landmark?: string;\n  }[];\n}\n\nexport interface DocumentType {\n  documentTypeId: number;\n  typeName: string;\n  description?: string;\n  isActive: boolean;\n}\n\nexport interface FileUploadResult {\n  success: boolean;\n  message: string;\n  fileName?: string;\n  filePath?: string;\n  fileSize: number;\n  documentId?: number;\n  imageId?: number;\n}\n\nclass ApiService {\n  private api: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = API_BASE_URL;\n\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('authToken');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor for error handling and token refresh\n    this.api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          const refreshToken = localStorage.getItem('refreshToken');\n          const token = localStorage.getItem('authToken');\n\n          if (refreshToken && token) {\n            try {\n              const response = await axios.post(`${this.baseURL}/auth/refresh-token`, {\n                token,\n                refreshToken\n              });\n\n              if (response.data.success) {\n                localStorage.setItem('authToken', response.data.token);\n                localStorage.setItem('refreshToken', response.data.refreshToken);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n\n                // Retry original request with new token\n                originalRequest.headers.Authorization = `Bearer ${response.data.token}`;\n                return this.api(originalRequest);\n              }\n            } catch (refreshError) {\n              console.error('Token refresh failed:', refreshError);\n            }\n          }\n\n          // If refresh fails, logout user\n          this.logoutUser();\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private logoutUser() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n\n  // Authentication\n  async login(username: string, password: string, role: string): Promise<LoginResponse> {\n    try {\n      const response = await this.api.post('/auth/login', {\n        username,\n        password,\n        role,\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Login API error:', error);\n      throw new Error(error.response?.data?.message || 'Login failed');\n    }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout API error:', error);\n    } finally {\n      this.logoutUser();\n    }\n  }\n\n  async getCurrentUser(): Promise<User> {\n    try {\n      const response = await this.api.get('/auth/me');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get current user API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to get user information');\n    }\n  }\n\n  // Leads\n  async getLeads(pageNumber = 1, pageSize = 10, status?: string, assignedTo?: number): Promise<PagedResult<LeadListItem>> {\n    try {\n      const params = new URLSearchParams({\n        pageNumber: pageNumber.toString(),\n        pageSize: pageSize.toString(),\n      });\n\n      if (status) params.append('status', status);\n      if (assignedTo) params.append('assignedTo', assignedTo.toString());\n\n      const response = await this.api.get(`/leads?${params.toString()}`);\n      const data = response.data;\n\n      // Ensure totalCount is available for backward compatibility\n      return {\n        ...data,\n        totalCount: data.totalRecords || data.totalCount || 0\n      };\n    } catch (error: any) {\n      console.error('Get leads API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch leads');\n    }\n  }\n\n  async getLead(id: number): Promise<Lead> {\n    try {\n      const response = await this.api.get(`/leads/${id}`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Get lead API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch lead');\n    }\n  }\n\n  async createLead(leadData: CreateLeadRequest): Promise<Lead> {\n    try {\n      const response = await this.api.post('/leads', leadData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Create lead API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to create lead');\n    }\n  }\n\n  async updateLeadStatus(id: number, status: string, comments?: string, rejectionReason?: string): Promise<void> {\n    try {\n      await this.api.put(`/leads/${id}/status`, {\n        status,\n        comments,\n        rejectionReason,\n      });\n    } catch (error: any) {\n      console.error('Update lead status API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to update lead status');\n    }\n  }\n\n  async assignLead(id: number, agentId: number, comments?: string): Promise<void> {\n    try {\n      await this.api.put(`/leads/${id}/assign`, {\n        agentId,\n        comments,\n      });\n    } catch (error: any) {\n      console.error('Assign lead API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to assign lead');\n    }\n  }\n\n  async getDashboardStats(): Promise<DashboardStats> {\n    try {\n      const response = await this.api.get('/leads/dashboard-stats');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get dashboard stats API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard statistics');\n    }\n  }\n\n  // Verification\n  async getAgentDashboardStats(): Promise<AgentDashboardStats> {\n    try {\n      const response = await this.api.get('/verification/agent/dashboard-stats');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get agent dashboard stats API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch agent dashboard statistics');\n    }\n  }\n\n  async getSupervisorDashboardStats(): Promise<SupervisorDashboardStats> {\n    try {\n      const response = await this.api.get('/verification/supervisor/dashboard-stats');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get supervisor dashboard stats API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch supervisor dashboard statistics');\n    }\n  }\n\n  async saveVerificationData(leadId: number, verificationData: VerificationData): Promise<VerificationData> {\n    try {\n      const response = await this.api.post(`/verification/leads/${leadId}`, verificationData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Save verification data API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to save verification data');\n    }\n  }\n\n  async updateVerificationData(leadId: number, verificationData: VerificationData): Promise<VerificationData> {\n    try {\n      const response = await this.api.put(`/verification/leads/${leadId}`, verificationData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Update verification data API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to update verification data');\n    }\n  }\n\n  async getVerificationData(leadId: number): Promise<VerificationData> {\n    try {\n      const response = await this.api.get(`/verification/leads/${leadId}`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Get verification data API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch verification data');\n    }\n  }\n\n  // Documents\n  async getDocumentTypes(): Promise<DocumentType[]> {\n    try {\n      const response = await this.api.get('/documents/types');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get document types API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch document types');\n    }\n  }\n\n  async uploadDocument(leadId: number, documentType: string, file: File): Promise<FileUploadResult> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('documentTypeId', '1'); // Default document type ID\n      formData.append('documentCategory', documentType);\n\n      const response = await this.api.post(`/documents/leads/${leadId}/verification-documents`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Upload document API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to upload document');\n    }\n  }\n\n  async getVerificationDocuments(leadId: number): Promise<Document[]> {\n    try {\n      const response = await this.api.get(`/documents/leads/${leadId}/verification-documents`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Get verification documents API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch verification documents');\n    }\n  }\n\n  // Users (for Admin)\n  async getUsers(): Promise<User[]> {\n    try {\n      const response = await this.api.get('/users');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get users API error:', error);\n      // Return mock data for now since UsersController doesn't exist yet\n      return [\n        {\n          userId: 1,\n          username: 'agent1',\n          firstName: 'John',\n          lastName: 'Agent',\n          email: '<EMAIL>',\n          role: 'Agent',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T08:30:00Z',\n        },\n        {\n          userId: 2,\n          username: 'supervisor1',\n          firstName: 'Jane',\n          lastName: 'Supervisor',\n          email: '<EMAIL>',\n          role: 'Supervisor',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T09:15:00Z',\n        },\n        {\n          userId: 3,\n          username: 'admin1',\n          firstName: 'Admin',\n          lastName: 'User',\n          email: '<EMAIL>',\n          role: 'Admin',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T10:00:00Z',\n        },\n      ];\n    }\n  }\n\n  async createUser(userData: any): Promise<User> {\n    try {\n      const response = await this.api.post('/users', userData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Create user API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to create user');\n    }\n  }\n\n  async updateUser(userId: number, userData: any): Promise<User> {\n    try {\n      const response = await this.api.put(`/users/${userId}`, userData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Update user API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to update user');\n    }\n  }\n\n  async toggleUserStatus(userId: number): Promise<void> {\n    try {\n      await this.api.put(`/users/${userId}/toggle-status`);\n    } catch (error: any) {\n      console.error('Toggle user status API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to toggle user status');\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "+JAAA,MAAO,CAAAA,KAAK,KAAyB,OAAO,CAE5C,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,6BAA6B,CAEnF;AA2NA,KAAM,CAAAC,UAAW,CAIfC,WAAWA,CAAA,CAAG,MAHNC,GAAG,aACHC,OAAO,QAGb,IAAI,CAACA,OAAO,CAAGP,YAAY,CAE3B,IAAI,CAACM,GAAG,CAAGP,KAAK,CAACS,MAAM,CAAC,CACtBD,OAAO,CAAE,IAAI,CAACA,OAAO,CACrBE,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACA,IAAI,CAACJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC/C,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAM,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,IAAI,CAACd,GAAG,CAACK,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC/BU,QAAQ,EAAKA,QAAQ,CACtB,KAAO,CAAAH,KAAK,EAAK,KAAAI,eAAA,CACf,KAAM,CAAAC,eAAe,CAAGL,KAAK,CAACN,MAAM,CAEpC,GAAI,EAAAU,eAAA,CAAAJ,KAAK,CAACG,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBE,MAAM,IAAK,GAAG,EAAI,CAACD,eAAe,CAACE,MAAM,CAAE,CAC7DF,eAAe,CAACE,MAAM,CAAG,IAAI,CAE7B,KAAM,CAAAC,YAAY,CAAGZ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACzD,KAAM,CAAAF,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAE/C,GAAIW,YAAY,EAAIb,KAAK,CAAE,CACzB,GAAI,CACF,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAxB,KAAK,CAAC8B,IAAI,IAAAV,MAAA,CAAI,IAAI,CAACZ,OAAO,wBAAuB,CACtEQ,KAAK,CACLa,YACF,CAAC,CAAC,CAEF,GAAIL,QAAQ,CAACO,IAAI,CAACC,OAAO,CAAE,CACzBf,YAAY,CAACgB,OAAO,CAAC,WAAW,CAAET,QAAQ,CAACO,IAAI,CAACf,KAAK,CAAC,CACtDC,YAAY,CAACgB,OAAO,CAAC,cAAc,CAAET,QAAQ,CAACO,IAAI,CAACF,YAAY,CAAC,CAChEZ,YAAY,CAACgB,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACX,QAAQ,CAACO,IAAI,CAACK,IAAI,CAAC,CAAC,CAEhE;AACAV,eAAe,CAACf,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaI,QAAQ,CAACO,IAAI,CAACf,KAAK,CAAE,CACvE,MAAO,KAAI,CAACT,GAAG,CAACmB,eAAe,CAAC,CAClC,CACF,CAAE,MAAOW,YAAY,CAAE,CACrBC,OAAO,CAACjB,KAAK,CAAC,uBAAuB,CAAEgB,YAAY,CAAC,CACtD,CACF,CAEA;AACA,IAAI,CAACE,UAAU,CAAC,CAAC,CACnB,CAEA,MAAO,CAAAjB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CACH,CAEQkB,UAAUA,CAAA,CAAG,CACnBtB,YAAY,CAACuB,UAAU,CAAC,WAAW,CAAC,CACpCvB,YAAY,CAACuB,UAAU,CAAC,cAAc,CAAC,CACvCvB,YAAY,CAACuB,UAAU,CAAC,MAAM,CAAC,CAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CACjC,CAEA;AACA,KAAM,CAAAC,KAAKA,CAACC,QAAgB,CAAEC,QAAgB,CAAEC,IAAY,CAA0B,CACpF,GAAI,CACF,KAAM,CAAAvB,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAACuB,IAAI,CAAC,aAAa,CAAE,CAClDe,QAAQ,CACRC,QAAQ,CACRC,IACF,CAAC,CAAC,CACF,MAAO,CAAAvB,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAA2B,gBAAA,CAAAC,qBAAA,CACnBX,OAAO,CAACjB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxC,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAF,gBAAA,CAAA3B,KAAK,CAACG,QAAQ,UAAAwB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBjB,IAAI,UAAAkB,qBAAA,iBAApBA,qBAAA,CAAsBE,OAAO,GAAI,cAAc,CAAC,CAClE,CACF,CAEA,KAAM,CAAAC,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,KAAI,CAAC7C,GAAG,CAACuB,IAAI,CAAC,cAAc,CAAC,CACrC,CAAE,MAAOT,KAAK,CAAE,CACdiB,OAAO,CAACjB,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CAC3C,CAAC,OAAS,CACR,IAAI,CAACkB,UAAU,CAAC,CAAC,CACnB,CACF,CAEA,KAAM,CAAAc,cAAcA,CAAA,CAAkB,CACpC,GAAI,CACF,KAAM,CAAA7B,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,CAAC,UAAU,CAAC,CAC/C,MAAO,CAAA9B,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAkC,gBAAA,CAAAC,qBAAA,CACnBlB,OAAO,CAACjB,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAK,gBAAA,CAAAlC,KAAK,CAACG,QAAQ,UAAA+B,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxB,IAAI,UAAAyB,qBAAA,iBAApBA,qBAAA,CAAsBL,OAAO,GAAI,gCAAgC,CAAC,CACpF,CACF,CAEA;AACA,KAAM,CAAAM,QAAQA,CAAA,CAA0G,IAAzG,CAAAC,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,QAAQ,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IAAE,CAAAhC,MAAe,CAAAgC,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,IAAE,CAAAE,UAAmB,CAAAJ,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAChF,GAAI,CACF,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CACjCP,UAAU,CAAEA,UAAU,CAACQ,QAAQ,CAAC,CAAC,CACjCJ,QAAQ,CAAEA,QAAQ,CAACI,QAAQ,CAAC,CAC9B,CAAC,CAAC,CAEF,GAAIvC,MAAM,CAAEqC,MAAM,CAACG,MAAM,CAAC,QAAQ,CAAExC,MAAM,CAAC,CAC3C,GAAIoC,UAAU,CAAEC,MAAM,CAACG,MAAM,CAAC,YAAY,CAAEJ,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC,CAElE,KAAM,CAAA1C,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,WAAAlC,MAAA,CAAW4C,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAE,CAAC,CAClE,KAAM,CAAAnC,IAAI,CAAGP,QAAQ,CAACO,IAAI,CAE1B;AACA,OAAAqC,aAAA,CAAAA,aAAA,IACKrC,IAAI,MACPsC,UAAU,CAAEtC,IAAI,CAACuC,YAAY,EAAIvC,IAAI,CAACsC,UAAU,EAAI,CAAC,GAEzD,CAAE,MAAOhD,KAAU,CAAE,KAAAkD,gBAAA,CAAAC,qBAAA,CACnBlC,OAAO,CAACjB,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAqB,gBAAA,CAAAlD,KAAK,CAACG,QAAQ,UAAA+C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxC,IAAI,UAAAyC,qBAAA,iBAApBA,qBAAA,CAAsBrB,OAAO,GAAI,uBAAuB,CAAC,CAC3E,CACF,CAEA,KAAM,CAAAsB,OAAOA,CAACC,EAAU,CAAiB,CACvC,GAAI,CACF,KAAM,CAAAlD,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,WAAAlC,MAAA,CAAWsD,EAAE,CAAE,CAAC,CACnD,MAAO,CAAAlD,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAsD,gBAAA,CAAAC,qBAAA,CACnBtC,OAAO,CAACjB,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAyB,gBAAA,CAAAtD,KAAK,CAACG,QAAQ,UAAAmD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB5C,IAAI,UAAA6C,qBAAA,iBAApBA,qBAAA,CAAsBzB,OAAO,GAAI,sBAAsB,CAAC,CAC1E,CACF,CAEA,KAAM,CAAA0B,UAAUA,CAACC,QAA2B,CAAiB,CAC3D,GAAI,CACF,KAAM,CAAAtD,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAACuB,IAAI,CAAC,QAAQ,CAAEgD,QAAQ,CAAC,CACxD,MAAO,CAAAtD,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAA0D,gBAAA,CAAAC,qBAAA,CACnB1C,OAAO,CAACjB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAA6B,gBAAA,CAAA1D,KAAK,CAACG,QAAQ,UAAAuD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBhD,IAAI,UAAAiD,qBAAA,iBAApBA,qBAAA,CAAsB7B,OAAO,GAAI,uBAAuB,CAAC,CAC3E,CACF,CAEA,KAAM,CAAA8B,gBAAgBA,CAACP,EAAU,CAAE/C,MAAc,CAAEuD,QAAiB,CAAEC,eAAwB,CAAiB,CAC7G,GAAI,CACF,KAAM,KAAI,CAAC5E,GAAG,CAAC6E,GAAG,WAAAhE,MAAA,CAAWsD,EAAE,YAAW,CACxC/C,MAAM,CACNuD,QAAQ,CACRC,eACF,CAAC,CAAC,CACJ,CAAE,MAAO9D,KAAU,CAAE,KAAAgE,gBAAA,CAAAC,qBAAA,CACnBhD,OAAO,CAACjB,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAmC,gBAAA,CAAAhE,KAAK,CAACG,QAAQ,UAAA6D,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBtD,IAAI,UAAAuD,qBAAA,iBAApBA,qBAAA,CAAsBnC,OAAO,GAAI,8BAA8B,CAAC,CAClF,CACF,CAEA,KAAM,CAAAoC,UAAUA,CAACb,EAAU,CAAEc,OAAe,CAAEN,QAAiB,CAAiB,CAC9E,GAAI,CACF,KAAM,KAAI,CAAC3E,GAAG,CAAC6E,GAAG,WAAAhE,MAAA,CAAWsD,EAAE,YAAW,CACxCc,OAAO,CACPN,QACF,CAAC,CAAC,CACJ,CAAE,MAAO7D,KAAU,CAAE,KAAAoE,gBAAA,CAAAC,qBAAA,CACnBpD,OAAO,CAACjB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAuC,gBAAA,CAAApE,KAAK,CAACG,QAAQ,UAAAiE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB1D,IAAI,UAAA2D,qBAAA,iBAApBA,qBAAA,CAAsBvC,OAAO,GAAI,uBAAuB,CAAC,CAC3E,CACF,CAEA,KAAM,CAAAwC,iBAAiBA,CAAA,CAA4B,CACjD,GAAI,CACF,KAAM,CAAAnE,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,CAAC,wBAAwB,CAAC,CAC7D,MAAO,CAAA9B,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAuE,gBAAA,CAAAC,qBAAA,CACnBvD,OAAO,CAACjB,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAA0C,gBAAA,CAAAvE,KAAK,CAACG,QAAQ,UAAAoE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB7D,IAAI,UAAA8D,qBAAA,iBAApBA,qBAAA,CAAsB1C,OAAO,GAAI,sCAAsC,CAAC,CAC1F,CACF,CAEA;AACA,KAAM,CAAA2C,sBAAsBA,CAAA,CAAiC,CAC3D,GAAI,CACF,KAAM,CAAAtE,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,CAAC,qCAAqC,CAAC,CAC1E,MAAO,CAAA9B,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAA0E,gBAAA,CAAAC,qBAAA,CACnB1D,OAAO,CAACjB,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC5D,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAA6C,gBAAA,CAAA1E,KAAK,CAACG,QAAQ,UAAAuE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBhE,IAAI,UAAAiE,qBAAA,iBAApBA,qBAAA,CAAsB7C,OAAO,GAAI,4CAA4C,CAAC,CAChG,CACF,CAEA,KAAM,CAAA8C,2BAA2BA,CAAA,CAAsC,CACrE,GAAI,CACF,KAAM,CAAAzE,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,CAAC,0CAA0C,CAAC,CAC/E,MAAO,CAAA9B,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAA6E,gBAAA,CAAAC,qBAAA,CACnB7D,OAAO,CAACjB,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjE,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAgD,gBAAA,CAAA7E,KAAK,CAACG,QAAQ,UAAA0E,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBnE,IAAI,UAAAoE,qBAAA,iBAApBA,qBAAA,CAAsBhD,OAAO,GAAI,iDAAiD,CAAC,CACrG,CACF,CAEA,KAAM,CAAAiD,oBAAoBA,CAACC,MAAc,CAAEC,gBAAkC,CAA6B,CACxG,GAAI,CACF,KAAM,CAAA9E,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAACuB,IAAI,wBAAAV,MAAA,CAAwBiF,MAAM,EAAIC,gBAAgB,CAAC,CACvF,MAAO,CAAA9E,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAkF,iBAAA,CAAAC,qBAAA,CACnBlE,OAAO,CAACjB,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAqD,iBAAA,CAAAlF,KAAK,CAACG,QAAQ,UAAA+E,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgBxE,IAAI,UAAAyE,qBAAA,iBAApBA,qBAAA,CAAsBrD,OAAO,GAAI,kCAAkC,CAAC,CACtF,CACF,CAEA,KAAM,CAAAsD,sBAAsBA,CAACJ,MAAc,CAAEC,gBAAkC,CAA6B,CAC1G,GAAI,CACF,KAAM,CAAA9E,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC6E,GAAG,wBAAAhE,MAAA,CAAwBiF,MAAM,EAAIC,gBAAgB,CAAC,CACtF,MAAO,CAAA9E,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAqF,iBAAA,CAAAC,qBAAA,CACnBrE,OAAO,CAACjB,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAwD,iBAAA,CAAArF,KAAK,CAACG,QAAQ,UAAAkF,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgB3E,IAAI,UAAA4E,qBAAA,iBAApBA,qBAAA,CAAsBxD,OAAO,GAAI,oCAAoC,CAAC,CACxF,CACF,CAEA,KAAM,CAAAyD,mBAAmBA,CAACP,MAAc,CAA6B,CACnE,GAAI,CACF,KAAM,CAAA7E,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,wBAAAlC,MAAA,CAAwBiF,MAAM,CAAE,CAAC,CACpE,MAAO,CAAA7E,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAwF,iBAAA,CAAAC,qBAAA,CACnBxE,OAAO,CAACjB,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAA2D,iBAAA,CAAAxF,KAAK,CAACG,QAAQ,UAAAqF,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgB9E,IAAI,UAAA+E,qBAAA,iBAApBA,qBAAA,CAAsB3D,OAAO,GAAI,mCAAmC,CAAC,CACvF,CACF,CAEA;AACA,KAAM,CAAA4D,gBAAgBA,CAAA,CAA4B,CAChD,GAAI,CACF,KAAM,CAAAvF,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,CAAC,kBAAkB,CAAC,CACvD,MAAO,CAAA9B,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAA2F,iBAAA,CAAAC,qBAAA,CACnB3E,OAAO,CAACjB,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAA8D,iBAAA,CAAA3F,KAAK,CAACG,QAAQ,UAAAwF,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgBjF,IAAI,UAAAkF,qBAAA,iBAApBA,qBAAA,CAAsB9D,OAAO,GAAI,gCAAgC,CAAC,CACpF,CACF,CAEA,KAAM,CAAA+D,cAAcA,CAACb,MAAc,CAAEc,YAAoB,CAAEC,IAAU,CAA6B,CAChG,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAAClD,MAAM,CAAC,MAAM,CAAEiD,IAAI,CAAC,CAC7BC,QAAQ,CAAClD,MAAM,CAAC,gBAAgB,CAAE,GAAG,CAAC,CAAE;AACxCkD,QAAQ,CAAClD,MAAM,CAAC,kBAAkB,CAAEgD,YAAY,CAAC,CAEjD,KAAM,CAAA3F,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAACuB,IAAI,qBAAAV,MAAA,CAAqBiF,MAAM,4BAA2BgB,QAAQ,CAAE,CAClG1G,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAa,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAkG,iBAAA,CAAAC,qBAAA,CACnBlF,OAAO,CAACjB,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAqE,iBAAA,CAAAlG,KAAK,CAACG,QAAQ,UAAA+F,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgBxF,IAAI,UAAAyF,qBAAA,iBAApBA,qBAAA,CAAsBrE,OAAO,GAAI,2BAA2B,CAAC,CAC/E,CACF,CAEA,KAAM,CAAAsE,wBAAwBA,CAACpB,MAAc,CAAuB,CAClE,GAAI,CACF,KAAM,CAAA7E,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,qBAAAlC,MAAA,CAAqBiF,MAAM,2BAAyB,CAAC,CACxF,MAAO,CAAA7E,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAqG,iBAAA,CAAAC,qBAAA,CACnBrF,OAAO,CAACjB,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC7D,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAwE,iBAAA,CAAArG,KAAK,CAACG,QAAQ,UAAAkG,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgB3F,IAAI,UAAA4F,qBAAA,iBAApBA,qBAAA,CAAsBxE,OAAO,GAAI,wCAAwC,CAAC,CAC5F,CACF,CAEA;AACA,KAAM,CAAAyE,QAAQA,CAAA,CAAoB,CAChC,GAAI,CACF,KAAM,CAAApG,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC+C,GAAG,CAAC,QAAQ,CAAC,CAC7C,MAAO,CAAA9B,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,CACnBiB,OAAO,CAACjB,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C;AACA,MAAO,CACL,CACEwG,MAAM,CAAE,CAAC,CACThF,QAAQ,CAAE,QAAQ,CAClBiF,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,oBAAoB,CAC3BjF,IAAI,CAAE,OAAO,CACbkF,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,sBACjB,CAAC,CACD,CACEN,MAAM,CAAE,CAAC,CACThF,QAAQ,CAAE,aAAa,CACvBiF,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,YAAY,CACtBC,KAAK,CAAE,yBAAyB,CAChCjF,IAAI,CAAE,YAAY,CAClBkF,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,sBACjB,CAAC,CACD,CACEN,MAAM,CAAE,CAAC,CACThF,QAAQ,CAAE,QAAQ,CAClBiF,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,eAAe,CACtBjF,IAAI,CAAE,OAAO,CACbkF,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,sBACjB,CAAC,CACF,CACH,CACF,CAEA,KAAM,CAAAC,UAAUA,CAACC,QAAa,CAAiB,CAC7C,GAAI,CACF,KAAM,CAAA7G,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAACuB,IAAI,CAAC,QAAQ,CAAEuG,QAAQ,CAAC,CACxD,MAAO,CAAA7G,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAiH,iBAAA,CAAAC,qBAAA,CACnBjG,OAAO,CAACjB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAoF,iBAAA,CAAAjH,KAAK,CAACG,QAAQ,UAAA8G,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgBvG,IAAI,UAAAwG,qBAAA,iBAApBA,qBAAA,CAAsBpF,OAAO,GAAI,uBAAuB,CAAC,CAC3E,CACF,CAEA,KAAM,CAAAqF,UAAUA,CAACX,MAAc,CAAEQ,QAAa,CAAiB,CAC7D,GAAI,CACF,KAAM,CAAA7G,QAAQ,CAAG,KAAM,KAAI,CAACjB,GAAG,CAAC6E,GAAG,WAAAhE,MAAA,CAAWyG,MAAM,EAAIQ,QAAQ,CAAC,CACjE,MAAO,CAAA7G,QAAQ,CAACO,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAAoH,iBAAA,CAAAC,qBAAA,CACnBpG,OAAO,CAACjB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAAuF,iBAAA,CAAApH,KAAK,CAACG,QAAQ,UAAAiH,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgB1G,IAAI,UAAA2G,qBAAA,iBAApBA,qBAAA,CAAsBvF,OAAO,GAAI,uBAAuB,CAAC,CAC3E,CACF,CAEA,KAAM,CAAAwF,gBAAgBA,CAACd,MAAc,CAAiB,CACpD,GAAI,CACF,KAAM,KAAI,CAACtH,GAAG,CAAC6E,GAAG,WAAAhE,MAAA,CAAWyG,MAAM,kBAAgB,CAAC,CACtD,CAAE,MAAOxG,KAAU,CAAE,KAAAuH,iBAAA,CAAAC,qBAAA,CACnBvG,OAAO,CAACjB,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,IAAI,CAAA6B,KAAK,CAAC,EAAA0F,iBAAA,CAAAvH,KAAK,CAACG,QAAQ,UAAAoH,iBAAA,kBAAAC,qBAAA,CAAdD,iBAAA,CAAgB7G,IAAI,UAAA8G,qBAAA,iBAApBA,qBAAA,CAAsB1F,OAAO,GAAI,8BAA8B,CAAC,CAClF,CACF,CACF,CAEA;AACA,MAAO,MAAM,CAAA2F,UAAU,CAAG,GAAI,CAAAzI,UAAU,CAAC,CAAC,CAC1C,cAAe,CAAAyI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}