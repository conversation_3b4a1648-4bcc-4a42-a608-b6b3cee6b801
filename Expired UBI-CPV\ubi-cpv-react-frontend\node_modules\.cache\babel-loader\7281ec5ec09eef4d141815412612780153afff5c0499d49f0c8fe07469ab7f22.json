{"ast": null, "code": "import _objectSpread from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14;import React,{useState,useRef}from'react';import{useParams,useNavigate}from'react-router-dom';import styled from'styled-components';import{<PERSON>,But<PERSON>,LoadingSpinner}from'../../styles/GlobalStyles';import CameraCapture from'../Camera/CameraCapture';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Container=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  padding: 20px;\\n\"])));const Header=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.mediumGray);const Title=styled.h1(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: \",\";\\n  margin-left: 20px;\\n\"])),props=>props.theme.colors.primary);const DocumentGrid=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n\"])));const DocumentCard=styled(Card)(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  padding: 30px 20px;\\n  border: 2px dashed \",\";\\n  transition: \",\";\\n\\n  &:hover {\\n    border-color: \",\";\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.transitions.default,props=>props.theme.colors.primary,props=>props.theme.colors.offWhite);const DocumentIcon=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  font-size: 48px;\\n  margin-bottom: 15px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.primary);const DocumentTitle=styled.h3(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  margin-bottom: 10px;\\n  font-size: 18px;\\n\"])),props=>props.theme.colors.primary);const DocumentDescription=styled.p(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  margin-bottom: 20px;\\n  font-size: 14px;\\n\"])),props=>props.theme.colors.textMedium);const UploadArea=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  border: 2px dashed \",\";\\n  border-radius: \",\";\\n  padding: 40px 20px;\\n  text-align: center;\\n  background-color: \",\";\\n  transition: \",\";\\n  cursor: pointer;\\n  margin-bottom: 15px;\\n\\n  &:hover {\\n    border-color: \",\";\\n    background-color: \",\";\\n  }\\n\"])),props=>props.isDragOver?props.theme.colors.primary:props.theme.colors.mediumGray,props=>props.theme.borderRadius.md,props=>props.isDragOver?props.theme.colors.offWhite:'transparent',props=>props.theme.transitions.default,props=>props.theme.colors.primary,props=>props.theme.colors.offWhite);const UploadIcon=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  font-size: 32px;\\n  margin-bottom: 10px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.primary);const UploadText=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  font-size: 14px;\\n\"])),props=>props.theme.colors.textMedium);const FileInput=styled.input(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  display: none;\\n\"])));const PreviewImage=styled.img(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  max-width: 100%;\\n  max-height: 200px;\\n  border-radius: \",\";\\n  margin-bottom: 10px;\\n\"])),props=>props.theme.borderRadius.sm);const FileName=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  color: \",\";\\n  margin-bottom: 10px;\\n  word-break: break-all;\\n\"])),props=>props.theme.colors.textDark);const ButtonGroup=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 10px;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n\"])));const CameraModal=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  display: \",\";\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.8);\\n  z-index: 1000;\\n  align-items: center;\\n  justify-content: center;\\n\"])),props=>props.show?'flex':'none');const documentTypes=[{id:'id-proof',title:'ID Proof',description:'Aadhaar Card, PAN Card, Passport, or Driving License',icon:'🆔',required:true},{id:'address-proof',title:'Address Proof',description:'Utility Bill, Bank Statement, or Rental Agreement',icon:'🏠',required:true},{id:'income-proof',title:'Income Proof',description:'Salary Slip, Bank Statement, or ITR',icon:'💰',required:true},{id:'office-photo',title:'Office Photo',description:'Photo of the office premises',icon:'🏢',required:false}];const DocumentUpload=()=>{var _documentTypes$find;const{id}=useParams();const navigate=useNavigate();const[uploadedFiles,setUploadedFiles]=useState({});const[dragOver,setDragOver]=useState({});const[showCamera,setShowCamera]=useState(false);const[currentDocType,setCurrentDocType]=useState('');const[uploading,setUploading]=useState(false);const[uploadProgress,setUploadProgress]=useState({});const fileInputRefs=useRef({});const handleFileSelect=(docType,file)=>{setUploadedFiles(prev=>_objectSpread(_objectSpread({},prev),{},{[docType]:file}));};const handleFileInputChange=(docType,e)=>{var _e$target$files;const file=(_e$target$files=e.target.files)===null||_e$target$files===void 0?void 0:_e$target$files[0];if(file){handleFileSelect(docType,file);}};const handleDragOver=(docType,e)=>{e.preventDefault();setDragOver(prev=>_objectSpread(_objectSpread({},prev),{},{[docType]:true}));};const handleDragLeave=docType=>{setDragOver(prev=>_objectSpread(_objectSpread({},prev),{},{[docType]:false}));};const handleDrop=(docType,e)=>{e.preventDefault();setDragOver(prev=>_objectSpread(_objectSpread({},prev),{},{[docType]:false}));const file=e.dataTransfer.files[0];if(file){handleFileSelect(docType,file);}};const handleUploadClick=docType=>{var _fileInputRefs$curren;(_fileInputRefs$curren=fileInputRefs.current[docType])===null||_fileInputRefs$curren===void 0?void 0:_fileInputRefs$curren.click();};const handleCameraCapture=docType=>{setCurrentDocType(docType);setShowCamera(true);};const handleRemoveFile=docType=>{setUploadedFiles(prev=>{const newFiles=_objectSpread({},prev);delete newFiles[docType];return newFiles;});};const handleSubmit=async()=>{if(!id)return;const leadId=parseInt(id);const filesToUpload=Object.entries(uploadedFiles);if(filesToUpload.length===0){alert('Please select at least one document to upload.');return;}setUploading(true);try{// Upload each file individually\nfor(const[docType,file]of filesToUpload){setUploadProgress(prev=>_objectSpread(_objectSpread({},prev),{},{[docType]:true}));await apiService.uploadDocument(leadId,docType,file);setUploadProgress(prev=>_objectSpread(_objectSpread({},prev),{},{[docType]:false}));}alert('All documents uploaded successfully!');navigate(\"/lead/\".concat(id));}catch(error){console.error('Error uploading documents:',error);alert('Failed to upload some documents. Please try again.');}finally{setUploading(false);setUploadProgress({});}};const handleBack=()=>{navigate(\"/lead/\".concat(id));};const isImageFile=file=>{return file.type.startsWith('image/');};const getFilePreview=file=>{if(isImageFile(file)){return URL.createObjectURL(file);}return null;};return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:handleBack,children:\"\\u2190 Back\"}),/*#__PURE__*/_jsxs(Title,{children:[\"Document Upload - Lead #\",id]})]}),/*#__PURE__*/_jsx(DocumentGrid,{children:documentTypes.map(docType=>{const uploadedFile=uploadedFiles[docType.id];const isDragOver=dragOver[docType.id]||false;return/*#__PURE__*/_jsxs(DocumentCard,{children:[/*#__PURE__*/_jsx(DocumentIcon,{children:docType.icon}),/*#__PURE__*/_jsxs(DocumentTitle,{children:[docType.title,docType.required&&/*#__PURE__*/_jsx(\"span\",{style:{color:'red'},children:\" *\"})]}),/*#__PURE__*/_jsx(DocumentDescription,{children:docType.description}),uploadedFile?/*#__PURE__*/_jsxs(\"div\",{children:[isImageFile(uploadedFile)&&/*#__PURE__*/_jsx(PreviewImage,{src:getFilePreview(uploadedFile)||'',alt:\"Preview\"}),/*#__PURE__*/_jsx(FileName,{children:uploadedFile.name}),uploadProgress[docType.id]?/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',gap:'8px'},children:[/*#__PURE__*/_jsx(LoadingSpinner,{}),/*#__PURE__*/_jsx(\"span\",{children:\"Uploading...\"})]}):/*#__PURE__*/_jsxs(ButtonGroup,{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline\",onClick:()=>handleRemoveFile(docType.id),disabled:uploading,children:\"Remove\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>handleUploadClick(docType.id),disabled:uploading,children:\"Replace\"})]})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(UploadArea,{isDragOver:isDragOver,onClick:()=>handleUploadClick(docType.id),onDragOver:e=>handleDragOver(docType.id,e),onDragLeave:()=>handleDragLeave(docType.id),onDrop:e=>handleDrop(docType.id,e),children:[/*#__PURE__*/_jsx(UploadIcon,{children:\"\\uD83D\\uDCC1\"}),/*#__PURE__*/_jsx(UploadText,{children:\"Click to upload or drag and drop\"})]}),/*#__PURE__*/_jsxs(ButtonGroup,{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>handleUploadClick(docType.id),children:\"Choose File\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleCameraCapture(docType.id),children:\"\\uD83D\\uDCF7 Camera\"})]})]}),/*#__PURE__*/_jsx(FileInput,{ref:el=>{fileInputRefs.current[docType.id]=el;},type:\"file\",accept:\"image/*,.pdf\",onChange:e=>handleFileInputChange(docType.id,e)})]},docType.id);})}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'30px',textAlign:'center'},children:/*#__PURE__*/_jsx(Button,{onClick:handleSubmit,size:\"lg\",disabled:uploading,children:uploading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(LoadingSpinner,{}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:'8px'},children:\"Uploading...\"})]}):'Upload All Documents'})}),/*#__PURE__*/_jsx(CameraModal,{show:showCamera,children:/*#__PURE__*/_jsx(CameraCapture,{documentType:((_documentTypes$find=documentTypes.find(dt=>dt.id===currentDocType))===null||_documentTypes$find===void 0?void 0:_documentTypes$find.title)||'Document',onCapture:(blob,filename)=>{const file=new File([blob],filename,{type:'image/jpeg'});handleFileSelect(currentDocType,file);setShowCamera(false);},onCancel:()=>setShowCamera(false)})})]});};export default DocumentUpload;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useParams", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "LoadingSpinner", "CameraCapture", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Container", "div", "_templateObject", "_taggedTemplateLiteral", "Header", "_templateObject2", "props", "theme", "colors", "mediumGray", "Title", "h1", "_templateObject3", "primary", "DocumentGrid", "_templateObject4", "DocumentCard", "_templateObject5", "transitions", "default", "offWhite", "DocumentIcon", "_templateObject6", "DocumentTitle", "h3", "_templateObject7", "DocumentDescription", "p", "_templateObject8", "textMedium", "UploadArea", "_templateObject9", "isDragOver", "borderRadius", "md", "UploadIcon", "_templateObject0", "UploadText", "_templateObject1", "FileInput", "input", "_templateObject10", "PreviewImage", "img", "_templateObject11", "sm", "FileName", "_templateObject12", "textDark", "ButtonGroup", "_templateObject13", "CameraModal", "_templateObject14", "show", "documentTypes", "id", "title", "description", "icon", "required", "DocumentUpload", "_documentTypes$find", "navigate", "uploadedFiles", "setUploadedFiles", "dragOver", "setDragOver", "showCamera", "setShowCamera", "currentDocType", "setCurrentDocType", "uploading", "setUploading", "uploadProgress", "setUploadProgress", "fileInputRefs", "handleFileSelect", "docType", "file", "prev", "_objectSpread", "handleFileInputChange", "e", "_e$target$files", "target", "files", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "handleUploadClick", "_fileInputRefs$curren", "current", "click", "handleCameraCapture", "handleRemoveFile", "newFiles", "handleSubmit", "leadId", "parseInt", "filesToUpload", "Object", "entries", "length", "alert", "uploadDocument", "concat", "error", "console", "handleBack", "isImageFile", "type", "startsWith", "getFilePreview", "URL", "createObjectURL", "children", "variant", "onClick", "map", "uploadedFile", "style", "color", "src", "alt", "name", "display", "alignItems", "justifyContent", "gap", "size", "disabled", "onDragOver", "onDragLeave", "onDrop", "ref", "el", "accept", "onChange", "marginTop", "textAlign", "marginLeft", "documentType", "find", "dt", "onCapture", "blob", "filename", "File", "onCancel"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Documents/DocumentUpload.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport CameraCapture from '../Camera/CameraCapture';\nimport { apiService } from '../../services/apiService';\n\nconst Container = styled.div`\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n  margin-left: 20px;\n`;\n\nconst DocumentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n\nconst DocumentCard = styled(Card)`\n  text-align: center;\n  padding: 30px 20px;\n  border: 2px dashed ${props => props.theme.colors.mediumGray};\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    background-color: ${props => props.theme.colors.offWhite};\n  }\n`;\n\nconst DocumentIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: 15px;\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst DocumentTitle = styled.h3`\n  color: ${props => props.theme.colors.primary};\n  margin-bottom: 10px;\n  font-size: 18px;\n`;\n\nconst DocumentDescription = styled.p`\n  color: ${props => props.theme.colors.textMedium};\n  margin-bottom: 20px;\n  font-size: 14px;\n`;\n\nconst UploadArea = styled.div<{ isDragOver: boolean }>`\n  border: 2px dashed ${props => props.isDragOver ? props.theme.colors.primary : props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 40px 20px;\n  text-align: center;\n  background-color: ${props => props.isDragOver ? props.theme.colors.offWhite : 'transparent'};\n  transition: ${props => props.theme.transitions.default};\n  cursor: pointer;\n  margin-bottom: 15px;\n\n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    background-color: ${props => props.theme.colors.offWhite};\n  }\n`;\n\nconst UploadIcon = styled.div`\n  font-size: 32px;\n  margin-bottom: 10px;\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst UploadText = styled.div`\n  color: ${props => props.theme.colors.textMedium};\n  font-size: 14px;\n`;\n\nconst FileInput = styled.input`\n  display: none;\n`;\n\nconst PreviewImage = styled.img`\n  max-width: 100%;\n  max-height: 200px;\n  border-radius: ${props => props.theme.borderRadius.sm};\n  margin-bottom: 10px;\n`;\n\nconst FileName = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textDark};\n  margin-bottom: 10px;\n  word-break: break-all;\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  flex-wrap: wrap;\n`;\n\nconst CameraModal = styled.div<{ show: boolean }>`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.8);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n\n\n\ninterface DocumentType {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  required: boolean;\n}\n\nconst documentTypes: DocumentType[] = [\n  {\n    id: 'id-proof',\n    title: 'ID Proof',\n    description: 'Aadhaar Card, PAN Card, Passport, or Driving License',\n    icon: '🆔',\n    required: true,\n  },\n  {\n    id: 'address-proof',\n    title: 'Address Proof',\n    description: 'Utility Bill, Bank Statement, or Rental Agreement',\n    icon: '🏠',\n    required: true,\n  },\n  {\n    id: 'income-proof',\n    title: 'Income Proof',\n    description: 'Salary Slip, Bank Statement, or ITR',\n    icon: '💰',\n    required: true,\n  },\n  {\n    id: 'office-photo',\n    title: 'Office Photo',\n    description: 'Photo of the office premises',\n    icon: '🏢',\n    required: false,\n  },\n];\n\nconst DocumentUpload: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [uploadedFiles, setUploadedFiles] = useState<{ [key: string]: File }>({});\n  const [dragOver, setDragOver] = useState<{ [key: string]: boolean }>({});\n  const [showCamera, setShowCamera] = useState(false);\n  const [currentDocType, setCurrentDocType] = useState<string>('');\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: boolean }>({});\n  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});\n\n  const handleFileSelect = (docType: string, file: File) => {\n    setUploadedFiles(prev => ({ ...prev, [docType]: file }));\n  };\n\n  const handleFileInputChange = (docType: string, e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      handleFileSelect(docType, file);\n    }\n  };\n\n  const handleDragOver = (docType: string, e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(prev => ({ ...prev, [docType]: true }));\n  };\n\n  const handleDragLeave = (docType: string) => {\n    setDragOver(prev => ({ ...prev, [docType]: false }));\n  };\n\n  const handleDrop = (docType: string, e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(prev => ({ ...prev, [docType]: false }));\n\n    const file = e.dataTransfer.files[0];\n    if (file) {\n      handleFileSelect(docType, file);\n    }\n  };\n\n  const handleUploadClick = (docType: string) => {\n    fileInputRefs.current[docType]?.click();\n  };\n\n  const handleCameraCapture = (docType: string) => {\n    setCurrentDocType(docType);\n    setShowCamera(true);\n  };\n\n  const handleRemoveFile = (docType: string) => {\n    setUploadedFiles(prev => {\n      const newFiles = { ...prev };\n      delete newFiles[docType];\n      return newFiles;\n    });\n  };\n\n  const handleSubmit = async () => {\n    if (!id) return;\n\n    const leadId = parseInt(id);\n    const filesToUpload = Object.entries(uploadedFiles);\n\n    if (filesToUpload.length === 0) {\n      alert('Please select at least one document to upload.');\n      return;\n    }\n\n    setUploading(true);\n\n    try {\n      // Upload each file individually\n      for (const [docType, file] of filesToUpload) {\n        setUploadProgress(prev => ({ ...prev, [docType]: true }));\n\n        await apiService.uploadDocument(leadId, docType, file);\n\n        setUploadProgress(prev => ({ ...prev, [docType]: false }));\n      }\n\n      alert('All documents uploaded successfully!');\n      navigate(`/lead/${id}`);\n    } catch (error) {\n      console.error('Error uploading documents:', error);\n      alert('Failed to upload some documents. Please try again.');\n    } finally {\n      setUploading(false);\n      setUploadProgress({});\n    }\n  };\n\n  const handleBack = () => {\n    navigate(`/lead/${id}`);\n  };\n\n  const isImageFile = (file: File) => {\n    return file.type.startsWith('image/');\n  };\n\n  const getFilePreview = (file: File) => {\n    if (isImageFile(file)) {\n      return URL.createObjectURL(file);\n    }\n    return null;\n  };\n\n  return (\n    <Container>\n      <Header>\n        <Button variant=\"outline\" onClick={handleBack}>\n          ← Back\n        </Button>\n        <Title>Document Upload - Lead #{id}</Title>\n      </Header>\n\n      <DocumentGrid>\n        {documentTypes.map((docType) => {\n          const uploadedFile = uploadedFiles[docType.id];\n          const isDragOver = dragOver[docType.id] || false;\n\n          return (\n            <DocumentCard key={docType.id}>\n              <DocumentIcon>{docType.icon}</DocumentIcon>\n              <DocumentTitle>\n                {docType.title}\n                {docType.required && <span style={{ color: 'red' }}> *</span>}\n              </DocumentTitle>\n              <DocumentDescription>{docType.description}</DocumentDescription>\n\n              {uploadedFile ? (\n                <div>\n                  {isImageFile(uploadedFile) && (\n                    <PreviewImage\n                      src={getFilePreview(uploadedFile) || ''}\n                      alt=\"Preview\"\n                    />\n                  )}\n                  <FileName>{uploadedFile.name}</FileName>\n                  {uploadProgress[docType.id] ? (\n                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>\n                      <LoadingSpinner />\n                      <span>Uploading...</span>\n                    </div>\n                  ) : (\n                    <ButtonGroup>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => handleRemoveFile(docType.id)}\n                        disabled={uploading}\n                      >\n                        Remove\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleUploadClick(docType.id)}\n                        disabled={uploading}\n                      >\n                        Replace\n                      </Button>\n                    </ButtonGroup>\n                  )}\n                </div>\n              ) : (\n                <div>\n                  <UploadArea\n                    isDragOver={isDragOver}\n                    onClick={() => handleUploadClick(docType.id)}\n                    onDragOver={(e) => handleDragOver(docType.id, e)}\n                    onDragLeave={() => handleDragLeave(docType.id)}\n                    onDrop={(e) => handleDrop(docType.id, e)}\n                  >\n                    <UploadIcon>📁</UploadIcon>\n                    <UploadText>\n                      Click to upload or drag and drop\n                    </UploadText>\n                  </UploadArea>\n\n                  <ButtonGroup>\n                    <Button\n                      size=\"sm\"\n                      onClick={() => handleUploadClick(docType.id)}\n                    >\n                      Choose File\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      variant=\"secondary\"\n                      onClick={() => handleCameraCapture(docType.id)}\n                    >\n                      📷 Camera\n                    </Button>\n                  </ButtonGroup>\n                </div>\n              )}\n\n              <FileInput\n                ref={(el) => {\n                  fileInputRefs.current[docType.id] = el;\n                }}\n                type=\"file\"\n                accept=\"image/*,.pdf\"\n                onChange={(e) => handleFileInputChange(docType.id, e)}\n              />\n            </DocumentCard>\n          );\n        })}\n      </DocumentGrid>\n\n      <div style={{ marginTop: '30px', textAlign: 'center' }}>\n        <Button onClick={handleSubmit} size=\"lg\" disabled={uploading}>\n          {uploading ? (\n            <>\n              <LoadingSpinner />\n              <span style={{ marginLeft: '8px' }}>Uploading...</span>\n            </>\n          ) : (\n            'Upload All Documents'\n          )}\n        </Button>\n      </div>\n\n      {/* Camera Modal */}\n      <CameraModal show={showCamera}>\n        <CameraCapture\n          documentType={documentTypes.find(dt => dt.id === currentDocType)?.title || 'Document'}\n          onCapture={(blob, filename) => {\n            const file = new File([blob], filename, { type: 'image/jpeg' });\n            handleFileSelect(currentDocType, file);\n            setShowCamera(false);\n          }}\n          onCancel={() => setShowCamera(false)}\n        />\n      </CameraModal>\n    </Container>\n  );\n};\n\nexport default DocumentUpload;\n"], "mappings": "umBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC/C,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,MAAO,CAAAC,aAAa,KAAM,yBAAyB,CACnD,OAASC,UAAU,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvD,KAAM,CAAAC,SAAS,CAAGZ,MAAM,CAACa,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,qEAI3B,CAED,KAAM,CAAAC,MAAM,CAAGhB,MAAM,CAACa,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,sIAKIG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAClE,CAED,KAAM,CAAAC,KAAK,CAAGtB,MAAM,CAACuB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,wFAGZG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAE7C,CAED,KAAM,CAAAC,YAAY,CAAG1B,MAAM,CAACa,GAAG,CAAAc,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,0GAI9B,CAED,KAAM,CAAAa,YAAY,CAAG5B,MAAM,CAACC,IAAI,CAAC,CAAA4B,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,+KAGVG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAC7CH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACW,WAAW,CAACC,OAAO,CAGpCb,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAC/BP,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,QAAQ,CAE3D,CAED,KAAM,CAAAC,YAAY,CAAGjC,MAAM,CAACa,GAAG,CAAAqB,gBAAA,GAAAA,gBAAA,CAAAnB,sBAAA,qEAGpBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAC7C,CAED,KAAM,CAAAU,aAAa,CAAGnC,MAAM,CAACoC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,qEACpBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAG7C,CAED,KAAM,CAAAa,mBAAmB,CAAGtC,MAAM,CAACuC,CAAC,CAAAC,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,qEACzBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACqB,UAAU,CAGhD,CAED,KAAM,CAAAC,UAAU,CAAG1C,MAAM,CAACa,GAAG,CAAA8B,gBAAA,GAAAA,gBAAA,CAAA5B,sBAAA,4QACNG,KAAK,EAAIA,KAAK,CAAC0B,UAAU,CAAG1B,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAAGP,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAC1FH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAC0B,YAAY,CAACC,EAAE,CAGjC5B,KAAK,EAAIA,KAAK,CAAC0B,UAAU,CAAG1B,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,QAAQ,CAAG,aAAa,CAC7Ed,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACW,WAAW,CAACC,OAAO,CAKpCb,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAC/BP,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,QAAQ,CAE3D,CAED,KAAM,CAAAe,UAAU,CAAG/C,MAAM,CAACa,GAAG,CAAAmC,gBAAA,GAAAA,gBAAA,CAAAjC,sBAAA,qEAGlBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAC7C,CAED,KAAM,CAAAwB,UAAU,CAAGjD,MAAM,CAACa,GAAG,CAAAqC,gBAAA,GAAAA,gBAAA,CAAAnC,sBAAA,6CAClBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACqB,UAAU,CAEhD,CAED,KAAM,CAAAU,SAAS,CAAGnD,MAAM,CAACoD,KAAK,CAAAC,iBAAA,GAAAA,iBAAA,CAAAtC,sBAAA,4BAE7B,CAED,KAAM,CAAAuC,YAAY,CAAGtD,MAAM,CAACuD,GAAG,CAAAC,iBAAA,GAAAA,iBAAA,CAAAzC,sBAAA,mGAGZG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAC0B,YAAY,CAACY,EAAE,CAEtD,CAED,KAAM,CAAAC,QAAQ,CAAG1D,MAAM,CAACa,GAAG,CAAA8C,iBAAA,GAAAA,iBAAA,CAAA5C,sBAAA,+FAEhBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACwC,QAAQ,CAG9C,CAED,KAAM,CAAAC,WAAW,CAAG7D,MAAM,CAACa,GAAG,CAAAiD,iBAAA,GAAAA,iBAAA,CAAA/C,sBAAA,0FAK7B,CAED,KAAM,CAAAgD,WAAW,CAAG/D,MAAM,CAACa,GAAG,CAAAmD,iBAAA,GAAAA,iBAAA,CAAAjD,sBAAA,sNACjBG,KAAK,EAAIA,KAAK,CAAC+C,IAAI,CAAG,MAAM,CAAG,MAAM,CAUjD,CAYD,KAAM,CAAAC,aAA6B,CAAG,CACpC,CACEC,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,UAAU,CACjBC,WAAW,CAAE,sDAAsD,CACnEC,IAAI,CAAE,IAAI,CACVC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEJ,EAAE,CAAE,eAAe,CACnBC,KAAK,CAAE,eAAe,CACtBC,WAAW,CAAE,mDAAmD,CAChEC,IAAI,CAAE,IAAI,CACVC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEJ,EAAE,CAAE,cAAc,CAClBC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,qCAAqC,CAClDC,IAAI,CAAE,IAAI,CACVC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEJ,EAAE,CAAE,cAAc,CAClBC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,8BAA8B,CAC3CC,IAAI,CAAE,IAAI,CACVC,QAAQ,CAAE,KACZ,CAAC,CACF,CAED,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CACrC,KAAM,CAAEN,EAAG,CAAC,CAAGrE,SAAS,CAAiB,CAAC,CAC1C,KAAM,CAAA4E,QAAQ,CAAG3E,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC4E,aAAa,CAAEC,gBAAgB,CAAC,CAAGhF,QAAQ,CAA0B,CAAC,CAAC,CAAC,CAC/E,KAAM,CAACiF,QAAQ,CAAEC,WAAW,CAAC,CAAGlF,QAAQ,CAA6B,CAAC,CAAC,CAAC,CACxE,KAAM,CAACmF,UAAU,CAAEC,aAAa,CAAC,CAAGpF,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqF,cAAc,CAAEC,iBAAiB,CAAC,CAAGtF,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACuF,SAAS,CAAEC,YAAY,CAAC,CAAGxF,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACyF,cAAc,CAAEC,iBAAiB,CAAC,CAAG1F,QAAQ,CAA6B,CAAC,CAAC,CAAC,CACpF,KAAM,CAAA2F,aAAa,CAAG1F,MAAM,CAA6C,CAAC,CAAC,CAAC,CAE5E,KAAM,CAAA2F,gBAAgB,CAAGA,CAACC,OAAe,CAAEC,IAAU,GAAK,CACxDd,gBAAgB,CAACe,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,OAAO,EAAGC,IAAI,EAAG,CAAC,CAC1D,CAAC,CAED,KAAM,CAAAG,qBAAqB,CAAGA,CAACJ,OAAe,CAAEK,CAAsC,GAAK,KAAAC,eAAA,CACzF,KAAM,CAAAL,IAAI,EAAAK,eAAA,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,UAAAF,eAAA,iBAAdA,eAAA,CAAiB,CAAC,CAAC,CAChC,GAAIL,IAAI,CAAE,CACRF,gBAAgB,CAACC,OAAO,CAAEC,IAAI,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAQ,cAAc,CAAGA,CAACT,OAAe,CAAEK,CAAkB,GAAK,CAC9DA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBrB,WAAW,CAACa,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,OAAO,EAAG,IAAI,EAAG,CAAC,CACrD,CAAC,CAED,KAAM,CAAAW,eAAe,CAAIX,OAAe,EAAK,CAC3CX,WAAW,CAACa,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,OAAO,EAAG,KAAK,EAAG,CAAC,CACtD,CAAC,CAED,KAAM,CAAAY,UAAU,CAAGA,CAACZ,OAAe,CAAEK,CAAkB,GAAK,CAC1DA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBrB,WAAW,CAACa,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,OAAO,EAAG,KAAK,EAAG,CAAC,CAEpD,KAAM,CAAAC,IAAI,CAAGI,CAAC,CAACQ,YAAY,CAACL,KAAK,CAAC,CAAC,CAAC,CACpC,GAAIP,IAAI,CAAE,CACRF,gBAAgB,CAACC,OAAO,CAAEC,IAAI,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAAa,iBAAiB,CAAId,OAAe,EAAK,KAAAe,qBAAA,CAC7C,CAAAA,qBAAA,CAAAjB,aAAa,CAACkB,OAAO,CAAChB,OAAO,CAAC,UAAAe,qBAAA,iBAA9BA,qBAAA,CAAgCE,KAAK,CAAC,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAIlB,OAAe,EAAK,CAC/CP,iBAAiB,CAACO,OAAO,CAAC,CAC1BT,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAA4B,gBAAgB,CAAInB,OAAe,EAAK,CAC5Cb,gBAAgB,CAACe,IAAI,EAAI,CACvB,KAAM,CAAAkB,QAAQ,CAAAjB,aAAA,IAAQD,IAAI,CAAE,CAC5B,MAAO,CAAAkB,QAAQ,CAACpB,OAAO,CAAC,CACxB,MAAO,CAAAoB,QAAQ,CACjB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAC3C,EAAE,CAAE,OAET,KAAM,CAAA4C,MAAM,CAAGC,QAAQ,CAAC7C,EAAE,CAAC,CAC3B,KAAM,CAAA8C,aAAa,CAAGC,MAAM,CAACC,OAAO,CAACxC,aAAa,CAAC,CAEnD,GAAIsC,aAAa,CAACG,MAAM,GAAK,CAAC,CAAE,CAC9BC,KAAK,CAAC,gDAAgD,CAAC,CACvD,OACF,CAEAjC,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,IAAK,KAAM,CAACK,OAAO,CAAEC,IAAI,CAAC,EAAI,CAAAuB,aAAa,CAAE,CAC3C3B,iBAAiB,CAACK,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,OAAO,EAAG,IAAI,EAAG,CAAC,CAEzD,KAAM,CAAApF,UAAU,CAACiH,cAAc,CAACP,MAAM,CAAEtB,OAAO,CAAEC,IAAI,CAAC,CAEtDJ,iBAAiB,CAACK,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,OAAO,EAAG,KAAK,EAAG,CAAC,CAC5D,CAEA4B,KAAK,CAAC,sCAAsC,CAAC,CAC7C3C,QAAQ,UAAA6C,MAAA,CAAUpD,EAAE,CAAE,CAAC,CACzB,CAAE,MAAOqD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDH,KAAK,CAAC,oDAAoD,CAAC,CAC7D,CAAC,OAAS,CACRjC,YAAY,CAAC,KAAK,CAAC,CACnBE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAoC,UAAU,CAAGA,CAAA,GAAM,CACvBhD,QAAQ,UAAA6C,MAAA,CAAUpD,EAAE,CAAE,CAAC,CACzB,CAAC,CAED,KAAM,CAAAwD,WAAW,CAAIjC,IAAU,EAAK,CAClC,MAAO,CAAAA,IAAI,CAACkC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CACvC,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIpC,IAAU,EAAK,CACrC,GAAIiC,WAAW,CAACjC,IAAI,CAAC,CAAE,CACrB,MAAO,CAAAqC,GAAG,CAACC,eAAe,CAACtC,IAAI,CAAC,CAClC,CACA,MAAO,KAAI,CACb,CAAC,CAED,mBACEjF,KAAA,CAACG,SAAS,EAAAqH,QAAA,eACRxH,KAAA,CAACO,MAAM,EAAAiH,QAAA,eACL1H,IAAA,CAACL,MAAM,EAACgI,OAAO,CAAC,SAAS,CAACC,OAAO,CAAET,UAAW,CAAAO,QAAA,CAAC,aAE/C,CAAQ,CAAC,cACTxH,KAAA,CAACa,KAAK,EAAA2G,QAAA,EAAC,0BAAwB,CAAC9D,EAAE,EAAQ,CAAC,EACrC,CAAC,cAET5D,IAAA,CAACmB,YAAY,EAAAuG,QAAA,CACV/D,aAAa,CAACkE,GAAG,CAAE3C,OAAO,EAAK,CAC9B,KAAM,CAAA4C,YAAY,CAAG1D,aAAa,CAACc,OAAO,CAACtB,EAAE,CAAC,CAC9C,KAAM,CAAAvB,UAAU,CAAGiC,QAAQ,CAACY,OAAO,CAACtB,EAAE,CAAC,EAAI,KAAK,CAEhD,mBACE1D,KAAA,CAACmB,YAAY,EAAAqG,QAAA,eACX1H,IAAA,CAAC0B,YAAY,EAAAgG,QAAA,CAAExC,OAAO,CAACnB,IAAI,CAAe,CAAC,cAC3C7D,KAAA,CAAC0B,aAAa,EAAA8F,QAAA,EACXxC,OAAO,CAACrB,KAAK,CACbqB,OAAO,CAAClB,QAAQ,eAAIhE,IAAA,SAAM+H,KAAK,CAAE,CAAEC,KAAK,CAAE,KAAM,CAAE,CAAAN,QAAA,CAAC,IAAE,CAAM,CAAC,EAChD,CAAC,cAChB1H,IAAA,CAAC+B,mBAAmB,EAAA2F,QAAA,CAAExC,OAAO,CAACpB,WAAW,CAAsB,CAAC,CAE/DgE,YAAY,cACX5H,KAAA,QAAAwH,QAAA,EACGN,WAAW,CAACU,YAAY,CAAC,eACxB9H,IAAA,CAAC+C,YAAY,EACXkF,GAAG,CAAEV,cAAc,CAACO,YAAY,CAAC,EAAI,EAAG,CACxCI,GAAG,CAAC,SAAS,CACd,CACF,cACDlI,IAAA,CAACmD,QAAQ,EAAAuE,QAAA,CAAEI,YAAY,CAACK,IAAI,CAAW,CAAC,CACvCrD,cAAc,CAACI,OAAO,CAACtB,EAAE,CAAC,cACzB1D,KAAA,QAAK6H,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAM,CAAE,CAAAb,QAAA,eAC1F1H,IAAA,CAACJ,cAAc,GAAE,CAAC,cAClBI,IAAA,SAAA0H,QAAA,CAAM,cAAY,CAAM,CAAC,EACtB,CAAC,cAENxH,KAAA,CAACoD,WAAW,EAAAoE,QAAA,eACV1H,IAAA,CAACL,MAAM,EACL6I,IAAI,CAAC,IAAI,CACTb,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,CAAA,GAAMvB,gBAAgB,CAACnB,OAAO,CAACtB,EAAE,CAAE,CAC5C6E,QAAQ,CAAE7D,SAAU,CAAA8C,QAAA,CACrB,QAED,CAAQ,CAAC,cACT1H,IAAA,CAACL,MAAM,EACL6I,IAAI,CAAC,IAAI,CACTZ,OAAO,CAAEA,CAAA,GAAM5B,iBAAiB,CAACd,OAAO,CAACtB,EAAE,CAAE,CAC7C6E,QAAQ,CAAE7D,SAAU,CAAA8C,QAAA,CACrB,SAED,CAAQ,CAAC,EACE,CACd,EACE,CAAC,cAENxH,KAAA,QAAAwH,QAAA,eACExH,KAAA,CAACiC,UAAU,EACTE,UAAU,CAAEA,UAAW,CACvBuF,OAAO,CAAEA,CAAA,GAAM5B,iBAAiB,CAACd,OAAO,CAACtB,EAAE,CAAE,CAC7C8E,UAAU,CAAGnD,CAAC,EAAKI,cAAc,CAACT,OAAO,CAACtB,EAAE,CAAE2B,CAAC,CAAE,CACjDoD,WAAW,CAAEA,CAAA,GAAM9C,eAAe,CAACX,OAAO,CAACtB,EAAE,CAAE,CAC/CgF,MAAM,CAAGrD,CAAC,EAAKO,UAAU,CAACZ,OAAO,CAACtB,EAAE,CAAE2B,CAAC,CAAE,CAAAmC,QAAA,eAEzC1H,IAAA,CAACwC,UAAU,EAAAkF,QAAA,CAAC,cAAE,CAAY,CAAC,cAC3B1H,IAAA,CAAC0C,UAAU,EAAAgF,QAAA,CAAC,kCAEZ,CAAY,CAAC,EACH,CAAC,cAEbxH,KAAA,CAACoD,WAAW,EAAAoE,QAAA,eACV1H,IAAA,CAACL,MAAM,EACL6I,IAAI,CAAC,IAAI,CACTZ,OAAO,CAAEA,CAAA,GAAM5B,iBAAiB,CAACd,OAAO,CAACtB,EAAE,CAAE,CAAA8D,QAAA,CAC9C,aAED,CAAQ,CAAC,cACT1H,IAAA,CAACL,MAAM,EACL6I,IAAI,CAAC,IAAI,CACTb,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAEA,CAAA,GAAMxB,mBAAmB,CAAClB,OAAO,CAACtB,EAAE,CAAE,CAAA8D,QAAA,CAChD,qBAED,CAAQ,CAAC,EACE,CAAC,EACX,CACN,cAED1H,IAAA,CAAC4C,SAAS,EACRiG,GAAG,CAAGC,EAAE,EAAK,CACX9D,aAAa,CAACkB,OAAO,CAAChB,OAAO,CAACtB,EAAE,CAAC,CAAGkF,EAAE,CACxC,CAAE,CACFzB,IAAI,CAAC,MAAM,CACX0B,MAAM,CAAC,cAAc,CACrBC,QAAQ,CAAGzD,CAAC,EAAKD,qBAAqB,CAACJ,OAAO,CAACtB,EAAE,CAAE2B,CAAC,CAAE,CACvD,CAAC,GAlFeL,OAAO,CAACtB,EAmFb,CAAC,CAEnB,CAAC,CAAC,CACU,CAAC,cAEf5D,IAAA,QAAK+H,KAAK,CAAE,CAAEkB,SAAS,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAxB,QAAA,cACrD1H,IAAA,CAACL,MAAM,EAACiI,OAAO,CAAErB,YAAa,CAACiC,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAE7D,SAAU,CAAA8C,QAAA,CAC1D9C,SAAS,cACR1E,KAAA,CAAAE,SAAA,EAAAsH,QAAA,eACE1H,IAAA,CAACJ,cAAc,GAAE,CAAC,cAClBI,IAAA,SAAM+H,KAAK,CAAE,CAAEoB,UAAU,CAAE,KAAM,CAAE,CAAAzB,QAAA,CAAC,cAAY,CAAM,CAAC,EACvD,CAAC,CAEH,sBACD,CACK,CAAC,CACN,CAAC,cAGN1H,IAAA,CAACwD,WAAW,EAACE,IAAI,CAAEc,UAAW,CAAAkD,QAAA,cAC5B1H,IAAA,CAACH,aAAa,EACZuJ,YAAY,CAAE,EAAAlF,mBAAA,CAAAP,aAAa,CAAC0F,IAAI,CAACC,EAAE,EAAIA,EAAE,CAAC1F,EAAE,GAAKc,cAAc,CAAC,UAAAR,mBAAA,iBAAlDA,mBAAA,CAAoDL,KAAK,GAAI,UAAW,CACtF0F,SAAS,CAAEA,CAACC,IAAI,CAAEC,QAAQ,GAAK,CAC7B,KAAM,CAAAtE,IAAI,CAAG,GAAI,CAAAuE,IAAI,CAAC,CAACF,IAAI,CAAC,CAAEC,QAAQ,CAAE,CAAEpC,IAAI,CAAE,YAAa,CAAC,CAAC,CAC/DpC,gBAAgB,CAACP,cAAc,CAAES,IAAI,CAAC,CACtCV,aAAa,CAAC,KAAK,CAAC,CACtB,CAAE,CACFkF,QAAQ,CAAEA,CAAA,GAAMlF,aAAa,CAAC,KAAK,CAAE,CACtC,CAAC,CACS,CAAC,EACL,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}