{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{Card,Button}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatsContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\"])));const StatCard=styled(Card)(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  transition: \",\";\\n\\n  &:hover {\\n    transform: translateY(-5px);\\n    box-shadow: \",\";\\n  }\\n\"])),props=>props.theme.transitions.default,props=>props.theme.shadows.lg);const StatIcon=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n  font-size: 20px;\\n  color: \",\";\\n  background: \",\";\\n\"])),props=>props.theme.colors.white,props=>props.color);const StatValue=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin-bottom: 5px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.textDark);const StatLabel=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  color: \",\";\\n  font-weight: 500;\\n\"])),props=>props.theme.colors.textLight);const TableContainer=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  overflow-x: auto;\\n\"])));const Table=styled.table(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const StatusBadge=styled.span(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.status){case'pending-review':return\"\\n          background-color: #f3e5f5;\\n          color: #4a148c;\\n        \";case'approved':return\"\\n          background-color: #e8f5e9;\\n          color: #2e7d32;\\n        \";case'rejected':return\"\\n          background-color: #ffebee;\\n          color: #c62828;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const FilterContainer=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 10px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background-color: \",\";\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.white);const SupervisorDashboard=()=>{const[leads,setLeads]=useState([]);const[stats,setStats]=useState(null);const[loading,setLoading]=useState(true);const[statusFilter,setStatusFilter]=useState('pending-review');const navigate=useNavigate();useEffect(()=>{loadDashboardData();},[statusFilter]);const loadDashboardData=async()=>{try{setLoading(true);const[leadsResponse,statsResponse]=await Promise.all([apiService.getLeads(1,50,statusFilter||undefined),apiService.getSupervisorDashboardStats()]);setLeads(leadsResponse.data||[]);setStats(statsResponse);}catch(error){console.error('Error loading dashboard data:',error);// Mock data for demo\nsetLeads([{leadId:1,customerName:'John Doe',mobileNumber:'9876543210',loanType:'Personal Loan',status:'pending-review',createdDate:'2024-01-15T10:30:00Z',assignedToName:'Agent Smith',createdByName:'Admin User',documentCount:3,croppedImageCount:2},{leadId:2,customerName:'Jane Smith',mobileNumber:'9876543211',loanType:'Home Loan',status:'pending-review',createdDate:'2024-01-14T09:15:00Z',assignedToName:'Agent Johnson',createdByName:'Admin User',documentCount:5,croppedImageCount:3}]);setStats({pendingReviews:2,approvedToday:1,rejectedToday:0,totalReviewed:10,approvalRate:85.5,agentPerformance:[{agentId:1,agentName:'Agent Smith',assignedCount:5,completedCount:4,approvedCount:3,rejectedCount:1,completionRate:80,approvalRate:75}],totalLeads:15,completedLeads:10});}finally{setLoading(false);}};const navigationItems=[{icon:'🏠',label:'Dashboard',active:true},{icon:'👁️',label:'Review Queue',onClick:()=>navigate('/supervisor/review')},{icon:'✅',label:'Approved',onClick:()=>navigate('/supervisor/approved')},{icon:'❌',label:'Rejected',onClick:()=>navigate('/supervisor/rejected')},{icon:'📊',label:'Reports',onClick:()=>navigate('/supervisor/reports')}];const handleLeadClick=leadId=>{navigate(\"/lead/\".concat(leadId));};const handleReviewLead=leadId=>{navigate(\"/supervisor/review/\".concat(leadId));};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Supervisor Dashboard\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(\"div\",{children:\"Loading...\"})});}return/*#__PURE__*/_jsxs(DashboardLayout,{title:\"Supervisor Dashboard\",navigationItems:navigationItems,children:[/*#__PURE__*/_jsxs(StatsContainer,{children:[/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #f3e5f5, #4a148c)\",children:\"\\u23F3\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.pendingReviews)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"Pending Review\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #28a745, #1e7e34)\",children:\"\\u2705\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.approvedToday)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"Approved Today\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #dc3545, #c82333)\",children:\"\\u274C\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.rejectedToday)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"Rejected Today\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #007E3A, #005a2a)\",children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsx(StatValue,{children:stats!==null&&stats!==void 0&&stats.approvalRate?\"\".concat(stats.approvalRate.toFixed(1),\"%\"):'0%'}),/*#__PURE__*/_jsx(StatLabel,{children:\"Approval Rate\"})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Review Queue\"}),/*#__PURE__*/_jsx(FilterContainer,{children:/*#__PURE__*/_jsxs(FilterSelect,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"pending-review\",children:\"Pending Review\"}),/*#__PURE__*/_jsx(\"option\",{value:\"approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:\"Rejected\"}),/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Statuses\"})]})}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Customer Name\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Mobile\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Loan Type\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Status\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Submitted Date\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Agent\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:leads.map(lead=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:lead.customerName}),/*#__PURE__*/_jsx(TableCell,{children:lead.mobileNumber}),/*#__PURE__*/_jsx(TableCell,{children:lead.loanType}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(StatusBadge,{status:lead.status,children:lead.status.replace('-',' ').toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:lead.createdDate?formatDate(lead.createdDate):'-'}),/*#__PURE__*/_jsx(TableCell,{children:lead.assignedToName||'Unassigned'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'8px'},children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>handleLeadClick(lead.leadId),children:\"View\"}),lead.status==='pending-review'&&/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleReviewLead(lead.leadId),children:\"Review\"})]})})]},lead.leadId))})]})}),leads.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px',color:'#777'},children:\"No leads found for the selected status.\"})]})]});};export default SupervisorDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "StatsContainer", "div", "_templateObject", "_taggedTemplateLiteral", "StatCard", "_templateObject2", "props", "theme", "transitions", "default", "shadows", "lg", "StatIcon", "_templateObject3", "colors", "white", "color", "StatValue", "_templateObject4", "textDark", "StatLabel", "_templateObject5", "textLight", "TableContainer", "_templateObject6", "Table", "table", "_templateObject7", "TableHeader", "th", "_templateObject8", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject9", "TableRow", "tr", "_templateObject0", "StatusBadge", "span", "_templateObject1", "status", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject10", "FilterSelect", "select", "_templateObject11", "mediumGray", "borderRadius", "sm", "SupervisorDashboard", "leads", "setLeads", "stats", "setStats", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "navigate", "loadDashboardData", "leadsResponse", "statsResponse", "Promise", "all", "getLeads", "undefined", "getSupervisorDashboardStats", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedToName", "createdByName", "documentCount", "croppedImageCount", "pendingReviews", "approvedToday", "<PERSON><PERSON><PERSON><PERSON>", "totalReviewed", "approvalRate", "agentPerformance", "agentId", "<PERSON><PERSON><PERSON>", "assignedCount", "completedCount", "approvedCount", "rejectedCount", "completionRate", "totalLeads", "completedLeads", "navigationItems", "icon", "label", "active", "onClick", "handleLeadClick", "concat", "handleReviewLead", "formatDate", "dateString", "Date", "toLocaleDateString", "title", "children", "toFixed", "style", "marginBottom", "value", "onChange", "e", "target", "map", "lead", "replace", "toUpperCase", "display", "gap", "size", "variant", "length", "textAlign", "padding"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Dashboard/SupervisorDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem, SupervisorDashboardStats } from '../../services/apiService';\n\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n\nconst StatIcon = styled.div<{ color: string }>`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ${props => props.theme.colors.white};\n  background: ${props => props.color};\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'approved':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background-color: ${props => props.theme.colors.white};\n`;\n\nconst SupervisorDashboard: React.FC = () => {\n  const [leads, setLeads] = useState<LeadListItem[]>([]);\n  const [stats, setStats] = useState<SupervisorDashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('pending-review');\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [statusFilter]);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [leadsResponse, statsResponse] = await Promise.all([\n        apiService.getLeads(1, 50, statusFilter || undefined),\n        apiService.getSupervisorDashboardStats(),\n      ]);\n\n      setLeads(leadsResponse.data || []);\n      setStats(statsResponse);\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Mock data for demo\n      setLeads([\n        {\n          leadId: 1,\n          customerName: 'John Doe',\n          mobileNumber: '9876543210',\n          loanType: 'Personal Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-15T10:30:00Z',\n          assignedToName: 'Agent Smith',\n          createdByName: 'Admin User',\n          documentCount: 3,\n          croppedImageCount: 2,\n        },\n        {\n          leadId: 2,\n          customerName: 'Jane Smith',\n          mobileNumber: '9876543211',\n          loanType: 'Home Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-14T09:15:00Z',\n          assignedToName: 'Agent Johnson',\n          createdByName: 'Admin User',\n          documentCount: 5,\n          croppedImageCount: 3,\n        },\n      ]);\n      setStats({\n        pendingReviews: 2,\n        approvedToday: 1,\n        rejectedToday: 0,\n        totalReviewed: 10,\n        approvalRate: 85.5,\n        agentPerformance: [\n          {\n            agentId: 1,\n            agentName: 'Agent Smith',\n            assignedCount: 5,\n            completedCount: 4,\n            approvedCount: 3,\n            rejectedCount: 1,\n            completionRate: 80,\n            approvalRate: 75,\n          },\n        ],\n        totalLeads: 15,\n        completedLeads: 10,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', active: true },\n    { icon: '👁️', label: 'Review Queue', onClick: () => navigate('/supervisor/review') },\n    { icon: '✅', label: 'Approved', onClick: () => navigate('/supervisor/approved') },\n    { icon: '❌', label: 'Rejected', onClick: () => navigate('/supervisor/rejected') },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/supervisor/reports') },\n  ];\n\n  const handleLeadClick = (leadId: number) => {\n    navigate(`/lead/${leadId}`);\n  };\n\n  const handleReviewLead = (leadId: number) => {\n    navigate(`/supervisor/review/${leadId}`);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Supervisor Dashboard\" navigationItems={navigationItems}>\n        <div>Loading...</div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Supervisor Dashboard\" navigationItems={navigationItems}>\n      {/* Stats Cards */}\n      <StatsContainer>\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #f3e5f5, #4a148c)\">⏳</StatIcon>\n          <StatValue>{stats?.pendingReviews || 0}</StatValue>\n          <StatLabel>Pending Review</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #28a745, #1e7e34)\">✅</StatIcon>\n          <StatValue>{stats?.approvedToday || 0}</StatValue>\n          <StatLabel>Approved Today</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #dc3545, #c82333)\">❌</StatIcon>\n          <StatValue>{stats?.rejectedToday || 0}</StatValue>\n          <StatLabel>Rejected Today</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #007E3A, #005a2a)\">📊</StatIcon>\n          <StatValue>{stats?.approvalRate ? `${stats.approvalRate.toFixed(1)}%` : '0%'}</StatValue>\n          <StatLabel>Approval Rate</StatLabel>\n        </StatCard>\n      </StatsContainer>\n\n      {/* Review Queue */}\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Review Queue</h2>\n\n        <FilterContainer>\n          <FilterSelect\n            value={statusFilter}\n            onChange={(e) => setStatusFilter(e.target.value)}\n          >\n            <option value=\"pending-review\">Pending Review</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"rejected\">Rejected</option>\n            <option value=\"\">All Statuses</option>\n          </FilterSelect>\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer Name</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Submitted Date</TableHeader>\n                <TableHeader>Agent</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {leads.map((lead) => (\n                <TableRow key={lead.leadId}>\n                  <TableCell>{lead.customerName}</TableCell>\n                  <TableCell>{lead.mobileNumber}</TableCell>\n                  <TableCell>{lead.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={lead.status}>\n                      {lead.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    {lead.createdDate ? formatDate(lead.createdDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    {lead.assignedToName || 'Unassigned'}\n                  </TableCell>\n                  <TableCell>\n                    <div style={{ display: 'flex', gap: '8px' }}>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleLeadClick(lead.leadId)}\n                      >\n                        View\n                      </Button>\n                      {lead.status === 'pending-review' && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"secondary\"\n                          onClick={() => handleReviewLead(lead.leadId)}\n                        >\n                          Review\n                        </Button>\n                      )}\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {leads.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            No leads found for the selected status.\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default SupervisorDashboard;\n"], "mappings": "kZAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,KAAwB,2BAA2B,CACxE,OAASC,UAAU,KAAgD,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/F,KAAM,CAAAC,cAAc,CAAGT,MAAM,CAACU,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,kIAKhC,CAED,KAAM,CAAAC,QAAQ,CAAGb,MAAM,CAACE,IAAI,CAAC,CAAAY,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,sMAKbG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,WAAW,CAACC,OAAO,CAItCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,OAAO,CAACC,EAAE,CAEhD,CAED,KAAM,CAAAC,QAAQ,CAAGrB,MAAM,CAACU,GAAG,CAAAY,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,uNAShBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACC,KAAK,CAC5BT,KAAK,EAAIA,KAAK,CAACU,KAAK,CACnC,CAED,KAAM,CAAAC,SAAS,CAAG1B,MAAM,CAACU,GAAG,CAAAiB,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,yFAIjBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACK,QAAQ,CAC9C,CAED,KAAM,CAAAC,SAAS,CAAG7B,MAAM,CAACU,GAAG,CAAAoB,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,kEAEjBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACQ,SAAS,CAE/C,CAED,KAAM,CAAAC,cAAc,CAAGhC,MAAM,CAACU,GAAG,CAAAuB,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,+BAEhC,CAED,KAAM,CAAAsB,KAAK,CAAGlC,MAAM,CAACmC,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAxB,sBAAA,wDAGzB,CAED,KAAM,CAAAyB,WAAW,CAAGrC,MAAM,CAACsC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA3B,sBAAA,qJAGAG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACiB,SAAS,CAC5CzB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACkB,QAAQ,CAE/C1B,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACmB,UAAU,CAChD,CAED,KAAM,CAAAC,SAAS,CAAG3C,MAAM,CAAC4C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjC,sBAAA,uFAGEG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACiB,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAG9C,MAAM,CAAC+C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAApC,sBAAA,wDAEFG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACiB,SAAS,CAE5D,CAED,KAAM,CAAAS,WAAW,CAAGjD,MAAM,CAACkD,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAvC,sBAAA,mIAO3BG,KAAK,EAAI,CACT,OAAQA,KAAK,CAACqC,MAAM,EAClB,IAAK,gBAAgB,CACnB,oFAIF,IAAK,UAAU,CACb,oFAIF,IAAK,UAAU,CACb,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAGrD,MAAM,CAACU,GAAG,CAAA4C,iBAAA,GAAAA,iBAAA,CAAA1C,sBAAA,sFAKjC,CAED,KAAM,CAAA2C,YAAY,CAAGvD,MAAM,CAACwD,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,+HAEZG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACmC,UAAU,CACzC3C,KAAK,EAAIA,KAAK,CAACC,KAAK,CAAC2C,YAAY,CAACC,EAAE,CAEjC7C,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACC,KAAK,CACtD,CAED,KAAM,CAAAqC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGlE,QAAQ,CAAiB,EAAE,CAAC,CACtD,KAAM,CAACmE,KAAK,CAAEC,QAAQ,CAAC,CAAGpE,QAAQ,CAAkC,IAAI,CAAC,CACzE,KAAM,CAACqE,OAAO,CAAEC,UAAU,CAAC,CAAGtE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACuE,YAAY,CAAEC,eAAe,CAAC,CAAGxE,QAAQ,CAAC,gBAAgB,CAAC,CAClE,KAAM,CAAAyE,QAAQ,CAAGvE,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACdyE,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAACH,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAG,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACFJ,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAACK,aAAa,CAAEC,aAAa,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACvDvE,UAAU,CAACwE,QAAQ,CAAC,CAAC,CAAE,EAAE,CAAER,YAAY,EAAIS,SAAS,CAAC,CACrDzE,UAAU,CAAC0E,2BAA2B,CAAC,CAAC,CACzC,CAAC,CAEFf,QAAQ,CAACS,aAAa,CAACO,IAAI,EAAI,EAAE,CAAC,CAClCd,QAAQ,CAACQ,aAAa,CAAC,CACzB,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD;AACAjB,QAAQ,CAAC,CACP,CACEmB,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,UAAU,CACxBC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,eAAe,CACzBjC,MAAM,CAAE,gBAAgB,CACxBkC,WAAW,CAAE,sBAAsB,CACnCC,cAAc,CAAE,aAAa,CAC7BC,aAAa,CAAE,YAAY,CAC3BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACD,CACER,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,WAAW,CACrBjC,MAAM,CAAE,gBAAgB,CACxBkC,WAAW,CAAE,sBAAsB,CACnCC,cAAc,CAAE,eAAe,CAC/BC,aAAa,CAAE,YAAY,CAC3BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACF,CAAC,CACFzB,QAAQ,CAAC,CACP0B,cAAc,CAAE,CAAC,CACjBC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,EAAE,CACjBC,YAAY,CAAE,IAAI,CAClBC,gBAAgB,CAAE,CAChB,CACEC,OAAO,CAAE,CAAC,CACVC,SAAS,CAAE,aAAa,CACxBC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,EAAE,CAClBR,YAAY,CAAE,EAChB,CAAC,CACF,CACDS,UAAU,CAAE,EAAE,CACdC,cAAc,CAAE,EAClB,CAAC,CAAC,CACJ,CAAC,OAAS,CACRtC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAuC,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,MAAM,CAAE,IAAK,CAAC,CAChD,CAAEF,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,cAAc,CAAEE,OAAO,CAAEA,CAAA,GAAMxC,QAAQ,CAAC,oBAAoB,CAAE,CAAC,CACrF,CAAEqC,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,UAAU,CAAEE,OAAO,CAAEA,CAAA,GAAMxC,QAAQ,CAAC,sBAAsB,CAAE,CAAC,CACjF,CAAEqC,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,UAAU,CAAEE,OAAO,CAAEA,CAAA,GAAMxC,QAAQ,CAAC,sBAAsB,CAAE,CAAC,CACjF,CAAEqC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEE,OAAO,CAAEA,CAAA,GAAMxC,QAAQ,CAAC,qBAAqB,CAAE,CAAC,CACjF,CAED,KAAM,CAAAyC,eAAe,CAAI7B,MAAc,EAAK,CAC1CZ,QAAQ,UAAA0C,MAAA,CAAU9B,MAAM,CAAE,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA+B,gBAAgB,CAAI/B,MAAc,EAAK,CAC3CZ,QAAQ,uBAAA0C,MAAA,CAAuB9B,MAAM,CAAE,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAgC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,GAAInD,OAAO,CAAE,CACX,mBACE5D,IAAA,CAACL,eAAe,EAACqH,KAAK,CAAC,sBAAsB,CAACZ,eAAe,CAAEA,eAAgB,CAAAa,QAAA,cAC7EjH,IAAA,QAAAiH,QAAA,CAAK,YAAU,CAAK,CAAC,CACN,CAAC,CAEtB,CAEA,mBACE/G,KAAA,CAACP,eAAe,EAACqH,KAAK,CAAC,sBAAsB,CAACZ,eAAe,CAAEA,eAAgB,CAAAa,QAAA,eAE7E/G,KAAA,CAACC,cAAc,EAAA8G,QAAA,eACb/G,KAAA,CAACK,QAAQ,EAAA0G,QAAA,eACPjH,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA8F,QAAA,CAAC,QAAC,CAAU,CAAC,cACxEjH,IAAA,CAACoB,SAAS,EAAA6F,QAAA,CAAE,CAAAvD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE2B,cAAc,GAAI,CAAC,CAAY,CAAC,cACnDrF,IAAA,CAACuB,SAAS,EAAA0F,QAAA,CAAC,gBAAc,CAAW,CAAC,EAC7B,CAAC,cAEX/G,KAAA,CAACK,QAAQ,EAAA0G,QAAA,eACPjH,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA8F,QAAA,CAAC,QAAC,CAAU,CAAC,cACxEjH,IAAA,CAACoB,SAAS,EAAA6F,QAAA,CAAE,CAAAvD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE4B,aAAa,GAAI,CAAC,CAAY,CAAC,cAClDtF,IAAA,CAACuB,SAAS,EAAA0F,QAAA,CAAC,gBAAc,CAAW,CAAC,EAC7B,CAAC,cAEX/G,KAAA,CAACK,QAAQ,EAAA0G,QAAA,eACPjH,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA8F,QAAA,CAAC,QAAC,CAAU,CAAC,cACxEjH,IAAA,CAACoB,SAAS,EAAA6F,QAAA,CAAE,CAAAvD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE6B,aAAa,GAAI,CAAC,CAAY,CAAC,cAClDvF,IAAA,CAACuB,SAAS,EAAA0F,QAAA,CAAC,gBAAc,CAAW,CAAC,EAC7B,CAAC,cAEX/G,KAAA,CAACK,QAAQ,EAAA0G,QAAA,eACPjH,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA8F,QAAA,CAAC,cAAE,CAAU,CAAC,cACzEjH,IAAA,CAACoB,SAAS,EAAA6F,QAAA,CAAEvD,KAAK,SAALA,KAAK,WAALA,KAAK,CAAE+B,YAAY,IAAAiB,MAAA,CAAMhD,KAAK,CAAC+B,YAAY,CAACyB,OAAO,CAAC,CAAC,CAAC,MAAM,IAAI,CAAY,CAAC,cACzFlH,IAAA,CAACuB,SAAS,EAAA0F,QAAA,CAAC,eAAa,CAAW,CAAC,EAC5B,CAAC,EACG,CAAC,cAGjB/G,KAAA,CAACN,IAAI,EAAAqH,QAAA,eACHjH,IAAA,OAAImH,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEjG,KAAK,CAAE,SAAU,CAAE,CAAA8F,QAAA,CAAC,cAAY,CAAI,CAAC,cAExEjH,IAAA,CAAC+C,eAAe,EAAAkE,QAAA,cACd/G,KAAA,CAAC+C,YAAY,EACXoE,KAAK,CAAEvD,YAAa,CACpBwD,QAAQ,CAAGC,CAAC,EAAKxD,eAAe,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAJ,QAAA,eAEjDjH,IAAA,WAAQqH,KAAK,CAAC,gBAAgB,CAAAJ,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACtDjH,IAAA,WAAQqH,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CjH,IAAA,WAAQqH,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CjH,IAAA,WAAQqH,KAAK,CAAC,EAAE,CAAAJ,QAAA,CAAC,cAAY,CAAQ,CAAC,EAC1B,CAAC,CACA,CAAC,cAElBjH,IAAA,CAAC0B,cAAc,EAAAuF,QAAA,cACb/G,KAAA,CAAC0B,KAAK,EAAAqF,QAAA,eACJjH,IAAA,UAAAiH,QAAA,cACE/G,KAAA,OAAA+G,QAAA,eACEjH,IAAA,CAAC+B,WAAW,EAAAkF,QAAA,CAAC,eAAa,CAAa,CAAC,cACxCjH,IAAA,CAAC+B,WAAW,EAAAkF,QAAA,CAAC,QAAM,CAAa,CAAC,cACjCjH,IAAA,CAAC+B,WAAW,EAAAkF,QAAA,CAAC,WAAS,CAAa,CAAC,cACpCjH,IAAA,CAAC+B,WAAW,EAAAkF,QAAA,CAAC,QAAM,CAAa,CAAC,cACjCjH,IAAA,CAAC+B,WAAW,EAAAkF,QAAA,CAAC,gBAAc,CAAa,CAAC,cACzCjH,IAAA,CAAC+B,WAAW,EAAAkF,QAAA,CAAC,OAAK,CAAa,CAAC,cAChCjH,IAAA,CAAC+B,WAAW,EAAAkF,QAAA,CAAC,SAAO,CAAa,CAAC,EAChC,CAAC,CACA,CAAC,cACRjH,IAAA,UAAAiH,QAAA,CACGzD,KAAK,CAACiE,GAAG,CAAEC,IAAI,eACdxH,KAAA,CAACsC,QAAQ,EAAAyE,QAAA,eACPjH,IAAA,CAACqC,SAAS,EAAA4E,QAAA,CAAES,IAAI,CAAC7C,YAAY,CAAY,CAAC,cAC1C7E,IAAA,CAACqC,SAAS,EAAA4E,QAAA,CAAES,IAAI,CAAC5C,YAAY,CAAY,CAAC,cAC1C9E,IAAA,CAACqC,SAAS,EAAA4E,QAAA,CAAES,IAAI,CAAC3C,QAAQ,CAAY,CAAC,cACtC/E,IAAA,CAACqC,SAAS,EAAA4E,QAAA,cACRjH,IAAA,CAAC2C,WAAW,EAACG,MAAM,CAAE4E,IAAI,CAAC5E,MAAO,CAAAmE,QAAA,CAC9BS,IAAI,CAAC5E,MAAM,CAAC6E,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CACjC,CAAC,CACL,CAAC,cACZ5H,IAAA,CAACqC,SAAS,EAAA4E,QAAA,CACPS,IAAI,CAAC1C,WAAW,CAAG4B,UAAU,CAACc,IAAI,CAAC1C,WAAW,CAAC,CAAG,GAAG,CAC7C,CAAC,cACZhF,IAAA,CAACqC,SAAS,EAAA4E,QAAA,CACPS,IAAI,CAACzC,cAAc,EAAI,YAAY,CAC3B,CAAC,cACZjF,IAAA,CAACqC,SAAS,EAAA4E,QAAA,cACR/G,KAAA,QAAKiH,KAAK,CAAE,CAAEU,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,KAAM,CAAE,CAAAb,QAAA,eAC1CjH,IAAA,CAACH,MAAM,EACLkI,IAAI,CAAC,IAAI,CACTvB,OAAO,CAAEA,CAAA,GAAMC,eAAe,CAACiB,IAAI,CAAC9C,MAAM,CAAE,CAAAqC,QAAA,CAC7C,MAED,CAAQ,CAAC,CACRS,IAAI,CAAC5E,MAAM,GAAK,gBAAgB,eAC/B9C,IAAA,CAACH,MAAM,EACLkI,IAAI,CAAC,IAAI,CACTC,OAAO,CAAC,WAAW,CACnBxB,OAAO,CAAEA,CAAA,GAAMG,gBAAgB,CAACe,IAAI,CAAC9C,MAAM,CAAE,CAAAqC,QAAA,CAC9C,QAED,CAAQ,CACT,EACE,CAAC,CACG,CAAC,GAjCCS,IAAI,CAAC9C,MAkCV,CACX,CAAC,CACG,CAAC,EACH,CAAC,CACM,CAAC,CAEhBpB,KAAK,CAACyE,MAAM,GAAK,CAAC,eACjBjI,IAAA,QAAKmH,KAAK,CAAE,CAAEe,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEhH,KAAK,CAAE,MAAO,CAAE,CAAA8F,QAAA,CAAC,yCAErE,CAAK,CACN,EACG,CAAC,EACQ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAA1D,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}