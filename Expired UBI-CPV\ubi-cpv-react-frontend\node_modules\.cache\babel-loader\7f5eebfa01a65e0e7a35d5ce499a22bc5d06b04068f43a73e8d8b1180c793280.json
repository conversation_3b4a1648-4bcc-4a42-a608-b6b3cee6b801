{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{Card,Button,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReportsContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  gap: 20px;\\n\"])));const StatsGrid=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\"])));const StatCard=styled(Card)(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #007E3A, #005a2a);\\n  color: white;\\n\"])));const StatValue=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-size: 32px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n\"])));const StatLabel=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  opacity: 0.9;\\n  font-weight: 500;\\n\"])));const ChartContainer=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\\n  @media (max-width: 768px) {\\n    grid-template-columns: 1fr;\\n  }\\n\"])));const ChartCard=styled(Card)(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 20px;\\n\"])));const ChartTitle=styled.h3(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n  color: #007E3A;\\n  text-align: center;\\n\"])));const PerformanceBar=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  height: 20px;\\n  background-color: #f0f0f0;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 10px;\\n  \\n  &::after {\\n    content: '';\\n    display: block;\\n    width: \",\"%;\\n    height: 100%;\\n    background-color: \",\";\\n    transition: width 0.3s ease;\\n  }\\n\"])),props=>props.percentage,props=>props.color);const PerformanceItem=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  margin-bottom: 15px;\\n\"])));const PerformanceLabel=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: between;\\n  margin-bottom: 5px;\\n  font-size: 14px;\\n  font-weight: 500;\\n\"])));const FilterContainer=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background: white;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const RecentActivity=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  max-height: 300px;\\n  overflow-y: auto;\\n\"])));const ActivityItem=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: between;\\n  align-items: center;\\n  padding: 12px 0;\\n  border-bottom: 1px solid \",\";\\n  \\n  &:last-child {\\n    border-bottom: none;\\n  }\\n\"])),props=>props.theme.colors.lightGray);const ActivityInfo=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  flex: 1;\\n\"])));const ActivityTitle=styled.div(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n\"])));const ActivityDate=styled.div(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  font-size: 12px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.textLight);const AgentReports=()=>{const[loading,setLoading]=useState(true);const[timeFilter,setTimeFilter]=useState('week');const[stats,setStats]=useState({totalTasks:0,completedTasks:0,approvedTasks:0,rejectedTasks:0,averageTime:0,efficiency:0});const[recentActivity,setRecentActivity]=useState([]);const navigate=useNavigate();useEffect(()=>{loadReportsData();},[timeFilter]);const loadReportsData=async()=>{try{setLoading(true);// In a real app, this would be a dedicated reports API endpoint\nconst[dashboardStats]=await Promise.all([apiService.getAgentDashboardStats()]);setStats({totalTasks:dashboardStats.totalAssigned||0,completedTasks:dashboardStats.completedLeads||0,approvedTasks:dashboardStats.completedLeads||0,rejectedTasks:dashboardStats.rejectedLeads||0,averageTime:2.5,// Mock data\nefficiency:85// Mock data\n});// Mock recent activity\nsetRecentActivity([{id:1,title:'Completed verification for John Doe',date:'2024-01-16T14:30:00Z',type:'completed'},{id:2,title:'Started verification for Jane Smith',date:'2024-01-16T10:15:00Z',type:'started'},{id:3,title:'Uploaded documents for Alice Johnson',date:'2024-01-15T16:45:00Z',type:'upload'}]);}catch(error){console.error('Error loading reports data:',error);// Use mock data\nsetStats({totalTasks:15,completedTasks:12,approvedTasks:10,rejectedTasks:2,averageTime:2.5,efficiency:85});}finally{setLoading(false);}};const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>navigate('/agent/dashboard')},{icon:'📋',label:'My Tasks',onClick:()=>navigate('/agent/tasks')},{icon:'✅',label:'Completed',onClick:()=>navigate('/agent/completed')},{icon:'📊',label:'Reports',active:true}];const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};const getCompletionRate=()=>{return stats.totalTasks>0?Math.round(stats.completedTasks/stats.totalTasks*100):0;};const getApprovalRate=()=>{return stats.completedTasks>0?Math.round(stats.approvedTasks/stats.completedTasks*100):0;};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Performance Reports\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Performance Reports\",navigationItems:navigationItems,children:/*#__PURE__*/_jsxs(ReportsContainer,{children:[/*#__PURE__*/_jsxs(FilterContainer,{children:[/*#__PURE__*/_jsxs(FilterSelect,{value:timeFilter,onChange:e=>setTimeFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"week\",children:\"This Week\"}),/*#__PURE__*/_jsx(\"option\",{value:\"month\",children:\"This Month\"}),/*#__PURE__*/_jsx(\"option\",{value:\"quarter\",children:\"This Quarter\"}),/*#__PURE__*/_jsx(\"option\",{value:\"year\",children:\"This Year\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>window.print(),children:\"\\uD83D\\uDCC4 Export Report\"})]}),/*#__PURE__*/_jsxs(StatsGrid,{children:[/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:stats.totalTasks}),/*#__PURE__*/_jsx(StatLabel,{children:\"Total Tasks Assigned\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:stats.completedTasks}),/*#__PURE__*/_jsx(StatLabel,{children:\"Tasks Completed\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:stats.averageTime}),/*#__PURE__*/_jsx(StatLabel,{children:\"Avg. Days per Task\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsxs(StatValue,{children:[stats.efficiency,\"%\"]}),/*#__PURE__*/_jsx(StatLabel,{children:\"Efficiency Score\"})]})]}),/*#__PURE__*/_jsxs(ChartContainer,{children:[/*#__PURE__*/_jsxs(ChartCard,{children:[/*#__PURE__*/_jsx(ChartTitle,{children:\"Task Performance\"}),/*#__PURE__*/_jsxs(PerformanceItem,{children:[/*#__PURE__*/_jsxs(PerformanceLabel,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Completion Rate\"}),/*#__PURE__*/_jsxs(\"span\",{children:[getCompletionRate(),\"%\"]})]}),/*#__PURE__*/_jsx(PerformanceBar,{percentage:getCompletionRate(),color:\"#2e7d32\"})]}),/*#__PURE__*/_jsxs(PerformanceItem,{children:[/*#__PURE__*/_jsxs(PerformanceLabel,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Approval Rate\"}),/*#__PURE__*/_jsxs(\"span\",{children:[getApprovalRate(),\"%\"]})]}),/*#__PURE__*/_jsx(PerformanceBar,{percentage:getApprovalRate(),color:\"#007E3A\"})]}),/*#__PURE__*/_jsxs(PerformanceItem,{children:[/*#__PURE__*/_jsxs(PerformanceLabel,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Efficiency\"}),/*#__PURE__*/_jsxs(\"span\",{children:[stats.efficiency,\"%\"]})]}),/*#__PURE__*/_jsx(PerformanceBar,{percentage:stats.efficiency,color:\"#FFD100\"})]})]}),/*#__PURE__*/_jsxs(ChartCard,{children:[/*#__PURE__*/_jsx(ChartTitle,{children:\"Recent Activity\"}),/*#__PURE__*/_jsx(RecentActivity,{children:recentActivity.map(activity=>/*#__PURE__*/_jsx(ActivityItem,{children:/*#__PURE__*/_jsxs(ActivityInfo,{children:[/*#__PURE__*/_jsx(ActivityTitle,{children:activity.title}),/*#__PURE__*/_jsx(ActivityDate,{children:formatDate(activity.date)})]})},activity.id))})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Performance Summary\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'20px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Tasks Overview\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Total Assigned: \",stats.totalTasks]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Completed: \",stats.completedTasks]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Pending: \",stats.totalTasks-stats.completedTasks]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Quality Metrics\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Approved: \",stats.approvedTasks]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Rejected: \",stats.rejectedTasks]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Success Rate: \",getApprovalRate(),\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Efficiency\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Avg. Time: \",stats.averageTime,\" days\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Efficiency: \",stats.efficiency,\"%\"]}),/*#__PURE__*/_jsx(\"p\",{children:\"Productivity: High\"})]})]})]})]})});};export default AgentReports;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "ReportsContainer", "div", "_templateObject", "_taggedTemplateLiteral", "StatsGrid", "_templateObject2", "StatCard", "_templateObject3", "StatValue", "_templateObject4", "StatLabel", "_templateObject5", "ChartContainer", "_templateObject6", "ChartCard", "_templateObject7", "ChartTitle", "h3", "_templateObject8", "PerformanceBar", "_templateObject9", "props", "percentage", "color", "PerformanceItem", "_templateObject0", "PerformanceLabel", "_templateObject1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject10", "FilterSelect", "select", "_templateObject11", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "RecentActivity", "_templateObject12", "ActivityItem", "_templateObject13", "lightGray", "ActivityInfo", "_templateObject14", "ActivityTitle", "_templateObject15", "ActivityDate", "_templateObject16", "textLight", "AgentReports", "loading", "setLoading", "timeFilter", "setTimeFilter", "stats", "setStats", "totalTasks", "completedTasks", "approvedTasks", "rejectedTasks", "averageTime", "efficiency", "recentActivity", "setRecentActivity", "navigate", "loadReportsData", "dashboardStats", "Promise", "all", "getAgentDashboardStats", "totalAssigned", "completedLeads", "rejectedLeads", "id", "title", "date", "type", "error", "console", "navigationItems", "icon", "label", "onClick", "active", "formatDate", "dateString", "Date", "toLocaleDateString", "getCompletionRate", "Math", "round", "getApprovalRate", "children", "value", "onChange", "e", "target", "variant", "window", "print", "map", "activity", "style", "marginBottom", "display", "gridTemplateColumns", "gap"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Agent/AgentReports.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\n\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n\nconst StatValue = styled.div`\n  font-size: 32px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n\nconst PerformanceBar = styled.div<{ percentage: number; color: string }>`\n  width: 100%;\n  height: 20px;\n  background-color: #f0f0f0;\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 10px;\n  \n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.percentage}%;\n    height: 100%;\n    background-color: ${props => props.color};\n    transition: width 0.3s ease;\n  }\n`;\n\nconst PerformanceItem = styled.div`\n  margin-bottom: 15px;\n`;\n\nconst PerformanceLabel = styled.div`\n  display: flex;\n  justify-content: between;\n  margin-bottom: 5px;\n  font-size: 14px;\n  font-weight: 500;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst RecentActivity = styled.div`\n  max-height: 300px;\n  overflow-y: auto;\n`;\n\nconst ActivityItem = styled.div`\n  display: flex;\n  justify-content: between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst ActivityInfo = styled.div`\n  flex: 1;\n`;\n\nconst ActivityTitle = styled.div`\n  font-weight: 500;\n  margin-bottom: 4px;\n`;\n\nconst ActivityDate = styled.div`\n  font-size: 12px;\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst AgentReports: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('week');\n  const [stats, setStats] = useState({\n    totalTasks: 0,\n    completedTasks: 0,\n    approvedTasks: 0,\n    rejectedTasks: 0,\n    averageTime: 0,\n    efficiency: 0,\n  });\n  const [recentActivity, setRecentActivity] = useState<any[]>([]);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n      \n      // In a real app, this would be a dedicated reports API endpoint\n      const [dashboardStats] = await Promise.all([\n        apiService.getAgentDashboardStats(),\n      ]);\n\n      setStats({\n        totalTasks: dashboardStats.totalAssigned || 0,\n        completedTasks: dashboardStats.completedLeads || 0,\n        approvedTasks: dashboardStats.completedLeads || 0,\n        rejectedTasks: dashboardStats.rejectedLeads || 0,\n        averageTime: 2.5, // Mock data\n        efficiency: 85, // Mock data\n      });\n\n      // Mock recent activity\n      setRecentActivity([\n        {\n          id: 1,\n          title: 'Completed verification for John Doe',\n          date: '2024-01-16T14:30:00Z',\n          type: 'completed',\n        },\n        {\n          id: 2,\n          title: 'Started verification for Jane Smith',\n          date: '2024-01-16T10:15:00Z',\n          type: 'started',\n        },\n        {\n          id: 3,\n          title: 'Uploaded documents for Alice Johnson',\n          date: '2024-01-15T16:45:00Z',\n          type: 'upload',\n        },\n      ]);\n\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data\n      setStats({\n        totalTasks: 15,\n        completedTasks: 12,\n        approvedTasks: 10,\n        rejectedTasks: 2,\n        averageTime: 2.5,\n        efficiency: 85,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },\n    { icon: '📋', label: 'My Tasks', onClick: () => navigate('/agent/tasks') },\n    { icon: '✅', label: 'Completed', onClick: () => navigate('/agent/completed') },\n    { icon: '📊', label: 'Reports', active: true },\n  ];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getCompletionRate = () => {\n    return stats.totalTasks > 0 ? Math.round((stats.completedTasks / stats.totalTasks) * 100) : 0;\n  };\n\n  const getApprovalRate = () => {\n    return stats.completedTasks > 0 ? Math.round((stats.approvedTasks / stats.completedTasks) * 100) : 0;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Performance Reports\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Performance Reports\" navigationItems={navigationItems}>\n      <ReportsContainer>\n        <FilterContainer>\n          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </FilterSelect>\n          \n          <Button variant=\"outline\" onClick={() => window.print()}>\n            📄 Export Report\n          </Button>\n        </FilterContainer>\n\n        {/* Key Metrics */}\n        <StatsGrid>\n          <StatCard>\n            <StatValue>{stats.totalTasks}</StatValue>\n            <StatLabel>Total Tasks Assigned</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{stats.completedTasks}</StatValue>\n            <StatLabel>Tasks Completed</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{stats.averageTime}</StatValue>\n            <StatLabel>Avg. Days per Task</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{stats.efficiency}%</StatValue>\n            <StatLabel>Efficiency Score</StatLabel>\n          </StatCard>\n        </StatsGrid>\n\n        {/* Performance Charts */}\n        <ChartContainer>\n          <ChartCard>\n            <ChartTitle>Task Performance</ChartTitle>\n            <PerformanceItem>\n              <PerformanceLabel>\n                <span>Completion Rate</span>\n                <span>{getCompletionRate()}%</span>\n              </PerformanceLabel>\n              <PerformanceBar percentage={getCompletionRate()} color=\"#2e7d32\" />\n            </PerformanceItem>\n            <PerformanceItem>\n              <PerformanceLabel>\n                <span>Approval Rate</span>\n                <span>{getApprovalRate()}%</span>\n              </PerformanceLabel>\n              <PerformanceBar percentage={getApprovalRate()} color=\"#007E3A\" />\n            </PerformanceItem>\n            <PerformanceItem>\n              <PerformanceLabel>\n                <span>Efficiency</span>\n                <span>{stats.efficiency}%</span>\n              </PerformanceLabel>\n              <PerformanceBar percentage={stats.efficiency} color=\"#FFD100\" />\n            </PerformanceItem>\n          </ChartCard>\n\n          <ChartCard>\n            <ChartTitle>Recent Activity</ChartTitle>\n            <RecentActivity>\n              {recentActivity.map((activity) => (\n                <ActivityItem key={activity.id}>\n                  <ActivityInfo>\n                    <ActivityTitle>{activity.title}</ActivityTitle>\n                    <ActivityDate>{formatDate(activity.date)}</ActivityDate>\n                  </ActivityInfo>\n                </ActivityItem>\n              ))}\n            </RecentActivity>\n          </ChartCard>\n        </ChartContainer>\n\n        {/* Summary Card */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Performance Summary</h3>\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px' }}>\n            <div>\n              <h4>Tasks Overview</h4>\n              <p>Total Assigned: {stats.totalTasks}</p>\n              <p>Completed: {stats.completedTasks}</p>\n              <p>Pending: {stats.totalTasks - stats.completedTasks}</p>\n            </div>\n            <div>\n              <h4>Quality Metrics</h4>\n              <p>Approved: {stats.approvedTasks}</p>\n              <p>Rejected: {stats.rejectedTasks}</p>\n              <p>Success Rate: {getApprovalRate()}%</p>\n            </div>\n            <div>\n              <h4>Efficiency</h4>\n              <p>Avg. Time: {stats.averageTime} days</p>\n              <p>Efficiency: {stats.efficiency}%</p>\n              <p>Productivity: High</p>\n            </div>\n          </div>\n        </Card>\n      </ReportsContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default AgentReports;\n"], "mappings": "4eAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,KAAM,CAAAC,gBAAgB,CAAGV,MAAM,CAACW,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,0CAGlC,CAED,KAAM,CAAAC,SAAS,CAAGd,MAAM,CAACW,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,kIAK3B,CAED,KAAM,CAAAG,QAAQ,CAAGhB,MAAM,CAACE,IAAI,CAAC,CAAAe,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,mMAQ5B,CAED,KAAM,CAAAK,SAAS,CAAGlB,MAAM,CAACW,GAAG,CAAAQ,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,0EAI3B,CAED,KAAM,CAAAO,SAAS,CAAGpB,MAAM,CAACW,GAAG,CAAAU,gBAAA,GAAAA,gBAAA,CAAAR,sBAAA,oEAI3B,CAED,KAAM,CAAAS,cAAc,CAAGtB,MAAM,CAACW,GAAG,CAAAY,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,4KAShC,CAED,KAAM,CAAAW,SAAS,CAAGxB,MAAM,CAACE,IAAI,CAAC,CAAAuB,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,4BAE7B,CAED,KAAM,CAAAa,UAAU,CAAG1B,MAAM,CAAC2B,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,4EAI3B,CAED,KAAM,CAAAgB,cAAc,CAAG7B,MAAM,CAACW,GAAG,CAAAmB,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,+SAWpBkB,KAAK,EAAIA,KAAK,CAACC,UAAU,CAEdD,KAAK,EAAIA,KAAK,CAACE,KAAK,CAG3C,CAED,KAAM,CAAAC,eAAe,CAAGlC,MAAM,CAACW,GAAG,CAAAwB,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,kCAEjC,CAED,KAAM,CAAAuB,gBAAgB,CAAGpC,MAAM,CAACW,GAAG,CAAA0B,gBAAA,GAAAA,gBAAA,CAAAxB,sBAAA,yHAMlC,CAED,KAAM,CAAAyB,eAAe,CAAGtC,MAAM,CAACW,GAAG,CAAA4B,iBAAA,GAAAA,iBAAA,CAAA1B,sBAAA,sFAKjC,CAED,KAAM,CAAA2B,YAAY,CAAGxC,MAAM,CAACyC,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAA7B,sBAAA,6LAEZkB,KAAK,EAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCd,KAAK,EAAIA,KAAK,CAACY,KAAK,CAACG,YAAY,CAACC,EAAE,CAKnChB,KAAK,EAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAC,cAAc,CAAGjD,MAAM,CAACW,GAAG,CAAAuC,iBAAA,GAAAA,iBAAA,CAAArC,sBAAA,qDAGhC,CAED,KAAM,CAAAsC,YAAY,CAAGnD,MAAM,CAACW,GAAG,CAAAyC,iBAAA,GAAAA,iBAAA,CAAAvC,sBAAA,2LAKFkB,KAAK,EAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACS,SAAS,CAKjE,CAED,KAAM,CAAAC,YAAY,CAAGtD,MAAM,CAACW,GAAG,CAAA4C,iBAAA,GAAAA,iBAAA,CAAA1C,sBAAA,sBAE9B,CAED,KAAM,CAAA2C,aAAa,CAAGxD,MAAM,CAACW,GAAG,CAAA8C,iBAAA,GAAAA,iBAAA,CAAA5C,sBAAA,sDAG/B,CAED,KAAM,CAAA6C,YAAY,CAAG1D,MAAM,CAACW,GAAG,CAAAgD,iBAAA,GAAAA,iBAAA,CAAA9C,sBAAA,6CAEpBkB,KAAK,EAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACgB,SAAS,CAC/C,CAED,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGlE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmE,UAAU,CAAEC,aAAa,CAAC,CAAGpE,QAAQ,CAAC,MAAM,CAAC,CACpD,KAAM,CAACqE,KAAK,CAAEC,QAAQ,CAAC,CAAGtE,QAAQ,CAAC,CACjCuE,UAAU,CAAE,CAAC,CACbC,cAAc,CAAE,CAAC,CACjBC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CAAC,CAChBC,WAAW,CAAE,CAAC,CACdC,UAAU,CAAE,CACd,CAAC,CAAC,CACF,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAG9E,QAAQ,CAAQ,EAAE,CAAC,CAC/D,KAAM,CAAA+E,QAAQ,CAAG7E,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd+E,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAACb,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAa,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFd,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAACe,cAAc,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACzC3E,UAAU,CAAC4E,sBAAsB,CAAC,CAAC,CACpC,CAAC,CAEFd,QAAQ,CAAC,CACPC,UAAU,CAAEU,cAAc,CAACI,aAAa,EAAI,CAAC,CAC7Cb,cAAc,CAAES,cAAc,CAACK,cAAc,EAAI,CAAC,CAClDb,aAAa,CAAEQ,cAAc,CAACK,cAAc,EAAI,CAAC,CACjDZ,aAAa,CAAEO,cAAc,CAACM,aAAa,EAAI,CAAC,CAChDZ,WAAW,CAAE,GAAG,CAAE;AAClBC,UAAU,CAAE,EAAI;AAClB,CAAC,CAAC,CAEF;AACAE,iBAAiB,CAAC,CAChB,CACEU,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,qCAAqC,CAC5CC,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,WACR,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,qCAAqC,CAC5CC,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,SACR,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,sCAAsC,CAC7CC,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,QACR,CAAC,CACF,CAAC,CAEJ,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD;AACAtB,QAAQ,CAAC,CACPC,UAAU,CAAE,EAAE,CACdC,cAAc,CAAE,EAAE,CAClBC,aAAa,CAAE,EAAE,CACjBC,aAAa,CAAE,CAAC,CAChBC,WAAW,CAAE,GAAG,CAChBC,UAAU,CAAE,EACd,CAAC,CAAC,CACJ,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4B,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC/E,CAAEgB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAU,CAAEC,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,cAAc,CAAE,CAAC,CAC1E,CAAEgB,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAMlB,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC9E,CAAEgB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEE,MAAM,CAAE,IAAK,CAAC,CAC/C,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,MAAO,CAAAlC,KAAK,CAACE,UAAU,CAAG,CAAC,CAAGiC,IAAI,CAACC,KAAK,CAAEpC,KAAK,CAACG,cAAc,CAAGH,KAAK,CAACE,UAAU,CAAI,GAAG,CAAC,CAAG,CAAC,CAC/F,CAAC,CAED,KAAM,CAAAmC,eAAe,CAAGA,CAAA,GAAM,CAC5B,MAAO,CAAArC,KAAK,CAACG,cAAc,CAAG,CAAC,CAAGgC,IAAI,CAACC,KAAK,CAAEpC,KAAK,CAACI,aAAa,CAAGJ,KAAK,CAACG,cAAc,CAAI,GAAG,CAAC,CAAG,CAAC,CACtG,CAAC,CAED,GAAIP,OAAO,CAAE,CACX,mBACEvD,IAAA,CAACN,eAAe,EAACqF,KAAK,CAAC,qBAAqB,CAACK,eAAe,CAAEA,eAAgB,CAAAa,QAAA,cAC5EjG,IAAA,CAACH,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,mBACEG,IAAA,CAACN,eAAe,EAACqF,KAAK,CAAC,qBAAqB,CAACK,eAAe,CAAEA,eAAgB,CAAAa,QAAA,cAC5E/F,KAAA,CAACC,gBAAgB,EAAA8F,QAAA,eACf/F,KAAA,CAAC6B,eAAe,EAAAkE,QAAA,eACd/F,KAAA,CAAC+B,YAAY,EAACiE,KAAK,CAAEzC,UAAW,CAAC0C,QAAQ,CAAGC,CAAC,EAAK1C,aAAa,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAD,QAAA,eAC9EjG,IAAA,WAAQkG,KAAK,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAQ,CAAC,cACvCjG,IAAA,WAAQkG,KAAK,CAAC,OAAO,CAAAD,QAAA,CAAC,YAAU,CAAQ,CAAC,cACzCjG,IAAA,WAAQkG,KAAK,CAAC,SAAS,CAAAD,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC7CjG,IAAA,WAAQkG,KAAK,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAQ,CAAC,EAC3B,CAAC,cAEfjG,IAAA,CAACJ,MAAM,EAAC0G,OAAO,CAAC,SAAS,CAACf,OAAO,CAAEA,CAAA,GAAMgB,MAAM,CAACC,KAAK,CAAC,CAAE,CAAAP,QAAA,CAAC,4BAEzD,CAAQ,CAAC,EACM,CAAC,cAGlB/F,KAAA,CAACK,SAAS,EAAA0F,QAAA,eACR/F,KAAA,CAACO,QAAQ,EAAAwF,QAAA,eACPjG,IAAA,CAACW,SAAS,EAAAsF,QAAA,CAAEtC,KAAK,CAACE,UAAU,CAAY,CAAC,cACzC7D,IAAA,CAACa,SAAS,EAAAoF,QAAA,CAAC,sBAAoB,CAAW,CAAC,EACnC,CAAC,cACX/F,KAAA,CAACO,QAAQ,EAAAwF,QAAA,eACPjG,IAAA,CAACW,SAAS,EAAAsF,QAAA,CAAEtC,KAAK,CAACG,cAAc,CAAY,CAAC,cAC7C9D,IAAA,CAACa,SAAS,EAAAoF,QAAA,CAAC,iBAAe,CAAW,CAAC,EAC9B,CAAC,cACX/F,KAAA,CAACO,QAAQ,EAAAwF,QAAA,eACPjG,IAAA,CAACW,SAAS,EAAAsF,QAAA,CAAEtC,KAAK,CAACM,WAAW,CAAY,CAAC,cAC1CjE,IAAA,CAACa,SAAS,EAAAoF,QAAA,CAAC,oBAAkB,CAAW,CAAC,EACjC,CAAC,cACX/F,KAAA,CAACO,QAAQ,EAAAwF,QAAA,eACP/F,KAAA,CAACS,SAAS,EAAAsF,QAAA,EAAEtC,KAAK,CAACO,UAAU,CAAC,GAAC,EAAW,CAAC,cAC1ClE,IAAA,CAACa,SAAS,EAAAoF,QAAA,CAAC,kBAAgB,CAAW,CAAC,EAC/B,CAAC,EACF,CAAC,cAGZ/F,KAAA,CAACa,cAAc,EAAAkF,QAAA,eACb/F,KAAA,CAACe,SAAS,EAAAgF,QAAA,eACRjG,IAAA,CAACmB,UAAU,EAAA8E,QAAA,CAAC,kBAAgB,CAAY,CAAC,cACzC/F,KAAA,CAACyB,eAAe,EAAAsE,QAAA,eACd/F,KAAA,CAAC2B,gBAAgB,EAAAoE,QAAA,eACfjG,IAAA,SAAAiG,QAAA,CAAM,iBAAe,CAAM,CAAC,cAC5B/F,KAAA,SAAA+F,QAAA,EAAOJ,iBAAiB,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,EACnB,CAAC,cACnB7F,IAAA,CAACsB,cAAc,EAACG,UAAU,CAAEoE,iBAAiB,CAAC,CAAE,CAACnE,KAAK,CAAC,SAAS,CAAE,CAAC,EACpD,CAAC,cAClBxB,KAAA,CAACyB,eAAe,EAAAsE,QAAA,eACd/F,KAAA,CAAC2B,gBAAgB,EAAAoE,QAAA,eACfjG,IAAA,SAAAiG,QAAA,CAAM,eAAa,CAAM,CAAC,cAC1B/F,KAAA,SAAA+F,QAAA,EAAOD,eAAe,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,EACjB,CAAC,cACnBhG,IAAA,CAACsB,cAAc,EAACG,UAAU,CAAEuE,eAAe,CAAC,CAAE,CAACtE,KAAK,CAAC,SAAS,CAAE,CAAC,EAClD,CAAC,cAClBxB,KAAA,CAACyB,eAAe,EAAAsE,QAAA,eACd/F,KAAA,CAAC2B,gBAAgB,EAAAoE,QAAA,eACfjG,IAAA,SAAAiG,QAAA,CAAM,YAAU,CAAM,CAAC,cACvB/F,KAAA,SAAA+F,QAAA,EAAOtC,KAAK,CAACO,UAAU,CAAC,GAAC,EAAM,CAAC,EAChB,CAAC,cACnBlE,IAAA,CAACsB,cAAc,EAACG,UAAU,CAAEkC,KAAK,CAACO,UAAW,CAACxC,KAAK,CAAC,SAAS,CAAE,CAAC,EACjD,CAAC,EACT,CAAC,cAEZxB,KAAA,CAACe,SAAS,EAAAgF,QAAA,eACRjG,IAAA,CAACmB,UAAU,EAAA8E,QAAA,CAAC,iBAAe,CAAY,CAAC,cACxCjG,IAAA,CAAC0C,cAAc,EAAAuD,QAAA,CACZ9B,cAAc,CAACsC,GAAG,CAAEC,QAAQ,eAC3B1G,IAAA,CAAC4C,YAAY,EAAAqD,QAAA,cACX/F,KAAA,CAAC6C,YAAY,EAAAkD,QAAA,eACXjG,IAAA,CAACiD,aAAa,EAAAgD,QAAA,CAAES,QAAQ,CAAC3B,KAAK,CAAgB,CAAC,cAC/C/E,IAAA,CAACmD,YAAY,EAAA8C,QAAA,CAAER,UAAU,CAACiB,QAAQ,CAAC1B,IAAI,CAAC,CAAe,CAAC,EAC5C,CAAC,EAJE0B,QAAQ,CAAC5B,EAKd,CACf,CAAC,CACY,CAAC,EACR,CAAC,EACE,CAAC,cAGjB5E,KAAA,CAACP,IAAI,EAAAsG,QAAA,eACHjG,IAAA,OAAI2G,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAElF,KAAK,CAAE,SAAU,CAAE,CAAAuE,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC/E/F,KAAA,QAAKyG,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEC,mBAAmB,CAAE,sCAAsC,CAAEC,GAAG,CAAE,MAAO,CAAE,CAAAd,QAAA,eACxG/F,KAAA,QAAA+F,QAAA,eACEjG,IAAA,OAAAiG,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB/F,KAAA,MAAA+F,QAAA,EAAG,kBAAgB,CAACtC,KAAK,CAACE,UAAU,EAAI,CAAC,cACzC3D,KAAA,MAAA+F,QAAA,EAAG,aAAW,CAACtC,KAAK,CAACG,cAAc,EAAI,CAAC,cACxC5D,KAAA,MAAA+F,QAAA,EAAG,WAAS,CAACtC,KAAK,CAACE,UAAU,CAAGF,KAAK,CAACG,cAAc,EAAI,CAAC,EACtD,CAAC,cACN5D,KAAA,QAAA+F,QAAA,eACEjG,IAAA,OAAAiG,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxB/F,KAAA,MAAA+F,QAAA,EAAG,YAAU,CAACtC,KAAK,CAACI,aAAa,EAAI,CAAC,cACtC7D,KAAA,MAAA+F,QAAA,EAAG,YAAU,CAACtC,KAAK,CAACK,aAAa,EAAI,CAAC,cACtC9D,KAAA,MAAA+F,QAAA,EAAG,gBAAc,CAACD,eAAe,CAAC,CAAC,CAAC,GAAC,EAAG,CAAC,EACtC,CAAC,cACN9F,KAAA,QAAA+F,QAAA,eACEjG,IAAA,OAAAiG,QAAA,CAAI,YAAU,CAAI,CAAC,cACnB/F,KAAA,MAAA+F,QAAA,EAAG,aAAW,CAACtC,KAAK,CAACM,WAAW,CAAC,OAAK,EAAG,CAAC,cAC1C/D,KAAA,MAAA+F,QAAA,EAAG,cAAY,CAACtC,KAAK,CAACO,UAAU,CAAC,GAAC,EAAG,CAAC,cACtClE,IAAA,MAAAiG,QAAA,CAAG,oBAAkB,CAAG,CAAC,EACtB,CAAC,EACH,CAAC,EACF,CAAC,EACS,CAAC,CACJ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAA3C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}