{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Admin\\\\UserManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n_c3 = Title;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c4 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c5 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c6 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c7 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c8 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ${props => props.active ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.active ? '#2e7d32' : '#c62828'};\n`;\n_c9 = StatusBadge;\nconst RoleBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.role) {\n    case 'Admin':\n      return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n    case 'Supervisor':\n      return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n    case 'Agent':\n      return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c0 = RoleBadge;\nconst Modal = styled.div`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n_c1 = Modal;\nconst ModalContent = styled.div`\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n`;\n_c10 = ModalContent;\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c11 = ModalHeader;\nconst ModalTitle = styled.h2`\n  color: ${props => props.theme.colors.primary};\n  margin: 0;\n`;\n_c12 = ModalTitle;\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ${props => props.theme.colors.textMedium};\n\n  &:hover {\n    color: ${props => props.theme.colors.textDark};\n  }\n`;\n_c13 = CloseButton;\nconst Notification = styled.div`\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  padding: 16px 24px;\n  border-radius: ${props => props.theme.borderRadius.lg};\n  color: white;\n  font-weight: 500;\n  z-index: 1000;\n  animation: slideIn 0.3s ease-out;\n  box-shadow: ${props => props.theme.shadows.lg};\n\n  ${props => props.type === 'success' ? `\n    background: ${props.theme.colors.success};\n  ` : `\n    background: ${props.theme.colors.error};\n  `}\n\n  @keyframes slideIn {\n    from {\n      transform: translateX(100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n`;\n\n// User interface is now imported from apiService\n\nconst UserManagement = () => {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: 'Agent',\n    password: '',\n    confirmPassword: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [notification, setNotification] = useState(null);\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Auto-hide notification after 5 seconds\n  useEffect(() => {\n    if (notification) {\n      const timer = setTimeout(() => {\n        setNotification(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [notification]);\n  const showNotification = (type, message) => {\n    setNotification({\n      type,\n      message\n    });\n  };\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const usersData = await apiService.getUsers();\n      setUsers(usersData);\n    } catch (error) {\n      console.error('Error loading users:', error);\n      // Show user-friendly error message\n      alert('Failed to load users. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!editingUser) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 6) {\n        newErrors.password = 'Password must be at least 6 characters';\n      }\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    try {\n      const userData = {\n        username: formData.username,\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        email: formData.email,\n        role: formData.role,\n        ...(formData.password && {\n          password: formData.password\n        })\n      };\n      if (editingUser) {\n        // Update existing user\n        const updatedUser = await apiService.updateUser(editingUser.userId, userData);\n        setUsers(prev => prev.map(user => user.userId === editingUser.userId ? updatedUser : user));\n        alert('User updated successfully!');\n      } else {\n        // Create new user\n        const newUser = await apiService.createUser(userData);\n        setUsers(prev => [...prev, newUser]);\n        alert('User created successfully!');\n      }\n      handleCloseModal();\n    } catch (error) {\n      console.error('Error saving user:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to save user. Please try again.';\n      alert(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n  const handleEditUser = user => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      email: user.email,\n      role: user.role,\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n  const handleToggleUserStatus = async userId => {\n    try {\n      await apiService.toggleUserStatus(userId);\n      // Update the local state to reflect the change\n      setUsers(prev => prev.map(user => user.userId === userId ? {\n        ...user,\n        isActive: !user.isActive\n      } : user));\n      alert('User status updated successfully!');\n    } catch (error) {\n      console.error('Error toggling user status:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update user status. Please try again.';\n      alert(errorMessage);\n    }\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n  };\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: handleBack,\n          children: \"\\u2190 Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          style: {\n            marginLeft: '20px'\n          },\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCreateUser,\n        children: \"+ Create User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          padding: '60px',\n          flexDirection: 'column',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Loading users...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Created Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Last Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: users.map(user => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [user.firstName, \" \", user.lastName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(RoleBadge, {\n                    role: user.role,\n                    children: user.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                    active: user.isActive,\n                    children: user.isActive ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatDate(user.createdDate)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Never'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      onClick: () => handleEditUser(user),\n                      children: \"Edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: user.isActive ? 'danger' : 'secondary',\n                      onClick: () => handleToggleUserStatus(user.userId),\n                      children: user.isActive ? 'Deactivate' : 'Activate'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 23\n                }, this)]\n              }, user.userId, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), users.length === 0 && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '60px',\n            color: '#777',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              opacity: 0.3\n            },\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '18px',\n              fontWeight: 500\n            },\n            children: \"No users found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px'\n            },\n            children: \"Click \\\"Create User\\\" to add the first user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n          children: [/*#__PURE__*/_jsxDEV(ModalTitle, {\n            children: editingUser ? 'Edit User' : 'Create New User'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CloseButton, {\n            onClick: handleCloseModal,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"username\",\n              children: \"Username *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"username\",\n              name: \"username\",\n              value: formData.username,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this), errors.username && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"firstName\",\n              children: \"First Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"firstName\",\n              name: \"firstName\",\n              value: formData.firstName,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), errors.firstName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.firstName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 36\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"lastName\",\n              children: \"Last Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"lastName\",\n              name: \"lastName\",\n              value: formData.lastName,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), errors.lastName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.lastName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"email\",\n              children: \"Email *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"role\",\n              children: \"Role *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              id: \"role\",\n              name: \"role\",\n              value: formData.role,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Agent\",\n                children: \"Agent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Supervisor\",\n                children: \"Supervisor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Admin\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), !editingUser && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"password\",\n                children: \"Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"confirmPassword\",\n                children: \"Confirm Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"password\",\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 46\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '10px',\n              justifyContent: 'flex-end',\n              marginTop: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: handleCloseModal,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: loading,\n              children: loading ? 'Saving...' : editingUser ? 'Update User' : 'Create User'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 395,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagement, \"POzTC7bYeFuxGGKRPCqT/QcjJ1I=\", false, function () {\n  return [useNavigate];\n});\n_c14 = UserManagement;\nexport default UserManagement;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"TableContainer\");\n$RefreshReg$(_c5, \"Table\");\n$RefreshReg$(_c6, \"TableHeader\");\n$RefreshReg$(_c7, \"TableCell\");\n$RefreshReg$(_c8, \"TableRow\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"RoleBadge\");\n$RefreshReg$(_c1, \"Modal\");\n$RefreshReg$(_c10, \"ModalContent\");\n$RefreshReg$(_c11, \"ModalHeader\");\n$RefreshReg$(_c12, \"ModalTitle\");\n$RefreshReg$(_c13, \"CloseButton\");\n$RefreshReg$(_c14, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "Input", "Select", "FormGroup", "Label", "ErrorMessage", "LoadingSpinner", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Container", "div", "_c", "Header", "props", "theme", "colors", "mediumGray", "_c2", "Title", "h1", "primary", "_c3", "TableContainer", "_c4", "Table", "table", "_c5", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c6", "TableCell", "td", "_c7", "TableRow", "tr", "_c8", "StatusBadge", "span", "active", "_c9", "RoleBadge", "role", "_c0", "Modal", "show", "_c1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "white", "borderRadius", "md", "_c10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c11", "ModalTitle", "h2", "_c12", "CloseButton", "button", "textDark", "_c13", "Notification", "lg", "shadows", "type", "success", "error", "UserManagement", "_s", "navigate", "users", "setUsers", "showModal", "setShowModal", "editingUser", "setEditingUser", "loading", "setLoading", "submitting", "setSubmitting", "formData", "setFormData", "username", "firstName", "lastName", "email", "password", "confirmPassword", "errors", "setErrors", "notification", "setNotification", "loadUsers", "timer", "setTimeout", "clearTimeout", "showNotification", "message", "usersData", "getUsers", "console", "alert", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "trim", "test", "length", "Object", "keys", "handleSubmit", "preventDefault", "userData", "updatedUser", "updateUser", "userId", "map", "user", "newUser", "createUser", "handleCloseModal", "errorMessage", "Error", "handleCreateUser", "handleEditUser", "handleToggleUserStatus", "toggleUserStatus", "isActive", "handleBack", "formatDate", "dateString", "Date", "toLocaleDateString", "children", "style", "display", "alignItems", "variant", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "justifyContent", "padding", "flexDirection", "gap", "size", "color", "createdDate", "lastLoginDate", "textAlign", "fontSize", "opacity", "fontWeight", "onSubmit", "htmlFor", "id", "onChange", "required", "marginTop", "disabled", "_c14", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/UserManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, User } from '../../services/apiService';\n\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ active: boolean }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ${props => props.active ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.active ? '#2e7d32' : '#c62828'};\n`;\n\nconst RoleBadge = styled.span<{ role: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.role) {\n      case 'Admin':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'Supervisor':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'Agent':\n        return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst Modal = styled.div<{ show: boolean }>`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst ModalContent = styled.div`\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n`;\n\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst ModalTitle = styled.h2`\n  color: ${props => props.theme.colors.primary};\n  margin: 0;\n`;\n\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ${props => props.theme.colors.textMedium};\n\n  &:hover {\n    color: ${props => props.theme.colors.textDark};\n  }\n`;\n\nconst Notification = styled.div<{ type: 'success' | 'error' }>`\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  padding: 16px 24px;\n  border-radius: ${props => props.theme.borderRadius.lg};\n  color: white;\n  font-weight: 500;\n  z-index: 1000;\n  animation: slideIn 0.3s ease-out;\n  box-shadow: ${props => props.theme.shadows.lg};\n\n  ${props => props.type === 'success' ? `\n    background: ${props.theme.colors.success};\n  ` : `\n    background: ${props.theme.colors.error};\n  `}\n\n  @keyframes slideIn {\n    from {\n      transform: translateX(100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n`;\n\n// User interface is now imported from apiService\n\nconst UserManagement: React.FC = () => {\n  const navigate = useNavigate();\n  const [users, setUsers] = useState<User[]>([]);\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: 'Agent' as 'Agent' | 'Supervisor' | 'Admin',\n    password: '',\n    confirmPassword: '',\n  });\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\n  const [notification, setNotification] = useState<{\n    type: 'success' | 'error';\n    message: string;\n  } | null>(null);\n\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Auto-hide notification after 5 seconds\n  useEffect(() => {\n    if (notification) {\n      const timer = setTimeout(() => {\n        setNotification(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [notification]);\n\n  const showNotification = (type: 'success' | 'error', message: string) => {\n    setNotification({ type, message });\n  };\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const usersData = await apiService.getUsers();\n      setUsers(usersData);\n    } catch (error) {\n      console.error('Error loading users:', error);\n      // Show user-friendly error message\n      alert('Failed to load users. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: { [key: string]: string } = {};\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!editingUser) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 6) {\n        newErrors.password = 'Password must be at least 6 characters';\n      }\n\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const userData = {\n        username: formData.username,\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        email: formData.email,\n        role: formData.role,\n        ...(formData.password && { password: formData.password }),\n      };\n\n      if (editingUser) {\n        // Update existing user\n        const updatedUser = await apiService.updateUser(editingUser.userId, userData);\n        setUsers(prev => prev.map(user =>\n          user.userId === editingUser.userId ? updatedUser : user\n        ));\n        alert('User updated successfully!');\n      } else {\n        // Create new user\n        const newUser = await apiService.createUser(userData);\n        setUsers(prev => [...prev, newUser]);\n        alert('User created successfully!');\n      }\n\n      handleCloseModal();\n    } catch (error) {\n      console.error('Error saving user:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to save user. Please try again.';\n      alert(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      email: user.email,\n      role: user.role,\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleToggleUserStatus = async (userId: number) => {\n    try {\n      await apiService.toggleUserStatus(userId);\n      // Update the local state to reflect the change\n      setUsers(prev => prev.map(user =>\n        user.userId === userId ? { ...user, isActive: !user.isActive } : user\n      ));\n      alert('User status updated successfully!');\n    } catch (error) {\n      console.error('Error toggling user status:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update user status. Please try again.';\n      alert(errorMessage);\n    }\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n  };\n\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  return (\n    <Container>\n      <Header>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <Button variant=\"outline\" onClick={handleBack}>\n            ← Back\n          </Button>\n          <Title style={{ marginLeft: '20px' }}>User Management</Title>\n        </div>\n        <Button onClick={handleCreateUser}>\n          + Create User\n        </Button>\n      </Header>\n\n      <Card>\n        {loading ? (\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            padding: '60px',\n            flexDirection: 'column',\n            gap: '16px'\n          }}>\n            <LoadingSpinner size=\"lg\" />\n            <div style={{ color: '#666' }}>Loading users...</div>\n          </div>\n        ) : (\n          <>\n            <TableContainer>\n              <Table>\n                <thead>\n                  <tr>\n                    <TableHeader>Name</TableHeader>\n                    <TableHeader>Username</TableHeader>\n                    <TableHeader>Email</TableHeader>\n                    <TableHeader>Role</TableHeader>\n                    <TableHeader>Status</TableHeader>\n                    <TableHeader>Created Date</TableHeader>\n                    <TableHeader>Last Login</TableHeader>\n                    <TableHeader>Actions</TableHeader>\n                  </tr>\n                </thead>\n                <tbody>\n                  {users.map((user) => (\n                    <TableRow key={user.userId}>\n                      <TableCell>{user.firstName} {user.lastName}</TableCell>\n                      <TableCell>{user.username}</TableCell>\n                      <TableCell>{user.email}</TableCell>\n                      <TableCell>\n                        <RoleBadge role={user.role}>{user.role}</RoleBadge>\n                      </TableCell>\n                      <TableCell>\n                        <StatusBadge active={user.isActive}>\n                          {user.isActive ? 'Active' : 'Inactive'}\n                        </StatusBadge>\n                      </TableCell>\n                      <TableCell>{formatDate(user.createdDate)}</TableCell>\n                      <TableCell>\n                        {user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Never'}\n                      </TableCell>\n                      <TableCell>\n                        <div style={{ display: 'flex', gap: '8px' }}>\n                          <Button\n                            size=\"sm\"\n                            onClick={() => handleEditUser(user)}\n                          >\n                            Edit\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant={user.isActive ? 'danger' : 'secondary'}\n                            onClick={() => handleToggleUserStatus(user.userId)}\n                          >\n                            {user.isActive ? 'Deactivate' : 'Activate'}\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </tbody>\n              </Table>\n            </TableContainer>\n\n            {users.length === 0 && !loading && (\n              <div style={{\n                textAlign: 'center',\n                padding: '60px',\n                color: '#777',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '16px'\n              }}>\n                <div style={{ fontSize: '48px', opacity: 0.3 }}>👥</div>\n                <div style={{ fontSize: '18px', fontWeight: 500 }}>No users found</div>\n                <div style={{ fontSize: '14px' }}>Click \"Create User\" to add the first user</div>\n              </div>\n            )}\n          </>\n        )}\n      </Card>\n\n      {/* Create/Edit User Modal */}\n      <Modal show={showModal}>\n        <ModalContent>\n          <ModalHeader>\n            <ModalTitle>\n              {editingUser ? 'Edit User' : 'Create New User'}\n            </ModalTitle>\n            <CloseButton onClick={handleCloseModal}>×</CloseButton>\n          </ModalHeader>\n\n          <form onSubmit={handleSubmit}>\n            <FormGroup>\n              <Label htmlFor=\"username\">Username *</Label>\n              <Input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                value={formData.username}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.username && <ErrorMessage>{errors.username}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"firstName\">First Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"firstName\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.firstName && <ErrorMessage>{errors.firstName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"lastName\">Last Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"lastName\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.lastName && <ErrorMessage>{errors.lastName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"email\">Email *</Label>\n              <Input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"role\">Role *</Label>\n              <Select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"Agent\">Agent</option>\n                <option value=\"Supervisor\">Supervisor</option>\n                <option value=\"Admin\">Admin</option>\n              </Select>\n            </FormGroup>\n\n            {!editingUser && (\n              <>\n                <FormGroup>\n                  <Label htmlFor=\"password\">Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.password && <ErrorMessage>{errors.password}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label htmlFor=\"confirmPassword\">Confirm Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    value={formData.confirmPassword}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.confirmPassword && <ErrorMessage>{errors.confirmPassword}</ErrorMessage>}\n                </FormGroup>\n              </>\n            )}\n\n            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}>\n              <Button type=\"button\" variant=\"outline\" onClick={handleCloseModal}>\n                Cancel\n              </Button>\n              <Button type=\"submit\" disabled={loading}>\n                {loading ? 'Saving...' : (editingUser ? 'Update User' : 'Create User')}\n              </Button>\n            </div>\n          </form>\n        </ModalContent>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,cAAc,QAAQ,2BAA2B;AACvH,SAASC,UAAU,QAAc,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,SAAS,GAAGd,MAAM,CAACe,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,SAAS;AAMf,MAAMG,MAAM,GAAGjB,MAAM,CAACe,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACC,GAAA,GAPIL,MAAM;AASZ,MAAMM,KAAK,GAAGvB,MAAM,CAACwB,EAAE;AACvB;AACA;AACA,WAAWN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C,CAAC;AAACC,GAAA,GAJIH,KAAK;AAMX,MAAMI,cAAc,GAAG3B,MAAM,CAACe,GAAG;AACjC;AACA,CAAC;AAACa,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAG7B,MAAM,CAAC8B,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAGhC,MAAM,CAACiC,EAAE;AAC7B;AACA;AACA,6BAA6Bf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAClE,sBAAsBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACe,QAAQ;AAC1D;AACA,WAAWjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU;AACjD,CAAC;AAACC,GAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAGtC,MAAM,CAACuC,EAAE;AAC3B;AACA;AACA,6BAA6BrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAClE,CAAC;AAACM,GAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGzC,MAAM,CAAC0C,EAAE;AAC1B;AACA,wBAAwBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAC7D;AACA,CAAC;AAACS,GAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAG5C,MAAM,CAAC6C,IAAyB;AACpD;AACA;AACA;AACA;AACA;AACA,sBAAsB3B,KAAK,IAAIA,KAAK,CAAC4B,MAAM,GAAG,SAAS,GAAG,SAAS;AACnE,WAAW5B,KAAK,IAAIA,KAAK,CAAC4B,MAAM,GAAG,SAAS,GAAG,SAAS;AACxD,CAAC;AAACC,GAAA,GARIH,WAAW;AAUjB,MAAMI,SAAS,GAAGhD,MAAM,CAAC6C,IAAsB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,IAAI3B,KAAK,IAAI;EACT,QAAQA,KAAK,CAAC+B,IAAI;IAChB,KAAK,OAAO;MACV,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,YAAY;MACf,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,OAAO;MACV,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA/BIF,SAAS;AAiCf,MAAMG,KAAK,GAAGnD,MAAM,CAACe,GAAsB;AAC3C,aAAaG,KAAK,IAAIA,KAAK,CAACkC,IAAI,GAAG,MAAM,GAAG,MAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,KAAK;AAaX,MAAMG,YAAY,GAAGtD,MAAM,CAACe,GAAG;AAC/B,gBAAgBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACmC,KAAK;AACjD,mBAAmBrC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqC,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GARIJ,YAAY;AAUlB,MAAMK,WAAW,GAAG3D,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACuC,IAAA,GAPID,WAAW;AASjB,MAAME,UAAU,GAAG7D,MAAM,CAAC8D,EAAE;AAC5B,WAAW5C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C;AACA,CAAC;AAACsC,IAAA,GAHIF,UAAU;AAKhB,MAAMG,WAAW,GAAGhE,MAAM,CAACiE,MAAM;AACjC;AACA;AACA;AACA;AACA,WAAW/C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU;AACjD;AACA;AACA,aAAalB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC8C,QAAQ;AACjD;AACA,CAAC;AAACC,IAAA,GAVIH,WAAW;AAYjB,MAAMI,YAAY,GAAGpE,MAAM,CAACe,GAAkC;AAC9D;AACA;AACA;AACA;AACA,mBAAmBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqC,YAAY,CAACa,EAAE;AACvD;AACA;AACA;AACA;AACA,gBAAgBnD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmD,OAAO,CAACD,EAAE;AAC/C;AACA,IAAInD,KAAK,IAAIA,KAAK,CAACqD,IAAI,KAAK,SAAS,GAAG;AACxC,kBAAkBrD,KAAK,CAACC,KAAK,CAACC,MAAM,CAACoD,OAAO;AAC5C,GAAG,GAAG;AACN,kBAAkBtD,KAAK,CAACC,KAAK,CAACC,MAAM,CAACqD,KAAK;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAG7E,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8E,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACsF,OAAO,EAAEC,UAAU,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwF,UAAU,EAAEC,aAAa,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0F,QAAQ,EAAEC,WAAW,CAAC,GAAG3F,QAAQ,CAAC;IACvC4F,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACT3C,IAAI,EAAE,OAA2C;IACjD4C,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnG,QAAQ,CAA4B,CAAC,CAAC,CAAC;EACnE,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAGtC,IAAI,CAAC;EAEfC,SAAS,CAAC,MAAM;IACdqG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArG,SAAS,CAAC,MAAM;IACd,IAAImG,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BH,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMI,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACH,YAAY,CAAC,CAAC;EAElB,MAAMM,gBAAgB,GAAGA,CAAChC,IAAyB,EAAEiC,OAAe,KAAK;IACvEN,eAAe,CAAC;MAAE3B,IAAI;MAAEiC;IAAQ,CAAC,CAAC;EACpC,CAAC;EAED,MAAML,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,SAAS,GAAG,MAAMhG,UAAU,CAACiG,QAAQ,CAAC,CAAC;MAC7C5B,QAAQ,CAAC2B,SAAS,CAAC;IACrB,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAmC,KAAK,CAAC,yCAAyC,CAAC;IAClD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzB,WAAW,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAIjB,MAAM,CAACgB,IAAI,CAAC,EAAE;MAChBf,SAAS,CAACkB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAoC,GAAG,CAAC,CAAC;IAE/C,IAAI,CAAC7B,QAAQ,CAACE,QAAQ,CAAC4B,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAAC3B,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACF,QAAQ,CAACG,SAAS,CAAC2B,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAAC1B,SAAS,GAAG,wBAAwB;IAChD;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAAC0B,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACzB,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACJ,QAAQ,CAACK,KAAK,CAACyB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACxB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC0B,IAAI,CAAC/B,QAAQ,CAACK,KAAK,CAAC,EAAE;MAC/CwB,SAAS,CAACxB,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACX,WAAW,EAAE;MAChB,IAAI,CAACM,QAAQ,CAACM,QAAQ,EAAE;QACtBuB,SAAS,CAACvB,QAAQ,GAAG,sBAAsB;MAC7C,CAAC,MAAM,IAAIN,QAAQ,CAACM,QAAQ,CAAC0B,MAAM,GAAG,CAAC,EAAE;QACvCH,SAAS,CAACvB,QAAQ,GAAG,wCAAwC;MAC/D;MAEA,IAAIN,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,eAAe,EAAE;QAClDsB,SAAS,CAACtB,eAAe,GAAG,wBAAwB;MACtD;IACF;IAEAE,SAAS,CAACoB,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOZ,CAAkB,IAAK;IACjDA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA/B,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMwC,QAAQ,GAAG;QACfnC,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;QAC7BC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK;QACrB3C,IAAI,EAAEsC,QAAQ,CAACtC,IAAI;QACnB,IAAIsC,QAAQ,CAACM,QAAQ,IAAI;UAAEA,QAAQ,EAAEN,QAAQ,CAACM;QAAS,CAAC;MAC1D,CAAC;MAED,IAAIZ,WAAW,EAAE;QACf;QACA,MAAM4C,WAAW,GAAG,MAAMpH,UAAU,CAACqH,UAAU,CAAC7C,WAAW,CAAC8C,MAAM,EAAEH,QAAQ,CAAC;QAC7E9C,QAAQ,CAACoC,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACF,MAAM,KAAK9C,WAAW,CAAC8C,MAAM,GAAGF,WAAW,GAAGI,IACrD,CAAC,CAAC;QACFrB,KAAK,CAAC,4BAA4B,CAAC;MACrC,CAAC,MAAM;QACL;QACA,MAAMsB,OAAO,GAAG,MAAMzH,UAAU,CAAC0H,UAAU,CAACP,QAAQ,CAAC;QACrD9C,QAAQ,CAACoC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEgB,OAAO,CAAC,CAAC;QACpCtB,KAAK,CAAC,4BAA4B,CAAC;MACrC;MAEAwB,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO3D,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM4D,YAAY,GAAG5D,KAAK,YAAY6D,KAAK,GAAG7D,KAAK,CAAC+B,OAAO,GAAG,wCAAwC;MACtGI,KAAK,CAACyB,YAAY,CAAC;IACrB,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrD,cAAc,CAAC,IAAI,CAAC;IACpBM,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACT3C,IAAI,EAAE,OAAO;MACb4C,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbhB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMwD,cAAc,GAAIP,IAAU,IAAK;IACrC/C,cAAc,CAAC+C,IAAI,CAAC;IACpBzC,WAAW,CAAC;MACVC,QAAQ,EAAEwC,IAAI,CAACxC,QAAQ;MACvBC,SAAS,EAAEuC,IAAI,CAACvC,SAAS;MACzBC,QAAQ,EAAEsC,IAAI,CAACtC,QAAQ;MACvBC,KAAK,EAAEqC,IAAI,CAACrC,KAAK;MACjB3C,IAAI,EAAEgF,IAAI,CAAChF,IAAI;MACf4C,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbhB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMyD,sBAAsB,GAAG,MAAOV,MAAc,IAAK;IACvD,IAAI;MACF,MAAMtH,UAAU,CAACiI,gBAAgB,CAACX,MAAM,CAAC;MACzC;MACAjD,QAAQ,CAACoC,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACF,MAAM,KAAKA,MAAM,GAAG;QAAE,GAAGE,IAAI;QAAEU,QAAQ,EAAE,CAACV,IAAI,CAACU;MAAS,CAAC,GAAGV,IACnE,CAAC,CAAC;MACFrB,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAM4D,YAAY,GAAG5D,KAAK,YAAY6D,KAAK,GAAG7D,KAAK,CAAC+B,OAAO,GAAG,iDAAiD;MAC/GI,KAAK,CAACyB,YAAY,CAAC;IACrB;EACF,CAAC;EAED,MAAMD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpD,YAAY,CAAC,KAAK,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;IACpBM,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACT3C,IAAI,EAAE,OAAO;MACb4C,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;EAED,MAAM4C,UAAU,GAAGA,CAAA,KAAM;IACvBhE,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,MAAMiE,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,oBACErI,OAAA,CAACG,SAAS;IAAAmI,QAAA,gBACRtI,OAAA,CAACM,MAAM;MAAAgI,QAAA,gBACLtI,OAAA;QAAKuI,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACpDtI,OAAA,CAACT,MAAM;UAACmJ,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEV,UAAW;UAAAK,QAAA,EAAC;QAE/C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA,CAACY,KAAK;UAAC2H,KAAK,EAAE;YAAES,UAAU,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACN/I,OAAA,CAACT,MAAM;QAACoJ,OAAO,EAAEf,gBAAiB;QAAAU,QAAA,EAAC;MAEnC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAET/I,OAAA,CAACV,IAAI;MAAAgJ,QAAA,EACF9D,OAAO,gBACNxE,OAAA;QAAKuI,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfS,cAAc,EAAE,QAAQ;UACxBR,UAAU,EAAE,QAAQ;UACpBS,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE;QACP,CAAE;QAAAd,QAAA,gBACAtI,OAAA,CAACH,cAAc;UAACwJ,IAAI,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B/I,OAAA;UAAKuI,KAAK,EAAE;YAAEe,KAAK,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAAgB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,gBAEN/I,OAAA,CAAAE,SAAA;QAAAoI,QAAA,gBACEtI,OAAA,CAACgB,cAAc;UAAAsH,QAAA,eACbtI,OAAA,CAACkB,KAAK;YAAAoH,QAAA,gBACJtI,OAAA;cAAAsI,QAAA,eACEtI,OAAA;gBAAAsI,QAAA,gBACEtI,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC/B/I,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnC/I,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAChC/I,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC/B/I,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACjC/I,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACvC/I,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrC/I,OAAA,CAACqB,WAAW;kBAAAiH,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR/I,OAAA;cAAAsI,QAAA,EACGpE,KAAK,CAACmD,GAAG,CAAEC,IAAI,iBACdtH,OAAA,CAAC8B,QAAQ;gBAAAwG,QAAA,gBACPtI,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,GAAEhB,IAAI,CAACvC,SAAS,EAAC,GAAC,EAACuC,IAAI,CAACtC,QAAQ;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvD/I,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,EAAEhB,IAAI,CAACxC;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtC/I,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,EAAEhB,IAAI,CAACrC;gBAAK;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC/I,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,eACRtI,OAAA,CAACqC,SAAS;oBAACC,IAAI,EAAEgF,IAAI,CAAChF,IAAK;oBAAAgG,QAAA,EAAEhB,IAAI,CAAChF;kBAAI;oBAAAsG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACZ/I,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,eACRtI,OAAA,CAACiC,WAAW;oBAACE,MAAM,EAAEmF,IAAI,CAACU,QAAS;oBAAAM,QAAA,EAChChB,IAAI,CAACU,QAAQ,GAAG,QAAQ,GAAG;kBAAU;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACZ/I,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,EAAEJ,UAAU,CAACZ,IAAI,CAACiC,WAAW;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrD/I,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,EACPhB,IAAI,CAACkC,aAAa,GAAGtB,UAAU,CAACZ,IAAI,CAACkC,aAAa,CAAC,GAAG;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACZ/I,OAAA,CAAC2B,SAAS;kBAAA2G,QAAA,eACRtI,OAAA;oBAAKuI,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEY,GAAG,EAAE;oBAAM,CAAE;oBAAAd,QAAA,gBAC1CtI,OAAA,CAACT,MAAM;sBACL8J,IAAI,EAAC,IAAI;sBACTV,OAAO,EAAEA,CAAA,KAAMd,cAAc,CAACP,IAAI,CAAE;sBAAAgB,QAAA,EACrC;oBAED;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT/I,OAAA,CAACT,MAAM;sBACL8J,IAAI,EAAC,IAAI;sBACTX,OAAO,EAAEpB,IAAI,CAACU,QAAQ,GAAG,QAAQ,GAAG,WAAY;sBAChDW,OAAO,EAAEA,CAAA,KAAMb,sBAAsB,CAACR,IAAI,CAACF,MAAM,CAAE;sBAAAkB,QAAA,EAElDhB,IAAI,CAACU,QAAQ,GAAG,YAAY,GAAG;oBAAU;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GAhCCzB,IAAI,CAACF,MAAM;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiChB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,EAEhB7E,KAAK,CAAC0C,MAAM,KAAK,CAAC,IAAI,CAACpC,OAAO,iBAC7BxE,OAAA;UAAKuI,KAAK,EAAE;YACVkB,SAAS,EAAE,QAAQ;YACnBP,OAAO,EAAE,MAAM;YACfI,KAAK,EAAE,MAAM;YACbd,OAAO,EAAE,MAAM;YACfW,aAAa,EAAE,QAAQ;YACvBV,UAAU,EAAE,QAAQ;YACpBW,GAAG,EAAE;UACP,CAAE;UAAAd,QAAA,gBACAtI,OAAA;YAAKuI,KAAK,EAAE;cAAEmB,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAI,CAAE;YAAArB,QAAA,EAAC;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxD/I,OAAA;YAAKuI,KAAK,EAAE;cAAEmB,QAAQ,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAI,CAAE;YAAAtB,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvE/I,OAAA;YAAKuI,KAAK,EAAE;cAAEmB,QAAQ,EAAE;YAAO,CAAE;YAAApB,QAAA,EAAC;UAAyC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CACN;MAAA,eACD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGP/I,OAAA,CAACwC,KAAK;MAACC,IAAI,EAAE2B,SAAU;MAAAkE,QAAA,eACrBtI,OAAA,CAAC2C,YAAY;QAAA2F,QAAA,gBACXtI,OAAA,CAACgD,WAAW;UAAAsF,QAAA,gBACVtI,OAAA,CAACkD,UAAU;YAAAoF,QAAA,EACRhE,WAAW,GAAG,WAAW,GAAG;UAAiB;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACb/I,OAAA,CAACqD,WAAW;YAACsF,OAAO,EAAElB,gBAAiB;YAAAa,QAAA,EAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAEd/I,OAAA;UAAM6J,QAAQ,EAAE9C,YAAa;UAAAuB,QAAA,gBAC3BtI,OAAA,CAACN,SAAS;YAAA4I,QAAA,gBACRtI,OAAA,CAACL,KAAK;cAACmK,OAAO,EAAC,UAAU;cAAAxB,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C/I,OAAA,CAACR,KAAK;cACJoE,IAAI,EAAC,MAAM;cACXmG,EAAE,EAAC,UAAU;cACb3D,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEzB,QAAQ,CAACE,QAAS;cACzBkF,QAAQ,EAAE9D,iBAAkB;cAC5B+D,QAAQ;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACD3D,MAAM,CAACN,QAAQ,iBAAI9E,OAAA,CAACJ,YAAY;cAAA0I,QAAA,EAAElD,MAAM,CAACN;YAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAEZ/I,OAAA,CAACN,SAAS;YAAA4I,QAAA,gBACRtI,OAAA,CAACL,KAAK;cAACmK,OAAO,EAAC,WAAW;cAAAxB,QAAA,EAAC;YAAY;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/C/I,OAAA,CAACR,KAAK;cACJoE,IAAI,EAAC,MAAM;cACXmG,EAAE,EAAC,WAAW;cACd3D,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEzB,QAAQ,CAACG,SAAU;cAC1BiF,QAAQ,EAAE9D,iBAAkB;cAC5B+D,QAAQ;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACD3D,MAAM,CAACL,SAAS,iBAAI/E,OAAA,CAACJ,YAAY;cAAA0I,QAAA,EAAElD,MAAM,CAACL;YAAS;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEZ/I,OAAA,CAACN,SAAS;YAAA4I,QAAA,gBACRtI,OAAA,CAACL,KAAK;cAACmK,OAAO,EAAC,UAAU;cAAAxB,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7C/I,OAAA,CAACR,KAAK;cACJoE,IAAI,EAAC,MAAM;cACXmG,EAAE,EAAC,UAAU;cACb3D,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEzB,QAAQ,CAACI,QAAS;cACzBgF,QAAQ,EAAE9D,iBAAkB;cAC5B+D,QAAQ;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACD3D,MAAM,CAACJ,QAAQ,iBAAIhF,OAAA,CAACJ,YAAY;cAAA0I,QAAA,EAAElD,MAAM,CAACJ;YAAQ;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAEZ/I,OAAA,CAACN,SAAS;YAAA4I,QAAA,gBACRtI,OAAA,CAACL,KAAK;cAACmK,OAAO,EAAC,OAAO;cAAAxB,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtC/I,OAAA,CAACR,KAAK;cACJoE,IAAI,EAAC,OAAO;cACZmG,EAAE,EAAC,OAAO;cACV3D,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEzB,QAAQ,CAACK,KAAM;cACtB+E,QAAQ,EAAE9D,iBAAkB;cAC5B+D,QAAQ;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACD3D,MAAM,CAACH,KAAK,iBAAIjF,OAAA,CAACJ,YAAY;cAAA0I,QAAA,EAAElD,MAAM,CAACH;YAAK;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEZ/I,OAAA,CAACN,SAAS;YAAA4I,QAAA,gBACRtI,OAAA,CAACL,KAAK;cAACmK,OAAO,EAAC,MAAM;cAAAxB,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpC/I,OAAA,CAACP,MAAM;cACLsK,EAAE,EAAC,MAAM;cACT3D,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEzB,QAAQ,CAACtC,IAAK;cACrB0H,QAAQ,EAAE9D,iBAAkB;cAC5B+D,QAAQ;cAAA3B,QAAA,gBAERtI,OAAA;gBAAQqG,KAAK,EAAC,OAAO;gBAAAiC,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC/I,OAAA;gBAAQqG,KAAK,EAAC,YAAY;gBAAAiC,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C/I,OAAA;gBAAQqG,KAAK,EAAC,OAAO;gBAAAiC,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAEX,CAACzE,WAAW,iBACXtE,OAAA,CAAAE,SAAA;YAAAoI,QAAA,gBACEtI,OAAA,CAACN,SAAS;cAAA4I,QAAA,gBACRtI,OAAA,CAACL,KAAK;gBAACmK,OAAO,EAAC,UAAU;gBAAAxB,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C/I,OAAA,CAACR,KAAK;gBACJoE,IAAI,EAAC,UAAU;gBACfmG,EAAE,EAAC,UAAU;gBACb3D,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEzB,QAAQ,CAACM,QAAS;gBACzB8E,QAAQ,EAAE9D,iBAAkB;gBAC5B+D,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACD3D,MAAM,CAACF,QAAQ,iBAAIlF,OAAA,CAACJ,YAAY;gBAAA0I,QAAA,EAAElD,MAAM,CAACF;cAAQ;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eAEZ/I,OAAA,CAACN,SAAS;cAAA4I,QAAA,gBACRtI,OAAA,CAACL,KAAK;gBAACmK,OAAO,EAAC,iBAAiB;gBAAAxB,QAAA,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3D/I,OAAA,CAACR,KAAK;gBACJoE,IAAI,EAAC,UAAU;gBACfmG,EAAE,EAAC,iBAAiB;gBACpB3D,IAAI,EAAC,iBAAiB;gBACtBC,KAAK,EAAEzB,QAAQ,CAACO,eAAgB;gBAChC6E,QAAQ,EAAE9D,iBAAkB;gBAC5B+D,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACD3D,MAAM,CAACD,eAAe,iBAAInF,OAAA,CAACJ,YAAY;gBAAA0I,QAAA,EAAElD,MAAM,CAACD;cAAe;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA,eACZ,CACH,eAED/I,OAAA;YAAKuI,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEY,GAAG,EAAE,MAAM;cAAEH,cAAc,EAAE,UAAU;cAAEiB,SAAS,EAAE;YAAO,CAAE;YAAA5B,QAAA,gBAC1FtI,OAAA,CAACT,MAAM;cAACqE,IAAI,EAAC,QAAQ;cAAC8E,OAAO,EAAC,SAAS;cAACC,OAAO,EAAElB,gBAAiB;cAAAa,QAAA,EAAC;YAEnE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/I,OAAA,CAACT,MAAM;cAACqE,IAAI,EAAC,QAAQ;cAACuG,QAAQ,EAAE3F,OAAQ;cAAA8D,QAAA,EACrC9D,OAAO,GAAG,WAAW,GAAIF,WAAW,GAAG,aAAa,GAAG;YAAc;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAC/E,EAAA,CApbID,cAAwB;EAAA,QACX3E,WAAW;AAAA;AAAAgL,IAAA,GADxBrG,cAAwB;AAsb9B,eAAeA,cAAc;AAAC,IAAA1D,EAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAA4G,IAAA;AAAAC,YAAA,CAAAhK,EAAA;AAAAgK,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAtJ,GAAA;AAAAsJ,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAAjJ,GAAA;AAAAiJ,YAAA,CAAA3I,GAAA;AAAA2I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAtH,IAAA;AAAAsH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA7G,IAAA;AAAA6G,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}