{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Demo\\\\ModernUIShowcase.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Container, Card, Button, Input, FormGroup, Label, Badge, Avatar, Divider, LoadingSpinner, TooltipWrapper, Tooltip } from '../../styles/GlobalStyles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShowcaseContainer = styled.div`\n  min-height: 100vh;\n  padding: ${props => props.theme.spacing.xl};\n  background: linear-gradient(135deg, \n    ${props => props.theme.colors.backgroundSecondary} 0%, \n    ${props => props.theme.colors.backgroundTertiary} 100%\n  );\n`;\n_c = ShowcaseContainer;\nconst Title = styled.h1`\n  font-size: ${props => props.theme.typography.fontSize['4xl']};\n  font-weight: ${props => props.theme.typography.fontWeight.bold};\n  background: ${props => props.theme.colors.primaryGradient};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: ${props => props.theme.spacing['2xl']};\n`;\n_c2 = Title;\nconst Section = styled.div`\n  margin-bottom: ${props => props.theme.spacing['2xl']};\n`;\n_c3 = Section;\nconst SectionTitle = styled.h2`\n  font-size: ${props => props.theme.typography.fontSize['2xl']};\n  font-weight: ${props => props.theme.typography.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n_c4 = SectionTitle;\nconst Grid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: ${props => props.theme.spacing.lg};\n`;\n_c5 = Grid;\nconst FlexContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${props => props.theme.spacing.md};\n  align-items: center;\n`;\n_c6 = FlexContainer;\nconst ModernUIShowcase = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const handleLoadingDemo = () => {\n    setLoading(true);\n    setTimeout(() => setLoading(false), 3000);\n  };\n  return /*#__PURE__*/_jsxDEV(ShowcaseContainer, {\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"Modern UI/UX Showcase\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Section, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Modern Cards\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            variant: \"default\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Default Card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"This is a default card with modern styling, subtle shadows, and smooth hover effects.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"glass\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Glass Card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Glassmorphism effect with backdrop blur and transparency for a modern look.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"neumorphism\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Neumorphism Card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Soft, extruded design that appears to emerge from the background.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Section, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Modern Buttons\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(FlexContainer, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              children: \"Primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              children: \"Secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              children: \"Outline\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"ghost\",\n              children: \"Ghost\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              children: \"Success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              children: \"Danger\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            spacing: \"lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlexContainer, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"xs\",\n              children: \"Extra Small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"sm\",\n              children: \"Small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"md\",\n              children: \"Medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"lg\",\n              children: \"Large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"xl\",\n              children: \"Extra Large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            spacing: \"lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlexContainer, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              rounded: true,\n              children: \"Rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              loading: loading,\n              onClick: handleLoadingDemo,\n              children: loading ? 'Loading...' : 'Click for Loading'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              disabled: true,\n              children: \"Disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Section, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Modern Form Elements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                required: true,\n                children: \"Default Input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"Enter your text...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Filled Input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                variant: \"filled\",\n                placeholder: \"Filled variant...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Flushed Input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                variant: \"flushed\",\n                placeholder: \"Flushed variant...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Small Input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                size: \"sm\",\n                placeholder: \"Small size...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Medium Input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                size: \"md\",\n                placeholder: \"Medium size...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Large Input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                size: \"lg\",\n                placeholder: \"Large size...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Section, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Badges & Avatars\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Badges\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlexContainer, {\n            children: [/*#__PURE__*/_jsxDEV(Badge, {\n              variant: \"primary\",\n              children: \"Primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              variant: \"secondary\",\n              children: \"Secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              variant: \"success\",\n              children: \"Success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              variant: \"warning\",\n              children: \"Warning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              variant: \"error\",\n              children: \"Error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              variant: \"info\",\n              children: \"Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            spacing: \"lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Avatars\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FlexContainer, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              size: \"xs\",\n              children: \"XS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              size: \"sm\",\n              children: \"SM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              size: \"md\",\n              children: \"MD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              size: \"lg\",\n              children: \"LG\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              size: \"xl\",\n              children: \"XL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Section, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"Interactive Elements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(FlexContainer, {\n            children: [/*#__PURE__*/_jsxDEV(TooltipWrapper, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                children: \"Hover for Tooltip\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                position: \"top\",\n                children: \"This is a tooltip!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"md\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernUIShowcase, \"/Rjh5rPqCCqf0XYnTUk9ZNavw3Q=\");\n_c7 = ModernUIShowcase;\nexport default ModernUIShowcase;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"ShowcaseContainer\");\n$RefreshReg$(_c2, \"Title\");\n$RefreshReg$(_c3, \"Section\");\n$RefreshReg$(_c4, \"SectionTitle\");\n$RefreshReg$(_c5, \"Grid\");\n$RefreshReg$(_c6, \"FlexContainer\");\n$RefreshReg$(_c7, \"ModernUIShowcase\");", "map": {"version": 3, "names": ["React", "useState", "styled", "Container", "Card", "<PERSON><PERSON>", "Input", "FormGroup", "Label", "Badge", "Avatar", "Divider", "LoadingSpinner", "TooltipWrapper", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ShowcaseContainer", "div", "props", "theme", "spacing", "xl", "colors", "backgroundSecondary", "backgroundTertiary", "_c", "Title", "h1", "typography", "fontSize", "fontWeight", "bold", "primaryGradient", "_c2", "Section", "_c3", "SectionTitle", "h2", "semibold", "textPrimary", "lg", "_c4", "Grid", "_c5", "FlexContainer", "md", "_c6", "ModernUIShowcase", "_s", "loading", "setLoading", "handleLoadingDemo", "setTimeout", "children", "max<PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "size", "rounded", "onClick", "disabled", "required", "placeholder", "position", "_c7", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Demo/ModernUIShowcase.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport {\n  Con<PERSON>er,\n  Card,\n  Button,\n  Input,\n  FormGroup,\n  Label,\n  Badge,\n  Avatar,\n  Divider,\n  Loading<PERSON><PERSON>ner,\n  TooltipWrapper,\n  Tooltip,\n} from '../../styles/GlobalStyles';\n\nconst ShowcaseContainer = styled.div`\n  min-height: 100vh;\n  padding: ${props => props.theme.spacing.xl};\n  background: linear-gradient(135deg, \n    ${props => props.theme.colors.backgroundSecondary} 0%, \n    ${props => props.theme.colors.backgroundTertiary} 100%\n  );\n`;\n\nconst Title = styled.h1`\n  font-size: ${props => props.theme.typography.fontSize['4xl']};\n  font-weight: ${props => props.theme.typography.fontWeight.bold};\n  background: ${props => props.theme.colors.primaryGradient};\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: ${props => props.theme.spacing['2xl']};\n`;\n\nconst Section = styled.div`\n  margin-bottom: ${props => props.theme.spacing['2xl']};\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${props => props.theme.typography.fontSize['2xl']};\n  font-weight: ${props => props.theme.typography.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin-bottom: ${props => props.theme.spacing.lg};\n`;\n\nconst Grid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: ${props => props.theme.spacing.lg};\n`;\n\nconst FlexContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${props => props.theme.spacing.md};\n  align-items: center;\n`;\n\nconst ModernUIShowcase: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n\n  const handleLoadingDemo = () => {\n    setLoading(true);\n    setTimeout(() => setLoading(false), 3000);\n  };\n\n  return (\n    <ShowcaseContainer>\n      <Container maxWidth=\"xl\">\n        <Title>Modern UI/UX Showcase</Title>\n        \n        {/* Cards Section */}\n        <Section>\n          <SectionTitle>Modern Cards</SectionTitle>\n          <Grid>\n            <Card variant=\"default\">\n              <h3>Default Card</h3>\n              <p>This is a default card with modern styling, subtle shadows, and smooth hover effects.</p>\n            </Card>\n            \n            <Card variant=\"glass\">\n              <h3>Glass Card</h3>\n              <p>Glassmorphism effect with backdrop blur and transparency for a modern look.</p>\n            </Card>\n            \n            <Card variant=\"neumorphism\">\n              <h3>Neumorphism Card</h3>\n              <p>Soft, extruded design that appears to emerge from the background.</p>\n            </Card>\n          </Grid>\n        </Section>\n\n        {/* Buttons Section */}\n        <Section>\n          <SectionTitle>Modern Buttons</SectionTitle>\n          <Card>\n            <FlexContainer>\n              <Button variant=\"primary\">Primary</Button>\n              <Button variant=\"secondary\">Secondary</Button>\n              <Button variant=\"outline\">Outline</Button>\n              <Button variant=\"ghost\">Ghost</Button>\n              <Button variant=\"success\">Success</Button>\n              <Button variant=\"danger\">Danger</Button>\n            </FlexContainer>\n            \n            <Divider spacing=\"lg\" />\n            \n            <FlexContainer>\n              <Button size=\"xs\">Extra Small</Button>\n              <Button size=\"sm\">Small</Button>\n              <Button size=\"md\">Medium</Button>\n              <Button size=\"lg\">Large</Button>\n              <Button size=\"xl\">Extra Large</Button>\n            </FlexContainer>\n            \n            <Divider spacing=\"lg\" />\n            \n            <FlexContainer>\n              <Button rounded>Rounded</Button>\n              <Button loading={loading} onClick={handleLoadingDemo}>\n                {loading ? 'Loading...' : 'Click for Loading'}\n              </Button>\n              <Button disabled>Disabled</Button>\n            </FlexContainer>\n          </Card>\n        </Section>\n\n        {/* Form Elements */}\n        <Section>\n          <SectionTitle>Modern Form Elements</SectionTitle>\n          <Grid>\n            <Card>\n              <FormGroup>\n                <Label required>Default Input</Label>\n                <Input placeholder=\"Enter your text...\" />\n              </FormGroup>\n              \n              <FormGroup>\n                <Label>Filled Input</Label>\n                <Input variant=\"filled\" placeholder=\"Filled variant...\" />\n              </FormGroup>\n              \n              <FormGroup>\n                <Label>Flushed Input</Label>\n                <Input variant=\"flushed\" placeholder=\"Flushed variant...\" />\n              </FormGroup>\n            </Card>\n            \n            <Card>\n              <FormGroup>\n                <Label>Small Input</Label>\n                <Input size=\"sm\" placeholder=\"Small size...\" />\n              </FormGroup>\n              \n              <FormGroup>\n                <Label>Medium Input</Label>\n                <Input size=\"md\" placeholder=\"Medium size...\" />\n              </FormGroup>\n              \n              <FormGroup>\n                <Label>Large Input</Label>\n                <Input size=\"lg\" placeholder=\"Large size...\" />\n              </FormGroup>\n            </Card>\n          </Grid>\n        </Section>\n\n        {/* Badges and Avatars */}\n        <Section>\n          <SectionTitle>Badges & Avatars</SectionTitle>\n          <Card>\n            <h4>Badges</h4>\n            <FlexContainer>\n              <Badge variant=\"primary\">Primary</Badge>\n              <Badge variant=\"secondary\">Secondary</Badge>\n              <Badge variant=\"success\">Success</Badge>\n              <Badge variant=\"warning\">Warning</Badge>\n              <Badge variant=\"error\">Error</Badge>\n              <Badge variant=\"info\">Info</Badge>\n            </FlexContainer>\n            \n            <Divider spacing=\"lg\" />\n            \n            <h4>Avatars</h4>\n            <FlexContainer>\n              <Avatar size=\"xs\">XS</Avatar>\n              <Avatar size=\"sm\">SM</Avatar>\n              <Avatar size=\"md\">MD</Avatar>\n              <Avatar size=\"lg\">LG</Avatar>\n              <Avatar size=\"xl\">XL</Avatar>\n            </FlexContainer>\n          </Card>\n        </Section>\n\n        {/* Interactive Elements */}\n        <Section>\n          <SectionTitle>Interactive Elements</SectionTitle>\n          <Card>\n            <FlexContainer>\n              <TooltipWrapper>\n                <Button variant=\"outline\">Hover for Tooltip</Button>\n                <Tooltip position=\"top\">This is a tooltip!</Tooltip>\n              </TooltipWrapper>\n              \n              <LoadingSpinner size=\"sm\" />\n              <LoadingSpinner size=\"md\" />\n              <LoadingSpinner size=\"lg\" />\n            </FlexContainer>\n          </Card>\n        </Section>\n      </Container>\n    </ShowcaseContainer>\n  );\n};\n\nexport default ModernUIShowcase;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SACEC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,cAAc,EACdC,cAAc,EACdC,OAAO,QACF,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,iBAAiB,GAAGf,MAAM,CAACgB,GAAG;AACpC;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C;AACA,MAAMH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACC,mBAAmB;AACrD,MAAML,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACE,kBAAkB;AACpD;AACA,CAAC;AAACC,EAAA,GAPIT,iBAAiB;AASvB,MAAMU,KAAK,GAAGzB,MAAM,CAAC0B,EAAE;AACvB,eAAeT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,UAAU,CAACC,QAAQ,CAAC,KAAK,CAAC;AAC9D,iBAAiBX,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,UAAU,CAACE,UAAU,CAACC,IAAI;AAChE,gBAAgBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACU,eAAe;AAC3D;AACA;AACA;AACA;AACA,mBAAmBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC,KAAK,CAAC;AACtD,CAAC;AAACa,GAAA,GATIP,KAAK;AAWX,MAAMQ,OAAO,GAAGjC,MAAM,CAACgB,GAAG;AAC1B,mBAAmBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC,KAAK,CAAC;AACtD,CAAC;AAACe,GAAA,GAFID,OAAO;AAIb,MAAME,YAAY,GAAGnC,MAAM,CAACoC,EAAE;AAC9B,eAAenB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,UAAU,CAACC,QAAQ,CAAC,KAAK,CAAC;AAC9D,iBAAiBX,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,UAAU,CAACE,UAAU,CAACQ,QAAQ;AACpE,WAAWpB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACiB,WAAW;AAClD,mBAAmBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACoB,EAAE;AAClD,CAAC;AAACC,GAAA,GALIL,YAAY;AAOlB,MAAMM,IAAI,GAAGzC,MAAM,CAACgB,GAAG;AACvB;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACoB,EAAE;AACxC,CAAC;AAACG,GAAA,GAJID,IAAI;AAMV,MAAME,aAAa,GAAG3C,MAAM,CAACgB,GAAG;AAChC;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACyB,EAAE;AACxC;AACA,CAAC;AAACC,GAAA,GALIF,aAAa;AAOnB,MAAMG,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMmD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BD,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,MAAMF,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEnC,OAAA,CAACC,iBAAiB;IAAAqC,QAAA,eAChBtC,OAAA,CAACb,SAAS;MAACoD,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBACtBtC,OAAA,CAACW,KAAK;QAAA2B,QAAA,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAGpC3C,OAAA,CAACmB,OAAO;QAAAmB,QAAA,gBACNtC,OAAA,CAACqB,YAAY;UAAAiB,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACzC3C,OAAA,CAAC2B,IAAI;UAAAW,QAAA,gBACHtC,OAAA,CAACZ,IAAI;YAACwD,OAAO,EAAC,SAAS;YAAAN,QAAA,gBACrBtC,OAAA;cAAAsC,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB3C,OAAA;cAAAsC,QAAA,EAAG;YAAqF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eAEP3C,OAAA,CAACZ,IAAI;YAACwD,OAAO,EAAC,OAAO;YAAAN,QAAA,gBACnBtC,OAAA;cAAAsC,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB3C,OAAA;cAAAsC,QAAA,EAAG;YAA2E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEP3C,OAAA,CAACZ,IAAI;YAACwD,OAAO,EAAC,aAAa;YAAAN,QAAA,gBACzBtC,OAAA;cAAAsC,QAAA,EAAI;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB3C,OAAA;cAAAsC,QAAA,EAAG;YAAiE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGV3C,OAAA,CAACmB,OAAO;QAAAmB,QAAA,gBACNtC,OAAA,CAACqB,YAAY;UAAAiB,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAC3C3C,OAAA,CAACZ,IAAI;UAAAkD,QAAA,gBACHtC,OAAA,CAAC6B,aAAa;YAAAS,QAAA,gBACZtC,OAAA,CAACX,MAAM;cAACuD,OAAO,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C3C,OAAA,CAACX,MAAM;cAACuD,OAAO,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C3C,OAAA,CAACX,MAAM;cAACuD,OAAO,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C3C,OAAA,CAACX,MAAM;cAACuD,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3C,OAAA,CAACX,MAAM;cAACuD,OAAO,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C3C,OAAA,CAACX,MAAM;cAACuD,OAAO,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAEhB3C,OAAA,CAACL,OAAO;YAACU,OAAO,EAAC;UAAI;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAExB3C,OAAA,CAAC6B,aAAa;YAAAS,QAAA,gBACZtC,OAAA,CAACX,MAAM;cAACwD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3C,OAAA,CAACX,MAAM;cAACwD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC3C,OAAA,CAACX,MAAM;cAACwD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjC3C,OAAA,CAACX,MAAM;cAACwD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC3C,OAAA,CAACX,MAAM;cAACwD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eAEhB3C,OAAA,CAACL,OAAO;YAACU,OAAO,EAAC;UAAI;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAExB3C,OAAA,CAAC6B,aAAa;YAAAS,QAAA,gBACZtC,OAAA,CAACX,MAAM;cAACyD,OAAO;cAAAR,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC3C,OAAA,CAACX,MAAM;cAAC6C,OAAO,EAAEA,OAAQ;cAACa,OAAO,EAAEX,iBAAkB;cAAAE,QAAA,EAClDJ,OAAO,GAAG,YAAY,GAAG;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACT3C,OAAA,CAACX,MAAM;cAAC2D,QAAQ;cAAAV,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGV3C,OAAA,CAACmB,OAAO;QAAAmB,QAAA,gBACNtC,OAAA,CAACqB,YAAY;UAAAiB,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACjD3C,OAAA,CAAC2B,IAAI;UAAAW,QAAA,gBACHtC,OAAA,CAACZ,IAAI;YAAAkD,QAAA,gBACHtC,OAAA,CAACT,SAAS;cAAA+C,QAAA,gBACRtC,OAAA,CAACR,KAAK;gBAACyD,QAAQ;gBAAAX,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrC3C,OAAA,CAACV,KAAK;gBAAC4D,WAAW,EAAC;cAAoB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEZ3C,OAAA,CAACT,SAAS;cAAA+C,QAAA,gBACRtC,OAAA,CAACR,KAAK;gBAAA8C,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B3C,OAAA,CAACV,KAAK;gBAACsD,OAAO,EAAC,QAAQ;gBAACM,WAAW,EAAC;cAAmB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eAEZ3C,OAAA,CAACT,SAAS;cAAA+C,QAAA,gBACRtC,OAAA,CAACR,KAAK;gBAAA8C,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5B3C,OAAA,CAACV,KAAK;gBAACsD,OAAO,EAAC,SAAS;gBAACM,WAAW,EAAC;cAAoB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEP3C,OAAA,CAACZ,IAAI;YAAAkD,QAAA,gBACHtC,OAAA,CAACT,SAAS;cAAA+C,QAAA,gBACRtC,OAAA,CAACR,KAAK;gBAAA8C,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B3C,OAAA,CAACV,KAAK;gBAACuD,IAAI,EAAC,IAAI;gBAACK,WAAW,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAEZ3C,OAAA,CAACT,SAAS;cAAA+C,QAAA,gBACRtC,OAAA,CAACR,KAAK;gBAAA8C,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B3C,OAAA,CAACV,KAAK;gBAACuD,IAAI,EAAC,IAAI;gBAACK,WAAW,EAAC;cAAgB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAEZ3C,OAAA,CAACT,SAAS;cAAA+C,QAAA,gBACRtC,OAAA,CAACR,KAAK;gBAAA8C,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B3C,OAAA,CAACV,KAAK;gBAACuD,IAAI,EAAC,IAAI;gBAACK,WAAW,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGV3C,OAAA,CAACmB,OAAO;QAAAmB,QAAA,gBACNtC,OAAA,CAACqB,YAAY;UAAAiB,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAC7C3C,OAAA,CAACZ,IAAI;UAAAkD,QAAA,gBACHtC,OAAA;YAAAsC,QAAA,EAAI;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf3C,OAAA,CAAC6B,aAAa;YAAAS,QAAA,gBACZtC,OAAA,CAACP,KAAK;cAACmD,OAAO,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC3C,OAAA,CAACP,KAAK;cAACmD,OAAO,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C3C,OAAA,CAACP,KAAK;cAACmD,OAAO,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC3C,OAAA,CAACP,KAAK;cAACmD,OAAO,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC3C,OAAA,CAACP,KAAK;cAACmD,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpC3C,OAAA,CAACP,KAAK;cAACmD,OAAO,EAAC,MAAM;cAAAN,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAEhB3C,OAAA,CAACL,OAAO;YAACU,OAAO,EAAC;UAAI;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAExB3C,OAAA;YAAAsC,QAAA,EAAI;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChB3C,OAAA,CAAC6B,aAAa;YAAAS,QAAA,gBACZtC,OAAA,CAACN,MAAM;cAACmD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7B3C,OAAA,CAACN,MAAM;cAACmD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7B3C,OAAA,CAACN,MAAM;cAACmD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7B3C,OAAA,CAACN,MAAM;cAACmD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7B3C,OAAA,CAACN,MAAM;cAACmD,IAAI,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGV3C,OAAA,CAACmB,OAAO;QAAAmB,QAAA,gBACNtC,OAAA,CAACqB,YAAY;UAAAiB,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACjD3C,OAAA,CAACZ,IAAI;UAAAkD,QAAA,eACHtC,OAAA,CAAC6B,aAAa;YAAAS,QAAA,gBACZtC,OAAA,CAACH,cAAc;cAAAyC,QAAA,gBACbtC,OAAA,CAACX,MAAM;gBAACuD,OAAO,EAAC,SAAS;gBAAAN,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpD3C,OAAA,CAACF,OAAO;gBAACqD,QAAQ,EAAC,KAAK;gBAAAb,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAEjB3C,OAAA,CAACJ,cAAc;cAACiD,IAAI,EAAC;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B3C,OAAA,CAACJ,cAAc;cAACiD,IAAI,EAAC;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B3C,OAAA,CAACJ,cAAc;cAACiD,IAAI,EAAC;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAExB,CAAC;AAACV,EAAA,CA3JID,gBAA0B;AAAAoB,GAAA,GAA1BpB,gBAA0B;AA6JhC,eAAeA,gBAAgB;AAAC,IAAAtB,EAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAA3C,EAAA;AAAA2C,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}