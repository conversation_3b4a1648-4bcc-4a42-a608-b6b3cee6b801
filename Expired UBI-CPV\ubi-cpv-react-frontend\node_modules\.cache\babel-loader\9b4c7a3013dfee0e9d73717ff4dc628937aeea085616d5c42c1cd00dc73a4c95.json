{"ast": null, "code": "import speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\nexport const progressEventReducer = function (listener, isDownloadStream) {\n  let freq = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 3;\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n  return throttle(e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n    bytesNotified = loaded;\n    const data = {\n      loaded,\n      total,\n      progress: total ? loaded / total : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null,\n      [isDownloadStream ? 'download' : 'upload']: true\n    };\n    listener(data);\n  }, freq);\n};\nexport const progressEventDecorator = (total, throttled) => {\n  const lengthComputable = total != null;\n  return [loaded => throttled[0]({\n    lengthComputable,\n    total,\n    loaded\n  }), throttled[1]];\n};\nexport const asyncDecorator = fn => function () {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return utils.asap(() => fn(...args));\n};", "map": {"version": 3, "names": ["speedometer", "throttle", "utils", "progressEventReducer", "listener", "isDownloadStream", "freq", "arguments", "length", "undefined", "bytesNotified", "_speedometer", "e", "loaded", "total", "lengthComputable", "progressBytes", "rate", "inRange", "data", "progress", "bytes", "estimated", "event", "progressEventDecorator", "throttled", "asyncDecorator", "fn", "_len", "args", "Array", "_key", "asap"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/axios/lib/helpers/progressEventReducer.js"], "sourcesContent": ["import speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\n\nexport const progressEventReducer = (listener, isDownloadStream, freq = 3) => {\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n\n  return throttle(e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n\n    bytesNotified = loaded;\n\n    const data = {\n      loaded,\n      total,\n      progress: total ? (loaded / total) : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null,\n      [isDownloadStream ? 'download' : 'upload']: true\n    };\n\n    listener(data);\n  }, freq);\n}\n\nexport const progressEventDecorator = (total, throttled) => {\n  const lengthComputable = total != null;\n\n  return [(loaded) => throttled[0]({\n    lengthComputable,\n    total,\n    loaded\n  }), throttled[1]];\n}\n\nexport const asyncDecorator = (fn) => (...args) => utils.asap(() => fn(...args));\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,KAAK,MAAM,aAAa;AAE/B,OAAO,MAAMC,oBAAoB,GAAG,SAAAA,CAACC,QAAQ,EAAEC,gBAAgB,EAAe;EAAA,IAAbC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACvE,IAAIG,aAAa,GAAG,CAAC;EACrB,MAAMC,YAAY,GAAGX,WAAW,CAAC,EAAE,EAAE,GAAG,CAAC;EAEzC,OAAOC,QAAQ,CAACW,CAAC,IAAI;IACnB,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAAM;IACvB,MAAMC,KAAK,GAAGF,CAAC,CAACG,gBAAgB,GAAGH,CAAC,CAACE,KAAK,GAAGL,SAAS;IACtD,MAAMO,aAAa,GAAGH,MAAM,GAAGH,aAAa;IAC5C,MAAMO,IAAI,GAAGN,YAAY,CAACK,aAAa,CAAC;IACxC,MAAME,OAAO,GAAGL,MAAM,IAAIC,KAAK;IAE/BJ,aAAa,GAAGG,MAAM;IAEtB,MAAMM,IAAI,GAAG;MACXN,MAAM;MACNC,KAAK;MACLM,QAAQ,EAAEN,KAAK,GAAID,MAAM,GAAGC,KAAK,GAAIL,SAAS;MAC9CY,KAAK,EAAEL,aAAa;MACpBC,IAAI,EAAEA,IAAI,GAAGA,IAAI,GAAGR,SAAS;MAC7Ba,SAAS,EAAEL,IAAI,IAAIH,KAAK,IAAII,OAAO,GAAG,CAACJ,KAAK,GAAGD,MAAM,IAAII,IAAI,GAAGR,SAAS;MACzEc,KAAK,EAAEX,CAAC;MACRG,gBAAgB,EAAED,KAAK,IAAI,IAAI;MAC/B,CAACT,gBAAgB,GAAG,UAAU,GAAG,QAAQ,GAAG;IAC9C,CAAC;IAEDD,QAAQ,CAACe,IAAI,CAAC;EAChB,CAAC,EAAEb,IAAI,CAAC;AACV,CAAC;AAED,OAAO,MAAMkB,sBAAsB,GAAGA,CAACV,KAAK,EAAEW,SAAS,KAAK;EAC1D,MAAMV,gBAAgB,GAAGD,KAAK,IAAI,IAAI;EAEtC,OAAO,CAAED,MAAM,IAAKY,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/BV,gBAAgB;IAChBD,KAAK;IACLD;EACF,CAAC,CAAC,EAAEY,SAAS,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AAED,OAAO,MAAMC,cAAc,GAAIC,EAAE,IAAK;EAAA,SAAAC,IAAA,GAAArB,SAAA,CAAAC,MAAA,EAAIqB,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAJF,IAAI,CAAAE,IAAA,IAAAxB,SAAA,CAAAwB,IAAA;EAAA;EAAA,OAAK7B,KAAK,CAAC8B,IAAI,CAAC,MAAML,EAAE,CAAC,GAAGE,IAAI,CAAC,CAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}