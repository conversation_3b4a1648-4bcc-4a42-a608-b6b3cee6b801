{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{Card,Button,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReportsContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  gap: 20px;\\n\"])));const StatsGrid=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\"])));const StatCard=styled(Card)(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #007E3A, #005a2a);\\n  color: white;\\n\"])));const StatValue=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n\"])));const StatLabel=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 12px;\\n  opacity: 0.9;\\n  font-weight: 500;\\n\"])));const ChartContainer=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\\n  @media (max-width: 768px) {\\n    grid-template-columns: 1fr;\\n  }\\n\"])));const ChartCard=styled(Card)(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 20px;\\n\"])));const ChartTitle=styled.h3(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n  color: #007E3A;\\n  text-align: center;\\n\"])));const FilterContainer=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background: white;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const MetricsTable=styled.table(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const TrendIndicator=styled.span(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  \\n  \",\"\\n\"])),props=>{switch(props.trend){case'up':return\"color: #2e7d32;\";case'down':return\"color: #c62828;\";case'stable':return\"color: #666;\";}});const AdminReports=()=>{const[loading,setLoading]=useState(true);const[timeFilter,setTimeFilter]=useState('month');const[systemStats,setSystemStats]=useState({totalUsers:0,totalLeads:0,completedLeads:0,pendingLeads:0,rejectedLeads:0,averageProcessingTime:0,systemEfficiency:0,activeAgents:0});const[departmentMetrics,setDepartmentMetrics]=useState([]);const[monthlyTrends,setMonthlyTrends]=useState([]);const navigate=useNavigate();useEffect(()=>{loadReportsData();},[timeFilter]);const loadReportsData=async()=>{try{setLoading(true);const[dashboardStats,users]=await Promise.all([apiService.getDashboardStats(),apiService.getUsers()]);setSystemStats({totalUsers:users.length||0,totalLeads:dashboardStats.totalLeads||0,completedLeads:dashboardStats.completedLeads||0,pendingLeads:dashboardStats.pendingLeads||0,rejectedLeads:dashboardStats.rejectedLeads||0,averageProcessingTime:2.8,// Mock data\nsystemEfficiency:87,// Mock data\nactiveAgents:users.filter(u=>u.role==='Agent'&&u.isActive).length||0});// Mock department metrics\nsetDepartmentMetrics([{department:'Personal Loans',totalLeads:150,completed:128,pending:15,rejected:7,avgTime:2.5,efficiency:89},{department:'Home Loans',totalLeads:89,completed:76,pending:8,rejected:5,avgTime:3.2,efficiency:85},{department:'Car Loans',totalLeads:67,completed:58,pending:6,rejected:3,avgTime:2.1,efficiency:92}]);// Mock monthly trends\nsetMonthlyTrends([{month:'Jan',leads:120,completed:105,efficiency:87},{month:'Feb',leads:135,completed:118,efficiency:89},{month:'Mar',leads:156,completed:142,efficiency:91},{month:'Apr',leads:142,completed:128,efficiency:88}]);}catch(error){console.error('Error loading reports data:',error);// Use mock data on error\nsetSystemStats({totalUsers:25,totalLeads:306,completedLeads:262,pendingLeads:29,rejectedLeads:15,averageProcessingTime:2.8,systemEfficiency:87,activeAgents:15});}finally{setLoading(false);}};const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>navigate('/admin/dashboard')},{icon:'👥',label:'Users',onClick:()=>navigate('/admin/users')},{icon:'📋',label:'Leads',onClick:()=>navigate('/admin/leads')},{icon:'📊',label:'Reports',active:true},{icon:'⚙️',label:'Settings',onClick:()=>navigate('/admin/settings')}];const getCompletionRate=()=>{return systemStats.totalLeads>0?Math.round(systemStats.completedLeads/systemStats.totalLeads*100):0;};const getSuccessRate=()=>{const totalProcessed=systemStats.completedLeads+systemStats.rejectedLeads;return totalProcessed>0?Math.round(systemStats.completedLeads/totalProcessed*100):0;};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"System Reports\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsx(DashboardLayout,{title:\"System Reports\",navigationItems:navigationItems,children:/*#__PURE__*/_jsxs(ReportsContainer,{children:[/*#__PURE__*/_jsxs(FilterContainer,{children:[/*#__PURE__*/_jsxs(FilterSelect,{value:timeFilter,onChange:e=>setTimeFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"week\",children:\"This Week\"}),/*#__PURE__*/_jsx(\"option\",{value:\"month\",children:\"This Month\"}),/*#__PURE__*/_jsx(\"option\",{value:\"quarter\",children:\"This Quarter\"}),/*#__PURE__*/_jsx(\"option\",{value:\"year\",children:\"This Year\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>window.print(),children:\"\\uD83D\\uDCC4 Export Report\"}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>navigate('/admin/analytics'),children:\"\\uD83D\\uDCC8 Advanced Analytics\"})]}),/*#__PURE__*/_jsxs(StatsGrid,{children:[/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:systemStats.totalUsers}),/*#__PURE__*/_jsx(StatLabel,{children:\"Total Users\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:systemStats.activeAgents}),/*#__PURE__*/_jsx(StatLabel,{children:\"Active Agents\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:systemStats.totalLeads}),/*#__PURE__*/_jsx(StatLabel,{children:\"Total Leads\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:systemStats.completedLeads}),/*#__PURE__*/_jsx(StatLabel,{children:\"Completed\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:systemStats.pendingLeads}),/*#__PURE__*/_jsx(StatLabel,{children:\"Pending\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:systemStats.rejectedLeads}),/*#__PURE__*/_jsx(StatLabel,{children:\"Rejected\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:systemStats.averageProcessingTime}),/*#__PURE__*/_jsx(StatLabel,{children:\"Avg. Days\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsxs(StatValue,{children:[systemStats.systemEfficiency,\"%\"]}),/*#__PURE__*/_jsx(StatLabel,{children:\"System Efficiency\"})]})]}),/*#__PURE__*/_jsxs(ChartContainer,{children:[/*#__PURE__*/_jsxs(ChartCard,{children:[/*#__PURE__*/_jsx(ChartTitle,{children:\"Monthly Performance Trends\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(4, 1fr)',gap:'15px',textAlign:'center'},children:monthlyTrends.map((trend,index)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',fontWeight:'bold',marginBottom:'8px'},children:trend.month}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'18px',color:'#007E3A',fontWeight:'bold'},children:trend.completed}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[\"of \",trend.leads]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#2e7d32',marginTop:'4px'},children:[trend.efficiency,\"% eff.\"]})]},index))})]}),/*#__PURE__*/_jsxs(ChartCard,{children:[/*#__PURE__*/_jsx(ChartTitle,{children:\"System Health\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gap:'15px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'32px',color:'#2e7d32',fontWeight:'bold'},children:[getCompletionRate(),\"%\"]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:\"Completion Rate\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'32px',color:'#007E3A',fontWeight:'bold'},children:[getSuccessRate(),\"%\"]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:\"Success Rate\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'32px',color:'#FFD100',fontWeight:'bold'},children:[systemStats.systemEfficiency,\"%\"]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:\"System Efficiency\"})]})]})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Department Performance\"}),/*#__PURE__*/_jsx(\"div\",{style:{overflowX:'auto'},children:/*#__PURE__*/_jsxs(MetricsTable,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Department\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Total Leads\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Completed\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Pending\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Rejected\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Avg. Time\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Efficiency\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Trend\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:departmentMetrics.map((dept,index)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{style:{fontWeight:'500'},children:dept.department}),/*#__PURE__*/_jsx(TableCell,{children:dept.totalLeads}),/*#__PURE__*/_jsx(TableCell,{style:{color:'#2e7d32'},children:dept.completed}),/*#__PURE__*/_jsx(TableCell,{style:{color:'#ff8f00'},children:dept.pending}),/*#__PURE__*/_jsx(TableCell,{style:{color:'#c62828'},children:dept.rejected}),/*#__PURE__*/_jsxs(TableCell,{children:[dept.avgTime,\" days\"]}),/*#__PURE__*/_jsxs(TableCell,{children:[dept.efficiency,\"%\"]}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(TrendIndicator,{trend:dept.efficiency>88?'up':dept.efficiency>85?'stable':'down',children:[dept.efficiency>88?'↗️':dept.efficiency>85?'➡️':'↘️',dept.efficiency>88?'Improving':dept.efficiency>85?'Stable':'Declining']})})]},index))})]})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Executive Summary\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(300px, 1fr))',gap:'20px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{style:{color:'#2e7d32',marginBottom:'10px'},children:\"\\uD83C\\uDFAF Performance Highlights\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{paddingLeft:'20px',lineHeight:'1.6'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"Car Loans department leads with 92% efficiency\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Overall system efficiency at 87%, up 3% from last month\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Average processing time reduced to 2.8 days\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{style:{color:'#ff8f00',marginBottom:'10px'},children:\"\\u26A0\\uFE0F Areas for Improvement\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{paddingLeft:'20px',lineHeight:'1.6'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"Home Loans processing time needs optimization\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Rejection rate in Personal Loans requires attention\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Agent workload distribution could be improved\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{style:{color:'#007E3A',marginBottom:'10px'},children:\"\\uD83D\\uDCC8 Recommendations\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{paddingLeft:'20px',lineHeight:'1.6'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"Implement best practices from Car Loans team\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Provide additional training for Home Loans agents\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Review and update verification guidelines\"})]})]})]})]})]})});};export default AdminReports;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "ReportsContainer", "div", "_templateObject", "_taggedTemplateLiteral", "StatsGrid", "_templateObject2", "StatCard", "_templateObject3", "StatValue", "_templateObject4", "StatLabel", "_templateObject5", "ChartContainer", "_templateObject6", "ChartCard", "_templateObject7", "ChartTitle", "h3", "_templateObject8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject9", "FilterSelect", "select", "_templateObject0", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "MetricsTable", "table", "_templateObject1", "TableHeader", "th", "_templateObject10", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject11", "TableRow", "tr", "_templateObject12", "TrendIndicator", "span", "_templateObject13", "trend", "AdminReports", "loading", "setLoading", "timeFilter", "setTimeFilter", "systemStats", "setSystemStats", "totalUsers", "totalLeads", "completedLeads", "pendingLeads", "rejectedLeads", "averageProcessingTime", "systemEfficiency", "activeAgents", "departmentMetrics", "setDepartmentMetrics", "monthlyTrends", "setMonthlyTrends", "navigate", "loadReportsData", "dashboardStats", "users", "Promise", "all", "getDashboardStats", "getUsers", "length", "filter", "u", "role", "isActive", "department", "completed", "pending", "rejected", "avgTime", "efficiency", "month", "leads", "error", "console", "navigationItems", "icon", "label", "onClick", "active", "getCompletionRate", "Math", "round", "getSuccessRate", "totalProcessed", "title", "children", "value", "onChange", "e", "target", "variant", "window", "print", "style", "display", "gridTemplateColumns", "gap", "textAlign", "map", "index", "fontSize", "fontWeight", "marginBottom", "color", "marginTop", "overflowX", "dept", "paddingLeft", "lineHeight"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/AdminReports.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\n\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 12px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst MetricsTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst TrendIndicator = styled.span<{ trend: 'up' | 'down' | 'stable' }>`\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  \n  ${props => {\n    switch (props.trend) {\n      case 'up':\n        return `color: #2e7d32;`;\n      case 'down':\n        return `color: #c62828;`;\n      case 'stable':\n        return `color: #666;`;\n    }\n  }}\n`;\n\nconst AdminReports: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('month');\n  const [systemStats, setSystemStats] = useState({\n    totalUsers: 0,\n    totalLeads: 0,\n    completedLeads: 0,\n    pendingLeads: 0,\n    rejectedLeads: 0,\n    averageProcessingTime: 0,\n    systemEfficiency: 0,\n    activeAgents: 0,\n  });\n  const [departmentMetrics, setDepartmentMetrics] = useState<any[]>([]);\n  const [monthlyTrends, setMonthlyTrends] = useState<any[]>([]);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n      \n      const [dashboardStats, users] = await Promise.all([\n        apiService.getDashboardStats(),\n        apiService.getUsers(),\n      ]);\n\n      setSystemStats({\n        totalUsers: users.length || 0,\n        totalLeads: dashboardStats.totalLeads || 0,\n        completedLeads: dashboardStats.completedLeads || 0,\n        pendingLeads: dashboardStats.pendingLeads || 0,\n        rejectedLeads: dashboardStats.rejectedLeads || 0,\n        averageProcessingTime: 2.8, // Mock data\n        systemEfficiency: 87, // Mock data\n        activeAgents: users.filter(u => u.role === 'Agent' && u.isActive).length || 0,\n      });\n\n      // Mock department metrics\n      setDepartmentMetrics([\n        {\n          department: 'Personal Loans',\n          totalLeads: 150,\n          completed: 128,\n          pending: 15,\n          rejected: 7,\n          avgTime: 2.5,\n          efficiency: 89,\n        },\n        {\n          department: 'Home Loans',\n          totalLeads: 89,\n          completed: 76,\n          pending: 8,\n          rejected: 5,\n          avgTime: 3.2,\n          efficiency: 85,\n        },\n        {\n          department: 'Car Loans',\n          totalLeads: 67,\n          completed: 58,\n          pending: 6,\n          rejected: 3,\n          avgTime: 2.1,\n          efficiency: 92,\n        },\n      ]);\n\n      // Mock monthly trends\n      setMonthlyTrends([\n        { month: 'Jan', leads: 120, completed: 105, efficiency: 87 },\n        { month: 'Feb', leads: 135, completed: 118, efficiency: 89 },\n        { month: 'Mar', leads: 156, completed: 142, efficiency: 91 },\n        { month: 'Apr', leads: 142, completed: 128, efficiency: 88 },\n      ]);\n\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data on error\n      setSystemStats({\n        totalUsers: 25,\n        totalLeads: 306,\n        completedLeads: 262,\n        pendingLeads: 29,\n        rejectedLeads: 15,\n        averageProcessingTime: 2.8,\n        systemEfficiency: 87,\n        activeAgents: 15,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/admin/dashboard') },\n    { icon: '👥', label: 'Users', onClick: () => navigate('/admin/users') },\n    { icon: '📋', label: 'Leads', onClick: () => navigate('/admin/leads') },\n    { icon: '📊', label: 'Reports', active: true },\n    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },\n  ];\n\n  const getCompletionRate = () => {\n    return systemStats.totalLeads > 0 ? Math.round((systemStats.completedLeads / systemStats.totalLeads) * 100) : 0;\n  };\n\n  const getSuccessRate = () => {\n    const totalProcessed = systemStats.completedLeads + systemStats.rejectedLeads;\n    return totalProcessed > 0 ? Math.round((systemStats.completedLeads / totalProcessed) * 100) : 0;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"System Reports\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"System Reports\" navigationItems={navigationItems}>\n      <ReportsContainer>\n        <FilterContainer>\n          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </FilterSelect>\n          \n          <Button variant=\"outline\" onClick={() => window.print()}>\n            📄 Export Report\n          </Button>\n          \n          <Button variant=\"secondary\" onClick={() => navigate('/admin/analytics')}>\n            📈 Advanced Analytics\n          </Button>\n        </FilterContainer>\n\n        {/* System Overview Stats */}\n        <StatsGrid>\n          <StatCard>\n            <StatValue>{systemStats.totalUsers}</StatValue>\n            <StatLabel>Total Users</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.activeAgents}</StatValue>\n            <StatLabel>Active Agents</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.totalLeads}</StatValue>\n            <StatLabel>Total Leads</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.completedLeads}</StatValue>\n            <StatLabel>Completed</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.pendingLeads}</StatValue>\n            <StatLabel>Pending</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.rejectedLeads}</StatValue>\n            <StatLabel>Rejected</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.averageProcessingTime}</StatValue>\n            <StatLabel>Avg. Days</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.systemEfficiency}%</StatValue>\n            <StatLabel>System Efficiency</StatLabel>\n          </StatCard>\n        </StatsGrid>\n\n        {/* Charts and Trends */}\n        <ChartContainer>\n          <ChartCard>\n            <ChartTitle>Monthly Performance Trends</ChartTitle>\n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '15px', textAlign: 'center' }}>\n              {monthlyTrends.map((trend, index) => (\n                <div key={index}>\n                  <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px' }}>\n                    {trend.month}\n                  </div>\n                  <div style={{ fontSize: '18px', color: '#007E3A', fontWeight: 'bold' }}>\n                    {trend.completed}\n                  </div>\n                  <div style={{ fontSize: '12px', color: '#666' }}>\n                    of {trend.leads}\n                  </div>\n                  <div style={{ fontSize: '12px', color: '#2e7d32', marginTop: '4px' }}>\n                    {trend.efficiency}% eff.\n                  </div>\n                </div>\n              ))}\n            </div>\n          </ChartCard>\n\n          <ChartCard>\n            <ChartTitle>System Health</ChartTitle>\n            <div style={{ display: 'grid', gap: '15px' }}>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '32px', color: '#2e7d32', fontWeight: 'bold' }}>\n                  {getCompletionRate()}%\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Completion Rate</div>\n              </div>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '32px', color: '#007E3A', fontWeight: 'bold' }}>\n                  {getSuccessRate()}%\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Success Rate</div>\n              </div>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '32px', color: '#FFD100', fontWeight: 'bold' }}>\n                  {systemStats.systemEfficiency}%\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>System Efficiency</div>\n              </div>\n            </div>\n          </ChartCard>\n        </ChartContainer>\n\n        {/* Department Performance */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Department Performance</h3>\n          <div style={{ overflowX: 'auto' }}>\n            <MetricsTable>\n              <thead>\n                <tr>\n                  <TableHeader>Department</TableHeader>\n                  <TableHeader>Total Leads</TableHeader>\n                  <TableHeader>Completed</TableHeader>\n                  <TableHeader>Pending</TableHeader>\n                  <TableHeader>Rejected</TableHeader>\n                  <TableHeader>Avg. Time</TableHeader>\n                  <TableHeader>Efficiency</TableHeader>\n                  <TableHeader>Trend</TableHeader>\n                </tr>\n              </thead>\n              <tbody>\n                {departmentMetrics.map((dept, index) => (\n                  <TableRow key={index}>\n                    <TableCell style={{ fontWeight: '500' }}>{dept.department}</TableCell>\n                    <TableCell>{dept.totalLeads}</TableCell>\n                    <TableCell style={{ color: '#2e7d32' }}>{dept.completed}</TableCell>\n                    <TableCell style={{ color: '#ff8f00' }}>{dept.pending}</TableCell>\n                    <TableCell style={{ color: '#c62828' }}>{dept.rejected}</TableCell>\n                    <TableCell>{dept.avgTime} days</TableCell>\n                    <TableCell>{dept.efficiency}%</TableCell>\n                    <TableCell>\n                      <TrendIndicator trend={dept.efficiency > 88 ? 'up' : dept.efficiency > 85 ? 'stable' : 'down'}>\n                        {dept.efficiency > 88 ? '↗️' : dept.efficiency > 85 ? '➡️' : '↘️'}\n                        {dept.efficiency > 88 ? 'Improving' : dept.efficiency > 85 ? 'Stable' : 'Declining'}\n                      </TrendIndicator>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </tbody>\n            </MetricsTable>\n          </div>\n        </Card>\n\n        {/* Key Insights */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Executive Summary</h3>\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>\n            <div>\n              <h4 style={{ color: '#2e7d32', marginBottom: '10px' }}>🎯 Performance Highlights</h4>\n              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>\n                <li>Car Loans department leads with 92% efficiency</li>\n                <li>Overall system efficiency at 87%, up 3% from last month</li>\n                <li>Average processing time reduced to 2.8 days</li>\n              </ul>\n            </div>\n            <div>\n              <h4 style={{ color: '#ff8f00', marginBottom: '10px' }}>⚠️ Areas for Improvement</h4>\n              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>\n                <li>Home Loans processing time needs optimization</li>\n                <li>Rejection rate in Personal Loans requires attention</li>\n                <li>Agent workload distribution could be improved</li>\n              </ul>\n            </div>\n            <div>\n              <h4 style={{ color: '#007E3A', marginBottom: '10px' }}>📈 Recommendations</h4>\n              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>\n                <li>Implement best practices from Car Loans team</li>\n                <li>Provide additional training for Home Loans agents</li>\n                <li>Review and update verification guidelines</li>\n              </ul>\n            </div>\n          </div>\n        </Card>\n      </ReportsContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default AdminReports;\n"], "mappings": "sbAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,KAAM,CAAAC,gBAAgB,CAAGV,MAAM,CAACW,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,0CAGlC,CAED,KAAM,CAAAC,SAAS,CAAGd,MAAM,CAACW,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,kIAK3B,CAED,KAAM,CAAAG,QAAQ,CAAGhB,MAAM,CAACE,IAAI,CAAC,CAAAe,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,mMAQ5B,CAED,KAAM,CAAAK,SAAS,CAAGlB,MAAM,CAACW,GAAG,CAAAQ,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,0EAI3B,CAED,KAAM,CAAAO,SAAS,CAAGpB,MAAM,CAACW,GAAG,CAAAU,gBAAA,GAAAA,gBAAA,CAAAR,sBAAA,oEAI3B,CAED,KAAM,CAAAS,cAAc,CAAGtB,MAAM,CAACW,GAAG,CAAAY,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,4KAShC,CAED,KAAM,CAAAW,SAAS,CAAGxB,MAAM,CAACE,IAAI,CAAC,CAAAuB,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,4BAE7B,CAED,KAAM,CAAAa,UAAU,CAAG1B,MAAM,CAAC2B,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,4EAI3B,CAED,KAAM,CAAAgB,eAAe,CAAG7B,MAAM,CAACW,GAAG,CAAAmB,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,sFAKjC,CAED,KAAM,CAAAkB,YAAY,CAAG/B,MAAM,CAACgC,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,6LAEZqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAKnCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAC,YAAY,CAAGzC,MAAM,CAAC0C,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAA9B,sBAAA,wDAGhC,CAED,KAAM,CAAA+B,WAAW,CAAG5C,MAAM,CAAC6C,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAjC,sBAAA,qJAGAqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS,CAC5Cb,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,QAAQ,CAE/Cd,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,UAAU,CAChD,CAED,KAAM,CAAAC,SAAS,CAAGlD,MAAM,CAACmD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAvC,sBAAA,uFAGEqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAGrD,MAAM,CAACsD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA1C,sBAAA,wDAEFqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS,CAE5D,CAED,KAAM,CAAAS,cAAc,CAAGxD,MAAM,CAACyD,IAAI,CAAAC,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,4HAO9BqB,KAAK,EAAI,CACT,OAAQA,KAAK,CAACyB,KAAK,EACjB,IAAK,IAAI,CACP,wBACF,IAAK,MAAM,CACT,wBACF,IAAK,QAAQ,CACX,qBACJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGjE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkE,UAAU,CAAEC,aAAa,CAAC,CAAGnE,QAAQ,CAAC,OAAO,CAAC,CACrD,KAAM,CAACoE,WAAW,CAAEC,cAAc,CAAC,CAAGrE,QAAQ,CAAC,CAC7CsE,UAAU,CAAE,CAAC,CACbC,UAAU,CAAE,CAAC,CACbC,cAAc,CAAE,CAAC,CACjBC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,qBAAqB,CAAE,CAAC,CACxBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,CAChB,CAAC,CAAC,CACF,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/E,QAAQ,CAAQ,EAAE,CAAC,CACrE,KAAM,CAACgF,aAAa,CAAEC,gBAAgB,CAAC,CAAGjF,QAAQ,CAAQ,EAAE,CAAC,CAC7D,KAAM,CAAAkF,QAAQ,CAAGhF,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACdkF,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAACjB,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAiB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFlB,UAAU,CAAC,IAAI,CAAC,CAEhB,KAAM,CAACmB,cAAc,CAAEC,KAAK,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAChD/E,UAAU,CAACgF,iBAAiB,CAAC,CAAC,CAC9BhF,UAAU,CAACiF,QAAQ,CAAC,CAAC,CACtB,CAAC,CAEFpB,cAAc,CAAC,CACbC,UAAU,CAAEe,KAAK,CAACK,MAAM,EAAI,CAAC,CAC7BnB,UAAU,CAAEa,cAAc,CAACb,UAAU,EAAI,CAAC,CAC1CC,cAAc,CAAEY,cAAc,CAACZ,cAAc,EAAI,CAAC,CAClDC,YAAY,CAAEW,cAAc,CAACX,YAAY,EAAI,CAAC,CAC9CC,aAAa,CAAEU,cAAc,CAACV,aAAa,EAAI,CAAC,CAChDC,qBAAqB,CAAE,GAAG,CAAE;AAC5BC,gBAAgB,CAAE,EAAE,CAAE;AACtBC,YAAY,CAAEQ,KAAK,CAACM,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,IAAI,GAAK,OAAO,EAAID,CAAC,CAACE,QAAQ,CAAC,CAACJ,MAAM,EAAI,CAC9E,CAAC,CAAC,CAEF;AACAX,oBAAoB,CAAC,CACnB,CACEgB,UAAU,CAAE,gBAAgB,CAC5BxB,UAAU,CAAE,GAAG,CACfyB,SAAS,CAAE,GAAG,CACdC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,EACd,CAAC,CACD,CACEL,UAAU,CAAE,YAAY,CACxBxB,UAAU,CAAE,EAAE,CACdyB,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,CAAC,CACVC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,EACd,CAAC,CACD,CACEL,UAAU,CAAE,WAAW,CACvBxB,UAAU,CAAE,EAAE,CACdyB,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,CAAC,CACVC,QAAQ,CAAE,CAAC,CACXC,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,EACd,CAAC,CACF,CAAC,CAEF;AACAnB,gBAAgB,CAAC,CACf,CAAEoB,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,GAAG,CAAEN,SAAS,CAAE,GAAG,CAAEI,UAAU,CAAE,EAAG,CAAC,CAC5D,CAAEC,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,GAAG,CAAEN,SAAS,CAAE,GAAG,CAAEI,UAAU,CAAE,EAAG,CAAC,CAC5D,CAAEC,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,GAAG,CAAEN,SAAS,CAAE,GAAG,CAAEI,UAAU,CAAE,EAAG,CAAC,CAC5D,CAAEC,KAAK,CAAE,KAAK,CAAEC,KAAK,CAAE,GAAG,CAAEN,SAAS,CAAE,GAAG,CAAEI,UAAU,CAAE,EAAG,CAAC,CAC7D,CAAC,CAEJ,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD;AACAlC,cAAc,CAAC,CACbC,UAAU,CAAE,EAAE,CACdC,UAAU,CAAE,GAAG,CACfC,cAAc,CAAE,GAAG,CACnBC,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,qBAAqB,CAAE,GAAG,CAC1BC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAChB,CAAC,CAAC,CACJ,CAAC,OAAS,CACRZ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAwC,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC/E,CAAEwB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,OAAO,CAAEC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,cAAc,CAAE,CAAC,CACvE,CAAEwB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,OAAO,CAAEC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,cAAc,CAAE,CAAC,CACvE,CAAEwB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEE,MAAM,CAAE,IAAK,CAAC,CAC9C,CAAEH,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAU,CAAEC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,iBAAiB,CAAE,CAAC,CAC9E,CAED,KAAM,CAAA4B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,MAAO,CAAA1C,WAAW,CAACG,UAAU,CAAG,CAAC,CAAGwC,IAAI,CAACC,KAAK,CAAE5C,WAAW,CAACI,cAAc,CAAGJ,WAAW,CAACG,UAAU,CAAI,GAAG,CAAC,CAAG,CAAC,CACjH,CAAC,CAED,KAAM,CAAA0C,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,cAAc,CAAG9C,WAAW,CAACI,cAAc,CAAGJ,WAAW,CAACM,aAAa,CAC7E,MAAO,CAAAwC,cAAc,CAAG,CAAC,CAAGH,IAAI,CAACC,KAAK,CAAE5C,WAAW,CAACI,cAAc,CAAG0C,cAAc,CAAI,GAAG,CAAC,CAAG,CAAC,CACjG,CAAC,CAED,GAAIlD,OAAO,CAAE,CACX,mBACEtD,IAAA,CAACN,eAAe,EAAC+G,KAAK,CAAC,gBAAgB,CAACV,eAAe,CAAEA,eAAgB,CAAAW,QAAA,cACvE1G,IAAA,CAACH,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,mBACEG,IAAA,CAACN,eAAe,EAAC+G,KAAK,CAAC,gBAAgB,CAACV,eAAe,CAAEA,eAAgB,CAAAW,QAAA,cACvExG,KAAA,CAACC,gBAAgB,EAAAuG,QAAA,eACfxG,KAAA,CAACoB,eAAe,EAAAoF,QAAA,eACdxG,KAAA,CAACsB,YAAY,EAACmF,KAAK,CAAEnD,UAAW,CAACoD,QAAQ,CAAGC,CAAC,EAAKpD,aAAa,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAD,QAAA,eAC9E1G,IAAA,WAAQ2G,KAAK,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAQ,CAAC,cACvC1G,IAAA,WAAQ2G,KAAK,CAAC,OAAO,CAAAD,QAAA,CAAC,YAAU,CAAQ,CAAC,cACzC1G,IAAA,WAAQ2G,KAAK,CAAC,SAAS,CAAAD,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC7C1G,IAAA,WAAQ2G,KAAK,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAQ,CAAC,EAC3B,CAAC,cAEf1G,IAAA,CAACJ,MAAM,EAACmH,OAAO,CAAC,SAAS,CAACb,OAAO,CAAEA,CAAA,GAAMc,MAAM,CAACC,KAAK,CAAC,CAAE,CAAAP,QAAA,CAAC,4BAEzD,CAAQ,CAAC,cAET1G,IAAA,CAACJ,MAAM,EAACmH,OAAO,CAAC,WAAW,CAACb,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,kBAAkB,CAAE,CAAAkC,QAAA,CAAC,iCAEzE,CAAQ,CAAC,EACM,CAAC,cAGlBxG,KAAA,CAACK,SAAS,EAAAmG,QAAA,eACRxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACP1G,IAAA,CAACW,SAAS,EAAA+F,QAAA,CAAEhD,WAAW,CAACE,UAAU,CAAY,CAAC,cAC/C5D,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,aAAW,CAAW,CAAC,EAC1B,CAAC,cACXxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACP1G,IAAA,CAACW,SAAS,EAAA+F,QAAA,CAAEhD,WAAW,CAACS,YAAY,CAAY,CAAC,cACjDnE,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,eAAa,CAAW,CAAC,EAC5B,CAAC,cACXxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACP1G,IAAA,CAACW,SAAS,EAAA+F,QAAA,CAAEhD,WAAW,CAACG,UAAU,CAAY,CAAC,cAC/C7D,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,aAAW,CAAW,CAAC,EAC1B,CAAC,cACXxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACP1G,IAAA,CAACW,SAAS,EAAA+F,QAAA,CAAEhD,WAAW,CAACI,cAAc,CAAY,CAAC,cACnD9D,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,cACXxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACP1G,IAAA,CAACW,SAAS,EAAA+F,QAAA,CAAEhD,WAAW,CAACK,YAAY,CAAY,CAAC,cACjD/D,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,SAAO,CAAW,CAAC,EACtB,CAAC,cACXxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACP1G,IAAA,CAACW,SAAS,EAAA+F,QAAA,CAAEhD,WAAW,CAACM,aAAa,CAAY,CAAC,cAClDhE,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,UAAQ,CAAW,CAAC,EACvB,CAAC,cACXxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACP1G,IAAA,CAACW,SAAS,EAAA+F,QAAA,CAAEhD,WAAW,CAACO,qBAAqB,CAAY,CAAC,cAC1DjE,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,cACXxG,KAAA,CAACO,QAAQ,EAAAiG,QAAA,eACPxG,KAAA,CAACS,SAAS,EAAA+F,QAAA,EAAEhD,WAAW,CAACQ,gBAAgB,CAAC,GAAC,EAAW,CAAC,cACtDlE,IAAA,CAACa,SAAS,EAAA6F,QAAA,CAAC,mBAAiB,CAAW,CAAC,EAChC,CAAC,EACF,CAAC,cAGZxG,KAAA,CAACa,cAAc,EAAA2F,QAAA,eACbxG,KAAA,CAACe,SAAS,EAAAyF,QAAA,eACR1G,IAAA,CAACmB,UAAU,EAAAuF,QAAA,CAAC,4BAA0B,CAAY,CAAC,cACnD1G,IAAA,QAAKkH,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,mBAAmB,CAAE,gBAAgB,CAAEC,GAAG,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAZ,QAAA,CACtGpC,aAAa,CAACiD,GAAG,CAAC,CAACnE,KAAK,CAAEoE,KAAK,gBAC9BtH,KAAA,QAAAwG,QAAA,eACE1G,IAAA,QAAKkH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAjB,QAAA,CACvEtD,KAAK,CAACuC,KAAK,CACT,CAAC,cACN3F,IAAA,QAAKkH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAS,CAAEF,UAAU,CAAE,MAAO,CAAE,CAAAhB,QAAA,CACpEtD,KAAK,CAACkC,SAAS,CACb,CAAC,cACNpF,KAAA,QAAKgH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,EAAC,KAC5C,CAACtD,KAAK,CAACwC,KAAK,EACZ,CAAC,cACN1F,KAAA,QAAKgH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,KAAM,CAAE,CAAAnB,QAAA,EAClEtD,KAAK,CAACsC,UAAU,CAAC,QACpB,EAAK,CAAC,GAZE8B,KAaL,CACN,CAAC,CACC,CAAC,EACG,CAAC,cAEZtH,KAAA,CAACe,SAAS,EAAAyF,QAAA,eACR1G,IAAA,CAACmB,UAAU,EAAAuF,QAAA,CAAC,eAAa,CAAY,CAAC,cACtCxG,KAAA,QAAKgH,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,GAAG,CAAE,MAAO,CAAE,CAAAX,QAAA,eAC3CxG,KAAA,QAAKgH,KAAK,CAAE,CAAEI,SAAS,CAAE,QAAS,CAAE,CAAAZ,QAAA,eAClCxG,KAAA,QAAKgH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAS,CAAEF,UAAU,CAAE,MAAO,CAAE,CAAAhB,QAAA,EACpEN,iBAAiB,CAAC,CAAC,CAAC,GACvB,EAAK,CAAC,cACNpG,IAAA,QAAKkH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAAC,iBAAe,CAAK,CAAC,EACnE,CAAC,cACNxG,KAAA,QAAKgH,KAAK,CAAE,CAAEI,SAAS,CAAE,QAAS,CAAE,CAAAZ,QAAA,eAClCxG,KAAA,QAAKgH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAS,CAAEF,UAAU,CAAE,MAAO,CAAE,CAAAhB,QAAA,EACpEH,cAAc,CAAC,CAAC,CAAC,GACpB,EAAK,CAAC,cACNvG,IAAA,QAAKkH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAAC,cAAY,CAAK,CAAC,EAChE,CAAC,cACNxG,KAAA,QAAKgH,KAAK,CAAE,CAAEI,SAAS,CAAE,QAAS,CAAE,CAAAZ,QAAA,eAClCxG,KAAA,QAAKgH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAS,CAAEF,UAAU,CAAE,MAAO,CAAE,CAAAhB,QAAA,EACpEhD,WAAW,CAACQ,gBAAgB,CAAC,GAChC,EAAK,CAAC,cACNlE,IAAA,QAAKkH,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAAC,mBAAiB,CAAK,CAAC,EACrE,CAAC,EACH,CAAC,EACG,CAAC,EACE,CAAC,cAGjBxG,KAAA,CAACP,IAAI,EAAA+G,QAAA,eACH1G,IAAA,OAAIkH,KAAK,CAAE,CAAES,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAlB,QAAA,CAAC,wBAAsB,CAAI,CAAC,cAClF1G,IAAA,QAAKkH,KAAK,CAAE,CAAEY,SAAS,CAAE,MAAO,CAAE,CAAApB,QAAA,cAChCxG,KAAA,CAACgC,YAAY,EAAAwE,QAAA,eACX1G,IAAA,UAAA0G,QAAA,cACExG,KAAA,OAAAwG,QAAA,eACE1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,YAAU,CAAa,CAAC,cACrC1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,aAAW,CAAa,CAAC,cACtC1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,WAAS,CAAa,CAAC,cACpC1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,SAAO,CAAa,CAAC,cAClC1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnC1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,WAAS,CAAa,CAAC,cACpC1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,YAAU,CAAa,CAAC,cACrC1G,IAAA,CAACqC,WAAW,EAAAqE,QAAA,CAAC,OAAK,CAAa,CAAC,EAC9B,CAAC,CACA,CAAC,cACR1G,IAAA,UAAA0G,QAAA,CACGtC,iBAAiB,CAACmD,GAAG,CAAC,CAACQ,IAAI,CAAEP,KAAK,gBACjCtH,KAAA,CAAC4C,QAAQ,EAAA4D,QAAA,eACP1G,IAAA,CAAC2C,SAAS,EAACuE,KAAK,CAAE,CAAEQ,UAAU,CAAE,KAAM,CAAE,CAAAhB,QAAA,CAAEqB,IAAI,CAAC1C,UAAU,CAAY,CAAC,cACtErF,IAAA,CAAC2C,SAAS,EAAA+D,QAAA,CAAEqB,IAAI,CAAClE,UAAU,CAAY,CAAC,cACxC7D,IAAA,CAAC2C,SAAS,EAACuE,KAAK,CAAE,CAAEU,KAAK,CAAE,SAAU,CAAE,CAAAlB,QAAA,CAAEqB,IAAI,CAACzC,SAAS,CAAY,CAAC,cACpEtF,IAAA,CAAC2C,SAAS,EAACuE,KAAK,CAAE,CAAEU,KAAK,CAAE,SAAU,CAAE,CAAAlB,QAAA,CAAEqB,IAAI,CAACxC,OAAO,CAAY,CAAC,cAClEvF,IAAA,CAAC2C,SAAS,EAACuE,KAAK,CAAE,CAAEU,KAAK,CAAE,SAAU,CAAE,CAAAlB,QAAA,CAAEqB,IAAI,CAACvC,QAAQ,CAAY,CAAC,cACnEtF,KAAA,CAACyC,SAAS,EAAA+D,QAAA,EAAEqB,IAAI,CAACtC,OAAO,CAAC,OAAK,EAAW,CAAC,cAC1CvF,KAAA,CAACyC,SAAS,EAAA+D,QAAA,EAAEqB,IAAI,CAACrC,UAAU,CAAC,GAAC,EAAW,CAAC,cACzC1F,IAAA,CAAC2C,SAAS,EAAA+D,QAAA,cACRxG,KAAA,CAAC+C,cAAc,EAACG,KAAK,CAAE2E,IAAI,CAACrC,UAAU,CAAG,EAAE,CAAG,IAAI,CAAGqC,IAAI,CAACrC,UAAU,CAAG,EAAE,CAAG,QAAQ,CAAG,MAAO,CAAAgB,QAAA,EAC3FqB,IAAI,CAACrC,UAAU,CAAG,EAAE,CAAG,IAAI,CAAGqC,IAAI,CAACrC,UAAU,CAAG,EAAE,CAAG,IAAI,CAAG,IAAI,CAChEqC,IAAI,CAACrC,UAAU,CAAG,EAAE,CAAG,WAAW,CAAGqC,IAAI,CAACrC,UAAU,CAAG,EAAE,CAAG,QAAQ,CAAG,WAAW,EACrE,CAAC,CACR,CAAC,GAbC8B,KAcL,CACX,CAAC,CACG,CAAC,EACI,CAAC,CACZ,CAAC,EACF,CAAC,cAGPtH,KAAA,CAACP,IAAI,EAAA+G,QAAA,eACH1G,IAAA,OAAIkH,KAAK,CAAE,CAAES,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAlB,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC7ExG,KAAA,QAAKgH,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,mBAAmB,CAAE,sCAAsC,CAAEC,GAAG,CAAE,MAAO,CAAE,CAAAX,QAAA,eACxGxG,KAAA,QAAAwG,QAAA,eACE1G,IAAA,OAAIkH,KAAK,CAAE,CAAEU,KAAK,CAAE,SAAS,CAAED,YAAY,CAAE,MAAO,CAAE,CAAAjB,QAAA,CAAC,qCAAyB,CAAI,CAAC,cACrFxG,KAAA,OAAIgH,KAAK,CAAE,CAAEc,WAAW,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAvB,QAAA,eACpD1G,IAAA,OAAA0G,QAAA,CAAI,gDAA8C,CAAI,CAAC,cACvD1G,IAAA,OAAA0G,QAAA,CAAI,yDAAuD,CAAI,CAAC,cAChE1G,IAAA,OAAA0G,QAAA,CAAI,6CAA2C,CAAI,CAAC,EAClD,CAAC,EACF,CAAC,cACNxG,KAAA,QAAAwG,QAAA,eACE1G,IAAA,OAAIkH,KAAK,CAAE,CAAEU,KAAK,CAAE,SAAS,CAAED,YAAY,CAAE,MAAO,CAAE,CAAAjB,QAAA,CAAC,oCAAwB,CAAI,CAAC,cACpFxG,KAAA,OAAIgH,KAAK,CAAE,CAAEc,WAAW,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAvB,QAAA,eACpD1G,IAAA,OAAA0G,QAAA,CAAI,+CAA6C,CAAI,CAAC,cACtD1G,IAAA,OAAA0G,QAAA,CAAI,qDAAmD,CAAI,CAAC,cAC5D1G,IAAA,OAAA0G,QAAA,CAAI,+CAA6C,CAAI,CAAC,EACpD,CAAC,EACF,CAAC,cACNxG,KAAA,QAAAwG,QAAA,eACE1G,IAAA,OAAIkH,KAAK,CAAE,CAAEU,KAAK,CAAE,SAAS,CAAED,YAAY,CAAE,MAAO,CAAE,CAAAjB,QAAA,CAAC,8BAAkB,CAAI,CAAC,cAC9ExG,KAAA,OAAIgH,KAAK,CAAE,CAAEc,WAAW,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAvB,QAAA,eACpD1G,IAAA,OAAA0G,QAAA,CAAI,8CAA4C,CAAI,CAAC,cACrD1G,IAAA,OAAA0G,QAAA,CAAI,mDAAiD,CAAI,CAAC,cAC1D1G,IAAA,OAAA0G,QAAA,CAAI,2CAAyC,CAAI,CAAC,EAChD,CAAC,EACF,CAAC,EACH,CAAC,EACF,CAAC,EACS,CAAC,CACJ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAArD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}