{"ast": null, "code": "import _objectSpread from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import{Card,Button,Input,Select,FormGroup,Label,ErrorMessage}from'../../styles/GlobalStyles';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Container=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n\"])));const Header=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.mediumGray);const Title=styled.h1(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.primary);const TableContainer=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  overflow-x: auto;\\n\"])));const Table=styled.table(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const StatusBadge=styled.span(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  background-color: \",\";\\n  color: \",\";\\n\"])),props=>props.active?'#e8f5e9':'#ffebee',props=>props.active?'#2e7d32':'#c62828');const RoleBadge=styled.span(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  \\n  \",\"\\n\"])),props=>{switch(props.role){case'Admin':return\"\\n          background-color: #f3e5f5;\\n          color: #4a148c;\\n        \";case'Supervisor':return\"\\n          background-color: #fff3e0;\\n          color: #e65100;\\n        \";case'Agent':return\"\\n          background-color: #e3f2fd;\\n          color: #0d47a1;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const Modal=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: \",\";\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 1000;\\n  align-items: center;\\n  justify-content: center;\\n\"])),props=>props.show?'flex':'none');const ModalContent=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  background: \",\";\\n  border-radius: \",\";\\n  padding: 30px;\\n  max-width: 500px;\\n  width: 90%;\\n  max-height: 90%;\\n  overflow-y: auto;\\n\"])),props=>props.theme.colors.white,props=>props.theme.borderRadius.md);const ModalHeader=styled.div(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.mediumGray);const ModalTitle=styled.h2(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  margin: 0;\\n\"])),props=>props.theme.colors.primary);const CloseButton=styled.button(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  background: none;\\n  border: none;\\n  font-size: 24px;\\n  cursor: pointer;\\n  color: \",\";\\n  \\n  &:hover {\\n    color: \",\";\\n  }\\n\"])),props=>props.theme.colors.textMedium,props=>props.theme.colors.textDark);const UserManagement=()=>{const navigate=useNavigate();const[users,setUsers]=useState([]);const[showModal,setShowModal]=useState(false);const[editingUser,setEditingUser]=useState(null);const[loading,setLoading]=useState(false);const[formData,setFormData]=useState({username:'',firstName:'',lastName:'',email:'',role:'Agent',password:'',confirmPassword:''});const[errors,setErrors]=useState({});useEffect(()=>{loadUsers();},[]);const loadUsers=async()=>{try{setLoading(true);// Mock users data\nconst mockUsers=[{userId:1,username:'agent1',firstName:'John',lastName:'Agent',email:'<EMAIL>',role:'Agent',isActive:true,createdDate:'2024-01-01T00:00:00Z',lastLoginDate:'2024-01-16T08:30:00Z'},{userId:2,username:'supervisor1',firstName:'Jane',lastName:'Supervisor',email:'<EMAIL>',role:'Supervisor',isActive:true,createdDate:'2024-01-01T00:00:00Z',lastLoginDate:'2024-01-16T09:15:00Z'},{userId:3,username:'admin1',firstName:'Admin',lastName:'User',email:'<EMAIL>',role:'Admin',isActive:true,createdDate:'2024-01-01T00:00:00Z',lastLoginDate:'2024-01-16T10:00:00Z'},{userId:4,username:'agent2',firstName:'Bob',lastName:'Agent',email:'<EMAIL>',role:'Agent',isActive:false,createdDate:'2024-01-05T00:00:00Z',lastLoginDate:'2024-01-10T14:20:00Z'}];setUsers(mockUsers);}catch(error){console.error('Error loading users:',error);}finally{setLoading(false);}};const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));// Clear error when user starts typing\nif(errors[name]){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:''}));}};const validateForm=()=>{const newErrors={};if(!formData.username.trim()){newErrors.username='Username is required';}if(!formData.firstName.trim()){newErrors.firstName='First name is required';}if(!formData.lastName.trim()){newErrors.lastName='Last name is required';}if(!formData.email.trim()){newErrors.email='Email is required';}else if(!/\\S+@\\S+\\.\\S+/.test(formData.email)){newErrors.email='Email is invalid';}if(!editingUser){if(!formData.password){newErrors.password='Password is required';}else if(formData.password.length<6){newErrors.password='Password must be at least 6 characters';}if(formData.password!==formData.confirmPassword){newErrors.confirmPassword='Passwords do not match';}}setErrors(newErrors);return Object.keys(newErrors).length===0;};const handleSubmit=async e=>{e.preventDefault();if(!validateForm()){return;}setLoading(true);try{if(editingUser){// Update existing user\nconst updatedUser=_objectSpread(_objectSpread({},editingUser),{},{username:formData.username,firstName:formData.firstName,lastName:formData.lastName,email:formData.email,role:formData.role});setUsers(prev=>prev.map(user=>user.userId===editingUser.userId?updatedUser:user));alert('User updated successfully!');}else{// Create new user\nconst newUser={userId:Math.max(...users.map(u=>u.userId))+1,username:formData.username,firstName:formData.firstName,lastName:formData.lastName,email:formData.email,role:formData.role,isActive:true,createdDate:new Date().toISOString()};setUsers(prev=>[...prev,newUser]);alert('User created successfully!');}handleCloseModal();}catch(error){console.error('Error saving user:',error);alert('Failed to save user. Please try again.');}finally{setLoading(false);}};const handleCreateUser=()=>{setEditingUser(null);setFormData({username:'',firstName:'',lastName:'',email:'',role:'Agent',password:'',confirmPassword:''});setErrors({});setShowModal(true);};const handleEditUser=user=>{setEditingUser(user);setFormData({username:user.username,firstName:user.firstName,lastName:user.lastName,email:user.email,role:user.role,password:'',confirmPassword:''});setErrors({});setShowModal(true);};const handleToggleUserStatus=userId=>{setUsers(prev=>prev.map(user=>user.userId===userId?_objectSpread(_objectSpread({},user),{},{isActive:!user.isActive}):user));};const handleCloseModal=()=>{setShowModal(false);setEditingUser(null);setFormData({username:'',firstName:'',lastName:'',email:'',role:'Agent',password:'',confirmPassword:''});setErrors({});};const handleBack=()=>{navigate('/admin/dashboard');};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:handleBack,children:\"\\u2190 Back\"}),/*#__PURE__*/_jsx(Title,{style:{marginLeft:'20px'},children:\"User Management\"})]}),/*#__PURE__*/_jsx(Button,{onClick:handleCreateUser,children:\"+ Create User\"})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Name\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Username\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Email\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Role\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Status\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Created Date\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Last Login\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:users.map(user=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsxs(TableCell,{children:[user.firstName,\" \",user.lastName]}),/*#__PURE__*/_jsx(TableCell,{children:user.username}),/*#__PURE__*/_jsx(TableCell,{children:user.email}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(RoleBadge,{role:user.role,children:user.role})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(StatusBadge,{active:user.isActive,children:user.isActive?'Active':'Inactive'})}),/*#__PURE__*/_jsx(TableCell,{children:formatDate(user.createdDate)}),/*#__PURE__*/_jsx(TableCell,{children:user.lastLoginDate?formatDate(user.lastLoginDate):'Never'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'8px'},children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>handleEditUser(user),children:\"Edit\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:user.isActive?'danger':'secondary',onClick:()=>handleToggleUserStatus(user.userId),children:user.isActive?'Deactivate':'Activate'})]})})]},user.userId))})]})}),users.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px',color:'#777'},children:\"No users found.\"})]}),/*#__PURE__*/_jsx(Modal,{show:showModal,children:/*#__PURE__*/_jsxs(ModalContent,{children:[/*#__PURE__*/_jsxs(ModalHeader,{children:[/*#__PURE__*/_jsx(ModalTitle,{children:editingUser?'Edit User':'Create New User'}),/*#__PURE__*/_jsx(CloseButton,{onClick:handleCloseModal,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"username\",children:\"Username *\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"username\",name:\"username\",value:formData.username,onChange:handleInputChange,required:true}),errors.username&&/*#__PURE__*/_jsx(ErrorMessage,{children:errors.username})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"firstName\",children:\"First Name *\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"firstName\",name:\"firstName\",value:formData.firstName,onChange:handleInputChange,required:true}),errors.firstName&&/*#__PURE__*/_jsx(ErrorMessage,{children:errors.firstName})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"lastName\",children:\"Last Name *\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"lastName\",name:\"lastName\",value:formData.lastName,onChange:handleInputChange,required:true}),errors.lastName&&/*#__PURE__*/_jsx(ErrorMessage,{children:errors.lastName})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"email\",children:\"Email *\"}),/*#__PURE__*/_jsx(Input,{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,required:true}),errors.email&&/*#__PURE__*/_jsx(ErrorMessage,{children:errors.email})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"role\",children:\"Role *\"}),/*#__PURE__*/_jsxs(Select,{id:\"role\",name:\"role\",value:formData.role,onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"Agent\",children:\"Agent\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Supervisor\",children:\"Supervisor\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Admin\",children:\"Admin\"})]})]}),!editingUser&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"password\",children:\"Password *\"}),/*#__PURE__*/_jsx(Input,{type:\"password\",id:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,required:true}),errors.password&&/*#__PURE__*/_jsx(ErrorMessage,{children:errors.password})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"confirmPassword\",children:\"Confirm Password *\"}),/*#__PURE__*/_jsx(Input,{type:\"password\",id:\"confirmPassword\",name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleInputChange,required:true}),errors.confirmPassword&&/*#__PURE__*/_jsx(ErrorMessage,{children:errors.confirmPassword})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'10px',justifyContent:'flex-end',marginTop:'20px'},children:[/*#__PURE__*/_jsx(Button,{type:\"button\",variant:\"outline\",onClick:handleCloseModal,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading,children:loading?'Saving...':editingUser?'Update User':'Create User'})]})]})]})})]});};export default UserManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "Input", "Select", "FormGroup", "Label", "ErrorMessage", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Container", "div", "_templateObject", "_taggedTemplateLiteral", "Header", "_templateObject2", "props", "theme", "colors", "mediumGray", "Title", "h1", "_templateObject3", "primary", "TableContainer", "_templateObject4", "Table", "table", "_templateObject5", "TableHeader", "th", "_templateObject6", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject7", "TableRow", "tr", "_templateObject8", "StatusBadge", "span", "_templateObject9", "active", "RoleBadge", "_templateObject0", "role", "Modal", "_templateObject1", "show", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject10", "white", "borderRadius", "md", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject11", "ModalTitle", "h2", "_templateObject12", "CloseButton", "button", "_templateObject13", "textDark", "UserManagement", "navigate", "users", "setUsers", "showModal", "setShowModal", "editingUser", "setEditingUser", "loading", "setLoading", "formData", "setFormData", "username", "firstName", "lastName", "email", "password", "confirmPassword", "errors", "setErrors", "loadUsers", "mockUsers", "userId", "isActive", "createdDate", "lastLoginDate", "error", "console", "handleInputChange", "e", "name", "value", "target", "prev", "_objectSpread", "validateForm", "newErrors", "trim", "test", "length", "Object", "keys", "handleSubmit", "preventDefault", "updatedUser", "map", "user", "alert", "newUser", "Math", "max", "u", "Date", "toISOString", "handleCloseModal", "handleCreateUser", "handleEditUser", "handleToggleUserStatus", "handleBack", "formatDate", "dateString", "toLocaleDateString", "children", "style", "display", "alignItems", "variant", "onClick", "marginLeft", "gap", "size", "textAlign", "padding", "color", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "justifyContent", "marginTop", "disabled"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/UserManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage } from '../../styles/GlobalStyles';\n\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ active: boolean }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ${props => props.active ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.active ? '#2e7d32' : '#c62828'};\n`;\n\nconst RoleBadge = styled.span<{ role: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  \n  ${props => {\n    switch (props.role) {\n      case 'Admin':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'Supervisor':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'Agent':\n        return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst Modal = styled.div<{ show: boolean }>`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst ModalContent = styled.div`\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n`;\n\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst ModalTitle = styled.h2`\n  color: ${props => props.theme.colors.primary};\n  margin: 0;\n`;\n\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ${props => props.theme.colors.textMedium};\n  \n  &:hover {\n    color: ${props => props.theme.colors.textDark};\n  }\n`;\n\ninterface User {\n  userId: number;\n  username: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  role: 'Agent' | 'Supervisor' | 'Admin';\n  isActive: boolean;\n  createdDate: string;\n  lastLoginDate?: string;\n}\n\nconst UserManagement: React.FC = () => {\n  const navigate = useNavigate();\n  const [users, setUsers] = useState<User[]>([]);\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: 'Agent' as 'Agent' | 'Supervisor' | 'Admin',\n    password: '',\n    confirmPassword: '',\n  });\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\n\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      // Mock users data\n      const mockUsers: User[] = [\n        {\n          userId: 1,\n          username: 'agent1',\n          firstName: 'John',\n          lastName: 'Agent',\n          email: '<EMAIL>',\n          role: 'Agent',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T08:30:00Z',\n        },\n        {\n          userId: 2,\n          username: 'supervisor1',\n          firstName: 'Jane',\n          lastName: 'Supervisor',\n          email: '<EMAIL>',\n          role: 'Supervisor',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T09:15:00Z',\n        },\n        {\n          userId: 3,\n          username: 'admin1',\n          firstName: 'Admin',\n          lastName: 'User',\n          email: '<EMAIL>',\n          role: 'Admin',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T10:00:00Z',\n        },\n        {\n          userId: 4,\n          username: 'agent2',\n          firstName: 'Bob',\n          lastName: 'Agent',\n          email: '<EMAIL>',\n          role: 'Agent',\n          isActive: false,\n          createdDate: '2024-01-05T00:00:00Z',\n          lastLoginDate: '2024-01-10T14:20:00Z',\n        },\n      ];\n      \n      setUsers(mockUsers);\n    } catch (error) {\n      console.error('Error loading users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: { [key: string]: string } = {};\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!editingUser) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 6) {\n        newErrors.password = 'Password must be at least 6 characters';\n      }\n\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      if (editingUser) {\n        // Update existing user\n        const updatedUser: User = {\n          ...editingUser,\n          username: formData.username,\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          role: formData.role,\n        };\n\n        setUsers(prev => prev.map(user => \n          user.userId === editingUser.userId ? updatedUser : user\n        ));\n        \n        alert('User updated successfully!');\n      } else {\n        // Create new user\n        const newUser: User = {\n          userId: Math.max(...users.map(u => u.userId)) + 1,\n          username: formData.username,\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          role: formData.role,\n          isActive: true,\n          createdDate: new Date().toISOString(),\n        };\n\n        setUsers(prev => [...prev, newUser]);\n        \n        alert('User created successfully!');\n      }\n\n      handleCloseModal();\n    } catch (error) {\n      console.error('Error saving user:', error);\n      alert('Failed to save user. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      email: user.email,\n      role: user.role,\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleToggleUserStatus = (userId: number) => {\n    setUsers(prev => prev.map(user => \n      user.userId === userId ? { ...user, isActive: !user.isActive } : user\n    ));\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n  };\n\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  return (\n    <Container>\n      <Header>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <Button variant=\"outline\" onClick={handleBack}>\n            ← Back\n          </Button>\n          <Title style={{ marginLeft: '20px' }}>User Management</Title>\n        </div>\n        <Button onClick={handleCreateUser}>\n          + Create User\n        </Button>\n      </Header>\n\n      <Card>\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Name</TableHeader>\n                <TableHeader>Username</TableHeader>\n                <TableHeader>Email</TableHeader>\n                <TableHeader>Role</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Created Date</TableHeader>\n                <TableHeader>Last Login</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {users.map((user) => (\n                <TableRow key={user.userId}>\n                  <TableCell>{user.firstName} {user.lastName}</TableCell>\n                  <TableCell>{user.username}</TableCell>\n                  <TableCell>{user.email}</TableCell>\n                  <TableCell>\n                    <RoleBadge role={user.role}>{user.role}</RoleBadge>\n                  </TableCell>\n                  <TableCell>\n                    <StatusBadge active={user.isActive}>\n                      {user.isActive ? 'Active' : 'Inactive'}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>{formatDate(user.createdDate)}</TableCell>\n                  <TableCell>\n                    {user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Never'}\n                  </TableCell>\n                  <TableCell>\n                    <div style={{ display: 'flex', gap: '8px' }}>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleEditUser(user)}\n                      >\n                        Edit\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant={user.isActive ? 'danger' : 'secondary'}\n                        onClick={() => handleToggleUserStatus(user.userId)}\n                      >\n                        {user.isActive ? 'Deactivate' : 'Activate'}\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {users.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            No users found.\n          </div>\n        )}\n      </Card>\n\n      {/* Create/Edit User Modal */}\n      <Modal show={showModal}>\n        <ModalContent>\n          <ModalHeader>\n            <ModalTitle>\n              {editingUser ? 'Edit User' : 'Create New User'}\n            </ModalTitle>\n            <CloseButton onClick={handleCloseModal}>×</CloseButton>\n          </ModalHeader>\n\n          <form onSubmit={handleSubmit}>\n            <FormGroup>\n              <Label htmlFor=\"username\">Username *</Label>\n              <Input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                value={formData.username}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.username && <ErrorMessage>{errors.username}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"firstName\">First Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"firstName\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.firstName && <ErrorMessage>{errors.firstName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"lastName\">Last Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"lastName\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.lastName && <ErrorMessage>{errors.lastName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"email\">Email *</Label>\n              <Input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"role\">Role *</Label>\n              <Select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"Agent\">Agent</option>\n                <option value=\"Supervisor\">Supervisor</option>\n                <option value=\"Admin\">Admin</option>\n              </Select>\n            </FormGroup>\n\n            {!editingUser && (\n              <>\n                <FormGroup>\n                  <Label htmlFor=\"password\">Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.password && <ErrorMessage>{errors.password}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label htmlFor=\"confirmPassword\">Confirm Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    value={formData.confirmPassword}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.confirmPassword && <ErrorMessage>{errors.confirmPassword}</ErrorMessage>}\n                </FormGroup>\n              </>\n            )}\n\n            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}>\n              <Button type=\"button\" variant=\"outline\" onClick={handleCloseModal}>\n                Cancel\n              </Button>\n              <Button type=\"submit\" disabled={loading}>\n                {loading ? 'Saving...' : (editingUser ? 'Update User' : 'Create User')}\n              </Button>\n            </div>\n          </form>\n        </ModalContent>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": "qlBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,MAAM,CAAEC,SAAS,CAAEC,KAAK,CAAEC,YAAY,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExG,KAAM,CAAAC,SAAS,CAAGd,MAAM,CAACe,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,qEAI3B,CAED,KAAM,CAAAC,MAAM,CAAGlB,MAAM,CAACe,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,yKAMIG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAClE,CAED,KAAM,CAAAC,KAAK,CAAGxB,MAAM,CAACyB,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,kEAGZG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAC7C,CAED,KAAM,CAAAC,cAAc,CAAG5B,MAAM,CAACe,GAAG,CAAAc,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,+BAEhC,CAED,KAAM,CAAAa,KAAK,CAAG9B,MAAM,CAAC+B,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,wDAGzB,CAED,KAAM,CAAAgB,WAAW,CAAGjC,MAAM,CAACkC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,qJAGAG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS,CAC5ChB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACe,QAAQ,CAE/CjB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU,CAChD,CAED,KAAM,CAAAC,SAAS,CAAGvC,MAAM,CAACwC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAxB,sBAAA,uFAGEG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAG1C,MAAM,CAAC2C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA3B,sBAAA,wDAEFG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS,CAE5D,CAED,KAAM,CAAAS,WAAW,CAAG7C,MAAM,CAAC8C,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAA9B,sBAAA,mKAMTG,KAAK,EAAIA,KAAK,CAAC4B,MAAM,CAAG,SAAS,CAAG,SAAS,CACxD5B,KAAK,EAAIA,KAAK,CAAC4B,MAAM,CAAG,SAAS,CAAG,SAAS,CACvD,CAED,KAAM,CAAAC,SAAS,CAAGjD,MAAM,CAAC8C,IAAI,CAAAI,gBAAA,GAAAA,gBAAA,CAAAjC,sBAAA,qIAOzBG,KAAK,EAAI,CACT,OAAQA,KAAK,CAAC+B,IAAI,EAChB,IAAK,OAAO,CACV,oFAIF,IAAK,YAAY,CACf,oFAIF,IAAK,OAAO,CACV,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,KAAK,CAAGpD,MAAM,CAACe,GAAG,CAAAsC,gBAAA,GAAAA,gBAAA,CAAApC,sBAAA,sNACXG,KAAK,EAAIA,KAAK,CAACkC,IAAI,CAAG,MAAM,CAAG,MAAM,CAUjD,CAED,KAAM,CAAAC,YAAY,CAAGvD,MAAM,CAACe,GAAG,CAAAyC,iBAAA,GAAAA,iBAAA,CAAAvC,sBAAA,oJACfG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACmC,KAAK,CAC9BrC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACqC,YAAY,CAACC,EAAE,CAMtD,CAED,KAAM,CAAAC,WAAW,CAAG5D,MAAM,CAACe,GAAG,CAAA8C,iBAAA,GAAAA,iBAAA,CAAA5C,sBAAA,yKAMDG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAClE,CAED,KAAM,CAAAuC,UAAU,CAAG9D,MAAM,CAAC+D,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA/C,sBAAA,uCACjBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAE7C,CAED,KAAM,CAAAsC,WAAW,CAAGjE,MAAM,CAACkE,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAAlD,sBAAA,8IAKtBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU,CAGpClB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC8C,QAAQ,CAEhD,CAcD,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAC,QAAQ,CAAGvE,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACwE,KAAK,CAAEC,QAAQ,CAAC,CAAG3E,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAAC4E,SAAS,CAAEC,YAAY,CAAC,CAAG7E,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC8E,WAAW,CAAEC,cAAc,CAAC,CAAG/E,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAACgF,OAAO,CAAEC,UAAU,CAAC,CAAGjF,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkF,QAAQ,CAAEC,WAAW,CAAC,CAAGnF,QAAQ,CAAC,CACvCoF,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTjC,IAAI,CAAE,OAA2C,CACjDkC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EACnB,CAAC,CAAC,CACF,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG3F,QAAQ,CAA4B,CAAC,CAAC,CAAC,CAEnEC,SAAS,CAAC,IAAM,CACd2F,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFX,UAAU,CAAC,IAAI,CAAC,CAChB;AACA,KAAM,CAAAY,SAAiB,CAAG,CACxB,CACEC,MAAM,CAAE,CAAC,CACTV,QAAQ,CAAE,QAAQ,CAClBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,oBAAoB,CAC3BjC,IAAI,CAAE,OAAO,CACbyC,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,sBACjB,CAAC,CACD,CACEH,MAAM,CAAE,CAAC,CACTV,QAAQ,CAAE,aAAa,CACvBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,YAAY,CACtBC,KAAK,CAAE,yBAAyB,CAChCjC,IAAI,CAAE,YAAY,CAClByC,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,sBACjB,CAAC,CACD,CACEH,MAAM,CAAE,CAAC,CACTV,QAAQ,CAAE,QAAQ,CAClBC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,eAAe,CACtBjC,IAAI,CAAE,OAAO,CACbyC,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,sBACjB,CAAC,CACD,CACEH,MAAM,CAAE,CAAC,CACTV,QAAQ,CAAE,QAAQ,CAClBC,SAAS,CAAE,KAAK,CAChBC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,mBAAmB,CAC1BjC,IAAI,CAAE,OAAO,CACbyC,QAAQ,CAAE,KAAK,CACfC,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,sBACjB,CAAC,CACF,CAEDtB,QAAQ,CAACkB,SAAS,CAAC,CACrB,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CAAC,OAAS,CACRjB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmB,iBAAiB,CAAIC,CAA0D,EAAK,CACxF,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChCrB,WAAW,CAACsB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACH,IAAI,EAAGC,KAAK,EAAG,CAAC,CAEjD;AACA,GAAIb,MAAM,CAACY,IAAI,CAAC,CAAE,CAChBX,SAAS,CAACc,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACH,IAAI,EAAG,EAAE,EAAG,CAAC,CAC9C,CACF,CAAC,CAED,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAe,CAClC,KAAM,CAAAC,SAAoC,CAAG,CAAC,CAAC,CAE/C,GAAI,CAAC1B,QAAQ,CAACE,QAAQ,CAACyB,IAAI,CAAC,CAAC,CAAE,CAC7BD,SAAS,CAACxB,QAAQ,CAAG,sBAAsB,CAC7C,CAEA,GAAI,CAACF,QAAQ,CAACG,SAAS,CAACwB,IAAI,CAAC,CAAC,CAAE,CAC9BD,SAAS,CAACvB,SAAS,CAAG,wBAAwB,CAChD,CAEA,GAAI,CAACH,QAAQ,CAACI,QAAQ,CAACuB,IAAI,CAAC,CAAC,CAAE,CAC7BD,SAAS,CAACtB,QAAQ,CAAG,uBAAuB,CAC9C,CAEA,GAAI,CAACJ,QAAQ,CAACK,KAAK,CAACsB,IAAI,CAAC,CAAC,CAAE,CAC1BD,SAAS,CAACrB,KAAK,CAAG,mBAAmB,CACvC,CAAC,IAAM,IAAI,CAAC,cAAc,CAACuB,IAAI,CAAC5B,QAAQ,CAACK,KAAK,CAAC,CAAE,CAC/CqB,SAAS,CAACrB,KAAK,CAAG,kBAAkB,CACtC,CAEA,GAAI,CAACT,WAAW,CAAE,CAChB,GAAI,CAACI,QAAQ,CAACM,QAAQ,CAAE,CACtBoB,SAAS,CAACpB,QAAQ,CAAG,sBAAsB,CAC7C,CAAC,IAAM,IAAIN,QAAQ,CAACM,QAAQ,CAACuB,MAAM,CAAG,CAAC,CAAE,CACvCH,SAAS,CAACpB,QAAQ,CAAG,wCAAwC,CAC/D,CAEA,GAAIN,QAAQ,CAACM,QAAQ,GAAKN,QAAQ,CAACO,eAAe,CAAE,CAClDmB,SAAS,CAACnB,eAAe,CAAG,wBAAwB,CACtD,CACF,CAEAE,SAAS,CAACiB,SAAS,CAAC,CACpB,MAAO,CAAAI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACG,MAAM,GAAK,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAb,CAAkB,EAAK,CACjDA,CAAC,CAACc,cAAc,CAAC,CAAC,CAElB,GAAI,CAACR,YAAY,CAAC,CAAC,CAAE,CACnB,OACF,CAEA1B,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,GAAIH,WAAW,CAAE,CACf;AACA,KAAM,CAAAsC,WAAiB,CAAAV,aAAA,CAAAA,aAAA,IAClB5B,WAAW,MACdM,QAAQ,CAAEF,QAAQ,CAACE,QAAQ,CAC3BC,SAAS,CAAEH,QAAQ,CAACG,SAAS,CAC7BC,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAC3BC,KAAK,CAAEL,QAAQ,CAACK,KAAK,CACrBjC,IAAI,CAAE4B,QAAQ,CAAC5B,IAAI,EACpB,CAEDqB,QAAQ,CAAC8B,IAAI,EAAIA,IAAI,CAACY,GAAG,CAACC,IAAI,EAC5BA,IAAI,CAACxB,MAAM,GAAKhB,WAAW,CAACgB,MAAM,CAAGsB,WAAW,CAAGE,IACrD,CAAC,CAAC,CAEFC,KAAK,CAAC,4BAA4B,CAAC,CACrC,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,OAAa,CAAG,CACpB1B,MAAM,CAAE2B,IAAI,CAACC,GAAG,CAAC,GAAGhD,KAAK,CAAC2C,GAAG,CAACM,CAAC,EAAIA,CAAC,CAAC7B,MAAM,CAAC,CAAC,CAAG,CAAC,CACjDV,QAAQ,CAAEF,QAAQ,CAACE,QAAQ,CAC3BC,SAAS,CAAEH,QAAQ,CAACG,SAAS,CAC7BC,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAC3BC,KAAK,CAAEL,QAAQ,CAACK,KAAK,CACrBjC,IAAI,CAAE4B,QAAQ,CAAC5B,IAAI,CACnByC,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,GAAI,CAAA4B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACtC,CAAC,CAEDlD,QAAQ,CAAC8B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEe,OAAO,CAAC,CAAC,CAEpCD,KAAK,CAAC,4BAA4B,CAAC,CACrC,CAEAO,gBAAgB,CAAC,CAAC,CACpB,CAAE,MAAO5B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CqB,KAAK,CAAC,wCAAwC,CAAC,CACjD,CAAC,OAAS,CACRtC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA8C,gBAAgB,CAAGA,CAAA,GAAM,CAC7BhD,cAAc,CAAC,IAAI,CAAC,CACpBI,WAAW,CAAC,CACVC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTjC,IAAI,CAAE,OAAO,CACbkC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EACnB,CAAC,CAAC,CACFE,SAAS,CAAC,CAAC,CAAC,CAAC,CACbd,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAED,KAAM,CAAAmD,cAAc,CAAIV,IAAU,EAAK,CACrCvC,cAAc,CAACuC,IAAI,CAAC,CACpBnC,WAAW,CAAC,CACVC,QAAQ,CAAEkC,IAAI,CAAClC,QAAQ,CACvBC,SAAS,CAAEiC,IAAI,CAACjC,SAAS,CACzBC,QAAQ,CAAEgC,IAAI,CAAChC,QAAQ,CACvBC,KAAK,CAAE+B,IAAI,CAAC/B,KAAK,CACjBjC,IAAI,CAAEgE,IAAI,CAAChE,IAAI,CACfkC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EACnB,CAAC,CAAC,CACFE,SAAS,CAAC,CAAC,CAAC,CAAC,CACbd,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAED,KAAM,CAAAoD,sBAAsB,CAAInC,MAAc,EAAK,CACjDnB,QAAQ,CAAC8B,IAAI,EAAIA,IAAI,CAACY,GAAG,CAACC,IAAI,EAC5BA,IAAI,CAACxB,MAAM,GAAKA,MAAM,CAAAY,aAAA,CAAAA,aAAA,IAAQY,IAAI,MAAEvB,QAAQ,CAAE,CAACuB,IAAI,CAACvB,QAAQ,GAAKuB,IACnE,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAQ,gBAAgB,CAAGA,CAAA,GAAM,CAC7BjD,YAAY,CAAC,KAAK,CAAC,CACnBE,cAAc,CAAC,IAAI,CAAC,CACpBI,WAAW,CAAC,CACVC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTjC,IAAI,CAAE,OAAO,CACbkC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EACnB,CAAC,CAAC,CACFE,SAAS,CAAC,CAAC,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAAuC,UAAU,CAAGA,CAAA,GAAM,CACvBzD,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,CAED,KAAM,CAAA0D,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAR,IAAI,CAACQ,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,mBACEvH,KAAA,CAACG,SAAS,EAAAqH,QAAA,eACRxH,KAAA,CAACO,MAAM,EAAAiH,QAAA,eACLxH,KAAA,QAAKyH,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACpD1H,IAAA,CAACP,MAAM,EAACqI,OAAO,CAAC,SAAS,CAACC,OAAO,CAAET,UAAW,CAAAI,QAAA,CAAC,aAE/C,CAAQ,CAAC,cACT1H,IAAA,CAACe,KAAK,EAAC4G,KAAK,CAAE,CAAEK,UAAU,CAAE,MAAO,CAAE,CAAAN,QAAA,CAAC,iBAAe,CAAO,CAAC,EAC1D,CAAC,cACN1H,IAAA,CAACP,MAAM,EAACsI,OAAO,CAAEZ,gBAAiB,CAAAO,QAAA,CAAC,eAEnC,CAAQ,CAAC,EACH,CAAC,cAETxH,KAAA,CAACV,IAAI,EAAAkI,QAAA,eACH1H,IAAA,CAACmB,cAAc,EAAAuG,QAAA,cACbxH,KAAA,CAACmB,KAAK,EAAAqG,QAAA,eACJ1H,IAAA,UAAA0H,QAAA,cACExH,KAAA,OAAAwH,QAAA,eACE1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,MAAI,CAAa,CAAC,cAC/B1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,UAAQ,CAAa,CAAC,cACnC1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,OAAK,CAAa,CAAC,cAChC1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,MAAI,CAAa,CAAC,cAC/B1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,QAAM,CAAa,CAAC,cACjC1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,cAAY,CAAa,CAAC,cACvC1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,YAAU,CAAa,CAAC,cACrC1H,IAAA,CAACwB,WAAW,EAAAkG,QAAA,CAAC,SAAO,CAAa,CAAC,EAChC,CAAC,CACA,CAAC,cACR1H,IAAA,UAAA0H,QAAA,CACG5D,KAAK,CAAC2C,GAAG,CAAEC,IAAI,eACdxG,KAAA,CAAC+B,QAAQ,EAAAyF,QAAA,eACPxH,KAAA,CAAC4B,SAAS,EAAA4F,QAAA,EAAEhB,IAAI,CAACjC,SAAS,CAAC,GAAC,CAACiC,IAAI,CAAChC,QAAQ,EAAY,CAAC,cACvD1E,IAAA,CAAC8B,SAAS,EAAA4F,QAAA,CAAEhB,IAAI,CAAClC,QAAQ,CAAY,CAAC,cACtCxE,IAAA,CAAC8B,SAAS,EAAA4F,QAAA,CAAEhB,IAAI,CAAC/B,KAAK,CAAY,CAAC,cACnC3E,IAAA,CAAC8B,SAAS,EAAA4F,QAAA,cACR1H,IAAA,CAACwC,SAAS,EAACE,IAAI,CAAEgE,IAAI,CAAChE,IAAK,CAAAgF,QAAA,CAAEhB,IAAI,CAAChE,IAAI,CAAY,CAAC,CAC1C,CAAC,cACZ1C,IAAA,CAAC8B,SAAS,EAAA4F,QAAA,cACR1H,IAAA,CAACoC,WAAW,EAACG,MAAM,CAAEmE,IAAI,CAACvB,QAAS,CAAAuC,QAAA,CAChChB,IAAI,CAACvB,QAAQ,CAAG,QAAQ,CAAG,UAAU,CAC3B,CAAC,CACL,CAAC,cACZnF,IAAA,CAAC8B,SAAS,EAAA4F,QAAA,CAAEH,UAAU,CAACb,IAAI,CAACtB,WAAW,CAAC,CAAY,CAAC,cACrDpF,IAAA,CAAC8B,SAAS,EAAA4F,QAAA,CACPhB,IAAI,CAACrB,aAAa,CAAGkC,UAAU,CAACb,IAAI,CAACrB,aAAa,CAAC,CAAG,OAAO,CACrD,CAAC,cACZrF,IAAA,CAAC8B,SAAS,EAAA4F,QAAA,cACRxH,KAAA,QAAKyH,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEK,GAAG,CAAE,KAAM,CAAE,CAAAP,QAAA,eAC1C1H,IAAA,CAACP,MAAM,EACLyI,IAAI,CAAC,IAAI,CACTH,OAAO,CAAEA,CAAA,GAAMX,cAAc,CAACV,IAAI,CAAE,CAAAgB,QAAA,CACrC,MAED,CAAQ,CAAC,cACT1H,IAAA,CAACP,MAAM,EACLyI,IAAI,CAAC,IAAI,CACTJ,OAAO,CAAEpB,IAAI,CAACvB,QAAQ,CAAG,QAAQ,CAAG,WAAY,CAChD4C,OAAO,CAAEA,CAAA,GAAMV,sBAAsB,CAACX,IAAI,CAACxB,MAAM,CAAE,CAAAwC,QAAA,CAElDhB,IAAI,CAACvB,QAAQ,CAAG,YAAY,CAAG,UAAU,CACpC,CAAC,EACN,CAAC,CACG,CAAC,GAhCCuB,IAAI,CAACxB,MAiCV,CACX,CAAC,CACG,CAAC,EACH,CAAC,CACM,CAAC,CAEhBpB,KAAK,CAACqC,MAAM,GAAK,CAAC,eACjBnG,IAAA,QAAK2H,KAAK,CAAE,CAAEQ,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,CAAC,iBAErE,CAAK,CACN,EACG,CAAC,cAGP1H,IAAA,CAAC2C,KAAK,EAACE,IAAI,CAAEmB,SAAU,CAAA0D,QAAA,cACrBxH,KAAA,CAAC4C,YAAY,EAAA4E,QAAA,eACXxH,KAAA,CAACiD,WAAW,EAAAuE,QAAA,eACV1H,IAAA,CAACqD,UAAU,EAAAqE,QAAA,CACRxD,WAAW,CAAG,WAAW,CAAG,iBAAiB,CACpC,CAAC,cACblE,IAAA,CAACwD,WAAW,EAACuE,OAAO,CAAEb,gBAAiB,CAAAQ,QAAA,CAAC,MAAC,CAAa,CAAC,EAC5C,CAAC,cAEdxH,KAAA,SAAMoI,QAAQ,CAAEhC,YAAa,CAAAoB,QAAA,eAC3BxH,KAAA,CAACN,SAAS,EAAA8H,QAAA,eACR1H,IAAA,CAACH,KAAK,EAAC0I,OAAO,CAAC,UAAU,CAAAb,QAAA,CAAC,YAAU,CAAO,CAAC,cAC5C1H,IAAA,CAACN,KAAK,EACJ8I,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,UAAU,CACb/C,IAAI,CAAC,UAAU,CACfC,KAAK,CAAErB,QAAQ,CAACE,QAAS,CACzBkE,QAAQ,CAAElD,iBAAkB,CAC5BmD,QAAQ,MACT,CAAC,CACD7D,MAAM,CAACN,QAAQ,eAAIxE,IAAA,CAACF,YAAY,EAAA4H,QAAA,CAAE5C,MAAM,CAACN,QAAQ,CAAe,CAAC,EACzD,CAAC,cAEZtE,KAAA,CAACN,SAAS,EAAA8H,QAAA,eACR1H,IAAA,CAACH,KAAK,EAAC0I,OAAO,CAAC,WAAW,CAAAb,QAAA,CAAC,cAAY,CAAO,CAAC,cAC/C1H,IAAA,CAACN,KAAK,EACJ8I,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,WAAW,CACd/C,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAErB,QAAQ,CAACG,SAAU,CAC1BiE,QAAQ,CAAElD,iBAAkB,CAC5BmD,QAAQ,MACT,CAAC,CACD7D,MAAM,CAACL,SAAS,eAAIzE,IAAA,CAACF,YAAY,EAAA4H,QAAA,CAAE5C,MAAM,CAACL,SAAS,CAAe,CAAC,EAC3D,CAAC,cAEZvE,KAAA,CAACN,SAAS,EAAA8H,QAAA,eACR1H,IAAA,CAACH,KAAK,EAAC0I,OAAO,CAAC,UAAU,CAAAb,QAAA,CAAC,aAAW,CAAO,CAAC,cAC7C1H,IAAA,CAACN,KAAK,EACJ8I,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,UAAU,CACb/C,IAAI,CAAC,UAAU,CACfC,KAAK,CAAErB,QAAQ,CAACI,QAAS,CACzBgE,QAAQ,CAAElD,iBAAkB,CAC5BmD,QAAQ,MACT,CAAC,CACD7D,MAAM,CAACJ,QAAQ,eAAI1E,IAAA,CAACF,YAAY,EAAA4H,QAAA,CAAE5C,MAAM,CAACJ,QAAQ,CAAe,CAAC,EACzD,CAAC,cAEZxE,KAAA,CAACN,SAAS,EAAA8H,QAAA,eACR1H,IAAA,CAACH,KAAK,EAAC0I,OAAO,CAAC,OAAO,CAAAb,QAAA,CAAC,SAAO,CAAO,CAAC,cACtC1H,IAAA,CAACN,KAAK,EACJ8I,IAAI,CAAC,OAAO,CACZC,EAAE,CAAC,OAAO,CACV/C,IAAI,CAAC,OAAO,CACZC,KAAK,CAAErB,QAAQ,CAACK,KAAM,CACtB+D,QAAQ,CAAElD,iBAAkB,CAC5BmD,QAAQ,MACT,CAAC,CACD7D,MAAM,CAACH,KAAK,eAAI3E,IAAA,CAACF,YAAY,EAAA4H,QAAA,CAAE5C,MAAM,CAACH,KAAK,CAAe,CAAC,EACnD,CAAC,cAEZzE,KAAA,CAACN,SAAS,EAAA8H,QAAA,eACR1H,IAAA,CAACH,KAAK,EAAC0I,OAAO,CAAC,MAAM,CAAAb,QAAA,CAAC,QAAM,CAAO,CAAC,cACpCxH,KAAA,CAACP,MAAM,EACL8I,EAAE,CAAC,MAAM,CACT/C,IAAI,CAAC,MAAM,CACXC,KAAK,CAAErB,QAAQ,CAAC5B,IAAK,CACrBgG,QAAQ,CAAElD,iBAAkB,CAC5BmD,QAAQ,MAAAjB,QAAA,eAER1H,IAAA,WAAQ2F,KAAK,CAAC,OAAO,CAAA+B,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpC1H,IAAA,WAAQ2F,KAAK,CAAC,YAAY,CAAA+B,QAAA,CAAC,YAAU,CAAQ,CAAC,cAC9C1H,IAAA,WAAQ2F,KAAK,CAAC,OAAO,CAAA+B,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC9B,CAAC,EACA,CAAC,CAEX,CAACxD,WAAW,eACXhE,KAAA,CAAAE,SAAA,EAAAsH,QAAA,eACExH,KAAA,CAACN,SAAS,EAAA8H,QAAA,eACR1H,IAAA,CAACH,KAAK,EAAC0I,OAAO,CAAC,UAAU,CAAAb,QAAA,CAAC,YAAU,CAAO,CAAC,cAC5C1H,IAAA,CAACN,KAAK,EACJ8I,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,UAAU,CACb/C,IAAI,CAAC,UAAU,CACfC,KAAK,CAAErB,QAAQ,CAACM,QAAS,CACzB8D,QAAQ,CAAElD,iBAAkB,CAC5BmD,QAAQ,MACT,CAAC,CACD7D,MAAM,CAACF,QAAQ,eAAI5E,IAAA,CAACF,YAAY,EAAA4H,QAAA,CAAE5C,MAAM,CAACF,QAAQ,CAAe,CAAC,EACzD,CAAC,cAEZ1E,KAAA,CAACN,SAAS,EAAA8H,QAAA,eACR1H,IAAA,CAACH,KAAK,EAAC0I,OAAO,CAAC,iBAAiB,CAAAb,QAAA,CAAC,oBAAkB,CAAO,CAAC,cAC3D1H,IAAA,CAACN,KAAK,EACJ8I,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,iBAAiB,CACpB/C,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAErB,QAAQ,CAACO,eAAgB,CAChC6D,QAAQ,CAAElD,iBAAkB,CAC5BmD,QAAQ,MACT,CAAC,CACD7D,MAAM,CAACD,eAAe,eAAI7E,IAAA,CAACF,YAAY,EAAA4H,QAAA,CAAE5C,MAAM,CAACD,eAAe,CAAe,CAAC,EACvE,CAAC,EACZ,CACH,cAED3E,KAAA,QAAKyH,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEK,GAAG,CAAE,MAAM,CAAEW,cAAc,CAAE,UAAU,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAnB,QAAA,eAC1F1H,IAAA,CAACP,MAAM,EAAC+I,IAAI,CAAC,QAAQ,CAACV,OAAO,CAAC,SAAS,CAACC,OAAO,CAAEb,gBAAiB,CAAAQ,QAAA,CAAC,QAEnE,CAAQ,CAAC,cACT1H,IAAA,CAACP,MAAM,EAAC+I,IAAI,CAAC,QAAQ,CAACM,QAAQ,CAAE1E,OAAQ,CAAAsD,QAAA,CACrCtD,OAAO,CAAG,WAAW,CAAIF,WAAW,CAAG,aAAa,CAAG,aAAc,CAChE,CAAC,EACN,CAAC,EACF,CAAC,EACK,CAAC,CACV,CAAC,EACC,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}