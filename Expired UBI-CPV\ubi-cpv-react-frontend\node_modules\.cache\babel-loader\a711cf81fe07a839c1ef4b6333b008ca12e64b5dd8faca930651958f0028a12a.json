{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2;import React from'react';import{Navigate}from'react-router-dom';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{LoadingSpinner}from'../../styles/GlobalStyles';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const LoadingContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  flex-direction: column;\\n  gap: 16px;\\n\"])));const LoadingText=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  font-size: 16px;\\n\"])),props=>props.theme.colors.textMedium);const ProtectedRoute=_ref=>{let{children,allowedRoles}=_ref;const{isAuthenticated,user,isLoading}=useAuth();if(isLoading){return/*#__PURE__*/_jsxs(LoadingContainer,{children:[/*#__PURE__*/_jsx(LoadingSpinner,{}),/*#__PURE__*/_jsx(LoadingText,{children:\"Loading...\"})]});}if(!isAuthenticated||!user){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}if(!allowedRoles.includes(user.role)){// Redirect to appropriate dashboard based on user role\nswitch(user.role){case'Agent':return/*#__PURE__*/_jsx(Navigate,{to:\"/agent/dashboard\",replace:true});case'Supervisor':return/*#__PURE__*/_jsx(Navigate,{to:\"/supervisor/dashboard\",replace:true});case'Admin':return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true});default:return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}}return/*#__PURE__*/_jsx(_Fragment,{children:children});};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "styled", "useAuth", "LoadingSpinner", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "LoadingContainer", "div", "_templateObject", "_taggedTemplateLiteral", "LoadingText", "_templateObject2", "props", "theme", "colors", "textMedium", "ProtectedRoute", "_ref", "children", "allowedRoles", "isAuthenticated", "user", "isLoading", "to", "replace", "includes", "role"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Auth/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { LoadingSpinner } from '../../styles/GlobalStyles';\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  flex-direction: column;\n  gap: 16px;\n`;\n\nconst LoadingText = styled.div`\n  color: ${props => props.theme.colors.textMedium};\n  font-size: 16px;\n`;\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  allowedRoles: string[];\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, allowedRoles }) => {\n  const { isAuthenticated, user, isLoading } = useAuth();\n\n  if (isLoading) {\n    return (\n      <LoadingContainer>\n        <LoadingSpinner />\n        <LoadingText>Loading...</LoadingText>\n      </LoadingContainer>\n    );\n  }\n\n  if (!isAuthenticated || !user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (!allowedRoles.includes(user.role)) {\n    // Redirect to appropriate dashboard based on user role\n    switch (user.role) {\n      case 'Agent':\n        return <Navigate to=\"/agent/dashboard\" replace />;\n      case 'Supervisor':\n        return <Navigate to=\"/supervisor/dashboard\" replace />;\n      case 'Admin':\n        return <Navigate to=\"/admin/dashboard\" replace />;\n      default:\n        return <Navigate to=\"/login\" replace />;\n    }\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "qNAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,cAAc,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE3D,KAAM,CAAAC,gBAAgB,CAAGT,MAAM,CAACU,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,+IAOlC,CAED,KAAM,CAAAC,WAAW,CAAGb,MAAM,CAACU,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,6CACnBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAEhD,CAOD,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,QAAQ,CAAEC,YAAa,CAAC,CAAAF,IAAA,CAC/E,KAAM,CAAEG,eAAe,CAAEC,IAAI,CAAEC,SAAU,CAAC,CAAGxB,OAAO,CAAC,CAAC,CAEtD,GAAIwB,SAAS,CAAE,CACb,mBACEnB,KAAA,CAACG,gBAAgB,EAAAY,QAAA,eACfjB,IAAA,CAACF,cAAc,GAAE,CAAC,cAClBE,IAAA,CAACS,WAAW,EAAAQ,QAAA,CAAC,YAAU,CAAa,CAAC,EACrB,CAAC,CAEvB,CAEA,GAAI,CAACE,eAAe,EAAI,CAACC,IAAI,CAAE,CAC7B,mBAAOpB,IAAA,CAACL,QAAQ,EAAC2B,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA,GAAI,CAACL,YAAY,CAACM,QAAQ,CAACJ,IAAI,CAACK,IAAI,CAAC,CAAE,CACrC;AACA,OAAQL,IAAI,CAACK,IAAI,EACf,IAAK,OAAO,CACV,mBAAOzB,IAAA,CAACL,QAAQ,EAAC2B,EAAE,CAAC,kBAAkB,CAACC,OAAO,MAAE,CAAC,CACnD,IAAK,YAAY,CACf,mBAAOvB,IAAA,CAACL,QAAQ,EAAC2B,EAAE,CAAC,uBAAuB,CAACC,OAAO,MAAE,CAAC,CACxD,IAAK,OAAO,CACV,mBAAOvB,IAAA,CAACL,QAAQ,EAAC2B,EAAE,CAAC,kBAAkB,CAACC,OAAO,MAAE,CAAC,CACnD,QACE,mBAAOvB,IAAA,CAACL,QAAQ,EAAC2B,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CAC3C,CACF,CAEA,mBAAOvB,IAAA,CAAAI,SAAA,EAAAa,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}