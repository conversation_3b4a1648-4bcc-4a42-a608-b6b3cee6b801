{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:59358/api';\n\n// Types matching the API DTOs\n\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.baseURL = void 0;\n    this.baseURL = API_BASE_URL;\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(config => {\n      const token = localStorage.getItem('authToken');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor for error handling and token refresh\n    this.api.interceptors.response.use(response => response, async error => {\n      var _error$response;\n      const originalRequest = error.config;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        const refreshToken = localStorage.getItem('refreshToken');\n        const token = localStorage.getItem('authToken');\n        if (refreshToken && token) {\n          try {\n            const response = await axios.post(`${this.baseURL}/auth/refresh-token`, {\n              token,\n              refreshToken\n            });\n            if (response.data.success) {\n              localStorage.setItem('authToken', response.data.token);\n              localStorage.setItem('refreshToken', response.data.refreshToken);\n              localStorage.setItem('user', JSON.stringify(response.data.user));\n\n              // Retry original request with new token\n              originalRequest.headers.Authorization = `Bearer ${response.data.token}`;\n              return this.api(originalRequest);\n            }\n          } catch (refreshError) {\n            console.error('Token refresh failed:', refreshError);\n          }\n        }\n\n        // If refresh fails, logout user\n        this.logoutUser();\n      }\n      return Promise.reject(error);\n    });\n  }\n  logoutUser() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n\n  // Authentication\n  async login(username, password, role) {\n    try {\n      const response = await this.api.post('/auth/login', {\n        username,\n        password,\n        role\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Login API error:', error);\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed');\n    }\n  }\n  async logout() {\n    try {\n      await this.api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout API error:', error);\n    } finally {\n      this.logoutUser();\n    }\n  }\n  async getCurrentUser() {\n    try {\n      const response = await this.api.get('/auth/me');\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Get current user API error:', error);\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to get user information');\n    }\n  }\n\n  // Leads\n  async getLeads(pageNumber = 1, pageSize = 10, status, assignedTo) {\n    try {\n      const params = new URLSearchParams({\n        pageNumber: pageNumber.toString(),\n        pageSize: pageSize.toString()\n      });\n      if (status) params.append('status', status);\n      if (assignedTo) params.append('assignedTo', assignedTo.toString());\n      const response = await this.api.get(`/leads?${params.toString()}`);\n      const data = response.data;\n\n      // Ensure totalCount is available for backward compatibility\n      return {\n        ...data,\n        totalCount: data.totalRecords || data.totalCount || 0\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('Get leads API error:', error);\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to fetch leads');\n    }\n  }\n  async getLead(id) {\n    try {\n      const response = await this.api.get(`/leads/${id}`);\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('Get lead API error:', error);\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to fetch lead');\n    }\n  }\n  async createLead(leadData) {\n    try {\n      const response = await this.api.post('/leads', leadData);\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      console.error('Create lead API error:', error);\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'Failed to create lead');\n    }\n  }\n  async updateLeadStatus(id, status, comments, rejectionReason) {\n    try {\n      await this.api.put(`/leads/${id}/status`, {\n        status,\n        comments,\n        rejectionReason\n      });\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      console.error('Update lead status API error:', error);\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Failed to update lead status');\n    }\n  }\n  async assignLead(id, agentId, comments) {\n    try {\n      await this.api.put(`/leads/${id}/assign`, {\n        agentId,\n        comments\n      });\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      console.error('Assign lead API error:', error);\n      throw new Error(((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message) || 'Failed to assign lead');\n    }\n  }\n  async getDashboardStats() {\n    try {\n      const response = await this.api.get('/leads/dashboard-stats');\n      return response.data;\n    } catch (error) {\n      var _error$response9, _error$response9$data;\n      console.error('Get dashboard stats API error:', error);\n      throw new Error(((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.message) || 'Failed to fetch dashboard statistics');\n    }\n  }\n\n  // Verification\n  async getAgentDashboardStats() {\n    try {\n      const response = await this.api.get('/verification/agent/dashboard-stats');\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response0$data;\n      console.error('Get agent dashboard stats API error:', error);\n      throw new Error(((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.message) || 'Failed to fetch agent dashboard statistics');\n    }\n  }\n  async getSupervisorDashboardStats() {\n    try {\n      const response = await this.api.get('/verification/supervisor/dashboard-stats');\n      return response.data;\n    } catch (error) {\n      var _error$response1, _error$response1$data;\n      console.error('Get supervisor dashboard stats API error:', error);\n      throw new Error(((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : (_error$response1$data = _error$response1.data) === null || _error$response1$data === void 0 ? void 0 : _error$response1$data.message) || 'Failed to fetch supervisor dashboard statistics');\n    }\n  }\n  async saveVerificationData(leadId, verificationData) {\n    try {\n      const response = await this.api.post(`/verification/leads/${leadId}`, verificationData);\n      return response.data;\n    } catch (error) {\n      var _error$response10, _error$response10$dat;\n      console.error('Save verification data API error:', error);\n      throw new Error(((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.message) || 'Failed to save verification data');\n    }\n  }\n  async updateVerificationData(leadId, verificationData) {\n    try {\n      const response = await this.api.put(`/verification/leads/${leadId}`, verificationData);\n      return response.data;\n    } catch (error) {\n      var _error$response11, _error$response11$dat;\n      console.error('Update verification data API error:', error);\n      throw new Error(((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : (_error$response11$dat = _error$response11.data) === null || _error$response11$dat === void 0 ? void 0 : _error$response11$dat.message) || 'Failed to update verification data');\n    }\n  }\n  async getVerificationData(leadId) {\n    try {\n      const response = await this.api.get(`/verification/leads/${leadId}`);\n      return response.data;\n    } catch (error) {\n      var _error$response12, _error$response12$dat;\n      console.error('Get verification data API error:', error);\n      throw new Error(((_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : (_error$response12$dat = _error$response12.data) === null || _error$response12$dat === void 0 ? void 0 : _error$response12$dat.message) || 'Failed to fetch verification data');\n    }\n  }\n\n  // Documents\n  async getDocumentTypes() {\n    try {\n      const response = await this.api.get('/documents/types');\n      return response.data;\n    } catch (error) {\n      var _error$response13, _error$response13$dat;\n      console.error('Get document types API error:', error);\n      throw new Error(((_error$response13 = error.response) === null || _error$response13 === void 0 ? void 0 : (_error$response13$dat = _error$response13.data) === null || _error$response13$dat === void 0 ? void 0 : _error$response13$dat.message) || 'Failed to fetch document types');\n    }\n  }\n  async uploadDocument(leadId, documentType, file) {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('documentTypeId', '1'); // Default document type ID\n      formData.append('documentCategory', documentType);\n      const response = await this.api.post(`/documents/leads/${leadId}/verification-documents`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response14, _error$response14$dat;\n      console.error('Upload document API error:', error);\n      throw new Error(((_error$response14 = error.response) === null || _error$response14 === void 0 ? void 0 : (_error$response14$dat = _error$response14.data) === null || _error$response14$dat === void 0 ? void 0 : _error$response14$dat.message) || 'Failed to upload document');\n    }\n  }\n  async getVerificationDocuments(leadId) {\n    try {\n      const response = await this.api.get(`/documents/leads/${leadId}/verification-documents`);\n      return response.data;\n    } catch (error) {\n      var _error$response15, _error$response15$dat;\n      console.error('Get verification documents API error:', error);\n      throw new Error(((_error$response15 = error.response) === null || _error$response15 === void 0 ? void 0 : (_error$response15$dat = _error$response15.data) === null || _error$response15$dat === void 0 ? void 0 : _error$response15$dat.message) || 'Failed to fetch verification documents');\n    }\n  }\n\n  // Users (for Admin)\n  async getUsers() {\n    try {\n      const response = await this.api.get('/users');\n      return response.data;\n    } catch (error) {\n      console.error('Get users API error:', error);\n      // Return mock data for now since UsersController doesn't exist yet\n      return [{\n        userId: 1,\n        username: 'agent1',\n        firstName: 'John',\n        lastName: 'Agent',\n        email: '<EMAIL>',\n        role: 'Agent',\n        isActive: true,\n        createdDate: '2024-01-01T00:00:00Z',\n        lastLoginDate: '2024-01-16T08:30:00Z'\n      }, {\n        userId: 2,\n        username: 'supervisor1',\n        firstName: 'Jane',\n        lastName: 'Supervisor',\n        email: '<EMAIL>',\n        role: 'Supervisor',\n        isActive: true,\n        createdDate: '2024-01-01T00:00:00Z',\n        lastLoginDate: '2024-01-16T09:15:00Z'\n      }, {\n        userId: 3,\n        username: 'admin1',\n        firstName: 'Admin',\n        lastName: 'User',\n        email: '<EMAIL>',\n        role: 'Admin',\n        isActive: true,\n        createdDate: '2024-01-01T00:00:00Z',\n        lastLoginDate: '2024-01-16T10:00:00Z'\n      }];\n    }\n  }\n  async createUser(userData) {\n    try {\n      const response = await this.api.post('/users', userData);\n      return response.data;\n    } catch (error) {\n      var _error$response16, _error$response16$dat;\n      console.error('Create user API error:', error);\n      throw new Error(((_error$response16 = error.response) === null || _error$response16 === void 0 ? void 0 : (_error$response16$dat = _error$response16.data) === null || _error$response16$dat === void 0 ? void 0 : _error$response16$dat.message) || 'Failed to create user');\n    }\n  }\n  async updateUser(userId, userData) {\n    try {\n      const response = await this.api.put(`/users/${userId}`, userData);\n      return response.data;\n    } catch (error) {\n      var _error$response17, _error$response17$dat;\n      console.error('Update user API error:', error);\n      throw new Error(((_error$response17 = error.response) === null || _error$response17 === void 0 ? void 0 : (_error$response17$dat = _error$response17.data) === null || _error$response17$dat === void 0 ? void 0 : _error$response17$dat.message) || 'Failed to update user');\n    }\n  }\n  async toggleUserStatus(userId) {\n    try {\n      await this.api.put(`/users/${userId}/toggle-status`);\n    } catch (error) {\n      var _error$response18, _error$response18$dat;\n      console.error('Toggle user status API error:', error);\n      throw new Error(((_error$response18 = error.response) === null || _error$response18 === void 0 ? void 0 : (_error$response18$dat = _error$response18.data) === null || _error$response18$dat === void 0 ? void 0 : _error$response18$dat.message) || 'Failed to toggle user status');\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "api", "baseURL", "create", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "data", "success", "setItem", "JSON", "stringify", "user", "refreshError", "console", "logoutUser", "removeItem", "window", "location", "href", "login", "username", "password", "role", "_error$response2", "_error$response2$data", "Error", "message", "logout", "getCurrentUser", "get", "_error$response3", "_error$response3$data", "getLeads", "pageNumber", "pageSize", "assignedTo", "params", "URLSearchParams", "toString", "append", "totalCount", "totalRecords", "_error$response4", "_error$response4$data", "getLead", "id", "_error$response5", "_error$response5$data", "createLead", "leadData", "_error$response6", "_error$response6$data", "updateLeadStatus", "comments", "rejectionReason", "put", "_error$response7", "_error$response7$data", "assignLead", "agentId", "_error$response8", "_error$response8$data", "getDashboardStats", "_error$response9", "_error$response9$data", "getAgentDashboardStats", "_error$response0", "_error$response0$data", "getSupervisorDashboardStats", "_error$response1", "_error$response1$data", "saveVerificationData", "leadId", "verificationData", "_error$response10", "_error$response10$dat", "updateVerificationData", "_error$response11", "_error$response11$dat", "getVerificationData", "_error$response12", "_error$response12$dat", "getDocumentTypes", "_error$response13", "_error$response13$dat", "uploadDocument", "documentType", "file", "formData", "FormData", "_error$response14", "_error$response14$dat", "getVerificationDocuments", "_error$response15", "_error$response15$dat", "getUsers", "userId", "firstName", "lastName", "email", "isActive", "createdDate", "lastLoginDate", "createUser", "userData", "_error$response16", "_error$response16$dat", "updateUser", "_error$response17", "_error$response17$dat", "toggleUserStatus", "_error$response18", "_error$response18$dat", "apiService"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/services/apiService.ts"], "sourcesContent": ["import axios, { AxiosInstance } from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:59358/api';\n\n// Types matching the API DTOs\nexport interface User {\n  userId: number;\n  username: string;\n  email: string;\n  role: 'Agent' | 'Supervisor' | 'Admin';\n  firstName: string;\n  lastName: string;\n  phoneNumber?: string;\n  isActive: boolean;\n  createdDate: string;\n  lastLoginDate?: string;\n}\n\nexport interface LoginResponse {\n  success: boolean;\n  message: string;\n  token?: string;\n  refreshToken?: string;\n  user?: User;\n  expiresAt?: string;\n}\n\nexport interface Lead {\n  leadId: number;\n  customerName: string;\n  mobileNumber: string;\n  loanType: string;\n  status: string;\n  createdDate: string;\n  assignedDate?: string;\n  startedDate?: string;\n  submittedDate?: string;\n  reviewedDate?: string;\n  approvedDate?: string;\n  rejectedDate?: string;\n  rejectionReason?: string;\n  reviewComments?: string;\n  creator?: User;\n  assignedAgent?: User;\n  reviewer?: User;\n  addresses: Address[];\n  statusHistory: StatusHistory[];\n  documents: Document[];\n  croppedImages: CroppedImage[];\n  verificationData?: VerificationData;\n}\n\nexport interface LeadListItem {\n  leadId: number;\n  customerName: string;\n  mobileNumber: string;\n  loanType: string;\n  status: string;\n  createdDate: string;\n  assignedDate?: string;\n  submittedDate?: string;\n  createdByName?: string;\n  assignedToName?: string;\n  reviewedByName?: string;\n  documentCount: number;\n  croppedImageCount: number;\n}\n\nexport interface PagedResult<T> {\n  data: T[];\n  totalRecords: number;\n  totalCount: number; // Alias for totalRecords for backward compatibility\n  pageNumber: number;\n  pageSize: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nexport interface Address {\n  addressId: number;\n  addressType: string;\n  address: string;\n  pincode: string;\n  state: string;\n  district: string;\n  landmark?: string;\n  createdDate: string;\n}\n\nexport interface StatusHistory {\n  historyId: number;\n  status: string;\n  timestamp: string;\n  comments?: string;\n  updatedByUser?: User;\n}\n\nexport interface Document {\n  documentId: number;\n  leadId: number;\n  documentTypeName: string;\n  fileName: string;\n  originalFileName: string;\n  filePath: string;\n  fileSize: number;\n  mimeType: string;\n  uploadedDate: string;\n  uploadedByName?: string;\n  isActive: boolean;\n}\n\nexport interface CroppedImage {\n  imageId: number;\n  leadId: number;\n  fileName: string;\n  originalFileName: string;\n  filePath: string;\n  fileSize: number;\n  mimeType: string;\n  cropData?: string;\n  pageNumber?: number;\n  createdDate: string;\n  createdByName?: string;\n}\n\nexport interface VerificationData {\n  verificationId?: number;\n  leadId?: number;\n  agentName: string;\n  agentContact: string;\n  addressConfirmed?: string;\n  personMet?: string;\n  relationship?: string;\n  officeAddress?: string;\n  officeState?: string;\n  officeDistrict?: string;\n  officePincode?: string;\n  landmark?: string;\n  companyType?: string;\n  businessNature?: string;\n  establishmentYear?: number;\n  employeesCount?: string;\n  grossSalary?: number;\n  netSalary?: number;\n  proofType?: string;\n  verificationDate?: string;\n  additionalNotes?: string;\n}\n\nexport interface DashboardStats {\n  totalLeads: number;\n  newLeads: number;\n  assignedLeads: number;\n  inProgressLeads: number;\n  pendingReviewLeads: number;\n  approvedLeads: number;\n  rejectedLeads: number;\n  completedLeads: number;\n  pendingLeads: number;\n}\n\nexport interface AgentDashboardStats {\n  pendingLeads: number;\n  inProgressLeads: number;\n  completedLeads: number;\n  rejectedLeads: number;\n  totalAssigned: number;\n}\n\nexport interface SupervisorDashboardStats {\n  pendingReviews: number;\n  approvedToday: number;\n  rejectedToday: number;\n  totalReviewed: number;\n  approvalRate: number;\n  agentPerformance: AgentPerformance[];\n  totalLeads: number;\n  completedLeads: number;\n}\n\nexport interface AgentPerformance {\n  agentId: number;\n  agentName: string;\n  assignedCount: number;\n  completedCount: number;\n  approvedCount: number;\n  rejectedCount: number;\n  completionRate: number;\n  approvalRate: number;\n}\n\nexport interface CreateLeadRequest {\n  customerName: string;\n  mobileNumber: string;\n  loanType: string;\n  addresses: {\n    type: string;\n    address: string;\n    pincode: string;\n    state: string;\n    district: string;\n    landmark?: string;\n  }[];\n}\n\nexport interface DocumentType {\n  documentTypeId: number;\n  typeName: string;\n  description?: string;\n  isActive: boolean;\n}\n\nexport interface FileUploadResult {\n  success: boolean;\n  message: string;\n  fileName?: string;\n  filePath?: string;\n  fileSize: number;\n  documentId?: number;\n  imageId?: number;\n}\n\nexport interface UploadProgress {\n  loaded: number;\n  total: number;\n  percentage: number;\n}\n\nexport interface DocumentUpload {\n  file: File;\n  documentType: string;\n  documentTypeId: number;\n  category: string;\n  description?: string;\n  isRequired: boolean;\n  uploadProgress?: UploadProgress;\n  uploadResult?: FileUploadResult;\n  error?: string;\n  status: 'pending' | 'uploading' | 'completed' | 'error';\n}\n\nexport interface CreateLeadWithDocumentsRequest extends CreateLeadRequest {\n  documents?: DocumentUpload[];\n}\n\nclass ApiService {\n  private api: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = API_BASE_URL;\n\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('authToken');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor for error handling and token refresh\n    this.api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          const refreshToken = localStorage.getItem('refreshToken');\n          const token = localStorage.getItem('authToken');\n\n          if (refreshToken && token) {\n            try {\n              const response = await axios.post(`${this.baseURL}/auth/refresh-token`, {\n                token,\n                refreshToken\n              });\n\n              if (response.data.success) {\n                localStorage.setItem('authToken', response.data.token);\n                localStorage.setItem('refreshToken', response.data.refreshToken);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n\n                // Retry original request with new token\n                originalRequest.headers.Authorization = `Bearer ${response.data.token}`;\n                return this.api(originalRequest);\n              }\n            } catch (refreshError) {\n              console.error('Token refresh failed:', refreshError);\n            }\n          }\n\n          // If refresh fails, logout user\n          this.logoutUser();\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private logoutUser() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n\n  // Authentication\n  async login(username: string, password: string, role: string): Promise<LoginResponse> {\n    try {\n      const response = await this.api.post('/auth/login', {\n        username,\n        password,\n        role,\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Login API error:', error);\n      throw new Error(error.response?.data?.message || 'Login failed');\n    }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout API error:', error);\n    } finally {\n      this.logoutUser();\n    }\n  }\n\n  async getCurrentUser(): Promise<User> {\n    try {\n      const response = await this.api.get('/auth/me');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get current user API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to get user information');\n    }\n  }\n\n  // Leads\n  async getLeads(pageNumber = 1, pageSize = 10, status?: string, assignedTo?: number): Promise<PagedResult<LeadListItem>> {\n    try {\n      const params = new URLSearchParams({\n        pageNumber: pageNumber.toString(),\n        pageSize: pageSize.toString(),\n      });\n\n      if (status) params.append('status', status);\n      if (assignedTo) params.append('assignedTo', assignedTo.toString());\n\n      const response = await this.api.get(`/leads?${params.toString()}`);\n      const data = response.data;\n\n      // Ensure totalCount is available for backward compatibility\n      return {\n        ...data,\n        totalCount: data.totalRecords || data.totalCount || 0\n      };\n    } catch (error: any) {\n      console.error('Get leads API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch leads');\n    }\n  }\n\n  async getLead(id: number): Promise<Lead> {\n    try {\n      const response = await this.api.get(`/leads/${id}`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Get lead API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch lead');\n    }\n  }\n\n  async createLead(leadData: CreateLeadRequest): Promise<Lead> {\n    try {\n      const response = await this.api.post('/leads', leadData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Create lead API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to create lead');\n    }\n  }\n\n  async updateLeadStatus(id: number, status: string, comments?: string, rejectionReason?: string): Promise<void> {\n    try {\n      await this.api.put(`/leads/${id}/status`, {\n        status,\n        comments,\n        rejectionReason,\n      });\n    } catch (error: any) {\n      console.error('Update lead status API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to update lead status');\n    }\n  }\n\n  async assignLead(id: number, agentId: number, comments?: string): Promise<void> {\n    try {\n      await this.api.put(`/leads/${id}/assign`, {\n        agentId,\n        comments,\n      });\n    } catch (error: any) {\n      console.error('Assign lead API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to assign lead');\n    }\n  }\n\n  async getDashboardStats(): Promise<DashboardStats> {\n    try {\n      const response = await this.api.get('/leads/dashboard-stats');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get dashboard stats API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard statistics');\n    }\n  }\n\n  // Verification\n  async getAgentDashboardStats(): Promise<AgentDashboardStats> {\n    try {\n      const response = await this.api.get('/verification/agent/dashboard-stats');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get agent dashboard stats API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch agent dashboard statistics');\n    }\n  }\n\n  async getSupervisorDashboardStats(): Promise<SupervisorDashboardStats> {\n    try {\n      const response = await this.api.get('/verification/supervisor/dashboard-stats');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get supervisor dashboard stats API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch supervisor dashboard statistics');\n    }\n  }\n\n  async saveVerificationData(leadId: number, verificationData: VerificationData): Promise<VerificationData> {\n    try {\n      const response = await this.api.post(`/verification/leads/${leadId}`, verificationData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Save verification data API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to save verification data');\n    }\n  }\n\n  async updateVerificationData(leadId: number, verificationData: VerificationData): Promise<VerificationData> {\n    try {\n      const response = await this.api.put(`/verification/leads/${leadId}`, verificationData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Update verification data API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to update verification data');\n    }\n  }\n\n  async getVerificationData(leadId: number): Promise<VerificationData> {\n    try {\n      const response = await this.api.get(`/verification/leads/${leadId}`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Get verification data API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch verification data');\n    }\n  }\n\n  // Documents\n  async getDocumentTypes(): Promise<DocumentType[]> {\n    try {\n      const response = await this.api.get('/documents/types');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get document types API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch document types');\n    }\n  }\n\n  async uploadDocument(leadId: number, documentType: string, file: File): Promise<FileUploadResult> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('documentTypeId', '1'); // Default document type ID\n      formData.append('documentCategory', documentType);\n\n      const response = await this.api.post(`/documents/leads/${leadId}/verification-documents`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Upload document API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to upload document');\n    }\n  }\n\n  async getVerificationDocuments(leadId: number): Promise<Document[]> {\n    try {\n      const response = await this.api.get(`/documents/leads/${leadId}/verification-documents`);\n      return response.data;\n    } catch (error: any) {\n      console.error('Get verification documents API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to fetch verification documents');\n    }\n  }\n\n  // Users (for Admin)\n  async getUsers(): Promise<User[]> {\n    try {\n      const response = await this.api.get('/users');\n      return response.data;\n    } catch (error: any) {\n      console.error('Get users API error:', error);\n      // Return mock data for now since UsersController doesn't exist yet\n      return [\n        {\n          userId: 1,\n          username: 'agent1',\n          firstName: 'John',\n          lastName: 'Agent',\n          email: '<EMAIL>',\n          role: 'Agent',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T08:30:00Z',\n        },\n        {\n          userId: 2,\n          username: 'supervisor1',\n          firstName: 'Jane',\n          lastName: 'Supervisor',\n          email: '<EMAIL>',\n          role: 'Supervisor',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T09:15:00Z',\n        },\n        {\n          userId: 3,\n          username: 'admin1',\n          firstName: 'Admin',\n          lastName: 'User',\n          email: '<EMAIL>',\n          role: 'Admin',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T10:00:00Z',\n        },\n      ];\n    }\n  }\n\n  async createUser(userData: any): Promise<User> {\n    try {\n      const response = await this.api.post('/users', userData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Create user API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to create user');\n    }\n  }\n\n  async updateUser(userId: number, userData: any): Promise<User> {\n    try {\n      const response = await this.api.put(`/users/${userId}`, userData);\n      return response.data;\n    } catch (error: any) {\n      console.error('Update user API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to update user');\n    }\n  }\n\n  async toggleUserStatus(userId: number): Promise<void> {\n    try {\n      await this.api.put(`/users/${userId}/toggle-status`);\n    } catch (error: any) {\n      console.error('Toggle user status API error:', error);\n      throw new Error(error.response?.data?.message || 'Failed to toggle user status');\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;AAE5C,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,6BAA6B;;AAEnF;;AAkPA,MAAMC,UAAU,CAAC;EAIfC,WAAWA,CAAA,EAAG;IAAA,KAHNC,GAAG;IAAA,KACHC,OAAO;IAGb,IAAI,CAACA,OAAO,GAAGP,YAAY;IAE3B,IAAI,CAACM,GAAG,GAAGP,KAAK,CAACS,MAAM,CAAC;MACtBD,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBE,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC/BS,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACf,MAAMC,eAAe,GAAGL,KAAK,CAACL,MAAM;MAEpC,IAAI,EAAAS,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;QAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;QAE7B,MAAMC,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QACzD,MAAMF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAE/C,IAAIU,YAAY,IAAIZ,KAAK,EAAE;UACzB,IAAI;YACF,MAAMO,QAAQ,GAAG,MAAMvB,KAAK,CAAC6B,IAAI,CAAC,GAAG,IAAI,CAACrB,OAAO,qBAAqB,EAAE;cACtEQ,KAAK;cACLY;YACF,CAAC,CAAC;YAEF,IAAIL,QAAQ,CAACO,IAAI,CAACC,OAAO,EAAE;cACzBd,YAAY,CAACe,OAAO,CAAC,WAAW,EAAET,QAAQ,CAACO,IAAI,CAACd,KAAK,CAAC;cACtDC,YAAY,CAACe,OAAO,CAAC,cAAc,EAAET,QAAQ,CAACO,IAAI,CAACF,YAAY,CAAC;cAChEX,YAAY,CAACe,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACX,QAAQ,CAACO,IAAI,CAACK,IAAI,CAAC,CAAC;;cAEhE;cACAV,eAAe,CAACd,OAAO,CAACQ,aAAa,GAAG,UAAUI,QAAQ,CAACO,IAAI,CAACd,KAAK,EAAE;cACvE,OAAO,IAAI,CAACT,GAAG,CAACkB,eAAe,CAAC;YAClC;UACF,CAAC,CAAC,OAAOW,YAAY,EAAE;YACrBC,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEgB,YAAY,CAAC;UACtD;QACF;;QAEA;QACA,IAAI,CAACE,UAAU,CAAC,CAAC;MACnB;MAEA,OAAOjB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEQkB,UAAUA,CAAA,EAAG;IACnBrB,YAAY,CAACsB,UAAU,CAAC,WAAW,CAAC;IACpCtB,YAAY,CAACsB,UAAU,CAAC,cAAc,CAAC;IACvCtB,YAAY,CAACsB,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;;EAEA;EACA,MAAMC,KAAKA,CAACC,QAAgB,EAAEC,QAAgB,EAAEC,IAAY,EAA0B;IACpF,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAACsB,IAAI,CAAC,aAAa,EAAE;QAClDe,QAAQ;QACRC,QAAQ;QACRC;MACF,CAAC,CAAC;MACF,OAAOvB,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA;MACnBX,OAAO,CAACjB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,MAAM,IAAI6B,KAAK,CAAC,EAAAF,gBAAA,GAAA3B,KAAK,CAACG,QAAQ,cAAAwB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,cAAc,CAAC;IAClE;EACF;EAEA,MAAMC,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAM,IAAI,CAAC5C,GAAG,CAACsB,IAAI,CAAC,cAAc,CAAC;IACrC,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C,CAAC,SAAS;MACR,IAAI,CAACkB,UAAU,CAAC,CAAC;IACnB;EACF;EAEA,MAAMc,cAAcA,CAAA,EAAkB;IACpC,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,UAAU,CAAC;MAC/C,OAAO9B,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACnBlB,OAAO,CAACjB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAM,IAAI6B,KAAK,CAAC,EAAAK,gBAAA,GAAAlC,KAAK,CAACG,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBL,OAAO,KAAI,gCAAgC,CAAC;IACpF;EACF;;EAEA;EACA,MAAMM,QAAQA,CAACC,UAAU,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAEhC,MAAe,EAAEiC,UAAmB,EAAsC;IACtH,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCJ,UAAU,EAAEA,UAAU,CAACK,QAAQ,CAAC,CAAC;QACjCJ,QAAQ,EAAEA,QAAQ,CAACI,QAAQ,CAAC;MAC9B,CAAC,CAAC;MAEF,IAAIpC,MAAM,EAAEkC,MAAM,CAACG,MAAM,CAAC,QAAQ,EAAErC,MAAM,CAAC;MAC3C,IAAIiC,UAAU,EAAEC,MAAM,CAACG,MAAM,CAAC,YAAY,EAAEJ,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC;MAElE,MAAMvC,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,UAAUO,MAAM,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC;MAClE,MAAMhC,IAAI,GAAGP,QAAQ,CAACO,IAAI;;MAE1B;MACA,OAAO;QACL,GAAGA,IAAI;QACPkC,UAAU,EAAElC,IAAI,CAACmC,YAAY,IAAInC,IAAI,CAACkC,UAAU,IAAI;MACtD,CAAC;IACH,CAAC,CAAC,OAAO5C,KAAU,EAAE;MAAA,IAAA8C,gBAAA,EAAAC,qBAAA;MACnB9B,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAM,IAAI6B,KAAK,CAAC,EAAAiB,gBAAA,GAAA9C,KAAK,CAACG,QAAQ,cAAA2C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpC,IAAI,cAAAqC,qBAAA,uBAApBA,qBAAA,CAAsBjB,OAAO,KAAI,uBAAuB,CAAC;IAC3E;EACF;EAEA,MAAMkB,OAAOA,CAACC,EAAU,EAAiB;IACvC,IAAI;MACF,MAAM9C,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,UAAUgB,EAAE,EAAE,CAAC;MACnD,OAAO9C,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAkD,gBAAA,EAAAC,qBAAA;MACnBlC,OAAO,CAACjB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAM,IAAI6B,KAAK,CAAC,EAAAqB,gBAAA,GAAAlD,KAAK,CAACG,QAAQ,cAAA+C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBrB,OAAO,KAAI,sBAAsB,CAAC;IAC1E;EACF;EAEA,MAAMsB,UAAUA,CAACC,QAA2B,EAAiB;IAC3D,IAAI;MACF,MAAMlD,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAACsB,IAAI,CAAC,QAAQ,EAAE4C,QAAQ,CAAC;MACxD,OAAOlD,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAsD,gBAAA,EAAAC,qBAAA;MACnBtC,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAM,IAAI6B,KAAK,CAAC,EAAAyB,gBAAA,GAAAtD,KAAK,CAACG,QAAQ,cAAAmD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5C,IAAI,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,uBAAuB,CAAC;IAC3E;EACF;EAEA,MAAM0B,gBAAgBA,CAACP,EAAU,EAAE3C,MAAc,EAAEmD,QAAiB,EAAEC,eAAwB,EAAiB;IAC7G,IAAI;MACF,MAAM,IAAI,CAACvE,GAAG,CAACwE,GAAG,CAAC,UAAUV,EAAE,SAAS,EAAE;QACxC3C,MAAM;QACNmD,QAAQ;QACRC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO1D,KAAU,EAAE;MAAA,IAAA4D,gBAAA,EAAAC,qBAAA;MACnB5C,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAM,IAAI6B,KAAK,CAAC,EAAA+B,gBAAA,GAAA5D,KAAK,CAACG,QAAQ,cAAAyD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlD,IAAI,cAAAmD,qBAAA,uBAApBA,qBAAA,CAAsB/B,OAAO,KAAI,8BAA8B,CAAC;IAClF;EACF;EAEA,MAAMgC,UAAUA,CAACb,EAAU,EAAEc,OAAe,EAAEN,QAAiB,EAAiB;IAC9E,IAAI;MACF,MAAM,IAAI,CAACtE,GAAG,CAACwE,GAAG,CAAC,UAAUV,EAAE,SAAS,EAAE;QACxCc,OAAO;QACPN;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOzD,KAAU,EAAE;MAAA,IAAAgE,gBAAA,EAAAC,qBAAA;MACnBhD,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAM,IAAI6B,KAAK,CAAC,EAAAmC,gBAAA,GAAAhE,KAAK,CAACG,QAAQ,cAAA6D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtD,IAAI,cAAAuD,qBAAA,uBAApBA,qBAAA,CAAsBnC,OAAO,KAAI,uBAAuB,CAAC;IAC3E;EACF;EAEA,MAAMoC,iBAAiBA,CAAA,EAA4B;IACjD,IAAI;MACF,MAAM/D,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,wBAAwB,CAAC;MAC7D,OAAO9B,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAmE,gBAAA,EAAAC,qBAAA;MACnBnD,OAAO,CAACjB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAI6B,KAAK,CAAC,EAAAsC,gBAAA,GAAAnE,KAAK,CAACG,QAAQ,cAAAgE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzD,IAAI,cAAA0D,qBAAA,uBAApBA,qBAAA,CAAsBtC,OAAO,KAAI,sCAAsC,CAAC;IAC1F;EACF;;EAEA;EACA,MAAMuC,sBAAsBA,CAAA,EAAiC;IAC3D,IAAI;MACF,MAAMlE,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,qCAAqC,CAAC;MAC1E,OAAO9B,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAsE,gBAAA,EAAAC,qBAAA;MACnBtD,OAAO,CAACjB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAM,IAAI6B,KAAK,CAAC,EAAAyC,gBAAA,GAAAtE,KAAK,CAACG,QAAQ,cAAAmE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5D,IAAI,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsBzC,OAAO,KAAI,4CAA4C,CAAC;IAChG;EACF;EAEA,MAAM0C,2BAA2BA,CAAA,EAAsC;IACrE,IAAI;MACF,MAAMrE,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,0CAA0C,CAAC;MAC/E,OAAO9B,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAyE,gBAAA,EAAAC,qBAAA;MACnBzD,OAAO,CAACjB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAM,IAAI6B,KAAK,CAAC,EAAA4C,gBAAA,GAAAzE,KAAK,CAACG,QAAQ,cAAAsE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/D,IAAI,cAAAgE,qBAAA,uBAApBA,qBAAA,CAAsB5C,OAAO,KAAI,iDAAiD,CAAC;IACrG;EACF;EAEA,MAAM6C,oBAAoBA,CAACC,MAAc,EAAEC,gBAAkC,EAA6B;IACxG,IAAI;MACF,MAAM1E,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAACsB,IAAI,CAAC,uBAAuBmE,MAAM,EAAE,EAAEC,gBAAgB,CAAC;MACvF,OAAO1E,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAA8E,iBAAA,EAAAC,qBAAA;MACnB9D,OAAO,CAACjB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAM,IAAI6B,KAAK,CAAC,EAAAiD,iBAAA,GAAA9E,KAAK,CAACG,QAAQ,cAAA2E,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBpE,IAAI,cAAAqE,qBAAA,uBAApBA,qBAAA,CAAsBjD,OAAO,KAAI,kCAAkC,CAAC;IACtF;EACF;EAEA,MAAMkD,sBAAsBA,CAACJ,MAAc,EAAEC,gBAAkC,EAA6B;IAC1G,IAAI;MACF,MAAM1E,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAACwE,GAAG,CAAC,uBAAuBiB,MAAM,EAAE,EAAEC,gBAAgB,CAAC;MACtF,OAAO1E,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAiF,iBAAA,EAAAC,qBAAA;MACnBjE,OAAO,CAACjB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAM,IAAI6B,KAAK,CAAC,EAAAoD,iBAAA,GAAAjF,KAAK,CAACG,QAAQ,cAAA8E,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBvE,IAAI,cAAAwE,qBAAA,uBAApBA,qBAAA,CAAsBpD,OAAO,KAAI,oCAAoC,CAAC;IACxF;EACF;EAEA,MAAMqD,mBAAmBA,CAACP,MAAc,EAA6B;IACnE,IAAI;MACF,MAAMzE,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,uBAAuB2C,MAAM,EAAE,CAAC;MACpE,OAAOzE,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAoF,iBAAA,EAAAC,qBAAA;MACnBpE,OAAO,CAACjB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAM,IAAI6B,KAAK,CAAC,EAAAuD,iBAAA,GAAApF,KAAK,CAACG,QAAQ,cAAAiF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB1E,IAAI,cAAA2E,qBAAA,uBAApBA,qBAAA,CAAsBvD,OAAO,KAAI,mCAAmC,CAAC;IACvF;EACF;;EAEA;EACA,MAAMwD,gBAAgBA,CAAA,EAA4B;IAChD,IAAI;MACF,MAAMnF,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,kBAAkB,CAAC;MACvD,OAAO9B,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAuF,iBAAA,EAAAC,qBAAA;MACnBvE,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAM,IAAI6B,KAAK,CAAC,EAAA0D,iBAAA,GAAAvF,KAAK,CAACG,QAAQ,cAAAoF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB7E,IAAI,cAAA8E,qBAAA,uBAApBA,qBAAA,CAAsB1D,OAAO,KAAI,gCAAgC,CAAC;IACpF;EACF;EAEA,MAAM2D,cAAcA,CAACb,MAAc,EAAEc,YAAoB,EAAEC,IAAU,EAA6B;IAChG,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACjD,MAAM,CAAC,MAAM,EAAEgD,IAAI,CAAC;MAC7BC,QAAQ,CAACjD,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC;MACxCiD,QAAQ,CAACjD,MAAM,CAAC,kBAAkB,EAAE+C,YAAY,CAAC;MAEjD,MAAMvF,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAACsB,IAAI,CAAC,oBAAoBmE,MAAM,yBAAyB,EAAEgB,QAAQ,EAAE;QAClGrG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOY,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAA8F,iBAAA,EAAAC,qBAAA;MACnB9E,OAAO,CAACjB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAM,IAAI6B,KAAK,CAAC,EAAAiE,iBAAA,GAAA9F,KAAK,CAACG,QAAQ,cAAA2F,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBpF,IAAI,cAAAqF,qBAAA,uBAApBA,qBAAA,CAAsBjE,OAAO,KAAI,2BAA2B,CAAC;IAC/E;EACF;EAEA,MAAMkE,wBAAwBA,CAACpB,MAAc,EAAuB;IAClE,IAAI;MACF,MAAMzE,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,oBAAoB2C,MAAM,yBAAyB,CAAC;MACxF,OAAOzE,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAiG,iBAAA,EAAAC,qBAAA;MACnBjF,OAAO,CAACjB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAM,IAAI6B,KAAK,CAAC,EAAAoE,iBAAA,GAAAjG,KAAK,CAACG,QAAQ,cAAA8F,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBvF,IAAI,cAAAwF,qBAAA,uBAApBA,qBAAA,CAAsBpE,OAAO,KAAI,wCAAwC,CAAC;IAC5F;EACF;;EAEA;EACA,MAAMqE,QAAQA,CAAA,EAAoB;IAChC,IAAI;MACF,MAAMhG,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAAC8C,GAAG,CAAC,QAAQ,CAAC;MAC7C,OAAO9B,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MACnBiB,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACA,OAAO,CACL;QACEoG,MAAM,EAAE,CAAC;QACT5E,QAAQ,EAAE,QAAQ;QAClB6E,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,oBAAoB;QAC3B7E,IAAI,EAAE,OAAO;QACb8E,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC,EACD;QACEN,MAAM,EAAE,CAAC;QACT5E,QAAQ,EAAE,aAAa;QACvB6E,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,YAAY;QACtBC,KAAK,EAAE,yBAAyB;QAChC7E,IAAI,EAAE,YAAY;QAClB8E,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC,EACD;QACEN,MAAM,EAAE,CAAC;QACT5E,QAAQ,EAAE,QAAQ;QAClB6E,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,eAAe;QACtB7E,IAAI,EAAE,OAAO;QACb8E,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC,CACF;IACH;EACF;EAEA,MAAMC,UAAUA,CAACC,QAAa,EAAiB;IAC7C,IAAI;MACF,MAAMzG,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAACsB,IAAI,CAAC,QAAQ,EAAEmG,QAAQ,CAAC;MACxD,OAAOzG,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAA6G,iBAAA,EAAAC,qBAAA;MACnB7F,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAM,IAAI6B,KAAK,CAAC,EAAAgF,iBAAA,GAAA7G,KAAK,CAACG,QAAQ,cAAA0G,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBnG,IAAI,cAAAoG,qBAAA,uBAApBA,qBAAA,CAAsBhF,OAAO,KAAI,uBAAuB,CAAC;IAC3E;EACF;EAEA,MAAMiF,UAAUA,CAACX,MAAc,EAAEQ,QAAa,EAAiB;IAC7D,IAAI;MACF,MAAMzG,QAAQ,GAAG,MAAM,IAAI,CAAChB,GAAG,CAACwE,GAAG,CAAC,UAAUyC,MAAM,EAAE,EAAEQ,QAAQ,CAAC;MACjE,OAAOzG,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAgH,iBAAA,EAAAC,qBAAA;MACnBhG,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAM,IAAI6B,KAAK,CAAC,EAAAmF,iBAAA,GAAAhH,KAAK,CAACG,QAAQ,cAAA6G,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBtG,IAAI,cAAAuG,qBAAA,uBAApBA,qBAAA,CAAsBnF,OAAO,KAAI,uBAAuB,CAAC;IAC3E;EACF;EAEA,MAAMoF,gBAAgBA,CAACd,MAAc,EAAiB;IACpD,IAAI;MACF,MAAM,IAAI,CAACjH,GAAG,CAACwE,GAAG,CAAC,UAAUyC,MAAM,gBAAgB,CAAC;IACtD,CAAC,CAAC,OAAOpG,KAAU,EAAE;MAAA,IAAAmH,iBAAA,EAAAC,qBAAA;MACnBnG,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAM,IAAI6B,KAAK,CAAC,EAAAsF,iBAAA,GAAAnH,KAAK,CAACG,QAAQ,cAAAgH,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBzG,IAAI,cAAA0G,qBAAA,uBAApBA,qBAAA,CAAsBtF,OAAO,KAAI,8BAA8B,CAAC;IAClF;EACF;AACF;;AAEA;AACA,OAAO,MAAMuF,UAAU,GAAG,IAAIpI,UAAU,CAAC,CAAC;AAC1C,eAAeoI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}