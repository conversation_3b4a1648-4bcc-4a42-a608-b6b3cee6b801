{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Auth\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Button, Input, FormGroup, Label, ErrorMessage, LoadingSpinner, Card } from '../../styles/GlobalStyles';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LoginContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: ${props => props.theme.spacing.lg};\n  background: linear-gradient(135deg,\n    ${props => props.theme.colors.backgroundSecondary} 0%,\n    ${props => props.theme.colors.backgroundTertiary} 50%,\n    ${props => props.theme.colors.backgroundSecondary} 100%\n  );\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, ${props => props.theme.colors.primary}20 0%, transparent 70%);\n    animation: float 20s ease-in-out infinite;\n  }\n\n  @keyframes float {\n    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }\n    50% { transform: translate(-50%, -50%) rotate(180deg); }\n  }\n`;\n_c = LoginContainer;\nconst LoginCard = styled(Card)`\n  width: 100%;\n  max-width: 420px;\n  position: relative;\n  z-index: 1;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  animation: fadeIn 0.8s ease-out;\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n_c2 = LoginCard;\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n_c3 = Header;\nconst LogoContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  margin-bottom: 15px;\n`;\n_c4 = LogoContainer;\nconst Logo = styled.div`\n  width: 120px;\n  height: 60px;\n  position: relative;\n`;\n_c5 = Logo;\nconst LogoULeft = styled.div`\n  width: 50px;\n  height: 50px;\n  background-color: ${props => props.theme.colors.primary};\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  left: 15px;\n  transform: rotate(180deg);\n  box-shadow: ${props => props.theme.shadows.sm};\n`;\n_c6 = LogoULeft;\nconst LogoURight = styled.div`\n  width: 50px;\n  height: 50px;\n  background-color: ${props => props.theme.colors.secondary};\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  right: 15px;\n  box-shadow: ${props => props.theme.shadows.sm};\n`;\n_c7 = LogoURight;\nconst Title = styled.h1`\n  color: ${props => props.theme.colors.secondary};\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 5px;\n  letter-spacing: 0.5px;\n`;\n_c8 = Title;\nconst Subtitle = styled.p`\n  color: ${props => props.theme.colors.textLight};\n  font-size: 14px;\n  margin-bottom: 5px;\n  font-weight: 500;\n`;\n_c9 = Subtitle;\nconst RoleSelector = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n`;\n_c0 = RoleSelector;\nconst RoleOption = styled.div`\n  flex: 1;\n  text-align: center;\n  padding: 10px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  background-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.offWhite};\n  color: ${props => props.active ? props.theme.colors.white : props.theme.colors.textDark};\n  border-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.mediumGray};\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.default};\n  font-weight: 500;\n  font-size: 13px;\n\n  &:first-child {\n    border-radius: ${props => props.theme.borderRadius.sm} 0 0 ${props => props.theme.borderRadius.sm};\n  }\n\n  &:last-child {\n    border-radius: 0 ${props => props.theme.borderRadius.sm} ${props => props.theme.borderRadius.sm} 0;\n  }\n\n  &:hover {\n    background-color: ${props => props.active ? props.theme.colors.secondaryDark : props.theme.colors.lightGray};\n  }\n`;\n_c1 = RoleOption;\nconst Login = () => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [selectedRole, setSelectedRole] = useState('Agent');\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const {\n    login,\n    isAuthenticated,\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      // Redirect based on user role\n      switch (user.role) {\n        case 'Agent':\n          navigate('/agent/dashboard');\n          break;\n        case 'Supervisor':\n          navigate('/supervisor/dashboard');\n          break;\n        case 'Admin':\n          navigate('/admin/dashboard');\n          break;\n        default:\n          navigate('/');\n      }\n    }\n  }, [isAuthenticated, user, navigate]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    if (!username || !password) {\n      setError('Please enter both username and password');\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      await login(username, password, selectedRole);\n    } catch (err) {\n      setError(err.message || 'Login failed. Please check your credentials.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LoginContainer, {\n    children: /*#__PURE__*/_jsxDEV(LoginCard, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(LogoContainer, {\n          children: /*#__PURE__*/_jsxDEV(Logo, {\n            children: [/*#__PURE__*/_jsxDEV(LogoULeft, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LogoURight, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          children: \"Union Bank of India\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n          children: \"Office Verification System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(RoleSelector, {\n          children: [/*#__PURE__*/_jsxDEV(RoleOption, {\n            active: selectedRole === 'Agent',\n            onClick: () => setSelectedRole('Agent'),\n            children: \"Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RoleOption, {\n            active: selectedRole === 'Supervisor',\n            onClick: () => setSelectedRole('Supervisor'),\n            children: \"Supervisor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RoleOption, {\n            active: selectedRole === 'Admin',\n            onClick: () => setSelectedRole('Admin'),\n            children: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            htmlFor: \"username\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            id: \"username\",\n            value: username,\n            onChange: e => setUsername(e.target.value),\n            placeholder: \"Enter your username\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"password\",\n            id: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            placeholder: \"Enter your password\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 23\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"primary\",\n          fullWidth: true,\n          disabled: isSubmitting,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '8px'\n              },\n              children: \"Logging in...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : 'Login'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"CfwXv+x/Q4QdYSl7MId08h2QN+c=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c10 = Login;\nexport default Login;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"LoginContainer\");\n$RefreshReg$(_c2, \"LoginCard\");\n$RefreshReg$(_c3, \"Header\");\n$RefreshReg$(_c4, \"LogoContainer\");\n$RefreshReg$(_c5, \"Logo\");\n$RefreshReg$(_c6, \"LogoULeft\");\n$RefreshReg$(_c7, \"LogoURight\");\n$RefreshReg$(_c8, \"Title\");\n$RefreshReg$(_c9, \"Subtitle\");\n$RefreshReg$(_c0, \"RoleSelector\");\n$RefreshReg$(_c1, \"RoleOption\");\n$RefreshReg$(_c10, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "useAuth", "<PERSON><PERSON>", "Input", "FormGroup", "Label", "ErrorMessage", "LoadingSpinner", "Card", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LoginContainer", "div", "props", "theme", "spacing", "lg", "colors", "backgroundSecondary", "backgroundTertiary", "primary", "_c", "LoginCard", "_c2", "Header", "_c3", "LogoContainer", "_c4", "Logo", "_c5", "LogoULeft", "shadows", "sm", "_c6", "LogoURight", "secondary", "_c7", "Title", "h1", "_c8", "Subtitle", "p", "textLight", "_c9", "RoleSelector", "_c0", "RoleOption", "mediumGray", "active", "offWhite", "white", "textDark", "transitions", "default", "borderRadius", "secondaryDark", "lightGray", "_c1", "<PERSON><PERSON>", "_s", "username", "setUsername", "password", "setPassword", "selectedR<PERSON>", "setSelectedRole", "error", "setError", "isSubmitting", "setIsSubmitting", "login", "isAuthenticated", "user", "navigate", "role", "handleSubmit", "e", "preventDefault", "err", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "onClick", "htmlFor", "type", "id", "value", "onChange", "target", "placeholder", "required", "variant", "fullWidth", "disabled", "style", "marginLeft", "_c10", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Auth/Login.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Button, Input, FormGroup, Label, ErrorMessage, LoadingSpinner, Card, Container } from '../../styles/GlobalStyles';\n\nconst LoginContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: ${props => props.theme.spacing.lg};\n  background: linear-gradient(135deg,\n    ${props => props.theme.colors.backgroundSecondary} 0%,\n    ${props => props.theme.colors.backgroundTertiary} 50%,\n    ${props => props.theme.colors.backgroundSecondary} 100%\n  );\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -50%;\n    left: -50%;\n    width: 200%;\n    height: 200%;\n    background: radial-gradient(circle, ${props => props.theme.colors.primary}20 0%, transparent 70%);\n    animation: float 20s ease-in-out infinite;\n  }\n\n  @keyframes float {\n    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }\n    50% { transform: translate(-50%, -50%) rotate(180deg); }\n  }\n`;\n\nconst LoginCard = styled(Card)`\n  width: 100%;\n  max-width: 420px;\n  position: relative;\n  z-index: 1;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  animation: fadeIn 0.8s ease-out;\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst LogoContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  margin-bottom: 15px;\n`;\n\nconst Logo = styled.div`\n  width: 120px;\n  height: 60px;\n  position: relative;\n`;\n\nconst LogoULeft = styled.div`\n  width: 50px;\n  height: 50px;\n  background-color: ${props => props.theme.colors.primary};\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  left: 15px;\n  transform: rotate(180deg);\n  box-shadow: ${props => props.theme.shadows.sm};\n`;\n\nconst LogoURight = styled.div`\n  width: 50px;\n  height: 50px;\n  background-color: ${props => props.theme.colors.secondary};\n  border-radius: 25px 25px 0 0;\n  position: absolute;\n  right: 15px;\n  box-shadow: ${props => props.theme.shadows.sm};\n`;\n\nconst Title = styled.h1`\n  color: ${props => props.theme.colors.secondary};\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 5px;\n  letter-spacing: 0.5px;\n`;\n\nconst Subtitle = styled.p`\n  color: ${props => props.theme.colors.textLight};\n  font-size: 14px;\n  margin-bottom: 5px;\n  font-weight: 500;\n`;\n\nconst RoleSelector = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n`;\n\nconst RoleOption = styled.div<{ active: boolean }>`\n  flex: 1;\n  text-align: center;\n  padding: 10px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  background-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.offWhite};\n  color: ${props => props.active ? props.theme.colors.white : props.theme.colors.textDark};\n  border-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.mediumGray};\n  cursor: pointer;\n  transition: ${props => props.theme.transitions.default};\n  font-weight: 500;\n  font-size: 13px;\n\n  &:first-child {\n    border-radius: ${props => props.theme.borderRadius.sm} 0 0 ${props => props.theme.borderRadius.sm};\n  }\n\n  &:last-child {\n    border-radius: 0 ${props => props.theme.borderRadius.sm} ${props => props.theme.borderRadius.sm} 0;\n  }\n\n  &:hover {\n    background-color: ${props => props.active ? props.theme.colors.secondaryDark : props.theme.colors.lightGray};\n  }\n`;\n\nconst Login: React.FC = () => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [selectedRole, setSelectedRole] = useState<'Agent' | 'Supervisor' | 'Admin'>('Agent');\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const { login, isAuthenticated, user } = useAuth();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      // Redirect based on user role\n      switch (user.role) {\n        case 'Agent':\n          navigate('/agent/dashboard');\n          break;\n        case 'Supervisor':\n          navigate('/supervisor/dashboard');\n          break;\n        case 'Admin':\n          navigate('/admin/dashboard');\n          break;\n        default:\n          navigate('/');\n      }\n    }\n  }, [isAuthenticated, user, navigate]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    if (!username || !password) {\n      setError('Please enter both username and password');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      await login(username, password, selectedRole);\n    } catch (err: any) {\n      setError(err.message || 'Login failed. Please check your credentials.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <LoginContainer>\n      <LoginCard>\n        <Header>\n          <LogoContainer>\n            <Logo>\n              <LogoULeft />\n              <LogoURight />\n            </Logo>\n          </LogoContainer>\n          <Title>Union Bank of India</Title>\n          <Subtitle>Office Verification System</Subtitle>\n        </Header>\n\n        <form onSubmit={handleSubmit}>\n          <RoleSelector>\n            <RoleOption\n              active={selectedRole === 'Agent'}\n              onClick={() => setSelectedRole('Agent')}\n            >\n              Agent\n            </RoleOption>\n            <RoleOption\n              active={selectedRole === 'Supervisor'}\n              onClick={() => setSelectedRole('Supervisor')}\n            >\n              Supervisor\n            </RoleOption>\n            <RoleOption\n              active={selectedRole === 'Admin'}\n              onClick={() => setSelectedRole('Admin')}\n            >\n              Admin\n            </RoleOption>\n          </RoleSelector>\n\n          <FormGroup>\n            <Label htmlFor=\"username\">Username</Label>\n            <Input\n              type=\"text\"\n              id=\"username\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              placeholder=\"Enter your username\"\n              required\n            />\n          </FormGroup>\n\n          <FormGroup>\n            <Label htmlFor=\"password\">Password</Label>\n            <Input\n              type=\"password\"\n              id=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              placeholder=\"Enter your password\"\n              required\n            />\n            {error && <ErrorMessage>{error}</ErrorMessage>}\n          </FormGroup>\n\n          <Button\n            type=\"submit\"\n            variant=\"primary\"\n            fullWidth\n            disabled={isSubmitting}\n          >\n            {isSubmitting ? (\n              <>\n                <LoadingSpinner />\n                <span style={{ marginLeft: '8px' }}>Logging in...</span>\n              </>\n            ) : (\n              'Login'\n            )}\n          </Button>\n        </form>\n      </LoginCard>\n    </LoginContainer>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,cAAc,EAAEC,IAAI,QAAmB,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3H,MAAMC,cAAc,GAAGb,MAAM,CAACc,GAAG;AACjC;AACA;AACA;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C;AACA,MAAMH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACC,mBAAmB;AACrD,MAAML,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACE,kBAAkB;AACpD,MAAMN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACC,mBAAmB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0CL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACG,OAAO;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GA7BIV,cAAc;AA+BpB,MAAMW,SAAS,GAAGxB,MAAM,CAACQ,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GArBID,SAAS;AAuBf,MAAME,MAAM,GAAG1B,MAAM,CAACc,GAAG;AACzB;AACA;AACA,CAAC;AAACa,GAAA,GAHID,MAAM;AAKZ,MAAME,aAAa,GAAG5B,MAAM,CAACc,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAJID,aAAa;AAMnB,MAAME,IAAI,GAAG9B,MAAM,CAACc,GAAG;AACvB;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAJID,IAAI;AAMV,MAAME,SAAS,GAAGhC,MAAM,CAACc,GAAG;AAC5B;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACG,OAAO;AACzD;AACA;AACA;AACA;AACA,gBAAgBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,OAAO,CAACC,EAAE;AAC/C,CAAC;AAACC,GAAA,GATIH,SAAS;AAWf,MAAMI,UAAU,GAAGpC,MAAM,CAACc,GAAG;AAC7B;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACkB,SAAS;AAC3D;AACA;AACA;AACA,gBAAgBtB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiB,OAAO,CAACC,EAAE;AAC/C,CAAC;AAACI,GAAA,GARIF,UAAU;AAUhB,MAAMG,KAAK,GAAGvC,MAAM,CAACwC,EAAE;AACvB,WAAWzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACkB,SAAS;AAChD;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GANIF,KAAK;AAQX,MAAMG,QAAQ,GAAG1C,MAAM,CAAC2C,CAAC;AACzB,WAAW5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACyB,SAAS;AAChD;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIH,QAAQ;AAOd,MAAMI,YAAY,GAAG9C,MAAM,CAACc,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACiC,GAAA,GAJID,YAAY;AAMlB,MAAME,UAAU,GAAGhD,MAAM,CAACc,GAAwB;AAClD;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAAC8B,UAAU;AAC5D,sBAAsBlC,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAGnC,KAAK,CAACC,KAAK,CAACG,MAAM,CAACkB,SAAS,GAAGtB,KAAK,CAACC,KAAK,CAACG,MAAM,CAACgC,QAAQ;AACxG,WAAWpC,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAGnC,KAAK,CAACC,KAAK,CAACG,MAAM,CAACiC,KAAK,GAAGrC,KAAK,CAACC,KAAK,CAACG,MAAM,CAACkC,QAAQ;AACzF,kBAAkBtC,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAGnC,KAAK,CAACC,KAAK,CAACG,MAAM,CAACkB,SAAS,GAAGtB,KAAK,CAACC,KAAK,CAACG,MAAM,CAAC8B,UAAU;AACtG;AACA,gBAAgBlC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsC,WAAW,CAACC,OAAO;AACxD;AACA;AACA;AACA;AACA,qBAAqBxC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACwC,YAAY,CAACtB,EAAE,QAAQnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACwC,YAAY,CAACtB,EAAE;AACrG;AACA;AACA;AACA,uBAAuBnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACwC,YAAY,CAACtB,EAAE,IAAInB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACwC,YAAY,CAACtB,EAAE;AACnG;AACA;AACA;AACA,wBAAwBnB,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAGnC,KAAK,CAACC,KAAK,CAACG,MAAM,CAACsC,aAAa,GAAG1C,KAAK,CAACC,KAAK,CAACG,MAAM,CAACuC,SAAS;AAC/G;AACA,CAAC;AAACC,GAAA,GAxBIX,UAAU;AA0BhB,MAAMY,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAmC,OAAO,CAAC;EAC3F,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAE2E,KAAK;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGzE,OAAO,CAAC,CAAC;EAClD,MAAM0E,QAAQ,GAAG5E,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,IAAI2E,eAAe,IAAIC,IAAI,EAAE;MAC3B;MACA,QAAQA,IAAI,CAACE,IAAI;QACf,KAAK,OAAO;UACVD,QAAQ,CAAC,kBAAkB,CAAC;UAC5B;QACF,KAAK,YAAY;UACfA,QAAQ,CAAC,uBAAuB,CAAC;UACjC;QACF,KAAK,OAAO;UACVA,QAAQ,CAAC,kBAAkB,CAAC;UAC5B;QACF;UACEA,QAAQ,CAAC,GAAG,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAACF,eAAe,EAAEC,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAErC,MAAME,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI,CAACP,QAAQ,IAAI,CAACE,QAAQ,EAAE;MAC1BK,QAAQ,CAAC,yCAAyC,CAAC;MACnD;IACF;IAEAE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMC,KAAK,CAACV,QAAQ,EAAEE,QAAQ,EAAEE,YAAY,CAAC;IAC/C,CAAC,CAAC,OAAOc,GAAQ,EAAE;MACjBX,QAAQ,CAACW,GAAG,CAACC,OAAO,IAAI,8CAA8C,CAAC;IACzE,CAAC,SAAS;MACRV,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE7D,OAAA,CAACG,cAAc;IAAAqE,QAAA,eACbxE,OAAA,CAACc,SAAS;MAAA0D,QAAA,gBACRxE,OAAA,CAACgB,MAAM;QAAAwD,QAAA,gBACLxE,OAAA,CAACkB,aAAa;UAAAsD,QAAA,eACZxE,OAAA,CAACoB,IAAI;YAAAoD,QAAA,gBACHxE,OAAA,CAACsB,SAAS;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACb5E,OAAA,CAAC0B,UAAU;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChB5E,OAAA,CAAC6B,KAAK;UAAA2C,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClC5E,OAAA,CAACgC,QAAQ;UAAAwC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eAET5E,OAAA;QAAM6E,QAAQ,EAAEV,YAAa;QAAAK,QAAA,gBAC3BxE,OAAA,CAACoC,YAAY;UAAAoC,QAAA,gBACXxE,OAAA,CAACsC,UAAU;YACTE,MAAM,EAAEgB,YAAY,KAAK,OAAQ;YACjCsB,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,OAAO,CAAE;YAAAe,QAAA,EACzC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5E,OAAA,CAACsC,UAAU;YACTE,MAAM,EAAEgB,YAAY,KAAK,YAAa;YACtCsB,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,YAAY,CAAE;YAAAe,QAAA,EAC9C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5E,OAAA,CAACsC,UAAU;YACTE,MAAM,EAAEgB,YAAY,KAAK,OAAQ;YACjCsB,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,OAAO,CAAE;YAAAe,QAAA,EACzC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEf5E,OAAA,CAACN,SAAS;UAAA8E,QAAA,gBACRxE,OAAA,CAACL,KAAK;YAACoF,OAAO,EAAC,UAAU;YAAAP,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C5E,OAAA,CAACP,KAAK;YACJuF,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,UAAU;YACbC,KAAK,EAAE9B,QAAS;YAChB+B,QAAQ,EAAGf,CAAC,IAAKf,WAAW,CAACe,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YAC7CG,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ5E,OAAA,CAACN,SAAS;UAAA8E,QAAA,gBACRxE,OAAA,CAACL,KAAK;YAACoF,OAAO,EAAC,UAAU;YAAAP,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C5E,OAAA,CAACP,KAAK;YACJuF,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbC,KAAK,EAAE5B,QAAS;YAChB6B,QAAQ,EAAGf,CAAC,IAAKb,WAAW,CAACa,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YAC7CG,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACDlB,KAAK,iBAAI1D,OAAA,CAACJ,YAAY;YAAA4E,QAAA,EAAEd;UAAK;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAEZ5E,OAAA,CAACR,MAAM;UACLwF,IAAI,EAAC,QAAQ;UACbO,OAAO,EAAC,SAAS;UACjBC,SAAS;UACTC,QAAQ,EAAE7B,YAAa;UAAAY,QAAA,EAEtBZ,YAAY,gBACX5D,OAAA,CAAAE,SAAA;YAAAsE,QAAA,gBACExE,OAAA,CAACH,cAAc;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClB5E,OAAA;cAAM0F,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAAM,CAAE;cAAAnB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACxD,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAACzB,EAAA,CAjIID,KAAe;EAAA,QAOsB3D,OAAO,EAC/BF,WAAW;AAAA;AAAAuG,IAAA,GARxB1C,KAAe;AAmIrB,eAAeA,KAAK;AAAC,IAAArC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAY,GAAA,EAAA2C,IAAA;AAAAC,YAAA,CAAAhF,EAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA9D,GAAA;AAAA8D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}