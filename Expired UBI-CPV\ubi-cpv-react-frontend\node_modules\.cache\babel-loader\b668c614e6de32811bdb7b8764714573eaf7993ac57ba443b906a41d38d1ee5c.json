{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8;import React,{useRef,useState,useEffect}from'react';import styled from'styled-components';import{Button}from'../../styles/GlobalStyles';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CameraContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 20px;\\n  background: \",\";\\n  border-radius: \",\";\\n  max-width: 600px;\\n  margin: 0 auto;\\n\"])),props=>props.theme.colors.white,props=>props.theme.borderRadius.md);const VideoContainer=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  position: relative;\\n  border-radius: \",\";\\n  overflow: hidden;\\n  box-shadow: \",\";\\n\"])),props=>props.theme.borderRadius.md,props=>props.theme.shadows.md);const Video=styled.video(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  max-width: 500px;\\n  height: auto;\\n  display: block;\\n\"])));const Canvas=styled.canvas(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  display: none;\\n\"])));const PreviewImage=styled.img(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  max-width: 500px;\\n  height: auto;\\n  border-radius: \",\";\\n  box-shadow: \",\";\\n\"])),props=>props.theme.borderRadius.md,props=>props.theme.shadows.md);const ButtonGroup=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 10px;\\n  flex-wrap: wrap;\\n  justify-content: center;\\n\"])));const ErrorMessage=styled.div(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  text-align: center;\\n  padding: 10px;\\n  background: #ffebee;\\n  border-radius: \",\";\\n  border: 1px solid \",\";\\n\"])),props=>props.theme.colors.error,props=>props.theme.borderRadius.sm,props=>props.theme.colors.error);const Instructions=styled.div(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  color: \",\";\\n  font-size: 14px;\\n  max-width: 400px;\\n\"])),props=>props.theme.colors.textMedium);const CameraCapture=_ref=>{let{onCapture,onCancel,documentType}=_ref;const videoRef=useRef(null);const canvasRef=useRef(null);const streamRef=useRef(null);const[isStreaming,setIsStreaming]=useState(false);const[capturedImage,setCapturedImage]=useState(null);const[error,setError]=useState('');const[isLoading,setIsLoading]=useState(false);useEffect(()=>{startCamera();return()=>{stopCamera();};},[]);const startCamera=async()=>{try{setIsLoading(true);setError('');const stream=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:1280},height:{ideal:720},facingMode:'environment'// Use back camera on mobile\n},audio:false});if(videoRef.current){videoRef.current.srcObject=stream;streamRef.current=stream;setIsStreaming(true);}}catch(err){console.error('Error accessing camera:',err);setError('Unable to access camera. Please check permissions and try again.');}finally{setIsLoading(false);}};const stopCamera=()=>{if(streamRef.current){streamRef.current.getTracks().forEach(track=>track.stop());streamRef.current=null;}setIsStreaming(false);};const capturePhoto=()=>{if(!videoRef.current||!canvasRef.current)return;const video=videoRef.current;const canvas=canvasRef.current;const context=canvas.getContext('2d');if(!context)return;// Set canvas dimensions to match video\ncanvas.width=video.videoWidth;canvas.height=video.videoHeight;// Draw the video frame to canvas\ncontext.drawImage(video,0,0,canvas.width,canvas.height);// Convert canvas to blob\ncanvas.toBlob(blob=>{if(blob){const imageUrl=URL.createObjectURL(blob);setCapturedImage(imageUrl);stopCamera();}},'image/jpeg',0.8);};const retakePhoto=()=>{setCapturedImage(null);startCamera();};const confirmCapture=()=>{if(!capturedImage||!canvasRef.current)return;canvasRef.current.toBlob(blob=>{if(blob){const filename=\"\".concat(documentType.toLowerCase().replace(/\\s+/g,'_'),\"_\").concat(Date.now(),\".jpg\");onCapture(blob,filename);}},'image/jpeg',0.8);};const handleCancel=()=>{stopCamera();onCancel();};if(error){return/*#__PURE__*/_jsxs(CameraContainer,{children:[/*#__PURE__*/_jsx(ErrorMessage,{children:error}),/*#__PURE__*/_jsxs(ButtonGroup,{children:[/*#__PURE__*/_jsx(Button,{onClick:startCamera,children:\"Try Again\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:handleCancel,children:\"Cancel\"})]})]});}return/*#__PURE__*/_jsxs(CameraContainer,{children:[/*#__PURE__*/_jsxs(\"h3\",{style:{color:'#007E3A',marginBottom:'10px'},children:[\"Capture \",documentType]}),/*#__PURE__*/_jsx(Instructions,{children:\"Position the document clearly in the frame and ensure good lighting for best results.\"}),isLoading&&/*#__PURE__*/_jsx(\"div\",{children:\"Loading camera...\"}),capturedImage?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(PreviewImage,{src:capturedImage,alt:\"Captured document\"}),/*#__PURE__*/_jsxs(ButtonGroup,{children:[/*#__PURE__*/_jsx(Button,{onClick:confirmCapture,children:\"Use This Photo\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:retakePhoto,children:\"Retake\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:handleCancel,children:\"Cancel\"})]})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(VideoContainer,{children:/*#__PURE__*/_jsx(Video,{ref:videoRef,autoPlay:true,playsInline:true,muted:true,style:{display:isStreaming?'block':'none'}})}),/*#__PURE__*/_jsx(Canvas,{ref:canvasRef}),isStreaming&&/*#__PURE__*/_jsxs(ButtonGroup,{children:[/*#__PURE__*/_jsx(Button,{onClick:capturePhoto,children:\"\\uD83D\\uDCF7 Capture Photo\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:handleCancel,children:\"Cancel\"})]})]})]});};export default CameraCapture;", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "styled", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CameraContainer", "div", "_templateObject", "_taggedTemplateLiteral", "props", "theme", "colors", "white", "borderRadius", "md", "VideoContainer", "_templateObject2", "shadows", "Video", "video", "_templateObject3", "<PERSON><PERSON>", "canvas", "_templateObject4", "PreviewImage", "img", "_templateObject5", "ButtonGroup", "_templateObject6", "ErrorMessage", "_templateObject7", "error", "sm", "Instructions", "_templateObject8", "textMedium", "CameraCapture", "_ref", "onCapture", "onCancel", "documentType", "videoRef", "canvasRef", "streamRef", "isStreaming", "setIsStreaming", "capturedImage", "setCapturedImage", "setError", "isLoading", "setIsLoading", "startCamera", "stopCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "width", "ideal", "height", "facingMode", "audio", "current", "srcObject", "err", "console", "getTracks", "for<PERSON>ach", "track", "stop", "capturePhoto", "context", "getContext", "videoWidth", "videoHeight", "drawImage", "toBlob", "blob", "imageUrl", "URL", "createObjectURL", "retakePhoto", "confirmCapture", "filename", "concat", "toLowerCase", "replace", "Date", "now", "handleCancel", "children", "onClick", "variant", "style", "color", "marginBottom", "src", "alt", "ref", "autoPlay", "playsInline", "muted", "display"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Camera/CameraCapture.tsx"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { Button } from '../../styles/GlobalStyles';\n\nconst CameraContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n  padding: 20px;\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\nconst VideoContainer = styled.div`\n  position: relative;\n  border-radius: ${props => props.theme.borderRadius.md};\n  overflow: hidden;\n  box-shadow: ${props => props.theme.shadows.md};\n`;\n\nconst Video = styled.video`\n  width: 100%;\n  max-width: 500px;\n  height: auto;\n  display: block;\n`;\n\nconst Canvas = styled.canvas`\n  display: none;\n`;\n\nconst PreviewImage = styled.img`\n  width: 100%;\n  max-width: 500px;\n  height: auto;\n  border-radius: ${props => props.theme.borderRadius.md};\n  box-shadow: ${props => props.theme.shadows.md};\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  justify-content: center;\n`;\n\nconst ErrorMessage = styled.div`\n  color: ${props => props.theme.colors.error};\n  text-align: center;\n  padding: 10px;\n  background: #ffebee;\n  border-radius: ${props => props.theme.borderRadius.sm};\n  border: 1px solid ${props => props.theme.colors.error};\n`;\n\nconst Instructions = styled.div`\n  text-align: center;\n  color: ${props => props.theme.colors.textMedium};\n  font-size: 14px;\n  max-width: 400px;\n`;\n\ninterface CameraCaptureProps {\n  onCapture: (blob: Blob, filename: string) => void;\n  onCancel: () => void;\n  documentType: string;\n}\n\nconst CameraCapture: React.FC<CameraCaptureProps> = ({\n  onCapture,\n  onCancel,\n  documentType,\n}) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n  \n  const [isStreaming, setIsStreaming] = useState(false);\n  const [capturedImage, setCapturedImage] = useState<string | null>(null);\n  const [error, setError] = useState<string>('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  useEffect(() => {\n    startCamera();\n    \n    return () => {\n      stopCamera();\n    };\n  }, []);\n\n  const startCamera = async () => {\n    try {\n      setIsLoading(true);\n      setError('');\n      \n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: { ideal: 1280 },\n          height: { ideal: 720 },\n          facingMode: 'environment' // Use back camera on mobile\n        },\n        audio: false\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        streamRef.current = stream;\n        setIsStreaming(true);\n      }\n    } catch (err) {\n      console.error('Error accessing camera:', err);\n      setError('Unable to access camera. Please check permissions and try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const stopCamera = () => {\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach(track => track.stop());\n      streamRef.current = null;\n    }\n    setIsStreaming(false);\n  };\n\n  const capturePhoto = () => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext('2d');\n\n    if (!context) return;\n\n    // Set canvas dimensions to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw the video frame to canvas\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Convert canvas to blob\n    canvas.toBlob((blob) => {\n      if (blob) {\n        const imageUrl = URL.createObjectURL(blob);\n        setCapturedImage(imageUrl);\n        stopCamera();\n      }\n    }, 'image/jpeg', 0.8);\n  };\n\n  const retakePhoto = () => {\n    setCapturedImage(null);\n    startCamera();\n  };\n\n  const confirmCapture = () => {\n    if (!capturedImage || !canvasRef.current) return;\n\n    canvasRef.current.toBlob((blob) => {\n      if (blob) {\n        const filename = `${documentType.toLowerCase().replace(/\\s+/g, '_')}_${Date.now()}.jpg`;\n        onCapture(blob, filename);\n      }\n    }, 'image/jpeg', 0.8);\n  };\n\n  const handleCancel = () => {\n    stopCamera();\n    onCancel();\n  };\n\n  if (error) {\n    return (\n      <CameraContainer>\n        <ErrorMessage>{error}</ErrorMessage>\n        <ButtonGroup>\n          <Button onClick={startCamera}>Try Again</Button>\n          <Button variant=\"outline\" onClick={handleCancel}>Cancel</Button>\n        </ButtonGroup>\n      </CameraContainer>\n    );\n  }\n\n  return (\n    <CameraContainer>\n      <h3 style={{ color: '#007E3A', marginBottom: '10px' }}>\n        Capture {documentType}\n      </h3>\n      \n      <Instructions>\n        Position the document clearly in the frame and ensure good lighting for best results.\n      </Instructions>\n\n      {isLoading && (\n        <div>Loading camera...</div>\n      )}\n\n      {capturedImage ? (\n        <>\n          <PreviewImage src={capturedImage} alt=\"Captured document\" />\n          <ButtonGroup>\n            <Button onClick={confirmCapture}>Use This Photo</Button>\n            <Button variant=\"outline\" onClick={retakePhoto}>Retake</Button>\n            <Button variant=\"outline\" onClick={handleCancel}>Cancel</Button>\n          </ButtonGroup>\n        </>\n      ) : (\n        <>\n          <VideoContainer>\n            <Video\n              ref={videoRef}\n              autoPlay\n              playsInline\n              muted\n              style={{ display: isStreaming ? 'block' : 'none' }}\n            />\n          </VideoContainer>\n          \n          <Canvas ref={canvasRef} />\n          \n          {isStreaming && (\n            <ButtonGroup>\n              <Button onClick={capturePhoto}>📷 Capture Photo</Button>\n              <Button variant=\"outline\" onClick={handleCancel}>Cancel</Button>\n            </ButtonGroup>\n          )}\n        </>\n      )}\n    </CameraContainer>\n  );\n};\n\nexport default CameraCapture;\n"], "mappings": "2TAAA,MAAO,CAAAA,KAAK,EAAIC,MAAM,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,MAAM,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnD,KAAM,CAAAC,eAAe,CAAGR,MAAM,CAACS,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,kMAMlBC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,KAAK,CAC9BH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAGtD,CAED,KAAM,CAAAC,cAAc,CAAGlB,MAAM,CAACS,GAAG,CAAAU,gBAAA,GAAAA,gBAAA,CAAAR,sBAAA,iGAEdC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAEvCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,OAAO,CAACH,EAAE,CAC9C,CAED,KAAM,CAAAI,KAAK,CAAGrB,MAAM,CAACsB,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,mFAKzB,CAED,KAAM,CAAAa,MAAM,CAAGxB,MAAM,CAACyB,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,4BAE3B,CAED,KAAM,CAAAgB,YAAY,CAAG3B,MAAM,CAAC4B,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,2GAIZC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CACvCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,OAAO,CAACH,EAAE,CAC9C,CAED,KAAM,CAAAa,WAAW,CAAG9B,MAAM,CAACS,GAAG,CAAAsB,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,0FAK7B,CAED,KAAM,CAAAqB,YAAY,CAAGhC,MAAM,CAACS,GAAG,CAAAwB,gBAAA,GAAAA,gBAAA,CAAAtB,sBAAA,2IACpBC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACoB,KAAK,CAIzBtB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACmB,EAAE,CACjCvB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACoB,KAAK,CACtD,CAED,KAAM,CAAAE,YAAY,CAAGpC,MAAM,CAACS,GAAG,CAAA4B,gBAAA,GAAAA,gBAAA,CAAA1B,sBAAA,yFAEpBC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACwB,UAAU,CAGhD,CAQD,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAI9C,IAJ+C,CACnDC,SAAS,CACTC,QAAQ,CACRC,YACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,QAAQ,CAAG/C,MAAM,CAAmB,IAAI,CAAC,CAC/C,KAAM,CAAAgD,SAAS,CAAGhD,MAAM,CAAoB,IAAI,CAAC,CACjD,KAAM,CAAAiD,SAAS,CAAGjD,MAAM,CAAqB,IAAI,CAAC,CAElD,KAAM,CAACkD,WAAW,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmD,aAAa,CAAEC,gBAAgB,CAAC,CAAGpD,QAAQ,CAAgB,IAAI,CAAC,CACvE,KAAM,CAACoC,KAAK,CAAEiB,QAAQ,CAAC,CAAGrD,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACsD,SAAS,CAAEC,YAAY,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAEjDC,SAAS,CAAC,IAAM,CACduD,WAAW,CAAC,CAAC,CAEb,MAAO,IAAM,CACXC,UAAU,CAAC,CAAC,CACd,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACFD,YAAY,CAAC,IAAI,CAAC,CAClBF,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAAK,MAAM,CAAG,KAAM,CAAAC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC,CACvDrC,KAAK,CAAE,CACLsC,KAAK,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAC,CACtBC,MAAM,CAAE,CAAED,KAAK,CAAE,GAAI,CAAC,CACtBE,UAAU,CAAE,aAAc;AAC5B,CAAC,CACDC,KAAK,CAAE,KACT,CAAC,CAAC,CAEF,GAAIpB,QAAQ,CAACqB,OAAO,CAAE,CACpBrB,QAAQ,CAACqB,OAAO,CAACC,SAAS,CAAGV,MAAM,CACnCV,SAAS,CAACmB,OAAO,CAAGT,MAAM,CAC1BR,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAAE,MAAOmB,GAAG,CAAE,CACZC,OAAO,CAAClC,KAAK,CAAC,yBAAyB,CAAEiC,GAAG,CAAC,CAC7ChB,QAAQ,CAAC,kEAAkE,CAAC,CAC9E,CAAC,OAAS,CACRE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAIT,SAAS,CAACmB,OAAO,CAAE,CACrBnB,SAAS,CAACmB,OAAO,CAACI,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAC5D1B,SAAS,CAACmB,OAAO,CAAG,IAAI,CAC1B,CACAjB,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAyB,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAC7B,QAAQ,CAACqB,OAAO,EAAI,CAACpB,SAAS,CAACoB,OAAO,CAAE,OAE7C,KAAM,CAAA3C,KAAK,CAAGsB,QAAQ,CAACqB,OAAO,CAC9B,KAAM,CAAAxC,MAAM,CAAGoB,SAAS,CAACoB,OAAO,CAChC,KAAM,CAAAS,OAAO,CAAGjD,MAAM,CAACkD,UAAU,CAAC,IAAI,CAAC,CAEvC,GAAI,CAACD,OAAO,CAAE,OAEd;AACAjD,MAAM,CAACmC,KAAK,CAAGtC,KAAK,CAACsD,UAAU,CAC/BnD,MAAM,CAACqC,MAAM,CAAGxC,KAAK,CAACuD,WAAW,CAEjC;AACAH,OAAO,CAACI,SAAS,CAACxD,KAAK,CAAE,CAAC,CAAE,CAAC,CAAEG,MAAM,CAACmC,KAAK,CAAEnC,MAAM,CAACqC,MAAM,CAAC,CAE3D;AACArC,MAAM,CAACsD,MAAM,CAAEC,IAAI,EAAK,CACtB,GAAIA,IAAI,CAAE,CACR,KAAM,CAAAC,QAAQ,CAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC,CAC1C9B,gBAAgB,CAAC+B,QAAQ,CAAC,CAC1B1B,UAAU,CAAC,CAAC,CACd,CACF,CAAC,CAAE,YAAY,CAAE,GAAG,CAAC,CACvB,CAAC,CAED,KAAM,CAAA6B,WAAW,CAAGA,CAAA,GAAM,CACxBlC,gBAAgB,CAAC,IAAI,CAAC,CACtBI,WAAW,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAA+B,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAACpC,aAAa,EAAI,CAACJ,SAAS,CAACoB,OAAO,CAAE,OAE1CpB,SAAS,CAACoB,OAAO,CAACc,MAAM,CAAEC,IAAI,EAAK,CACjC,GAAIA,IAAI,CAAE,CACR,KAAM,CAAAM,QAAQ,IAAAC,MAAA,CAAM5C,YAAY,CAAC6C,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,MAAAF,MAAA,CAAIG,IAAI,CAACC,GAAG,CAAC,CAAC,QAAM,CACvFlD,SAAS,CAACuC,IAAI,CAAEM,QAAQ,CAAC,CAC3B,CACF,CAAC,CAAE,YAAY,CAAE,GAAG,CAAC,CACvB,CAAC,CAED,KAAM,CAAAM,YAAY,CAAGA,CAAA,GAAM,CACzBrC,UAAU,CAAC,CAAC,CACZb,QAAQ,CAAC,CAAC,CACZ,CAAC,CAED,GAAIR,KAAK,CAAE,CACT,mBACE7B,KAAA,CAACG,eAAe,EAAAqF,QAAA,eACd1F,IAAA,CAAC6B,YAAY,EAAA6D,QAAA,CAAE3D,KAAK,CAAe,CAAC,cACpC7B,KAAA,CAACyB,WAAW,EAAA+D,QAAA,eACV1F,IAAA,CAACF,MAAM,EAAC6F,OAAO,CAAExC,WAAY,CAAAuC,QAAA,CAAC,WAAS,CAAQ,CAAC,cAChD1F,IAAA,CAACF,MAAM,EAAC8F,OAAO,CAAC,SAAS,CAACD,OAAO,CAAEF,YAAa,CAAAC,QAAA,CAAC,QAAM,CAAQ,CAAC,EACrD,CAAC,EACC,CAAC,CAEtB,CAEA,mBACExF,KAAA,CAACG,eAAe,EAAAqF,QAAA,eACdxF,KAAA,OAAI2F,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAS,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,EAAC,UAC7C,CAAClD,YAAY,EACnB,CAAC,cAELxC,IAAA,CAACiC,YAAY,EAAAyD,QAAA,CAAC,uFAEd,CAAc,CAAC,CAEdzC,SAAS,eACRjD,IAAA,QAAA0F,QAAA,CAAK,mBAAiB,CAAK,CAC5B,CAEA5C,aAAa,cACZ5C,KAAA,CAAAE,SAAA,EAAAsF,QAAA,eACE1F,IAAA,CAACwB,YAAY,EAACwE,GAAG,CAAElD,aAAc,CAACmD,GAAG,CAAC,mBAAmB,CAAE,CAAC,cAC5D/F,KAAA,CAACyB,WAAW,EAAA+D,QAAA,eACV1F,IAAA,CAACF,MAAM,EAAC6F,OAAO,CAAET,cAAe,CAAAQ,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACxD1F,IAAA,CAACF,MAAM,EAAC8F,OAAO,CAAC,SAAS,CAACD,OAAO,CAAEV,WAAY,CAAAS,QAAA,CAAC,QAAM,CAAQ,CAAC,cAC/D1F,IAAA,CAACF,MAAM,EAAC8F,OAAO,CAAC,SAAS,CAACD,OAAO,CAAEF,YAAa,CAAAC,QAAA,CAAC,QAAM,CAAQ,CAAC,EACrD,CAAC,EACd,CAAC,cAEHxF,KAAA,CAAAE,SAAA,EAAAsF,QAAA,eACE1F,IAAA,CAACe,cAAc,EAAA2E,QAAA,cACb1F,IAAA,CAACkB,KAAK,EACJgF,GAAG,CAAEzD,QAAS,CACd0D,QAAQ,MACRC,WAAW,MACXC,KAAK,MACLR,KAAK,CAAE,CAAES,OAAO,CAAE1D,WAAW,CAAG,OAAO,CAAG,MAAO,CAAE,CACpD,CAAC,CACY,CAAC,cAEjB5C,IAAA,CAACqB,MAAM,EAAC6E,GAAG,CAAExD,SAAU,CAAE,CAAC,CAEzBE,WAAW,eACV1C,KAAA,CAACyB,WAAW,EAAA+D,QAAA,eACV1F,IAAA,CAACF,MAAM,EAAC6F,OAAO,CAAErB,YAAa,CAAAoB,QAAA,CAAC,4BAAgB,CAAQ,CAAC,cACxD1F,IAAA,CAACF,MAAM,EAAC8F,OAAO,CAAC,SAAS,CAACD,OAAO,CAAEF,YAAa,CAAAC,QAAA,CAAC,QAAM,CAAQ,CAAC,EACrD,CACd,EACD,CACH,EACc,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAtD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}