{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{Card,Button}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatsContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\"])));const StatCard=styled(Card)(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  transition: \",\";\\n\\n  &:hover {\\n    transform: translateY(-5px);\\n    box-shadow: \",\";\\n  }\\n\"])),props=>props.theme.transitions.default,props=>props.theme.shadows.lg);const StatIcon=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n  font-size: 20px;\\n  color: \",\";\\n  background: \",\";\\n\"])),props=>props.theme.colors.white,props=>props.color);const StatValue=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin-bottom: 5px;\\n  color: \",\";\\n\"])),props=>props.theme.colors.textDark);const StatLabel=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  color: \",\";\\n  font-weight: 500;\\n\"])),props=>props.theme.colors.textLight);const ActionGrid=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\"])));const ActionCard=styled(Card)(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  padding: 30px 20px;\\n\"])));const ActionIcon=styled.div(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  font-size: 48px;\\n  margin-bottom: 15px;\\n\"])));const ActionTitle=styled.h3(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  margin-bottom: 10px;\\n  font-size: 18px;\\n\"])),props=>props.theme.colors.primary);const ActionDescription=styled.p(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  margin-bottom: 20px;\\n  font-size: 14px;\\n\"])),props=>props.theme.colors.textMedium);const TableContainer=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  overflow-x: auto;\\n\"])));const Table=styled.table(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const StatusBadge=styled.span(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.status){case'new':return\"\\n          background-color: #e3f2fd;\\n          color: #0d47a1;\\n        \";case'assigned':return\"\\n          background-color: #fff3e0;\\n          color: #e65100;\\n        \";case'in-progress':return\"\\n          background-color: #fff8e1;\\n          color: #ff8f00;\\n        \";case'pending-review':return\"\\n          background-color: #f3e5f5;\\n          color: #4a148c;\\n        \";case'approved':return\"\\n          background-color: #e8f5e9;\\n          color: #2e7d32;\\n        \";case'rejected':return\"\\n          background-color: #ffebee;\\n          color: #c62828;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const AdminDashboard=()=>{const[leads,setLeads]=useState([]);const[stats,setStats]=useState(null);const[loading,setLoading]=useState(true);const navigate=useNavigate();useEffect(()=>{loadDashboardData();},[]);const loadDashboardData=async()=>{try{setLoading(true);const[leadsResponse,statsResponse]=await Promise.all([apiService.getLeads(1,10),apiService.getDashboardStats()]);setLeads(leadsResponse.data||[]);setStats(statsResponse);}catch(error){console.error('Error loading dashboard data:',error);// Mock data for demo\nsetLeads([{leadId:1,customerName:'John Doe',mobileNumber:'9876543210',loanType:'Personal Loan',status:'new',createdDate:'2024-01-15T10:30:00Z',createdByName:'Admin User',documentCount:0,croppedImageCount:0},{leadId:2,customerName:'Jane Smith',mobileNumber:'9876543211',loanType:'Home Loan',status:'assigned',createdDate:'2024-01-14T09:15:00Z',createdByName:'Admin User',assignedToName:'Agent Smith',documentCount:2,croppedImageCount:1}]);setStats({totalLeads:25,newLeads:5,assignedLeads:8,inProgressLeads:6,pendingReviewLeads:4,approvedLeads:2,rejectedLeads:0});}finally{setLoading(false);}};const navigationItems=[{icon:'🏠',label:'Dashboard',active:true},{icon:'➕',label:'Create Lead',onClick:()=>navigate('/admin/create-lead')},{icon:'👥',label:'Manage Users',onClick:()=>navigate('/admin/users')},{icon:'📋',label:'All Leads',onClick:()=>navigate('/admin/leads')},{icon:'📊',label:'Reports',onClick:()=>navigate('/admin/reports')},{icon:'⚙️',label:'Settings',onClick:()=>navigate('/admin/settings')}];const handleCreateLead=()=>{navigate('/admin/create-lead');};const handleManageUsers=()=>{navigate('/admin/users');};const handleViewReports=()=>{navigate('/admin/reports');};const handleLeadClick=leadId=>{navigate(\"/lead/\".concat(leadId));};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Admin Dashboard\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(\"div\",{children:\"Loading...\"})});}return/*#__PURE__*/_jsxs(DashboardLayout,{title:\"Admin Dashboard\",navigationItems:navigationItems,children:[/*#__PURE__*/_jsxs(StatsContainer,{children:[/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #007E3A, #005a2a)\",children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.totalLeads)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"Total Leads\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #e3f2fd, #0d47a1)\",children:\"\\uD83C\\uDD95\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.newLeads)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"New Leads\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #fff8e1, #ff8f00)\",children:\"\\u23F3\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.inProgressLeads)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"In Progress\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #f3e5f5, #4a148c)\",children:\"\\uD83D\\uDC41\\uFE0F\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.pendingReviewLeads)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"Pending Review\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #28a745, #1e7e34)\",children:\"\\u2705\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.approvedLeads)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"Approved\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatIcon,{color:\"linear-gradient(135deg, #dc3545, #c82333)\",children:\"\\u274C\"}),/*#__PURE__*/_jsx(StatValue,{children:(stats===null||stats===void 0?void 0:stats.rejectedLeads)||0}),/*#__PURE__*/_jsx(StatLabel,{children:\"Rejected\"})]})]}),/*#__PURE__*/_jsxs(ActionGrid,{children:[/*#__PURE__*/_jsxs(ActionCard,{children:[/*#__PURE__*/_jsx(ActionIcon,{children:\"\\u2795\"}),/*#__PURE__*/_jsx(ActionTitle,{children:\"Create New Lead\"}),/*#__PURE__*/_jsx(ActionDescription,{children:\"Add a new customer verification lead to the system\"}),/*#__PURE__*/_jsx(Button,{onClick:handleCreateLead,children:\"Create Lead\"})]}),/*#__PURE__*/_jsxs(ActionCard,{children:[/*#__PURE__*/_jsx(ActionIcon,{children:\"\\uD83D\\uDC65\"}),/*#__PURE__*/_jsx(ActionTitle,{children:\"Manage Users\"}),/*#__PURE__*/_jsx(ActionDescription,{children:\"Add, edit, or deactivate agents and supervisors\"}),/*#__PURE__*/_jsx(Button,{onClick:handleManageUsers,children:\"Manage Users\"})]}),/*#__PURE__*/_jsxs(ActionCard,{children:[/*#__PURE__*/_jsx(ActionIcon,{children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsx(ActionTitle,{children:\"View Reports\"}),/*#__PURE__*/_jsx(ActionDescription,{children:\"Generate and view system performance reports\"}),/*#__PURE__*/_jsx(Button,{onClick:handleViewReports,children:\"View Reports\"})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Recent Leads\"}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Customer Name\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Mobile\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Loan Type\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Status\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Created Date\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Assigned Agent\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:leads.map(lead=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:lead.customerName}),/*#__PURE__*/_jsx(TableCell,{children:lead.mobileNumber}),/*#__PURE__*/_jsx(TableCell,{children:lead.loanType}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(StatusBadge,{status:lead.status,children:lead.status.replace('-',' ').toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:formatDate(lead.createdDate)}),/*#__PURE__*/_jsx(TableCell,{children:lead.assignedToName||'Unassigned'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>handleLeadClick(lead.leadId),children:\"View Details\"})})]},lead.leadId))})]})}),leads.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px',color:'#777'},children:\"No leads found.\"})]})]});};export default AdminDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "StatsContainer", "div", "_templateObject", "_taggedTemplateLiteral", "StatCard", "_templateObject2", "props", "theme", "transitions", "default", "shadows", "lg", "StatIcon", "_templateObject3", "colors", "white", "color", "StatValue", "_templateObject4", "textDark", "StatLabel", "_templateObject5", "textLight", "ActionGrid", "_templateObject6", "ActionCard", "_templateObject7", "ActionIcon", "_templateObject8", "ActionTitle", "h3", "_templateObject9", "primary", "ActionDescription", "p", "_templateObject0", "textMedium", "TableContainer", "_templateObject1", "Table", "table", "_templateObject10", "TableHeader", "th", "_templateObject11", "lightGray", "offWhite", "TableCell", "td", "_templateObject12", "TableRow", "tr", "_templateObject13", "StatusBadge", "span", "_templateObject14", "status", "AdminDashboard", "leads", "setLeads", "stats", "setStats", "loading", "setLoading", "navigate", "loadDashboardData", "leadsResponse", "statsResponse", "Promise", "all", "getLeads", "getDashboardStats", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "createdByName", "documentCount", "croppedImageCount", "assignedToName", "totalLeads", "newLeads", "assignedLeads", "inProgressLeads", "pendingReviewLeads", "approvedLeads", "rejectedLeads", "navigationItems", "icon", "label", "active", "onClick", "handleCreateLead", "handleManageUsers", "handleViewReports", "handleLeadClick", "concat", "formatDate", "dateString", "Date", "toLocaleDateString", "title", "children", "style", "marginBottom", "map", "lead", "replace", "toUpperCase", "size", "length", "textAlign", "padding"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Dashboard/AdminDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem, DashboardStats } from '../../services/apiService';\n\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n\nconst StatIcon = styled.div<{ color: string }>`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ${props => props.theme.colors.white};\n  background: ${props => props.color};\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n\nconst ActionGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst ActionCard = styled(Card)`\n  text-align: center;\n  padding: 30px 20px;\n`;\n\nconst ActionIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: 15px;\n`;\n\nconst ActionTitle = styled.h3`\n  color: ${props => props.theme.colors.primary};\n  margin-bottom: 10px;\n  font-size: 18px;\n`;\n\nconst ActionDescription = styled.p`\n  color: ${props => props.theme.colors.textMedium};\n  margin-bottom: 20px;\n  font-size: 14px;\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'new':\n        return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n      case 'assigned':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'in-progress':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'approved':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst AdminDashboard: React.FC = () => {\n  const [leads, setLeads] = useState<LeadListItem[]>([]);\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [leadsResponse, statsResponse] = await Promise.all([\n        apiService.getLeads(1, 10),\n        apiService.getDashboardStats(),\n      ]);\n\n      setLeads(leadsResponse.data || []);\n      setStats(statsResponse);\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Mock data for demo\n      setLeads([\n        {\n          leadId: 1,\n          customerName: 'John Doe',\n          mobileNumber: '9876543210',\n          loanType: 'Personal Loan',\n          status: 'new',\n          createdDate: '2024-01-15T10:30:00Z',\n          createdByName: 'Admin User',\n          documentCount: 0,\n          croppedImageCount: 0,\n        },\n        {\n          leadId: 2,\n          customerName: 'Jane Smith',\n          mobileNumber: '9876543211',\n          loanType: 'Home Loan',\n          status: 'assigned',\n          createdDate: '2024-01-14T09:15:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Agent Smith',\n          documentCount: 2,\n          croppedImageCount: 1,\n        },\n      ]);\n      setStats({\n        totalLeads: 25,\n        newLeads: 5,\n        assignedLeads: 8,\n        inProgressLeads: 6,\n        pendingReviewLeads: 4,\n        approvedLeads: 2,\n        rejectedLeads: 0,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', active: true },\n    { icon: '➕', label: 'Create Lead', onClick: () => navigate('/admin/create-lead') },\n    { icon: '👥', label: 'Manage Users', onClick: () => navigate('/admin/users') },\n    { icon: '📋', label: 'All Leads', onClick: () => navigate('/admin/leads') },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/admin/reports') },\n    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },\n  ];\n\n  const handleCreateLead = () => {\n    navigate('/admin/create-lead');\n  };\n\n  const handleManageUsers = () => {\n    navigate('/admin/users');\n  };\n\n  const handleViewReports = () => {\n    navigate('/admin/reports');\n  };\n\n  const handleLeadClick = (leadId: number) => {\n    navigate(`/lead/${leadId}`);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Admin Dashboard\" navigationItems={navigationItems}>\n        <div>Loading...</div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Admin Dashboard\" navigationItems={navigationItems}>\n      {/* Stats Cards */}\n      <StatsContainer>\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #007E3A, #005a2a)\">📊</StatIcon>\n          <StatValue>{stats?.totalLeads || 0}</StatValue>\n          <StatLabel>Total Leads</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #e3f2fd, #0d47a1)\">🆕</StatIcon>\n          <StatValue>{stats?.newLeads || 0}</StatValue>\n          <StatLabel>New Leads</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #fff8e1, #ff8f00)\">⏳</StatIcon>\n          <StatValue>{stats?.inProgressLeads || 0}</StatValue>\n          <StatLabel>In Progress</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #f3e5f5, #4a148c)\">👁️</StatIcon>\n          <StatValue>{stats?.pendingReviewLeads || 0}</StatValue>\n          <StatLabel>Pending Review</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #28a745, #1e7e34)\">✅</StatIcon>\n          <StatValue>{stats?.approvedLeads || 0}</StatValue>\n          <StatLabel>Approved</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #dc3545, #c82333)\">❌</StatIcon>\n          <StatValue>{stats?.rejectedLeads || 0}</StatValue>\n          <StatLabel>Rejected</StatLabel>\n        </StatCard>\n      </StatsContainer>\n\n      {/* Quick Actions */}\n      <ActionGrid>\n        <ActionCard>\n          <ActionIcon>➕</ActionIcon>\n          <ActionTitle>Create New Lead</ActionTitle>\n          <ActionDescription>\n            Add a new customer verification lead to the system\n          </ActionDescription>\n          <Button onClick={handleCreateLead}>Create Lead</Button>\n        </ActionCard>\n\n        <ActionCard>\n          <ActionIcon>👥</ActionIcon>\n          <ActionTitle>Manage Users</ActionTitle>\n          <ActionDescription>\n            Add, edit, or deactivate agents and supervisors\n          </ActionDescription>\n          <Button onClick={handleManageUsers}>Manage Users</Button>\n        </ActionCard>\n\n        <ActionCard>\n          <ActionIcon>📊</ActionIcon>\n          <ActionTitle>View Reports</ActionTitle>\n          <ActionDescription>\n            Generate and view system performance reports\n          </ActionDescription>\n          <Button onClick={handleViewReports}>View Reports</Button>\n        </ActionCard>\n      </ActionGrid>\n\n      {/* Recent Leads */}\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Recent Leads</h2>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer Name</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Created Date</TableHeader>\n                <TableHeader>Assigned Agent</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {leads.map((lead) => (\n                <TableRow key={lead.leadId}>\n                  <TableCell>{lead.customerName}</TableCell>\n                  <TableCell>{lead.mobileNumber}</TableCell>\n                  <TableCell>{lead.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={lead.status}>\n                      {lead.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>{formatDate(lead.createdDate)}</TableCell>\n                  <TableCell>\n                    {lead.assignedToName || 'Unassigned'}\n                  </TableCell>\n                  <TableCell>\n                    <Button\n                      size=\"sm\"\n                      onClick={() => handleLeadClick(lead.leadId)}\n                    >\n                      View Details\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {leads.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            No leads found.\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": "wcAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,KAAwB,2BAA2B,CACxE,OAASC,UAAU,KAAsC,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErF,KAAM,CAAAC,cAAc,CAAGT,MAAM,CAACU,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,kIAKhC,CAED,KAAM,CAAAC,QAAQ,CAAGb,MAAM,CAACE,IAAI,CAAC,CAAAY,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,sMAKbG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,WAAW,CAACC,OAAO,CAItCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,OAAO,CAACC,EAAE,CAEhD,CAED,KAAM,CAAAC,QAAQ,CAAGrB,MAAM,CAACU,GAAG,CAAAY,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,uNAShBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACC,KAAK,CAC5BT,KAAK,EAAIA,KAAK,CAACU,KAAK,CACnC,CAED,KAAM,CAAAC,SAAS,CAAG1B,MAAM,CAACU,GAAG,CAAAiB,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,yFAIjBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACK,QAAQ,CAC9C,CAED,KAAM,CAAAC,SAAS,CAAG7B,MAAM,CAACU,GAAG,CAAAoB,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,kEAEjBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACQ,SAAS,CAE/C,CAED,KAAM,CAAAC,UAAU,CAAGhC,MAAM,CAACU,GAAG,CAAAuB,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,kIAK5B,CAED,KAAM,CAAAsB,UAAU,CAAGlC,MAAM,CAACE,IAAI,CAAC,CAAAiC,gBAAA,GAAAA,gBAAA,CAAAvB,sBAAA,wDAG9B,CAED,KAAM,CAAAwB,UAAU,CAAGpC,MAAM,CAACU,GAAG,CAAA2B,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,sDAG5B,CAED,KAAM,CAAA0B,WAAW,CAAGtC,MAAM,CAACuC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA5B,sBAAA,qEAClBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACkB,OAAO,CAG7C,CAED,KAAM,CAAAC,iBAAiB,CAAG1C,MAAM,CAAC2C,CAAC,CAAAC,gBAAA,GAAAA,gBAAA,CAAAhC,sBAAA,qEACvBG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACsB,UAAU,CAGhD,CAED,KAAM,CAAAC,cAAc,CAAG9C,MAAM,CAACU,GAAG,CAAAqC,gBAAA,GAAAA,gBAAA,CAAAnC,sBAAA,+BAEhC,CAED,KAAM,CAAAoC,KAAK,CAAGhD,MAAM,CAACiD,KAAK,CAAAC,iBAAA,GAAAA,iBAAA,CAAAtC,sBAAA,wDAGzB,CAED,KAAM,CAAAuC,WAAW,CAAGnD,MAAM,CAACoD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAzC,sBAAA,qJAGAG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAAC+B,SAAS,CAC5CvC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACgC,QAAQ,CAE/CxC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACsB,UAAU,CAChD,CAED,KAAM,CAAAW,SAAS,CAAGxD,MAAM,CAACyD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA9C,sBAAA,uFAGEG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAAC+B,SAAS,CACjE,CAED,KAAM,CAAAK,QAAQ,CAAG3D,MAAM,CAAC4D,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAjD,sBAAA,wDAEFG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAAC+B,SAAS,CAE5D,CAED,KAAM,CAAAQ,WAAW,CAAG9D,MAAM,CAAC+D,IAAI,CAAAC,iBAAA,GAAAA,iBAAA,CAAApD,sBAAA,mIAO3BG,KAAK,EAAI,CACT,OAAQA,KAAK,CAACkD,MAAM,EAClB,IAAK,KAAK,CACR,oFAIF,IAAK,UAAU,CACb,oFAIF,IAAK,aAAa,CAChB,oFAIF,IAAK,gBAAgB,CACnB,oFAIF,IAAK,UAAU,CACb,oFAIF,IAAK,UAAU,CACb,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGvE,QAAQ,CAAiB,EAAE,CAAC,CACtD,KAAM,CAACwE,KAAK,CAAEC,QAAQ,CAAC,CAAGzE,QAAQ,CAAwB,IAAI,CAAC,CAC/D,KAAM,CAAC0E,OAAO,CAAEC,UAAU,CAAC,CAAG3E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAA4E,QAAQ,CAAG1E,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd4E,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACFF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAACG,aAAa,CAAEC,aAAa,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACvD1E,UAAU,CAAC2E,QAAQ,CAAC,CAAC,CAAE,EAAE,CAAC,CAC1B3E,UAAU,CAAC4E,iBAAiB,CAAC,CAAC,CAC/B,CAAC,CAEFZ,QAAQ,CAACO,aAAa,CAACM,IAAI,EAAI,EAAE,CAAC,CAClCX,QAAQ,CAACM,aAAa,CAAC,CACzB,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD;AACAd,QAAQ,CAAC,CACP,CACEgB,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,UAAU,CACxBC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,eAAe,CACzBtB,MAAM,CAAE,KAAK,CACbuB,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,YAAY,CAC3BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACD,CACEP,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,WAAW,CACrBtB,MAAM,CAAE,UAAU,CAClBuB,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,YAAY,CAC3BG,cAAc,CAAE,aAAa,CAC7BF,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACF,CAAC,CACFrB,QAAQ,CAAC,CACPuB,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,CAAC,CACXC,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,kBAAkB,CAAE,CAAC,CACrBC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CACjB,CAAC,CAAC,CACJ,CAAC,OAAS,CACR3B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4B,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,MAAM,CAAE,IAAK,CAAC,CAChD,CAAEF,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,aAAa,CAAEE,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,oBAAoB,CAAE,CAAC,CAClF,CAAE4B,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,cAAc,CAAEE,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,cAAc,CAAE,CAAC,CAC9E,CAAE4B,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEE,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,cAAc,CAAE,CAAC,CAC3E,CAAE4B,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEE,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,gBAAgB,CAAE,CAAC,CAC3E,CAAE4B,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAU,CAAEE,OAAO,CAAEA,CAAA,GAAM/B,QAAQ,CAAC,iBAAiB,CAAE,CAAC,CAC9E,CAED,KAAM,CAAAgC,gBAAgB,CAAGA,CAAA,GAAM,CAC7BhC,QAAQ,CAAC,oBAAoB,CAAC,CAChC,CAAC,CAED,KAAM,CAAAiC,iBAAiB,CAAGA,CAAA,GAAM,CAC9BjC,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAkC,iBAAiB,CAAGA,CAAA,GAAM,CAC9BlC,QAAQ,CAAC,gBAAgB,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAmC,eAAe,CAAIxB,MAAc,EAAK,CAC1CX,QAAQ,UAAAoC,MAAA,CAAUzB,MAAM,CAAE,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA0B,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,GAAI1C,OAAO,CAAE,CACX,mBACEjE,IAAA,CAACL,eAAe,EAACiH,KAAK,CAAC,iBAAiB,CAACd,eAAe,CAAEA,eAAgB,CAAAe,QAAA,cACxE7G,IAAA,QAAA6G,QAAA,CAAK,YAAU,CAAK,CAAC,CACN,CAAC,CAEtB,CAEA,mBACE3G,KAAA,CAACP,eAAe,EAACiH,KAAK,CAAC,iBAAiB,CAACd,eAAe,CAAEA,eAAgB,CAAAe,QAAA,eAExE3G,KAAA,CAACC,cAAc,EAAA0G,QAAA,eACb3G,KAAA,CAACK,QAAQ,EAAAsG,QAAA,eACP7G,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA0F,QAAA,CAAC,cAAE,CAAU,CAAC,cACzE7G,IAAA,CAACoB,SAAS,EAAAyF,QAAA,CAAE,CAAA9C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwB,UAAU,GAAI,CAAC,CAAY,CAAC,cAC/CvF,IAAA,CAACuB,SAAS,EAAAsF,QAAA,CAAC,aAAW,CAAW,CAAC,EAC1B,CAAC,cAEX3G,KAAA,CAACK,QAAQ,EAAAsG,QAAA,eACP7G,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA0F,QAAA,CAAC,cAAE,CAAU,CAAC,cACzE7G,IAAA,CAACoB,SAAS,EAAAyF,QAAA,CAAE,CAAA9C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyB,QAAQ,GAAI,CAAC,CAAY,CAAC,cAC7CxF,IAAA,CAACuB,SAAS,EAAAsF,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,cAEX3G,KAAA,CAACK,QAAQ,EAAAsG,QAAA,eACP7G,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA0F,QAAA,CAAC,QAAC,CAAU,CAAC,cACxE7G,IAAA,CAACoB,SAAS,EAAAyF,QAAA,CAAE,CAAA9C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE2B,eAAe,GAAI,CAAC,CAAY,CAAC,cACpD1F,IAAA,CAACuB,SAAS,EAAAsF,QAAA,CAAC,aAAW,CAAW,CAAC,EAC1B,CAAC,cAEX3G,KAAA,CAACK,QAAQ,EAAAsG,QAAA,eACP7G,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA0F,QAAA,CAAC,oBAAG,CAAU,CAAC,cAC1E7G,IAAA,CAACoB,SAAS,EAAAyF,QAAA,CAAE,CAAA9C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE4B,kBAAkB,GAAI,CAAC,CAAY,CAAC,cACvD3F,IAAA,CAACuB,SAAS,EAAAsF,QAAA,CAAC,gBAAc,CAAW,CAAC,EAC7B,CAAC,cAEX3G,KAAA,CAACK,QAAQ,EAAAsG,QAAA,eACP7G,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA0F,QAAA,CAAC,QAAC,CAAU,CAAC,cACxE7G,IAAA,CAACoB,SAAS,EAAAyF,QAAA,CAAE,CAAA9C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE6B,aAAa,GAAI,CAAC,CAAY,CAAC,cAClD5F,IAAA,CAACuB,SAAS,EAAAsF,QAAA,CAAC,UAAQ,CAAW,CAAC,EACvB,CAAC,cAEX3G,KAAA,CAACK,QAAQ,EAAAsG,QAAA,eACP7G,IAAA,CAACe,QAAQ,EAACI,KAAK,CAAC,2CAA2C,CAAA0F,QAAA,CAAC,QAAC,CAAU,CAAC,cACxE7G,IAAA,CAACoB,SAAS,EAAAyF,QAAA,CAAE,CAAA9C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE8B,aAAa,GAAI,CAAC,CAAY,CAAC,cAClD7F,IAAA,CAACuB,SAAS,EAAAsF,QAAA,CAAC,UAAQ,CAAW,CAAC,EACvB,CAAC,EACG,CAAC,cAGjB3G,KAAA,CAACwB,UAAU,EAAAmF,QAAA,eACT3G,KAAA,CAAC0B,UAAU,EAAAiF,QAAA,eACT7G,IAAA,CAAC8B,UAAU,EAAA+E,QAAA,CAAC,QAAC,CAAY,CAAC,cAC1B7G,IAAA,CAACgC,WAAW,EAAA6E,QAAA,CAAC,iBAAe,CAAa,CAAC,cAC1C7G,IAAA,CAACoC,iBAAiB,EAAAyE,QAAA,CAAC,oDAEnB,CAAmB,CAAC,cACpB7G,IAAA,CAACH,MAAM,EAACqG,OAAO,CAAEC,gBAAiB,CAAAU,QAAA,CAAC,aAAW,CAAQ,CAAC,EAC7C,CAAC,cAEb3G,KAAA,CAAC0B,UAAU,EAAAiF,QAAA,eACT7G,IAAA,CAAC8B,UAAU,EAAA+E,QAAA,CAAC,cAAE,CAAY,CAAC,cAC3B7G,IAAA,CAACgC,WAAW,EAAA6E,QAAA,CAAC,cAAY,CAAa,CAAC,cACvC7G,IAAA,CAACoC,iBAAiB,EAAAyE,QAAA,CAAC,iDAEnB,CAAmB,CAAC,cACpB7G,IAAA,CAACH,MAAM,EAACqG,OAAO,CAAEE,iBAAkB,CAAAS,QAAA,CAAC,cAAY,CAAQ,CAAC,EAC/C,CAAC,cAEb3G,KAAA,CAAC0B,UAAU,EAAAiF,QAAA,eACT7G,IAAA,CAAC8B,UAAU,EAAA+E,QAAA,CAAC,cAAE,CAAY,CAAC,cAC3B7G,IAAA,CAACgC,WAAW,EAAA6E,QAAA,CAAC,cAAY,CAAa,CAAC,cACvC7G,IAAA,CAACoC,iBAAiB,EAAAyE,QAAA,CAAC,8CAEnB,CAAmB,CAAC,cACpB7G,IAAA,CAACH,MAAM,EAACqG,OAAO,CAAEG,iBAAkB,CAAAQ,QAAA,CAAC,cAAY,CAAQ,CAAC,EAC/C,CAAC,EACH,CAAC,cAGb3G,KAAA,CAACN,IAAI,EAAAiH,QAAA,eACH7G,IAAA,OAAI8G,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAE5F,KAAK,CAAE,SAAU,CAAE,CAAA0F,QAAA,CAAC,cAAY,CAAI,CAAC,cAExE7G,IAAA,CAACwC,cAAc,EAAAqE,QAAA,cACb3G,KAAA,CAACwC,KAAK,EAAAmE,QAAA,eACJ7G,IAAA,UAAA6G,QAAA,cACE3G,KAAA,OAAA2G,QAAA,eACE7G,IAAA,CAAC6C,WAAW,EAAAgE,QAAA,CAAC,eAAa,CAAa,CAAC,cACxC7G,IAAA,CAAC6C,WAAW,EAAAgE,QAAA,CAAC,QAAM,CAAa,CAAC,cACjC7G,IAAA,CAAC6C,WAAW,EAAAgE,QAAA,CAAC,WAAS,CAAa,CAAC,cACpC7G,IAAA,CAAC6C,WAAW,EAAAgE,QAAA,CAAC,QAAM,CAAa,CAAC,cACjC7G,IAAA,CAAC6C,WAAW,EAAAgE,QAAA,CAAC,cAAY,CAAa,CAAC,cACvC7G,IAAA,CAAC6C,WAAW,EAAAgE,QAAA,CAAC,gBAAc,CAAa,CAAC,cACzC7G,IAAA,CAAC6C,WAAW,EAAAgE,QAAA,CAAC,SAAO,CAAa,CAAC,EAChC,CAAC,CACA,CAAC,cACR7G,IAAA,UAAA6G,QAAA,CACGhD,KAAK,CAACmD,GAAG,CAAEC,IAAI,eACd/G,KAAA,CAACmD,QAAQ,EAAAwD,QAAA,eACP7G,IAAA,CAACkD,SAAS,EAAA2D,QAAA,CAAEI,IAAI,CAAClC,YAAY,CAAY,CAAC,cAC1C/E,IAAA,CAACkD,SAAS,EAAA2D,QAAA,CAAEI,IAAI,CAACjC,YAAY,CAAY,CAAC,cAC1ChF,IAAA,CAACkD,SAAS,EAAA2D,QAAA,CAAEI,IAAI,CAAChC,QAAQ,CAAY,CAAC,cACtCjF,IAAA,CAACkD,SAAS,EAAA2D,QAAA,cACR7G,IAAA,CAACwD,WAAW,EAACG,MAAM,CAAEsD,IAAI,CAACtD,MAAO,CAAAkD,QAAA,CAC9BI,IAAI,CAACtD,MAAM,CAACuD,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CACjC,CAAC,CACL,CAAC,cACZnH,IAAA,CAACkD,SAAS,EAAA2D,QAAA,CAAEL,UAAU,CAACS,IAAI,CAAC/B,WAAW,CAAC,CAAY,CAAC,cACrDlF,IAAA,CAACkD,SAAS,EAAA2D,QAAA,CACPI,IAAI,CAAC3B,cAAc,EAAI,YAAY,CAC3B,CAAC,cACZtF,IAAA,CAACkD,SAAS,EAAA2D,QAAA,cACR7G,IAAA,CAACH,MAAM,EACLuH,IAAI,CAAC,IAAI,CACTlB,OAAO,CAAEA,CAAA,GAAMI,eAAe,CAACW,IAAI,CAACnC,MAAM,CAAE,CAAA+B,QAAA,CAC7C,cAED,CAAQ,CAAC,CACA,CAAC,GApBCI,IAAI,CAACnC,MAqBV,CACX,CAAC,CACG,CAAC,EACH,CAAC,CACM,CAAC,CAEhBjB,KAAK,CAACwD,MAAM,GAAK,CAAC,eACjBrH,IAAA,QAAK8G,KAAK,CAAE,CAAEQ,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEpG,KAAK,CAAE,MAAO,CAAE,CAAA0F,QAAA,CAAC,iBAErE,CAAK,CACN,EACG,CAAC,EACQ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAjD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}