{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import{apiService}from'../services/apiService';// User interface is imported from apiService\nimport{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(undefined);export const useAuth=()=>{const context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth must be used within an AuthProvider');}return context;};export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[token,setToken]=useState(null);const[isLoading,setIsLoading]=useState(true);useEffect(()=>{// Check for stored authentication data on app load\nconst storedToken=localStorage.getItem('authToken');const storedUser=localStorage.getItem('user');if(storedToken&&storedUser){try{const parsedUser=JSON.parse(storedUser);setToken(storedToken);setUser(parsedUser);}catch(error){console.error('Error parsing stored user data:',error);localStorage.removeItem('authToken');localStorage.removeItem('refreshToken');localStorage.removeItem('user');}}setIsLoading(false);},[]);const login=async(username,password,role)=>{try{setIsLoading(true);// Call the API service for authentication\nconst response=await apiService.login(username,password,role);if(response.success&&response.token&&response.user){// Store authentication data\nlocalStorage.setItem('authToken',response.token);localStorage.setItem('refreshToken',response.refreshToken||'');localStorage.setItem('user',JSON.stringify(response.user));setToken(response.token);setUser(response.user);}else{throw new Error(response.message||'Login failed');}}catch(error){console.error('Login error:',error);throw error;}finally{setIsLoading(false);}};const logout=async()=>{try{await apiService.logout();}catch(error){console.error('Logout error:',error);}finally{// Clear local state regardless of API call result\nlocalStorage.removeItem('authToken');localStorage.removeItem('refreshToken');localStorage.removeItem('user');setToken(null);setUser(null);}};const value={user,token,login,logout,isLoading,isAuthenticated:!!user&&!!token};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "apiService", "jsx", "_jsx", "AuthContext", "undefined", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "token", "setToken", "isLoading", "setIsLoading", "storedToken", "localStorage", "getItem", "storedUser", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "username", "password", "role", "response", "success", "setItem", "refreshToken", "stringify", "message", "logout", "value", "isAuthenticated", "Provider"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { apiService, User, LoginResponse } from '../services/apiService';\n\n// User interface is imported from apiService\n\ninterface AuthContextType {\n  user: User | null;\n  token: string | null;\n  login: (username: string, password: string, role: string) => Promise<void>;\n  logout: () => void;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for stored authentication data on app load\n    const storedToken = localStorage.getItem('authToken');\n    const storedUser = localStorage.getItem('user');\n\n    if (storedToken && storedUser) {\n      try {\n        const parsedUser = JSON.parse(storedUser);\n        setToken(storedToken);\n        setUser(parsedUser);\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('user');\n      }\n    }\n\n    setIsLoading(false);\n  }, []);\n\n  const login = async (username: string, password: string, role: string): Promise<void> => {\n    try {\n      setIsLoading(true);\n\n      // Call the API service for authentication\n      const response: LoginResponse = await apiService.login(username, password, role);\n\n      if (response.success && response.token && response.user) {\n        // Store authentication data\n        localStorage.setItem('authToken', response.token);\n        localStorage.setItem('refreshToken', response.refreshToken || '');\n        localStorage.setItem('user', JSON.stringify(response.user));\n\n        setToken(response.token);\n        setUser(response.user);\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await apiService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear local state regardless of API call result\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n      setToken(null);\n      setUser(null);\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    token,\n    login,\n    logout,\n    isLoading,\n    isAuthenticated: !!user && !!token,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAmB,OAAO,CACxF,OAASC,UAAU,KAA6B,wBAAwB,CAExE;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBAWA,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAA8BQ,SAAS,CAAC,CAEzE,MAAO,MAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGT,UAAU,CAACM,WAAW,CAAC,CACvC,GAAIG,OAAO,GAAKF,SAAS,CAAE,CACzB,KAAM,IAAI,CAAAG,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAMD,MAAO,MAAM,CAAAE,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpE,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGd,QAAQ,CAAc,IAAI,CAAC,CACnD,KAAM,CAACe,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAkB,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CACrD,KAAM,CAAAC,UAAU,CAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAE/C,GAAIF,WAAW,EAAIG,UAAU,CAAE,CAC7B,GAAI,CACF,KAAM,CAAAC,UAAU,CAAGC,IAAI,CAACC,KAAK,CAACH,UAAU,CAAC,CACzCN,QAAQ,CAACG,WAAW,CAAC,CACrBL,OAAO,CAACS,UAAU,CAAC,CACrB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDN,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC,CACpCR,YAAY,CAACQ,UAAU,CAAC,cAAc,CAAC,CACvCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC,CACjC,CACF,CAEAV,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,KAAK,CAAG,KAAAA,CAAOC,QAAgB,CAAEC,QAAgB,CAAEC,IAAY,GAAoB,CACvF,GAAI,CACFd,YAAY,CAAC,IAAI,CAAC,CAElB;AACA,KAAM,CAAAe,QAAuB,CAAG,KAAM,CAAA/B,UAAU,CAAC2B,KAAK,CAACC,QAAQ,CAAEC,QAAQ,CAAEC,IAAI,CAAC,CAEhF,GAAIC,QAAQ,CAACC,OAAO,EAAID,QAAQ,CAAClB,KAAK,EAAIkB,QAAQ,CAACpB,IAAI,CAAE,CACvD;AACAO,YAAY,CAACe,OAAO,CAAC,WAAW,CAAEF,QAAQ,CAAClB,KAAK,CAAC,CACjDK,YAAY,CAACe,OAAO,CAAC,cAAc,CAAEF,QAAQ,CAACG,YAAY,EAAI,EAAE,CAAC,CACjEhB,YAAY,CAACe,OAAO,CAAC,MAAM,CAAEX,IAAI,CAACa,SAAS,CAACJ,QAAQ,CAACpB,IAAI,CAAC,CAAC,CAE3DG,QAAQ,CAACiB,QAAQ,CAAClB,KAAK,CAAC,CACxBD,OAAO,CAACmB,QAAQ,CAACpB,IAAI,CAAC,CACxB,CAAC,IAAM,CACL,KAAM,IAAI,CAAAJ,KAAK,CAACwB,QAAQ,CAACK,OAAO,EAAI,cAAc,CAAC,CACrD,CACF,CAAE,MAAOZ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC,KAAM,CAAAA,KAAK,CACb,CAAC,OAAS,CACRR,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAqB,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzB,GAAI,CACF,KAAM,CAAArC,UAAU,CAACqC,MAAM,CAAC,CAAC,CAC3B,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAAC,OAAS,CACR;AACAN,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC,CACpCR,YAAY,CAACQ,UAAU,CAAC,cAAc,CAAC,CACvCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC,CAC/BZ,QAAQ,CAAC,IAAI,CAAC,CACdF,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CAAC,CAED,KAAM,CAAA0B,KAAsB,CAAG,CAC7B3B,IAAI,CACJE,KAAK,CACLc,KAAK,CACLU,MAAM,CACNtB,SAAS,CACTwB,eAAe,CAAE,CAAC,CAAC5B,IAAI,EAAI,CAAC,CAACE,KAC/B,CAAC,CAED,mBACEX,IAAA,CAACC,WAAW,CAACqC,QAAQ,EAACF,KAAK,CAAEA,KAAM,CAAA5B,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}