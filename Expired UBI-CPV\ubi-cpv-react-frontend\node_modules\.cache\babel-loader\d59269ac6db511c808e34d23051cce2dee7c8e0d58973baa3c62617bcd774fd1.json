{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Dashboard\\\\SupervisorDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c = StatsContainer;\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n_c2 = StatCard;\nconst StatIcon = styled.div`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ${props => props.theme.colors.white};\n  background: ${props => props.color};\n`;\n_c3 = StatIcon;\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n_c4 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n_c5 = StatLabel;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c6 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c7 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c8 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c9 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c0 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.status) {\n    case 'pending-review':\n      return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n    case 'approved':\n      return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n    case 'rejected':\n      return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c1 = StatusBadge;\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n_c10 = FilterContainer;\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background-color: ${props => props.theme.colors.white};\n`;\n_c11 = FilterSelect;\nconst SupervisorDashboard = () => {\n  _s();\n  const [leads, setLeads] = useState([]);\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('pending-review');\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadDashboardData();\n  }, [statusFilter]);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [leadsResponse, statsResponse] = await Promise.all([apiService.getLeads(1, 50, statusFilter || undefined), apiService.getSupervisorDashboardStats()]);\n      setLeads(leadsResponse.data || []);\n      setStats(statsResponse);\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Mock data for demo\n      setLeads([{\n        leadId: 1,\n        customerName: 'John Doe',\n        mobileNumber: '9876543210',\n        loanType: 'Personal Loan',\n        status: 'pending-review',\n        createdDate: '2024-01-15T10:30:00Z',\n        assignedToName: 'Agent Smith',\n        createdByName: 'Admin User',\n        documentCount: 3,\n        croppedImageCount: 2\n      }, {\n        leadId: 2,\n        customerName: 'Jane Smith',\n        mobileNumber: '9876543211',\n        loanType: 'Home Loan',\n        status: 'pending-review',\n        createdDate: '2024-01-14T09:15:00Z',\n        assignedToName: 'Agent Johnson',\n        createdByName: 'Admin User',\n        documentCount: 5,\n        croppedImageCount: 3\n      }]);\n      setStats({\n        pendingReviews: 2,\n        approvedToday: 1,\n        rejectedToday: 0,\n        totalReviewed: 10,\n        approvalRate: 85.5,\n        agentPerformance: [{\n          agentId: 1,\n          agentName: 'Agent Smith',\n          assignedCount: 5,\n          completedCount: 4,\n          approvedCount: 3,\n          rejectedCount: 1,\n          completionRate: 80,\n          approvalRate: 75\n        }],\n        totalLeads: 15,\n        completedLeads: 10\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    active: true\n  }, {\n    icon: '👁️',\n    label: 'Review Queue',\n    onClick: () => navigate('/supervisor/review')\n  }, {\n    icon: '✅',\n    label: 'Approved',\n    onClick: () => navigate('/supervisor/approved')\n  }, {\n    icon: '❌',\n    label: 'Rejected',\n    onClick: () => navigate('/supervisor/rejected')\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    onClick: () => navigate('/supervisor/reports')\n  }];\n  const handleLeadClick = leadId => {\n    navigate(`/lead/${leadId}`);\n  };\n  const handleReviewLead = leadId => {\n    navigate(`/supervisor/review/${leadId}`);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"Supervisor Dashboard\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"Supervisor Dashboard\",\n    navigationItems: navigationItems,\n    children: [/*#__PURE__*/_jsxDEV(StatsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #f3e5f5, #4a148c)\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.pendingReviews) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Pending Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #28a745, #1e7e34)\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.approvedToday) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Approved Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #dc3545, #c82333)\",\n          children: \"\\u274C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.rejectedToday) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Rejected Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #007E3A, #005a2a)\",\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: stats !== null && stats !== void 0 && stats.approvalRate ? `${stats.approvalRate.toFixed(1)}%` : '0%'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Approval Rate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '20px',\n          color: '#007E3A'\n        },\n        children: \"Review Queue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterContainer, {\n        children: /*#__PURE__*/_jsxDEV(FilterSelect, {\n          value: statusFilter,\n          onChange: e => setStatusFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"pending-review\",\n            children: \"Pending Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"approved\",\n            children: \"Approved\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"rejected\",\n            children: \"Rejected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Statuses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Customer Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Mobile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Loan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Submitted Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Agent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: leads.map(lead => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.customerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.mobileNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.loanType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                  status: lead.status,\n                  children: lead.status.replace('-', ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.createdDate ? formatDate(lead.createdDate) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.assignedToName || 'Unassigned'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    onClick: () => handleLeadClick(lead.leadId),\n                    children: \"View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this), lead.status === 'pending-review' && /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"secondary\",\n                    onClick: () => handleReviewLead(lead.leadId),\n                    children: \"Review\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this)]\n            }, lead.leadId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), leads.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#777'\n        },\n        children: \"No leads found for the selected status.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n};\n_s(SupervisorDashboard, \"H89BSMtuk7JEUQaBSDdWtGPK4eg=\", false, function () {\n  return [useNavigate];\n});\n_c12 = SupervisorDashboard;\nexport default SupervisorDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"StatsContainer\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"StatIcon\");\n$RefreshReg$(_c4, \"StatValue\");\n$RefreshReg$(_c5, \"StatLabel\");\n$RefreshReg$(_c6, \"TableContainer\");\n$RefreshReg$(_c7, \"Table\");\n$RefreshReg$(_c8, \"TableHeader\");\n$RefreshReg$(_c9, \"TableCell\");\n$RefreshReg$(_c0, \"TableRow\");\n$RefreshReg$(_c1, \"StatusBadge\");\n$RefreshReg$(_c10, \"FilterContainer\");\n$RefreshReg$(_c11, \"FilterSelect\");\n$RefreshReg$(_c12, \"SupervisorDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "apiService", "jsxDEV", "_jsxDEV", "StatsContainer", "div", "_c", "StatCard", "props", "theme", "transitions", "default", "shadows", "lg", "_c2", "StatIcon", "colors", "white", "color", "_c3", "StatValue", "textDark", "_c4", "StatLabel", "textLight", "_c5", "TableContainer", "_c6", "Table", "table", "_c7", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c8", "TableCell", "td", "_c9", "TableRow", "tr", "_c0", "StatusBadge", "span", "status", "_c1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c10", "FilterSelect", "select", "mediumGray", "borderRadius", "sm", "_c11", "SupervisorDashboard", "_s", "leads", "setLeads", "stats", "setStats", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "navigate", "loadDashboardData", "leadsResponse", "statsResponse", "Promise", "all", "getLeads", "undefined", "getSupervisorDashboardStats", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedToName", "createdByName", "documentCount", "croppedImageCount", "pendingReviews", "approvedToday", "<PERSON><PERSON><PERSON><PERSON>", "totalReviewed", "approvalRate", "agentPerformance", "agentId", "<PERSON><PERSON><PERSON>", "assignedCount", "completedCount", "approvedCount", "rejectedCount", "completionRate", "totalLeads", "completedLeads", "navigationItems", "icon", "label", "active", "onClick", "handleLeadClick", "handleReviewLead", "formatDate", "dateString", "Date", "toLocaleDateString", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "style", "marginBottom", "value", "onChange", "e", "target", "map", "lead", "replace", "toUpperCase", "display", "gap", "size", "variant", "length", "textAlign", "padding", "_c12", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Dashboard/SupervisorDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem, SupervisorDashboardStats } from '../../services/apiService';\n\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n\nconst StatIcon = styled.div<{ color: string }>`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ${props => props.theme.colors.white};\n  background: ${props => props.color};\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'approved':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background-color: ${props => props.theme.colors.white};\n`;\n\nconst SupervisorDashboard: React.FC = () => {\n  const [leads, setLeads] = useState<LeadListItem[]>([]);\n  const [stats, setStats] = useState<SupervisorDashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('pending-review');\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [statusFilter]);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [leadsResponse, statsResponse] = await Promise.all([\n        apiService.getLeads(1, 50, statusFilter || undefined),\n        apiService.getSupervisorDashboardStats(),\n      ]);\n\n      setLeads(leadsResponse.data || []);\n      setStats(statsResponse);\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Mock data for demo\n      setLeads([\n        {\n          leadId: 1,\n          customerName: 'John Doe',\n          mobileNumber: '9876543210',\n          loanType: 'Personal Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-15T10:30:00Z',\n          assignedToName: 'Agent Smith',\n          createdByName: 'Admin User',\n          documentCount: 3,\n          croppedImageCount: 2,\n        },\n        {\n          leadId: 2,\n          customerName: 'Jane Smith',\n          mobileNumber: '9876543211',\n          loanType: 'Home Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-14T09:15:00Z',\n          assignedToName: 'Agent Johnson',\n          createdByName: 'Admin User',\n          documentCount: 5,\n          croppedImageCount: 3,\n        },\n      ]);\n      setStats({\n        pendingReviews: 2,\n        approvedToday: 1,\n        rejectedToday: 0,\n        totalReviewed: 10,\n        approvalRate: 85.5,\n        agentPerformance: [\n          {\n            agentId: 1,\n            agentName: 'Agent Smith',\n            assignedCount: 5,\n            completedCount: 4,\n            approvedCount: 3,\n            rejectedCount: 1,\n            completionRate: 80,\n            approvalRate: 75,\n          },\n        ],\n        totalLeads: 15,\n        completedLeads: 10,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', active: true },\n    { icon: '👁️', label: 'Review Queue', onClick: () => navigate('/supervisor/review') },\n    { icon: '✅', label: 'Approved', onClick: () => navigate('/supervisor/approved') },\n    { icon: '❌', label: 'Rejected', onClick: () => navigate('/supervisor/rejected') },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/supervisor/reports') },\n  ];\n\n  const handleLeadClick = (leadId: number) => {\n    navigate(`/lead/${leadId}`);\n  };\n\n  const handleReviewLead = (leadId: number) => {\n    navigate(`/supervisor/review/${leadId}`);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Supervisor Dashboard\" navigationItems={navigationItems}>\n        <div>Loading...</div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Supervisor Dashboard\" navigationItems={navigationItems}>\n      {/* Stats Cards */}\n      <StatsContainer>\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #f3e5f5, #4a148c)\">⏳</StatIcon>\n          <StatValue>{stats?.pendingReviews || 0}</StatValue>\n          <StatLabel>Pending Review</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #28a745, #1e7e34)\">✅</StatIcon>\n          <StatValue>{stats?.approvedToday || 0}</StatValue>\n          <StatLabel>Approved Today</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #dc3545, #c82333)\">❌</StatIcon>\n          <StatValue>{stats?.rejectedToday || 0}</StatValue>\n          <StatLabel>Rejected Today</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #007E3A, #005a2a)\">📊</StatIcon>\n          <StatValue>{stats?.approvalRate ? `${stats.approvalRate.toFixed(1)}%` : '0%'}</StatValue>\n          <StatLabel>Approval Rate</StatLabel>\n        </StatCard>\n      </StatsContainer>\n\n      {/* Review Queue */}\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Review Queue</h2>\n\n        <FilterContainer>\n          <FilterSelect\n            value={statusFilter}\n            onChange={(e) => setStatusFilter(e.target.value)}\n          >\n            <option value=\"pending-review\">Pending Review</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"rejected\">Rejected</option>\n            <option value=\"\">All Statuses</option>\n          </FilterSelect>\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer Name</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Submitted Date</TableHeader>\n                <TableHeader>Agent</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {leads.map((lead) => (\n                <TableRow key={lead.leadId}>\n                  <TableCell>{lead.customerName}</TableCell>\n                  <TableCell>{lead.mobileNumber}</TableCell>\n                  <TableCell>{lead.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={lead.status}>\n                      {lead.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    {lead.createdDate ? formatDate(lead.createdDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    {lead.assignedToName || 'Unassigned'}\n                  </TableCell>\n                  <TableCell>\n                    <div style={{ display: 'flex', gap: '8px' }}>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleLeadClick(lead.leadId)}\n                      >\n                        View\n                      </Button>\n                      {lead.status === 'pending-review' && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"secondary\"\n                          onClick={() => handleReviewLead(lead.leadId)}\n                        >\n                          Review\n                        </Button>\n                      )}\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {leads.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            No leads found for the selected status.\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default SupervisorDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,QAAwB,2BAA2B;AACxE,SAASC,UAAU,QAAgD,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/F,MAAMC,cAAc,GAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,cAAc;AAOpB,MAAMG,QAAQ,GAAGV,MAAM,CAACE,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA,gBAAgBS,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,WAAW,CAACC,OAAO;AACxD;AACA;AACA;AACA,kBAAkBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,OAAO,CAACC,EAAE;AACjD;AACA,CAAC;AAACC,GAAA,GAXIP,QAAQ;AAad,MAAMQ,QAAQ,GAAGlB,MAAM,CAACQ,GAAsB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACC,KAAK;AAC5C,gBAAgBT,KAAK,IAAIA,KAAK,CAACU,KAAK;AACpC,CAAC;AAACC,GAAA,GAXIJ,QAAQ;AAad,MAAMK,SAAS,GAAGvB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACK,QAAQ;AAC/C,CAAC;AAACC,GAAA,GALIF,SAAS;AAOf,MAAMG,SAAS,GAAG1B,MAAM,CAACQ,GAAG;AAC5B;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACQ,SAAS;AAChD;AACA,CAAC;AAACC,GAAA,GAJIF,SAAS;AAMf,MAAMG,cAAc,GAAG7B,MAAM,CAACQ,GAAG;AACjC;AACA,CAAC;AAACsB,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAG/B,MAAM,CAACgC,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAGlC,MAAM,CAACmC,EAAE;AAC7B;AACA;AACA,6BAA6BxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACiB,SAAS;AAClE,sBAAsBzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACkB,QAAQ;AAC1D;AACA,WAAW1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACmB,UAAU;AACjD,CAAC;AAACC,GAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAGxC,MAAM,CAACyC,EAAE;AAC3B;AACA;AACA,6BAA6B9B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACiB,SAAS;AAClE,CAAC;AAACM,GAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAG3C,MAAM,CAAC4C,EAAE;AAC1B;AACA,wBAAwBjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACiB,SAAS;AAC7D;AACA,CAAC;AAACS,GAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAG9C,MAAM,CAAC+C,IAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIpC,KAAK,IAAI;EACT,QAAQA,KAAK,CAACqC,MAAM;IAClB,KAAK,gBAAgB;MACnB,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA/BIH,WAAW;AAiCjB,MAAMI,eAAe,GAAGlD,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GALID,eAAe;AAOrB,MAAME,YAAY,GAAGpD,MAAM,CAACqD,MAAM;AAClC;AACA,sBAAsB1C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACmC,UAAU;AAC5D,mBAAmB3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC2C,YAAY,CAACC,EAAE;AACvD;AACA,sBAAsB7C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACC,KAAK;AACvD,CAAC;AAACqC,IAAA,GANIL,YAAY;AAQlB,MAAMM,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACiE,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAkC,IAAI,CAAC;EACzE,MAAM,CAACmE,OAAO,EAAEC,UAAU,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,gBAAgB,CAAC;EAClE,MAAMuE,QAAQ,GAAGrE,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACduE,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACH,YAAY,CAAC,CAAC;EAElB,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACK,aAAa,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACvDrE,UAAU,CAACsE,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAER,YAAY,IAAIS,SAAS,CAAC,EACrDvE,UAAU,CAACwE,2BAA2B,CAAC,CAAC,CACzC,CAAC;MAEFf,QAAQ,CAACS,aAAa,CAACO,IAAI,IAAI,EAAE,CAAC;MAClCd,QAAQ,CAACQ,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACAjB,QAAQ,CAAC,CACP;QACEmB,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,UAAU;QACxBC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,eAAe;QACzBnC,MAAM,EAAE,gBAAgB;QACxBoC,WAAW,EAAE,sBAAsB;QACnCC,cAAc,EAAE,aAAa;QAC7BC,aAAa,EAAE,YAAY;QAC3BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACER,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,YAAY;QAC1BC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,WAAW;QACrBnC,MAAM,EAAE,gBAAgB;QACxBoC,WAAW,EAAE,sBAAsB;QACnCC,cAAc,EAAE,eAAe;QAC/BC,aAAa,EAAE,YAAY;QAC3BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,CACF,CAAC;MACFzB,QAAQ,CAAC;QACP0B,cAAc,EAAE,CAAC;QACjBC,aAAa,EAAE,CAAC;QAChBC,aAAa,EAAE,CAAC;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,IAAI;QAClBC,gBAAgB,EAAE,CAChB;UACEC,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,aAAa;UACxBC,aAAa,EAAE,CAAC;UAChBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,CAAC;UAChBC,aAAa,EAAE,CAAC;UAChBC,cAAc,EAAE,EAAE;UAClBR,YAAY,EAAE;QAChB,CAAC,CACF;QACDS,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,MAAM,EAAE;EAAK,CAAC,EAChD;IAAEF,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE,cAAc;IAAEE,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAC,oBAAoB;EAAE,CAAC,EACrF;IAAEqC,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,UAAU;IAAEE,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAC,sBAAsB;EAAE,CAAC,EACjF;IAAEqC,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,UAAU;IAAEE,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAC,sBAAsB;EAAE,CAAC,EACjF;IAAEqC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEE,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAAC,qBAAqB;EAAE,CAAC,CACjF;EAED,MAAMyC,eAAe,GAAI7B,MAAc,IAAK;IAC1CZ,QAAQ,CAAC,SAASY,MAAM,EAAE,CAAC;EAC7B,CAAC;EAED,MAAM8B,gBAAgB,GAAI9B,MAAc,IAAK;IAC3CZ,QAAQ,CAAC,sBAAsBY,MAAM,EAAE,CAAC;EAC1C,CAAC;EAED,MAAM+B,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAIlD,OAAO,EAAE;IACX,oBACE1D,OAAA,CAACL,eAAe;MAACkH,KAAK,EAAC,sBAAsB;MAACX,eAAe,EAAEA,eAAgB;MAAAY,QAAA,eAC7E9G,OAAA;QAAA8G,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEtB;EAEA,oBACElH,OAAA,CAACL,eAAe;IAACkH,KAAK,EAAC,sBAAsB;IAACX,eAAe,EAAEA,eAAgB;IAAAY,QAAA,gBAE7E9G,OAAA,CAACC,cAAc;MAAA6G,QAAA,gBACb9G,OAAA,CAACI,QAAQ;QAAA0G,QAAA,gBACP9G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA+F,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxElH,OAAA,CAACiB,SAAS;UAAA6F,QAAA,EAAE,CAAAtD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2B,cAAc,KAAI;QAAC;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACnDlH,OAAA,CAACoB,SAAS;UAAA0F,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEXlH,OAAA,CAACI,QAAQ;QAAA0G,QAAA,gBACP9G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA+F,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxElH,OAAA,CAACiB,SAAS;UAAA6F,QAAA,EAAE,CAAAtD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4B,aAAa,KAAI;QAAC;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClDlH,OAAA,CAACoB,SAAS;UAAA0F,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEXlH,OAAA,CAACI,QAAQ;QAAA0G,QAAA,gBACP9G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA+F,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxElH,OAAA,CAACiB,SAAS;UAAA6F,QAAA,EAAE,CAAAtD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6B,aAAa,KAAI;QAAC;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClDlH,OAAA,CAACoB,SAAS;UAAA0F,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEXlH,OAAA,CAACI,QAAQ;QAAA0G,QAAA,gBACP9G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA+F,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACzElH,OAAA,CAACiB,SAAS;UAAA6F,QAAA,EAAEtD,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE+B,YAAY,GAAG,GAAG/B,KAAK,CAAC+B,YAAY,CAAC4B,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACzFlH,OAAA,CAACoB,SAAS;UAAA0F,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGjBlH,OAAA,CAACJ,IAAI;MAAAkH,QAAA,gBACH9G,OAAA;QAAIoH,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEtG,KAAK,EAAE;QAAU,CAAE;QAAA+F,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAExElH,OAAA,CAAC4C,eAAe;QAAAkE,QAAA,eACd9G,OAAA,CAAC8C,YAAY;UACXwE,KAAK,EAAE1D,YAAa;UACpB2D,QAAQ,EAAGC,CAAC,IAAK3D,eAAe,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAR,QAAA,gBAEjD9G,OAAA;YAAQsH,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtDlH,OAAA;YAAQsH,KAAK,EAAC,UAAU;YAAAR,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClH,OAAA;YAAQsH,KAAK,EAAC,UAAU;YAAAR,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClH,OAAA;YAAQsH,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAElBlH,OAAA,CAACuB,cAAc;QAAAuF,QAAA,eACb9G,OAAA,CAACyB,KAAK;UAAAqF,QAAA,gBACJ9G,OAAA;YAAA8G,QAAA,eACE9G,OAAA;cAAA8G,QAAA,gBACE9G,OAAA,CAAC4B,WAAW;gBAAAkF,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxClH,OAAA,CAAC4B,WAAW;gBAAAkF,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjClH,OAAA,CAAC4B,WAAW;gBAAAkF,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpClH,OAAA,CAAC4B,WAAW;gBAAAkF,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjClH,OAAA,CAAC4B,WAAW;gBAAAkF,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzClH,OAAA,CAAC4B,WAAW;gBAAAkF,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChClH,OAAA,CAAC4B,WAAW;gBAAAkF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRlH,OAAA;YAAA8G,QAAA,EACGxD,KAAK,CAACoE,GAAG,CAAEC,IAAI,iBACd3H,OAAA,CAACqC,QAAQ;cAAAyE,QAAA,gBACP9G,OAAA,CAACkC,SAAS;gBAAA4E,QAAA,EAAEa,IAAI,CAAChD;cAAY;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1ClH,OAAA,CAACkC,SAAS;gBAAA4E,QAAA,EAAEa,IAAI,CAAC/C;cAAY;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1ClH,OAAA,CAACkC,SAAS;gBAAA4E,QAAA,EAAEa,IAAI,CAAC9C;cAAQ;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtClH,OAAA,CAACkC,SAAS;gBAAA4E,QAAA,eACR9G,OAAA,CAACwC,WAAW;kBAACE,MAAM,EAAEiF,IAAI,CAACjF,MAAO;kBAAAoE,QAAA,EAC9Ba,IAAI,CAACjF,MAAM,CAACkF,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACZlH,OAAA,CAACkC,SAAS;gBAAA4E,QAAA,EACPa,IAAI,CAAC7C,WAAW,GAAG2B,UAAU,CAACkB,IAAI,CAAC7C,WAAW,CAAC,GAAG;cAAG;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACZlH,OAAA,CAACkC,SAAS;gBAAA4E,QAAA,EACPa,IAAI,CAAC5C,cAAc,IAAI;cAAY;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACZlH,OAAA,CAACkC,SAAS;gBAAA4E,QAAA,eACR9G,OAAA;kBAAKoH,KAAK,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAM,CAAE;kBAAAjB,QAAA,gBAC1C9G,OAAA,CAACH,MAAM;oBACLmI,IAAI,EAAC,IAAI;oBACT1B,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACoB,IAAI,CAACjD,MAAM,CAAE;oBAAAoC,QAAA,EAC7C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACRS,IAAI,CAACjF,MAAM,KAAK,gBAAgB,iBAC/B1C,OAAA,CAACH,MAAM;oBACLmI,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAC,WAAW;oBACnB3B,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAACmB,IAAI,CAACjD,MAAM,CAAE;oBAAAoC,QAAA,EAC9C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GAjCCS,IAAI,CAACjD,MAAM;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkChB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhB5D,KAAK,CAAC4E,MAAM,KAAK,CAAC,iBACjBlI,OAAA;QAAKoH,KAAK,EAAE;UAAEe,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAErH,KAAK,EAAE;QAAO,CAAE;QAAA+F,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEtB,CAAC;AAAC7D,EAAA,CApNID,mBAA6B;EAAA,QAKhB3D,WAAW;AAAA;AAAA4I,IAAA,GALxBjF,mBAA6B;AAsNnC,eAAeA,mBAAmB;AAAC,IAAAjD,EAAA,EAAAQ,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,IAAA,EAAAM,IAAA,EAAAkF,IAAA;AAAAC,YAAA,CAAAnI,EAAA;AAAAmI,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}