{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{AuthProvider}from'./contexts/AuthContext';import{ThemeProvider}from'styled-components';import{GlobalStyles,theme}from'./styles/GlobalStyles';import Login from'./components/Auth/Login';import ProtectedRoute from'./components/Auth/ProtectedRoute';import AgentDashboard from'./components/Dashboard/AgentDashboard';import SupervisorDashboard from'./components/Dashboard/SupervisorDashboard';import AdminDashboard from'./components/Dashboard/AdminDashboard';import LeadDetails from'./components/Leads/LeadDetails';import VerificationForm from'./components/Verification/VerificationForm';import DocumentUpload from'./components/Documents/DocumentUpload';import CreateLead from'./components/Admin/CreateLead';import UserManagement from'./components/Admin/UserManagement';import AgentTasks from'./components/Agent/AgentTasks';import AgentCompleted from'./components/Agent/AgentCompleted';import AgentReports from'./components/Agent/AgentReports';import SupervisorReview from'./components/Supervisor/SupervisorReview';import SupervisorReports from'./components/Supervisor/SupervisorReports';import AdminReports from'./components/Admin/AdminReports';import LeadsList from'./components/Leads/LeadsList';import UserProfile from'./components/Profile/UserProfile';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(GlobalStyles,{}),/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent'],children:/*#__PURE__*/_jsx(AgentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/tasks\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent'],children:/*#__PURE__*/_jsx(AgentTasks,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/completed\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent'],children:/*#__PURE__*/_jsx(AgentCompleted,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/reports\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent'],children:/*#__PURE__*/_jsx(AgentReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/supervisor/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Supervisor'],children:/*#__PURE__*/_jsx(SupervisorDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/supervisor/review\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Supervisor'],children:/*#__PURE__*/_jsx(SupervisorReview,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/supervisor/reports\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Supervisor'],children:/*#__PURE__*/_jsx(SupervisorReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Admin'],children:/*#__PURE__*/_jsx(AdminDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/create-lead\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Admin'],children:/*#__PURE__*/_jsx(CreateLead,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/users\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Admin'],children:/*#__PURE__*/_jsx(UserManagement,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/leads\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Admin'],children:/*#__PURE__*/_jsx(LeadsList,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/reports\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Admin'],children:/*#__PURE__*/_jsx(AdminReports,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/lead/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent','Supervisor','Admin'],children:/*#__PURE__*/_jsx(LeadDetails,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/lead/:id/verification\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent'],children:/*#__PURE__*/_jsx(VerificationForm,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/lead/:id/documents\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent'],children:/*#__PURE__*/_jsx(DocumentUpload,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Agent','Supervisor','Admin'],children:/*#__PURE__*/_jsx(UserProfile,{})})})]})})})]});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "GlobalStyles", "theme", "<PERSON><PERSON>", "ProtectedRoute", "AgentDashboard", "SupervisorDashboard", "AdminDashboard", "LeadDetails", "VerificationForm", "DocumentUpload", "CreateLead", "UserManagement", "AgentTasks", "AgentCompleted", "AgentReports", "SupervisorReview", "SupervisorReports", "AdminReports", "LeadsList", "UserProfile", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element", "to", "replace", "allowedRoles"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from 'styled-components';\nimport { GlobalStyles, theme } from './styles/GlobalStyles';\nimport Login from './components/Auth/Login';\nimport ProtectedRoute from './components/Auth/ProtectedRoute';\nimport AgentDashboard from './components/Dashboard/AgentDashboard';\nimport SupervisorDashboard from './components/Dashboard/SupervisorDashboard';\nimport AdminDashboard from './components/Dashboard/AdminDashboard';\nimport LeadDetails from './components/Leads/LeadDetails';\nimport VerificationForm from './components/Verification/VerificationForm';\nimport DocumentUpload from './components/Documents/DocumentUpload';\nimport CreateLead from './components/Admin/CreateLead';\nimport UserManagement from './components/Admin/UserManagement';\nimport AgentTasks from './components/Agent/AgentTasks';\nimport AgentCompleted from './components/Agent/AgentCompleted';\nimport AgentReports from './components/Agent/AgentReports';\nimport SupervisorReview from './components/Supervisor/SupervisorReview';\nimport SupervisorReports from './components/Supervisor/SupervisorReports';\nimport AdminReports from './components/Admin/AdminReports';\nimport LeadsList from './components/Leads/LeadsList';\nimport UserProfile from './components/Profile/UserProfile';\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <GlobalStyles />\n      <AuthProvider>\n        <Router>\n          <Routes>\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/\" element={<Navigate to=\"/login\" replace />} />\n\n            {/* Agent Routes */}\n            <Route\n              path=\"/agent/dashboard\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent']}>\n                  <AgentDashboard />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/agent/tasks\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent']}>\n                  <AgentTasks />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/agent/completed\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent']}>\n                  <AgentCompleted />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/agent/reports\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent']}>\n                  <AgentReports />\n                </ProtectedRoute>\n              }\n            />\n\n            {/* Supervisor Routes */}\n            <Route\n              path=\"/supervisor/dashboard\"\n              element={\n                <ProtectedRoute allowedRoles={['Supervisor']}>\n                  <SupervisorDashboard />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/supervisor/review\"\n              element={\n                <ProtectedRoute allowedRoles={['Supervisor']}>\n                  <SupervisorReview />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/supervisor/reports\"\n              element={\n                <ProtectedRoute allowedRoles={['Supervisor']}>\n                  <SupervisorReports />\n                </ProtectedRoute>\n              }\n            />\n\n            {/* Admin Routes */}\n            <Route\n              path=\"/admin/dashboard\"\n              element={\n                <ProtectedRoute allowedRoles={['Admin']}>\n                  <AdminDashboard />\n                </ProtectedRoute>\n              }\n            />\n\n            <Route\n              path=\"/admin/create-lead\"\n              element={\n                <ProtectedRoute allowedRoles={['Admin']}>\n                  <CreateLead />\n                </ProtectedRoute>\n              }\n            />\n\n            <Route\n              path=\"/admin/users\"\n              element={\n                <ProtectedRoute allowedRoles={['Admin']}>\n                  <UserManagement />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/admin/leads\"\n              element={\n                <ProtectedRoute allowedRoles={['Admin']}>\n                  <LeadsList />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/admin/reports\"\n              element={\n                <ProtectedRoute allowedRoles={['Admin']}>\n                  <AdminReports />\n                </ProtectedRoute>\n              }\n            />\n\n            {/* Shared Routes */}\n            <Route\n              path=\"/lead/:id\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent', 'Supervisor', 'Admin']}>\n                  <LeadDetails />\n                </ProtectedRoute>\n              }\n            />\n\n            <Route\n              path=\"/lead/:id/verification\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent']}>\n                  <VerificationForm />\n                </ProtectedRoute>\n              }\n            />\n\n            <Route\n              path=\"/lead/:id/documents\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent']}>\n                  <DocumentUpload />\n                </ProtectedRoute>\n              }\n            />\n\n            {/* Profile Route */}\n            <Route\n              path=\"/profile\"\n              element={\n                <ProtectedRoute allowedRoles={['Agent', 'Supervisor', 'Admin']}>\n                  <UserProfile />\n                </ProtectedRoute>\n              }\n            />\n          </Routes>\n        </Router>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,YAAY,KAAQ,wBAAwB,CACrD,OAASC,aAAa,KAAQ,mBAAmB,CACjD,OAASC,YAAY,CAAEC,KAAK,KAAQ,uBAAuB,CAC3D,MAAO,CAAAC,KAAK,KAAM,yBAAyB,CAC3C,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,cAAc,KAAM,uCAAuC,CAClE,MAAO,CAAAC,mBAAmB,KAAM,4CAA4C,CAC5E,MAAO,CAAAC,cAAc,KAAM,uCAAuC,CAClE,MAAO,CAAAC,WAAW,KAAM,gCAAgC,CACxD,MAAO,CAAAC,gBAAgB,KAAM,4CAA4C,CACzE,MAAO,CAAAC,cAAc,KAAM,uCAAuC,CAClE,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,MAAO,CAAAC,cAAc,KAAM,mCAAmC,CAC9D,MAAO,CAAAC,UAAU,KAAM,+BAA+B,CACtD,MAAO,CAAAC,cAAc,KAAM,mCAAmC,CAC9D,MAAO,CAAAC,YAAY,KAAM,iCAAiC,CAC1D,MAAO,CAAAC,gBAAgB,KAAM,0CAA0C,CACvE,MAAO,CAAAC,iBAAiB,KAAM,2CAA2C,CACzE,MAAO,CAAAC,YAAY,KAAM,iCAAiC,CAC1D,MAAO,CAAAC,SAAS,KAAM,8BAA8B,CACpD,MAAO,CAAAC,WAAW,KAAM,kCAAkC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACED,KAAA,CAACxB,aAAa,EAACE,KAAK,CAAEA,KAAM,CAAAwB,QAAA,eAC1BJ,IAAA,CAACrB,YAAY,GAAE,CAAC,cAChBqB,IAAA,CAACvB,YAAY,EAAA2B,QAAA,cACXJ,IAAA,CAAC3B,MAAM,EAAA+B,QAAA,cACLF,KAAA,CAAC5B,MAAM,EAAA8B,QAAA,eACLJ,IAAA,CAACzB,KAAK,EAAC8B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACnB,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CmB,IAAA,CAACzB,KAAK,EAAC8B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEN,IAAA,CAACxB,QAAQ,EAAC+B,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAG7DR,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACjB,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cACFiB,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,cAAc,CACnBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACT,UAAU,GAAE,CAAC,CACA,CACjB,CACF,CAAC,cACFS,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACR,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cACFQ,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,gBAAgB,CACrBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACP,YAAY,GAAE,CAAC,CACF,CACjB,CACF,CAAC,cAGFO,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,uBAAuB,CAC5BC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAL,QAAA,cAC3CJ,IAAA,CAAChB,mBAAmB,GAAE,CAAC,CACT,CACjB,CACF,CAAC,cACFgB,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,oBAAoB,CACzBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAL,QAAA,cAC3CJ,IAAA,CAACN,gBAAgB,GAAE,CAAC,CACN,CACjB,CACF,CAAC,cACFM,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,qBAAqB,CAC1BC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAL,QAAA,cAC3CJ,IAAA,CAACL,iBAAiB,GAAE,CAAC,CACP,CACjB,CACF,CAAC,cAGFK,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACf,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cAEFe,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,oBAAoB,CACzBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACX,UAAU,GAAE,CAAC,CACA,CACjB,CACF,CAAC,cAEFW,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,cAAc,CACnBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACV,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cACFU,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,cAAc,CACnBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACH,SAAS,GAAE,CAAC,CACC,CACjB,CACF,CAAC,cACFG,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,gBAAgB,CACrBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACJ,YAAY,GAAE,CAAC,CACF,CACjB,CACF,CAAC,cAGFI,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,WAAW,CAChBC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,YAAY,CAAE,OAAO,CAAE,CAAAL,QAAA,cAC7DJ,IAAA,CAACd,WAAW,GAAE,CAAC,CACD,CACjB,CACF,CAAC,cAEFc,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,wBAAwB,CAC7BC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACb,gBAAgB,GAAE,CAAC,CACN,CACjB,CACF,CAAC,cAEFa,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,qBAAqB,CAC1BC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAL,QAAA,cACtCJ,IAAA,CAACZ,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cAGFY,IAAA,CAACzB,KAAK,EACJ8B,IAAI,CAAC,UAAU,CACfC,OAAO,cACLN,IAAA,CAAClB,cAAc,EAAC2B,YAAY,CAAE,CAAC,OAAO,CAAE,YAAY,CAAE,OAAO,CAAE,CAAAL,QAAA,cAC7DJ,IAAA,CAACF,WAAW,GAAE,CAAC,CACD,CACjB,CACF,CAAC,EACI,CAAC,CACH,CAAC,CACG,CAAC,EACF,CAAC,CAEpB,CAEA,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}