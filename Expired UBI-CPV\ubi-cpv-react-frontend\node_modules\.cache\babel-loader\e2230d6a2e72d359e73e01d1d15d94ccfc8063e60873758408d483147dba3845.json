{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Supervisor\\\\SupervisorReports.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n_c = ReportsContainer;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c2 = StatsGrid;\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n_c3 = StatCard;\nconst StatValue = styled.div`\n  font-size: 28px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n_c4 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n_c5 = StatLabel;\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c6 = ChartContainer;\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n_c7 = ChartCard;\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n_c8 = ChartTitle;\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n_c9 = FilterContainer;\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n\n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c0 = FilterSelect;\nconst TeamPerformanceTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c1 = TeamPerformanceTable;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c10 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c11 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c12 = TableRow;\nconst PerformanceBar = styled.div`\n  width: 100%;\n  height: 15px;\n  background-color: #f0f0f0;\n  border-radius: 8px;\n  overflow: hidden;\n\n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.percentage}%;\n    height: 100%;\n    background-color: ${props => props.color};\n    transition: width 0.3s ease;\n  }\n`;\n_c13 = PerformanceBar;\nconst SupervisorReports = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('month');\n  const [teamStats, setTeamStats] = useState({\n    totalAgents: 0,\n    totalTasks: 0,\n    completedTasks: 0,\n    pendingReviews: 0,\n    averageTime: 0,\n    teamEfficiency: 0\n  });\n  const [agentPerformance, setAgentPerformance] = useState([]);\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n      const [dashboardStats] = await Promise.all([apiService.getSupervisorDashboardStats()]);\n      setTeamStats({\n        totalAgents: 5,\n        // Mock data\n        totalTasks: dashboardStats.totalLeads || 0,\n        completedTasks: dashboardStats.completedLeads || 0,\n        pendingReviews: dashboardStats.pendingReviews || 0,\n        averageTime: 2.8,\n        // Mock data\n        teamEfficiency: 82 // Mock data\n      });\n\n      // Mock agent performance data\n      setAgentPerformance([{\n        agentName: 'John Agent',\n        tasksAssigned: 15,\n        tasksCompleted: 12,\n        approvalRate: 90,\n        avgTime: 2.5,\n        efficiency: 85\n      }, {\n        agentName: 'Jane Agent',\n        tasksAssigned: 18,\n        tasksCompleted: 16,\n        approvalRate: 95,\n        avgTime: 2.2,\n        efficiency: 92\n      }, {\n        agentName: 'Bob Agent',\n        tasksAssigned: 12,\n        tasksCompleted: 10,\n        approvalRate: 80,\n        avgTime: 3.1,\n        efficiency: 75\n      }]);\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data on error\n      setTeamStats({\n        totalAgents: 5,\n        totalTasks: 45,\n        completedTasks: 38,\n        pendingReviews: 7,\n        averageTime: 2.8,\n        teamEfficiency: 82\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    onClick: () => navigate('/supervisor/dashboard')\n  }, {\n    icon: '👁️',\n    label: 'Review Queue',\n    onClick: () => navigate('/supervisor/review')\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    active: true\n  }, {\n    icon: '👥',\n    label: 'Team',\n    onClick: () => navigate('/supervisor/team')\n  }];\n  const getCompletionRate = () => {\n    return teamStats.totalTasks > 0 ? Math.round(teamStats.completedTasks / teamStats.totalTasks * 100) : 0;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"Team Reports\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"Team Reports\",\n    navigationItems: navigationItems,\n    children: /*#__PURE__*/_jsxDEV(ReportsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(FilterContainer, {\n        children: [/*#__PURE__*/_jsxDEV(FilterSelect, {\n          value: timeFilter,\n          onChange: e => setTimeFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"week\",\n            children: \"This Week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"month\",\n            children: \"This Month\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"quarter\",\n            children: \"This Quarter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"year\",\n            children: \"This Year\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => window.print(),\n          children: \"\\uD83D\\uDCC4 Export Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: teamStats.totalAgents\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Team Members\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: teamStats.totalTasks\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Total Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: teamStats.completedTasks\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: teamStats.pendingReviews\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Pending Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: teamStats.averageTime\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Avg. Days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: [teamStats.teamEfficiency, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Team Efficiency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChartContainer, {\n        children: [/*#__PURE__*/_jsxDEV(ChartCard, {\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: \"Team Performance Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Completion Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [getCompletionRate(), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PerformanceBar, {\n              percentage: getCompletionRate(),\n              color: \"#2e7d32\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Team Efficiency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [teamStats.teamEfficiency, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PerformanceBar, {\n              percentage: teamStats.teamEfficiency,\n              color: \"#007E3A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Quality Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"88%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PerformanceBar, {\n              percentage: 88,\n              color: \"#FFD100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ChartCard, {\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: \"Task Distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '15px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '24px',\n                  fontWeight: 'bold',\n                  color: '#2e7d32'\n                },\n                children: teamStats.completedTasks\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666'\n                },\n                children: \"Completed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '24px',\n                  fontWeight: 'bold',\n                  color: '#4a148c'\n                },\n                children: teamStats.pendingReviews\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666'\n                },\n                children: \"Pending\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '24px',\n                  fontWeight: 'bold',\n                  color: '#ff8f00'\n                },\n                children: teamStats.totalTasks - teamStats.completedTasks - teamStats.pendingReviews\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666'\n                },\n                children: \"In Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '24px',\n                  fontWeight: 'bold',\n                  color: '#007E3A'\n                },\n                children: teamStats.totalTasks\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666'\n                },\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Agent Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowX: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(TeamPerformanceTable, {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Agent Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Tasks Assigned\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Completion Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Approval Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Avg. Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Efficiency\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: agentPerformance.map((agent, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  style: {\n                    fontWeight: '500'\n                  },\n                  children: agent.agentName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: agent.tasksAssigned\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: agent.tasksCompleted\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [Math.round(agent.tasksCompleted / agent.tasksAssigned * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [agent.approvalRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [agent.avgTime, \" days\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '10px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [agent.efficiency, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1,\n                        minWidth: '60px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(PerformanceBar, {\n                        percentage: agent.efficiency,\n                        color: agent.efficiency >= 85 ? '#2e7d32' : agent.efficiency >= 70 ? '#ff8f00' : '#c62828'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Key Insights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n            gap: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#2e7d32',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83C\\uDFAF Top Performer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Jane Agent leads with 95% approval rate and 92% efficiency\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#ff8f00',\n                marginBottom: '10px'\n              },\n              children: \"\\u26A0\\uFE0F Needs Attention\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Bob Agent requires support to improve efficiency from 75% to team average\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#007E3A',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83D\\uDCC8 Team Trend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Overall team performance improved by 8% compared to last month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(SupervisorReports, \"RJvlXBmOeWJ/k9vryp9C0u2nYZE=\", false, function () {\n  return [useNavigate];\n});\n_c14 = SupervisorReports;\nexport default SupervisorReports;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"ReportsContainer\");\n$RefreshReg$(_c2, \"StatsGrid\");\n$RefreshReg$(_c3, \"StatCard\");\n$RefreshReg$(_c4, \"StatValue\");\n$RefreshReg$(_c5, \"StatLabel\");\n$RefreshReg$(_c6, \"ChartContainer\");\n$RefreshReg$(_c7, \"ChartCard\");\n$RefreshReg$(_c8, \"ChartTitle\");\n$RefreshReg$(_c9, \"FilterContainer\");\n$RefreshReg$(_c0, \"FilterSelect\");\n$RefreshReg$(_c1, \"TeamPerformanceTable\");\n$RefreshReg$(_c10, \"TableHeader\");\n$RefreshReg$(_c11, \"TableCell\");\n$RefreshReg$(_c12, \"TableRow\");\n$RefreshReg$(_c13, \"PerformanceBar\");\n$RefreshReg$(_c14, \"SupervisorReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsxDEV", "_jsxDEV", "ReportsContainer", "div", "_c", "StatsGrid", "_c2", "StatCard", "_c3", "StatValue", "_c4", "StatLabel", "_c5", "ChartContainer", "_c6", "ChartCard", "_c7", "ChartTitle", "h3", "_c8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c9", "FilterSelect", "select", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "_c0", "TeamPerformanceTable", "table", "_c1", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c10", "TableCell", "td", "_c11", "TableRow", "tr", "_c12", "PerformanceBar", "percentage", "color", "_c13", "SupervisorReports", "_s", "loading", "setLoading", "timeFilter", "setTimeFilter", "teamStats", "setTeamStats", "totalAgents", "totalTasks", "completedTasks", "pendingReviews", "averageTime", "teamEfficiency", "agentPerformance", "setAgentPerformance", "navigate", "loadReportsData", "dashboardStats", "Promise", "all", "getSupervisorDashboardStats", "totalLeads", "completedLeads", "<PERSON><PERSON><PERSON>", "tasksAssigned", "tasksCompleted", "approvalRate", "avgTime", "efficiency", "error", "console", "navigationItems", "icon", "label", "onClick", "active", "getCompletionRate", "Math", "round", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "target", "variant", "window", "print", "style", "marginBottom", "display", "justifyContent", "gridTemplateColumns", "gap", "textAlign", "fontSize", "fontWeight", "overflowX", "map", "agent", "index", "alignItems", "flex", "min<PERSON><PERSON><PERSON>", "_c14", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Supervisor/SupervisorReports.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\n\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n\nconst StatValue = styled.div`\n  font-size: 28px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n\n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TeamPerformanceTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst PerformanceBar = styled.div<{ percentage: number; color: string }>`\n  width: 100%;\n  height: 15px;\n  background-color: #f0f0f0;\n  border-radius: 8px;\n  overflow: hidden;\n\n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.percentage}%;\n    height: 100%;\n    background-color: ${props => props.color};\n    transition: width 0.3s ease;\n  }\n`;\n\nconst SupervisorReports: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('month');\n  const [teamStats, setTeamStats] = useState({\n    totalAgents: 0,\n    totalTasks: 0,\n    completedTasks: 0,\n    pendingReviews: 0,\n    averageTime: 0,\n    teamEfficiency: 0,\n  });\n  const [agentPerformance, setAgentPerformance] = useState<any[]>([]);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n\n      const [dashboardStats] = await Promise.all([\n        apiService.getSupervisorDashboardStats(),\n      ]);\n\n      setTeamStats({\n        totalAgents: 5, // Mock data\n        totalTasks: dashboardStats.totalLeads || 0,\n        completedTasks: dashboardStats.completedLeads || 0,\n        pendingReviews: dashboardStats.pendingReviews || 0,\n        averageTime: 2.8, // Mock data\n        teamEfficiency: 82, // Mock data\n      });\n\n      // Mock agent performance data\n      setAgentPerformance([\n        {\n          agentName: 'John Agent',\n          tasksAssigned: 15,\n          tasksCompleted: 12,\n          approvalRate: 90,\n          avgTime: 2.5,\n          efficiency: 85,\n        },\n        {\n          agentName: 'Jane Agent',\n          tasksAssigned: 18,\n          tasksCompleted: 16,\n          approvalRate: 95,\n          avgTime: 2.2,\n          efficiency: 92,\n        },\n        {\n          agentName: 'Bob Agent',\n          tasksAssigned: 12,\n          tasksCompleted: 10,\n          approvalRate: 80,\n          avgTime: 3.1,\n          efficiency: 75,\n        },\n      ]);\n\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data on error\n      setTeamStats({\n        totalAgents: 5,\n        totalTasks: 45,\n        completedTasks: 38,\n        pendingReviews: 7,\n        averageTime: 2.8,\n        teamEfficiency: 82,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/supervisor/dashboard') },\n    { icon: '👁️', label: 'Review Queue', onClick: () => navigate('/supervisor/review') },\n    { icon: '📊', label: 'Reports', active: true },\n    { icon: '👥', label: 'Team', onClick: () => navigate('/supervisor/team') },\n  ];\n\n  const getCompletionRate = () => {\n    return teamStats.totalTasks > 0 ? Math.round((teamStats.completedTasks / teamStats.totalTasks) * 100) : 0;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Team Reports\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Team Reports\" navigationItems={navigationItems}>\n      <ReportsContainer>\n        <FilterContainer>\n          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </FilterSelect>\n\n          <Button variant=\"outline\" onClick={() => window.print()}>\n            📄 Export Report\n          </Button>\n        </FilterContainer>\n\n        {/* Team Overview Stats */}\n        <StatsGrid>\n          <StatCard>\n            <StatValue>{teamStats.totalAgents}</StatValue>\n            <StatLabel>Team Members</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.totalTasks}</StatValue>\n            <StatLabel>Total Tasks</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.completedTasks}</StatValue>\n            <StatLabel>Completed</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.pendingReviews}</StatValue>\n            <StatLabel>Pending Review</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.averageTime}</StatValue>\n            <StatLabel>Avg. Days</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.teamEfficiency}%</StatValue>\n            <StatLabel>Team Efficiency</StatLabel>\n          </StatCard>\n        </StatsGrid>\n\n        {/* Team Performance Charts */}\n        <ChartContainer>\n          <ChartCard>\n            <ChartTitle>Team Performance Overview</ChartTitle>\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>\n                <span>Completion Rate</span>\n                <span>{getCompletionRate()}%</span>\n              </div>\n              <PerformanceBar percentage={getCompletionRate()} color=\"#2e7d32\" />\n            </div>\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>\n                <span>Team Efficiency</span>\n                <span>{teamStats.teamEfficiency}%</span>\n              </div>\n              <PerformanceBar percentage={teamStats.teamEfficiency} color=\"#007E3A\" />\n            </div>\n            <div>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>\n                <span>Quality Score</span>\n                <span>88%</span>\n              </div>\n              <PerformanceBar percentage={88} color=\"#FFD100\" />\n            </div>\n          </ChartCard>\n\n          <ChartCard>\n            <ChartTitle>Task Distribution</ChartTitle>\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', textAlign: 'center' }}>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#2e7d32' }}>\n                  {teamStats.completedTasks}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Completed</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#4a148c' }}>\n                  {teamStats.pendingReviews}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Pending</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff8f00' }}>\n                  {teamStats.totalTasks - teamStats.completedTasks - teamStats.pendingReviews}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>In Progress</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#007E3A' }}>\n                  {teamStats.totalTasks}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Total</div>\n              </div>\n            </div>\n          </ChartCard>\n        </ChartContainer>\n\n        {/* Agent Performance Table */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Agent Performance</h3>\n          <div style={{ overflowX: 'auto' }}>\n            <TeamPerformanceTable>\n              <thead>\n                <tr>\n                  <TableHeader>Agent Name</TableHeader>\n                  <TableHeader>Tasks Assigned</TableHeader>\n                  <TableHeader>Completed</TableHeader>\n                  <TableHeader>Completion Rate</TableHeader>\n                  <TableHeader>Approval Rate</TableHeader>\n                  <TableHeader>Avg. Time</TableHeader>\n                  <TableHeader>Efficiency</TableHeader>\n                </tr>\n              </thead>\n              <tbody>\n                {agentPerformance.map((agent, index) => (\n                  <TableRow key={index}>\n                    <TableCell style={{ fontWeight: '500' }}>{agent.agentName}</TableCell>\n                    <TableCell>{agent.tasksAssigned}</TableCell>\n                    <TableCell>{agent.tasksCompleted}</TableCell>\n                    <TableCell>\n                      {Math.round((agent.tasksCompleted / agent.tasksAssigned) * 100)}%\n                    </TableCell>\n                    <TableCell>{agent.approvalRate}%</TableCell>\n                    <TableCell>{agent.avgTime} days</TableCell>\n                    <TableCell>\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>\n                        <span>{agent.efficiency}%</span>\n                        <div style={{ flex: 1, minWidth: '60px' }}>\n                          <PerformanceBar\n                            percentage={agent.efficiency}\n                            color={agent.efficiency >= 85 ? '#2e7d32' : agent.efficiency >= 70 ? '#ff8f00' : '#c62828'}\n                          />\n                        </div>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </tbody>\n            </TeamPerformanceTable>\n          </div>\n        </Card>\n\n        {/* Summary Insights */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Key Insights</h3>\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>\n            <div>\n              <h4 style={{ color: '#2e7d32', marginBottom: '10px' }}>🎯 Top Performer</h4>\n              <p>Jane Agent leads with 95% approval rate and 92% efficiency</p>\n            </div>\n            <div>\n              <h4 style={{ color: '#ff8f00', marginBottom: '10px' }}>⚠️ Needs Attention</h4>\n              <p>Bob Agent requires support to improve efficiency from 75% to team average</p>\n            </div>\n            <div>\n              <h4 style={{ color: '#007E3A', marginBottom: '10px' }}>📈 Team Trend</h4>\n              <p>Overall team performance improved by 8% compared to last month</p>\n            </div>\n          </div>\n        </Card>\n      </ReportsContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default SupervisorReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,2BAA2B;AACxE,SAASC,UAAU,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,gBAAgB,GAAGR,MAAM,CAACS,GAAG;AACnC;AACA;AACA,CAAC;AAACC,EAAA,GAHIF,gBAAgB;AAKtB,MAAMG,SAAS,GAAGX,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGb,MAAM,CAACE,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GARID,QAAQ;AAUd,MAAME,SAAS,GAAGf,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAGjB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAJID,SAAS;AAMf,MAAME,cAAc,GAAGnB,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GATID,cAAc;AAWpB,MAAME,SAAS,GAAGrB,MAAM,CAACE,IAAI,CAAC;AAC9B;AACA,CAAC;AAACoB,GAAA,GAFID,SAAS;AAIf,MAAME,UAAU,GAAGvB,MAAM,CAACwB,EAAE;AAC5B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,eAAe,GAAG1B,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GALID,eAAe;AAOrB,MAAME,YAAY,GAAG5B,MAAM,CAAC6B,MAAM;AAClC;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACC,GAAA,GAXIT,YAAY;AAalB,MAAMU,oBAAoB,GAAGtC,MAAM,CAACuC,KAAK;AACzC;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,oBAAoB;AAK1B,MAAMG,WAAW,GAAGzC,MAAM,CAAC0C,EAAE;AAC7B;AACA;AACA,6BAA6BZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS;AAClE,sBAAsBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,QAAQ;AAC1D;AACA,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,UAAU;AACjD,CAAC;AAACC,IAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAG/C,MAAM,CAACgD,EAAE;AAC3B;AACA;AACA,6BAA6BlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS;AAClE,CAAC;AAACM,IAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGlD,MAAM,CAACmD,EAAE;AAC1B;AACA,wBAAwBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS;AAC7D;AACA,CAAC;AAACS,IAAA,GAJIF,QAAQ;AAMd,MAAMG,cAAc,GAAGrD,MAAM,CAACS,GAA0C;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaqB,KAAK,IAAIA,KAAK,CAACwB,UAAU;AACtC;AACA,wBAAwBxB,KAAK,IAAIA,KAAK,CAACyB,KAAK;AAC5C;AACA;AACA,CAAC;AAACC,IAAA,GAfIH,cAAc;AAiBpB,MAAMI,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,OAAO,CAAC;EACrD,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC;IACzCoE,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAQ,EAAE,CAAC;EACnE,MAAM4E,QAAQ,GAAG1E,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd4E,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACb,UAAU,CAAC,CAAC;EAEhB,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM,CAACe,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACzCxE,UAAU,CAACyE,2BAA2B,CAAC,CAAC,CACzC,CAAC;MAEFd,YAAY,CAAC;QACXC,WAAW,EAAE,CAAC;QAAE;QAChBC,UAAU,EAAES,cAAc,CAACI,UAAU,IAAI,CAAC;QAC1CZ,cAAc,EAAEQ,cAAc,CAACK,cAAc,IAAI,CAAC;QAClDZ,cAAc,EAAEO,cAAc,CAACP,cAAc,IAAI,CAAC;QAClDC,WAAW,EAAE,GAAG;QAAE;QAClBC,cAAc,EAAE,EAAE,CAAE;MACtB,CAAC,CAAC;;MAEF;MACAE,mBAAmB,CAAC,CAClB;QACES,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,SAAS,EAAE,WAAW;QACtBC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;MACd,CAAC,CACF,CAAC;IAEJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACAvB,YAAY,CAAC;QACXC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,EAAE;QAClBC,cAAc,EAAE,CAAC;QACjBC,WAAW,EAAE,GAAG;QAChBC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAAC,uBAAuB;EAAE,CAAC,EACpF;IAAEiB,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE,cAAc;IAAEC,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAAC,oBAAoB;EAAE,CAAC,EACrF;IAAEiB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEE,MAAM,EAAE;EAAK,CAAC,EAC9C;IAAEH,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,MAAM;IAAEC,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAAC,kBAAkB;EAAE,CAAC,CAC3E;EAED,MAAMqB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAO/B,SAAS,CAACG,UAAU,GAAG,CAAC,GAAG6B,IAAI,CAACC,KAAK,CAAEjC,SAAS,CAACI,cAAc,GAAGJ,SAAS,CAACG,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;EAC3G,CAAC;EAED,IAAIP,OAAO,EAAE;IACX,oBACEpD,OAAA,CAACN,eAAe;MAACgG,KAAK,EAAC,cAAc;MAACR,eAAe,EAAEA,eAAgB;MAAAS,QAAA,eACrE3F,OAAA,CAACH,cAAc;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEtB;EAEA,oBACE/F,OAAA,CAACN,eAAe;IAACgG,KAAK,EAAC,cAAc;IAACR,eAAe,EAAEA,eAAgB;IAAAS,QAAA,eACrE3F,OAAA,CAACC,gBAAgB;MAAA0F,QAAA,gBACf3F,OAAA,CAACmB,eAAe;QAAAwE,QAAA,gBACd3F,OAAA,CAACqB,YAAY;UAAC2E,KAAK,EAAE1C,UAAW;UAAC2C,QAAQ,EAAGC,CAAC,IAAK3C,aAAa,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAL,QAAA,gBAC9E3F,OAAA;YAAQgG,KAAK,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvC/F,OAAA;YAAQgG,KAAK,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzC/F,OAAA;YAAQgG,KAAK,EAAC,SAAS;YAAAL,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7C/F,OAAA;YAAQgG,KAAK,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEf/F,OAAA,CAACJ,MAAM;UAACwG,OAAO,EAAC,SAAS;UAACf,OAAO,EAAEA,CAAA,KAAMgB,MAAM,CAACC,KAAK,CAAC,CAAE;UAAAX,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGlB/F,OAAA,CAACI,SAAS;QAAAuF,QAAA,gBACR3F,OAAA,CAACM,QAAQ;UAAAqF,QAAA,gBACP3F,OAAA,CAACQ,SAAS;YAAAmF,QAAA,EAAEnC,SAAS,CAACE;UAAW;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9C/F,OAAA,CAACU,SAAS;YAAAiF,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACX/F,OAAA,CAACM,QAAQ;UAAAqF,QAAA,gBACP3F,OAAA,CAACQ,SAAS;YAAAmF,QAAA,EAAEnC,SAAS,CAACG;UAAU;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7C/F,OAAA,CAACU,SAAS;YAAAiF,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACX/F,OAAA,CAACM,QAAQ;UAAAqF,QAAA,gBACP3F,OAAA,CAACQ,SAAS;YAAAmF,QAAA,EAAEnC,SAAS,CAACI;UAAc;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjD/F,OAAA,CAACU,SAAS;YAAAiF,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACX/F,OAAA,CAACM,QAAQ;UAAAqF,QAAA,gBACP3F,OAAA,CAACQ,SAAS;YAAAmF,QAAA,EAAEnC,SAAS,CAACK;UAAc;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjD/F,OAAA,CAACU,SAAS;YAAAiF,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACX/F,OAAA,CAACM,QAAQ;UAAAqF,QAAA,gBACP3F,OAAA,CAACQ,SAAS;YAAAmF,QAAA,EAAEnC,SAAS,CAACM;UAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9C/F,OAAA,CAACU,SAAS;YAAAiF,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACX/F,OAAA,CAACM,QAAQ;UAAAqF,QAAA,gBACP3F,OAAA,CAACQ,SAAS;YAAAmF,QAAA,GAAEnC,SAAS,CAACO,cAAc,EAAC,GAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAClD/F,OAAA,CAACU,SAAS;YAAAiF,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGZ/F,OAAA,CAACY,cAAc;QAAA+E,QAAA,gBACb3F,OAAA,CAACc,SAAS;UAAA6E,QAAA,gBACR3F,OAAA,CAACgB,UAAU;YAAA2E,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClD/F,OAAA;YAAKuG,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAb,QAAA,gBACnC3F,OAAA;cAAKuG,KAAK,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEF,YAAY,EAAE;cAAM,CAAE;cAAAb,QAAA,gBACpF3F,OAAA;gBAAA2F,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5B/F,OAAA;gBAAA2F,QAAA,GAAOJ,iBAAiB,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN/F,OAAA,CAAC8C,cAAc;cAACC,UAAU,EAAEwC,iBAAiB,CAAC,CAAE;cAACvC,KAAK,EAAC;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACN/F,OAAA;YAAKuG,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAb,QAAA,gBACnC3F,OAAA;cAAKuG,KAAK,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEF,YAAY,EAAE;cAAM,CAAE;cAAAb,QAAA,gBACpF3F,OAAA;gBAAA2F,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5B/F,OAAA;gBAAA2F,QAAA,GAAOnC,SAAS,CAACO,cAAc,EAAC,GAAC;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACN/F,OAAA,CAAC8C,cAAc;cAACC,UAAU,EAAES,SAAS,CAACO,cAAe;cAACf,KAAK,EAAC;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAKuG,KAAK,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEF,YAAY,EAAE;cAAM,CAAE;cAAAb,QAAA,gBACpF3F,OAAA;gBAAA2F,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1B/F,OAAA;gBAAA2F,QAAA,EAAM;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACN/F,OAAA,CAAC8C,cAAc;cAACC,UAAU,EAAE,EAAG;cAACC,KAAK,EAAC;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAEZ/F,OAAA,CAACc,SAAS;UAAA6E,QAAA,gBACR3F,OAAA,CAACgB,UAAU;YAAA2E,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1C/F,OAAA;YAAKuG,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEE,mBAAmB,EAAE,SAAS;cAAEC,GAAG,EAAE,MAAM;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAlB,QAAA,gBAChG3F,OAAA;cAAA2F,QAAA,gBACE3F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAE/D,KAAK,EAAE;gBAAU,CAAE;gBAAA2C,QAAA,EACpEnC,SAAS,CAACI;cAAc;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACN/F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAE9D,KAAK,EAAE;gBAAO,CAAE;gBAAA2C,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACN/F,OAAA;cAAA2F,QAAA,gBACE3F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAE/D,KAAK,EAAE;gBAAU,CAAE;gBAAA2C,QAAA,EACpEnC,SAAS,CAACK;cAAc;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACN/F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAE9D,KAAK,EAAE;gBAAO,CAAE;gBAAA2C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACN/F,OAAA;cAAA2F,QAAA,gBACE3F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAE/D,KAAK,EAAE;gBAAU,CAAE;gBAAA2C,QAAA,EACpEnC,SAAS,CAACG,UAAU,GAAGH,SAAS,CAACI,cAAc,GAAGJ,SAAS,CAACK;cAAc;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACN/F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAE9D,KAAK,EAAE;gBAAO,CAAE;gBAAA2C,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN/F,OAAA;cAAA2F,QAAA,gBACE3F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAE/D,KAAK,EAAE;gBAAU,CAAE;gBAAA2C,QAAA,EACpEnC,SAAS,CAACG;cAAU;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACN/F,OAAA;gBAAKuG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAE9D,KAAK,EAAE;gBAAO,CAAE;gBAAA2C,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGjB/F,OAAA,CAACL,IAAI;QAAAgG,QAAA,gBACH3F,OAAA;UAAIuG,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAExD,KAAK,EAAE;UAAU,CAAE;UAAA2C,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E/F,OAAA;UAAKuG,KAAK,EAAE;YAAES,SAAS,EAAE;UAAO,CAAE;UAAArB,QAAA,eAChC3F,OAAA,CAAC+B,oBAAoB;YAAA4D,QAAA,gBACnB3F,OAAA;cAAA2F,QAAA,eACE3F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA,CAACkC,WAAW;kBAAAyD,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrC/F,OAAA,CAACkC,WAAW;kBAAAyD,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACzC/F,OAAA,CAACkC,WAAW;kBAAAyD,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpC/F,OAAA,CAACkC,WAAW;kBAAAyD,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC1C/F,OAAA,CAACkC,WAAW;kBAAAyD,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACxC/F,OAAA,CAACkC,WAAW;kBAAAyD,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpC/F,OAAA,CAACkC,WAAW;kBAAAyD,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR/F,OAAA;cAAA2F,QAAA,EACG3B,gBAAgB,CAACiD,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACjCnH,OAAA,CAAC2C,QAAQ;gBAAAgD,QAAA,gBACP3F,OAAA,CAACwC,SAAS;kBAAC+D,KAAK,EAAE;oBAAEQ,UAAU,EAAE;kBAAM,CAAE;kBAAApB,QAAA,EAAEuB,KAAK,CAACxC;gBAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtE/F,OAAA,CAACwC,SAAS;kBAAAmD,QAAA,EAAEuB,KAAK,CAACvC;gBAAa;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5C/F,OAAA,CAACwC,SAAS;kBAAAmD,QAAA,EAAEuB,KAAK,CAACtC;gBAAc;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C/F,OAAA,CAACwC,SAAS;kBAAAmD,QAAA,GACPH,IAAI,CAACC,KAAK,CAAEyB,KAAK,CAACtC,cAAc,GAAGsC,KAAK,CAACvC,aAAa,GAAI,GAAG,CAAC,EAAC,GAClE;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZ/F,OAAA,CAACwC,SAAS;kBAAAmD,QAAA,GAAEuB,KAAK,CAACrC,YAAY,EAAC,GAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5C/F,OAAA,CAACwC,SAAS;kBAAAmD,QAAA,GAAEuB,KAAK,CAACpC,OAAO,EAAC,OAAK;gBAAA;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3C/F,OAAA,CAACwC,SAAS;kBAAAmD,QAAA,eACR3F,OAAA;oBAAKuG,KAAK,EAAE;sBAAEE,OAAO,EAAE,MAAM;sBAAEW,UAAU,EAAE,QAAQ;sBAAER,GAAG,EAAE;oBAAO,CAAE;oBAAAjB,QAAA,gBACjE3F,OAAA;sBAAA2F,QAAA,GAAOuB,KAAK,CAACnC,UAAU,EAAC,GAAC;oBAAA;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChC/F,OAAA;sBAAKuG,KAAK,EAAE;wBAAEc,IAAI,EAAE,CAAC;wBAAEC,QAAQ,EAAE;sBAAO,CAAE;sBAAA3B,QAAA,eACxC3F,OAAA,CAAC8C,cAAc;wBACbC,UAAU,EAAEmE,KAAK,CAACnC,UAAW;wBAC7B/B,KAAK,EAAEkE,KAAK,CAACnC,UAAU,IAAI,EAAE,GAAG,SAAS,GAAGmC,KAAK,CAACnC,UAAU,IAAI,EAAE,GAAG,SAAS,GAAG;sBAAU;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GAnBCoB,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGP/F,OAAA,CAACL,IAAI;QAAAgG,QAAA,gBACH3F,OAAA;UAAIuG,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAExD,KAAK,EAAE;UAAU,CAAE;UAAA2C,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxE/F,OAAA;UAAKuG,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEE,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAjB,QAAA,gBACxG3F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAIuG,KAAK,EAAE;gBAAEvD,KAAK,EAAE,SAAS;gBAAEwD,YAAY,EAAE;cAAO,CAAE;cAAAb,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5E/F,OAAA;cAAA2F,QAAA,EAAG;YAA0D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAIuG,KAAK,EAAE;gBAAEvD,KAAK,EAAE,SAAS;gBAAEwD,YAAY,EAAE;cAAO,CAAE;cAAAb,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E/F,OAAA;cAAA2F,QAAA,EAAG;YAAyE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAIuG,KAAK,EAAE;gBAAEvD,KAAK,EAAE,SAAS;gBAAEwD,YAAY,EAAE;cAAO,CAAE;cAAAb,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzE/F,OAAA;cAAA2F,QAAA,EAAG;YAA8D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEtB,CAAC;AAAC5C,EAAA,CA1QID,iBAA2B;EAAA,QAYd1D,WAAW;AAAA;AAAA+H,IAAA,GAZxBrE,iBAA2B;AA4QjC,eAAeA,iBAAiB;AAAC,IAAA/C,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAU,GAAA,EAAAG,GAAA,EAAAM,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAsE,IAAA;AAAAC,YAAA,CAAArH,EAAA;AAAAqH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}