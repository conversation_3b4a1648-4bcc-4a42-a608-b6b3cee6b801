{"ast": null, "code": "import _objectSpread from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{<PERSON>,<PERSON><PERSON>,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FilterContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background: white;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const SearchInput=styled.input(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  flex: 1;\\n  min-width: 250px;\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const TableContainer=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  overflow-x: auto;\\n\"])));const Table=styled.table(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n  cursor: pointer;\\n  \\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium,props=>props.theme.colors.mediumGray);const TableCell=styled.td(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const StatusBadge=styled.span(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n\\n  \",\"\\n\"])),props=>{switch(props.status){case'new':return\"\\n          background-color: #e3f2fd;\\n          color: #0d47a1;\\n        \";case'assigned':return\"\\n          background-color: #fff3e0;\\n          color: #e65100;\\n        \";case'in-progress':return\"\\n          background-color: #fff8e1;\\n          color: #ff8f00;\\n        \";case'pending-review':return\"\\n          background-color: #f3e5f5;\\n          color: #4a148c;\\n        \";case'approved':return\"\\n          background-color: #e8f5e9;\\n          color: #2e7d32;\\n        \";case'rejected':return\"\\n          background-color: #ffebee;\\n          color: #c62828;\\n        \";default:return\"\\n          background-color: #f5f5f5;\\n          color: #666;\\n        \";}});const ActionButtons=styled.div(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 8px;\\n\"])));const Pagination=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 10px;\\n  margin-top: 20px;\\n\"])));const PageButton=styled.button(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  background: \",\";\\n  color: \",\";\\n  border-radius: \",\";\\n  cursor: pointer;\\n  \\n  &:hover {\\n    background: \",\";\\n  }\\n  \\n  &:disabled {\\n    opacity: 0.5;\\n    cursor: not-allowed;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.active?props.theme.colors.primary:'white',props=>props.active?'white':props.theme.colors.textDark,props=>props.theme.borderRadius.sm,props=>props.active?props.theme.colors.primary:props.theme.colors.lightGray);const AssignModal=styled.div(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  display: \",\";\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 1000;\\n\"])),props=>props.isOpen?'flex':'none');const ModalContent=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  background: white;\\n  padding: 30px;\\n  border-radius: 8px;\\n  width: 90%;\\n  max-width: 400px;\\n\"])));const ModalTitle=styled.h3(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n  color: #007E3A;\\n\"])));const ModalActions=styled.div(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 10px;\\n  justify-content: flex-end;\\n  margin-top: 20px;\\n\"])));const LeadsList=()=>{const[leads,setLeads]=useState([]);const[agents,setAgents]=useState([]);const[loading,setLoading]=useState(true);const[statusFilter,setStatusFilter]=useState('all');const[searchTerm,setSearchTerm]=useState('');const[currentPage,setCurrentPage]=useState(1);const[totalPages,setTotalPages]=useState(1);const[selectedLead,setSelectedLead]=useState(null);const[selectedAgent,setSelectedAgent]=useState('');const[sortField,setSortField]=useState('createdDate');const[sortDirection,setSortDirection]=useState('desc');const pageSize=20;const navigate=useNavigate();useEffect(()=>{loadLeads();loadAgents();},[currentPage,statusFilter,sortField,sortDirection]);const loadLeads=async()=>{try{setLoading(true);const response=await apiService.getLeads(currentPage,pageSize,statusFilter==='all'?undefined:statusFilter);setLeads(response.data||[]);setTotalPages(Math.ceil((response.totalCount||0)/pageSize));}catch(error){console.error('Error loading leads:',error);// Mock data for demo\nsetLeads([{leadId:1,customerName:'John Doe',mobileNumber:'9876543210',loanType:'Personal Loan',status:'new',createdDate:'2024-01-15T10:30:00Z',createdByName:'Admin User',assignedToName:'',documentCount:0,croppedImageCount:0},{leadId:2,customerName:'Jane Smith',mobileNumber:'9876543211',loanType:'Home Loan',status:'assigned',createdDate:'2024-01-14T09:15:00Z',assignedDate:'2024-01-14T10:00:00Z',createdByName:'Admin User',assignedToName:'Agent Johnson',documentCount:2,croppedImageCount:1}]);setTotalPages(1);}finally{setLoading(false);}};const loadAgents=async()=>{try{const users=await apiService.getUsers();setAgents(users.filter(user=>user.role==='Agent'&&user.isActive));}catch(error){console.error('Error loading agents:',error);}};const filteredLeads=leads.filter(lead=>lead.customerName.toLowerCase().includes(searchTerm.toLowerCase())||lead.mobileNumber.includes(searchTerm)||lead.loanType.toLowerCase().includes(searchTerm.toLowerCase())||lead.assignedToName&&lead.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()));const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>navigate('/admin/dashboard')},{icon:'👥',label:'Users',onClick:()=>navigate('/admin/users')},{icon:'📋',label:'Leads',active:true},{icon:'📊',label:'Reports',onClick:()=>navigate('/admin/reports')},{icon:'⚙️',label:'Settings',onClick:()=>navigate('/admin/settings')}];const handleSort=field=>{if(sortField===field){setSortDirection(sortDirection==='asc'?'desc':'asc');}else{setSortField(field);setSortDirection('asc');}};const handleAssignLead=lead=>{setSelectedLead(lead);setSelectedAgent('');};const submitAssignment=async()=>{if(!selectedLead||!selectedAgent)return;try{await apiService.assignLead(selectedLead.leadId,parseInt(selectedAgent),'Assigned by admin');// Update local state\nsetLeads(leads=>leads.map(lead=>{var _agents$find,_agents$find2;return lead.leadId===selectedLead.leadId?_objectSpread(_objectSpread({},lead),{},{status:'assigned',assignedToName:((_agents$find=agents.find(a=>a.userId===parseInt(selectedAgent)))===null||_agents$find===void 0?void 0:_agents$find.firstName)+' '+((_agents$find2=agents.find(a=>a.userId===parseInt(selectedAgent)))===null||_agents$find2===void 0?void 0:_agents$find2.lastName),assignedDate:new Date().toISOString()}):lead;}));setSelectedLead(null);setSelectedAgent('');}catch(error){console.error('Error assigning lead:',error);alert('Failed to assign lead');}};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};const getSortIcon=field=>{if(sortField!==field)return'↕️';return sortDirection==='asc'?'↑':'↓';};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"All Leads\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsxs(DashboardLayout,{title:\"All Leads\",navigationItems:navigationItems,children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'20px'},children:[/*#__PURE__*/_jsx(\"h2\",{style:{color:'#007E3A'},children:\"Lead Management\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>navigate('/admin/create-lead'),children:\"+ Create New Lead\"})]}),/*#__PURE__*/_jsxs(FilterContainer,{children:[/*#__PURE__*/_jsxs(FilterSelect,{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"new\",children:\"New\"}),/*#__PURE__*/_jsx(\"option\",{value:\"assigned\",children:\"Assigned\"}),/*#__PURE__*/_jsx(\"option\",{value:\"in-progress\",children:\"In Progress\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-review\",children:\"Pending Review\"}),/*#__PURE__*/_jsx(\"option\",{value:\"approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:\"Rejected\"})]}),/*#__PURE__*/_jsx(SearchInput,{type:\"text\",placeholder:\"Search by customer, mobile, loan type, or agent...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(TableHeader,{onClick:()=>handleSort('leadId'),children:[\"ID \",getSortIcon('leadId')]}),/*#__PURE__*/_jsxs(TableHeader,{onClick:()=>handleSort('customerName'),children:[\"Customer \",getSortIcon('customerName')]}),/*#__PURE__*/_jsx(TableHeader,{children:\"Mobile\"}),/*#__PURE__*/_jsxs(TableHeader,{onClick:()=>handleSort('loanType'),children:[\"Loan Type \",getSortIcon('loanType')]}),/*#__PURE__*/_jsxs(TableHeader,{onClick:()=>handleSort('status'),children:[\"Status \",getSortIcon('status')]}),/*#__PURE__*/_jsx(TableHeader,{children:\"Assigned To\"}),/*#__PURE__*/_jsxs(TableHeader,{onClick:()=>handleSort('createdDate'),children:[\"Created \",getSortIcon('createdDate')]}),/*#__PURE__*/_jsx(TableHeader,{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredLeads.map(lead=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsxs(TableCell,{children:[\"#\",lead.leadId]}),/*#__PURE__*/_jsx(TableCell,{style:{fontWeight:'500'},children:lead.customerName}),/*#__PURE__*/_jsx(TableCell,{children:lead.mobileNumber}),/*#__PURE__*/_jsx(TableCell,{children:lead.loanType}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(StatusBadge,{status:lead.status,children:lead.status.replace('-',' ').toUpperCase()})}),/*#__PURE__*/_jsx(TableCell,{children:lead.assignedToName||'Unassigned'}),/*#__PURE__*/_jsx(TableCell,{children:formatDate(lead.createdDate)}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(ActionButtons,{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>navigate(\"/lead/\".concat(lead.leadId)),children:\"View\"}),lead.status==='new'&&/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleAssignLead(lead),children:\"Assign\"})]})})]},lead.leadId))})]})}),filteredLeads.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'40px',color:'#777'},children:searchTerm?'No leads found matching your search.':'No leads found.'}),/*#__PURE__*/_jsxs(Pagination,{children:[/*#__PURE__*/_jsx(PageButton,{onClick:()=>setCurrentPage(1),disabled:currentPage===1,children:\"First\"}),/*#__PURE__*/_jsx(PageButton,{onClick:()=>setCurrentPage(currentPage-1),disabled:currentPage===1,children:\"Previous\"}),Array.from({length:Math.min(5,totalPages)},(_,i)=>{const page=Math.max(1,currentPage-2)+i;if(page<=totalPages){return/*#__PURE__*/_jsx(PageButton,{active:page===currentPage,onClick:()=>setCurrentPage(page),children:page},page);}return null;}),/*#__PURE__*/_jsx(PageButton,{onClick:()=>setCurrentPage(currentPage+1),disabled:currentPage===totalPages,children:\"Next\"}),/*#__PURE__*/_jsx(PageButton,{onClick:()=>setCurrentPage(totalPages),disabled:currentPage===totalPages,children:\"Last\"})]})]}),/*#__PURE__*/_jsx(AssignModal,{isOpen:!!selectedLead,children:/*#__PURE__*/_jsxs(ModalContent,{children:[/*#__PURE__*/_jsxs(ModalTitle,{children:[\"Assign Lead - \",selectedLead===null||selectedLead===void 0?void 0:selectedLead.customerName]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'15px'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'5px',fontWeight:'500'},children:\"Select Agent:\"}),/*#__PURE__*/_jsxs(FilterSelect,{value:selectedAgent,onChange:e=>setSelectedAgent(e.target.value),style:{width:'100%'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Choose an agent...\"}),agents.map(agent=>/*#__PURE__*/_jsxs(\"option\",{value:agent.userId,children:[agent.firstName,\" \",agent.lastName,\" (\",agent.username,\")\"]},agent.userId))]})]}),/*#__PURE__*/_jsxs(ModalActions,{children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>{setSelectedLead(null);setSelectedAgent('');},children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:submitAssignment,disabled:!selectedAgent,children:\"Assign Lead\"})]})]})})]});};export default LeadsList;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_templateObject", "_taggedTemplateLiteral", "FilterSelect", "select", "_templateObject2", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "SearchInput", "input", "_templateObject3", "TableContainer", "_templateObject4", "Table", "table", "_templateObject5", "TableHeader", "th", "_templateObject6", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject7", "TableRow", "tr", "_templateObject8", "StatusBadge", "span", "_templateObject9", "status", "ActionButtons", "_templateObject0", "Pagination", "_templateObject1", "PageButton", "button", "_templateObject10", "active", "textDark", "AssignModal", "_templateObject11", "isOpen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject12", "ModalTitle", "h3", "_templateObject13", "ModalActions", "_templateObject14", "LeadsList", "leads", "setLeads", "agents", "setAgents", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "<PERSON><PERSON><PERSON>", "setSelectedLead", "selectedAgent", "setSelectedAgent", "sortField", "setSortField", "sortDirection", "setSortDirection", "pageSize", "navigate", "loadLeads", "loadAgents", "response", "getLeads", "undefined", "data", "Math", "ceil", "totalCount", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "createdByName", "assignedToName", "documentCount", "croppedImageCount", "assignedDate", "users", "getUsers", "filter", "user", "role", "isActive", "filteredLeads", "lead", "toLowerCase", "includes", "navigationItems", "icon", "label", "onClick", "handleSort", "field", "handleAssignLead", "submitAssignment", "assignLead", "parseInt", "map", "_agents$find", "_agents$find2", "_objectSpread", "find", "a", "userId", "firstName", "lastName", "Date", "toISOString", "alert", "formatDate", "dateString", "toLocaleDateString", "getSortIcon", "title", "children", "style", "display", "justifyContent", "alignItems", "marginBottom", "color", "value", "onChange", "e", "target", "type", "placeholder", "fontWeight", "replace", "toUpperCase", "size", "concat", "variant", "length", "textAlign", "padding", "disabled", "Array", "from", "min", "_", "i", "page", "max", "width", "agent", "username"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Leads/LeadsList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem, User } from '../../services/apiService';\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n  cursor: pointer;\n  \n  &:hover {\n    background-color: ${props => props.theme.colors.mediumGray};\n  }\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'new':\n        return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n      case 'assigned':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'in-progress':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'approved':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n\nconst Pagination = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 10px;\n  margin-top: 20px;\n`;\n\nconst PageButton = styled.button<{ active?: boolean }>`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  background: ${props => props.active ? props.theme.colors.primary : 'white'};\n  color: ${props => props.active ? 'white' : props.theme.colors.textDark};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  cursor: pointer;\n  \n  &:hover {\n    background: ${props => props.active ? props.theme.colors.primary : props.theme.colors.lightGray};\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\nconst AssignModal = styled.div<{ isOpen: boolean }>`\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n`;\n\nconst ModalContent = styled.div`\n  background: white;\n  padding: 30px;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 400px;\n`;\n\nconst ModalTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n`;\n\nconst ModalActions = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n\nconst LeadsList: React.FC = () => {\n  const [leads, setLeads] = useState<LeadListItem[]>([]);\n  const [agents, setAgents] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [selectedLead, setSelectedLead] = useState<LeadListItem | null>(null);\n  const [selectedAgent, setSelectedAgent] = useState('');\n  const [sortField, setSortField] = useState('createdDate');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n  const pageSize = 20;\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadLeads();\n    loadAgents();\n  }, [currentPage, statusFilter, sortField, sortDirection]);\n\n  const loadLeads = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLeads(\n        currentPage,\n        pageSize,\n        statusFilter === 'all' ? undefined : statusFilter\n      );\n      setLeads(response.data || []);\n      setTotalPages(Math.ceil((response.totalCount || 0) / pageSize));\n    } catch (error) {\n      console.error('Error loading leads:', error);\n      // Mock data for demo\n      setLeads([\n        {\n          leadId: 1,\n          customerName: 'John Doe',\n          mobileNumber: '9876543210',\n          loanType: 'Personal Loan',\n          status: 'new',\n          createdDate: '2024-01-15T10:30:00Z',\n          createdByName: 'Admin User',\n          assignedToName: '',\n          documentCount: 0,\n          croppedImageCount: 0,\n        },\n        {\n          leadId: 2,\n          customerName: 'Jane Smith',\n          mobileNumber: '9876543211',\n          loanType: 'Home Loan',\n          status: 'assigned',\n          createdDate: '2024-01-14T09:15:00Z',\n          assignedDate: '2024-01-14T10:00:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Agent Johnson',\n          documentCount: 2,\n          croppedImageCount: 1,\n        },\n      ]);\n      setTotalPages(1);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAgents = async () => {\n    try {\n      const users = await apiService.getUsers();\n      setAgents(users.filter(user => user.role === 'Agent' && user.isActive));\n    } catch (error) {\n      console.error('Error loading agents:', error);\n    }\n  };\n\n  const filteredLeads = leads.filter(lead =>\n    lead.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    lead.mobileNumber.includes(searchTerm) ||\n    lead.loanType.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (lead.assignedToName && lead.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/admin/dashboard') },\n    { icon: '👥', label: 'Users', onClick: () => navigate('/admin/users') },\n    { icon: '📋', label: 'Leads', active: true },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/admin/reports') },\n    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },\n  ];\n\n  const handleSort = (field: string) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  const handleAssignLead = (lead: LeadListItem) => {\n    setSelectedLead(lead);\n    setSelectedAgent('');\n  };\n\n  const submitAssignment = async () => {\n    if (!selectedLead || !selectedAgent) return;\n\n    try {\n      await apiService.assignLead(selectedLead.leadId, parseInt(selectedAgent), 'Assigned by admin');\n      \n      // Update local state\n      setLeads(leads => \n        leads.map(lead => \n          lead.leadId === selectedLead.leadId \n            ? { \n                ...lead, \n                status: 'assigned',\n                assignedToName: agents.find(a => a.userId === parseInt(selectedAgent))?.firstName + ' ' + \n                               agents.find(a => a.userId === parseInt(selectedAgent))?.lastName,\n                assignedDate: new Date().toISOString()\n              }\n            : lead\n        )\n      );\n\n      setSelectedLead(null);\n      setSelectedAgent('');\n    } catch (error) {\n      console.error('Error assigning lead:', error);\n      alert('Failed to assign lead');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getSortIcon = (field: string) => {\n    if (sortField !== field) return '↕️';\n    return sortDirection === 'asc' ? '↑' : '↓';\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"All Leads\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"All Leads\" navigationItems={navigationItems}>\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>\n          <h2 style={{ color: '#007E3A' }}>Lead Management</h2>\n          <Button onClick={() => navigate('/admin/create-lead')}>\n            + Create New Lead\n          </Button>\n        </div>\n\n        <FilterContainer>\n          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>\n            <option value=\"all\">All Status</option>\n            <option value=\"new\">New</option>\n            <option value=\"assigned\">Assigned</option>\n            <option value=\"in-progress\">In Progress</option>\n            <option value=\"pending-review\">Pending Review</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"rejected\">Rejected</option>\n          </FilterSelect>\n          \n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search by customer, mobile, loan type, or agent...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader onClick={() => handleSort('leadId')}>\n                  ID {getSortIcon('leadId')}\n                </TableHeader>\n                <TableHeader onClick={() => handleSort('customerName')}>\n                  Customer {getSortIcon('customerName')}\n                </TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader onClick={() => handleSort('loanType')}>\n                  Loan Type {getSortIcon('loanType')}\n                </TableHeader>\n                <TableHeader onClick={() => handleSort('status')}>\n                  Status {getSortIcon('status')}\n                </TableHeader>\n                <TableHeader>Assigned To</TableHeader>\n                <TableHeader onClick={() => handleSort('createdDate')}>\n                  Created {getSortIcon('createdDate')}\n                </TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredLeads.map((lead) => (\n                <TableRow key={lead.leadId}>\n                  <TableCell>#{lead.leadId}</TableCell>\n                  <TableCell style={{ fontWeight: '500' }}>{lead.customerName}</TableCell>\n                  <TableCell>{lead.mobileNumber}</TableCell>\n                  <TableCell>{lead.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={lead.status}>\n                      {lead.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>{lead.assignedToName || 'Unassigned'}</TableCell>\n                  <TableCell>{formatDate(lead.createdDate)}</TableCell>\n                  <TableCell>\n                    <ActionButtons>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => navigate(`/lead/${lead.leadId}`)}\n                      >\n                        View\n                      </Button>\n                      {lead.status === 'new' && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"secondary\"\n                          onClick={() => handleAssignLead(lead)}\n                        >\n                          Assign\n                        </Button>\n                      )}\n                    </ActionButtons>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {filteredLeads.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            {searchTerm ? 'No leads found matching your search.' : 'No leads found.'}\n          </div>\n        )}\n\n        {/* Pagination */}\n        <Pagination>\n          <PageButton \n            onClick={() => setCurrentPage(1)} \n            disabled={currentPage === 1}\n          >\n            First\n          </PageButton>\n          <PageButton \n            onClick={() => setCurrentPage(currentPage - 1)} \n            disabled={currentPage === 1}\n          >\n            Previous\n          </PageButton>\n          \n          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n            const page = Math.max(1, currentPage - 2) + i;\n            if (page <= totalPages) {\n              return (\n                <PageButton\n                  key={page}\n                  active={page === currentPage}\n                  onClick={() => setCurrentPage(page)}\n                >\n                  {page}\n                </PageButton>\n              );\n            }\n            return null;\n          })}\n          \n          <PageButton \n            onClick={() => setCurrentPage(currentPage + 1)} \n            disabled={currentPage === totalPages}\n          >\n            Next\n          </PageButton>\n          <PageButton \n            onClick={() => setCurrentPage(totalPages)} \n            disabled={currentPage === totalPages}\n          >\n            Last\n          </PageButton>\n        </Pagination>\n      </Card>\n\n      {/* Assignment Modal */}\n      <AssignModal isOpen={!!selectedLead}>\n        <ModalContent>\n          <ModalTitle>Assign Lead - {selectedLead?.customerName}</ModalTitle>\n          \n          <div style={{ marginBottom: '15px' }}>\n            <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>\n              Select Agent:\n            </label>\n            <FilterSelect\n              value={selectedAgent}\n              onChange={(e) => setSelectedAgent(e.target.value)}\n              style={{ width: '100%' }}\n            >\n              <option value=\"\">Choose an agent...</option>\n              {agents.map(agent => (\n                <option key={agent.userId} value={agent.userId}>\n                  {agent.firstName} {agent.lastName} ({agent.username})\n                </option>\n              ))}\n            </FilterSelect>\n          </div>\n\n          <ModalActions>\n            <Button\n              variant=\"outline\"\n              onClick={() => {\n                setSelectedLead(null);\n                setSelectedAgent('');\n              }}\n            >\n              Cancel\n            </Button>\n            <Button\n              onClick={submitAssignment}\n              disabled={!selectedAgent}\n            >\n              Assign Lead\n            </Button>\n          </ModalActions>\n        </ModalContent>\n      </AssignModal>\n    </DashboardLayout>\n  );\n};\n\nexport default LeadsList;\n"], "mappings": "umBAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAA4B,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3E,KAAM,CAAAC,eAAe,CAAGV,MAAM,CAACW,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,sFAKjC,CAED,KAAM,CAAAC,YAAY,CAAGd,MAAM,CAACe,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAH,sBAAA,6LAEZI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAKnCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAC,WAAW,CAAGxB,MAAM,CAACyB,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAb,sBAAA,wMAIVI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAInCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAI,cAAc,CAAG3B,MAAM,CAACW,GAAG,CAAAiB,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,+BAEhC,CAED,KAAM,CAAAgB,KAAK,CAAG7B,MAAM,CAAC8B,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,wDAGzB,CAED,KAAM,CAAAmB,WAAW,CAAGhC,MAAM,CAACiC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAArB,sBAAA,2NAGAI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAC5ClB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,QAAQ,CAE/CnB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACkB,UAAU,CAIzBpB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAE7D,CAED,KAAM,CAAAkB,SAAS,CAAGtC,MAAM,CAACuC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA3B,sBAAA,uFAGEI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAGzC,MAAM,CAAC0C,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAA9B,sBAAA,wDAEFI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAE5D,CAED,KAAM,CAAAS,WAAW,CAAG5C,MAAM,CAAC6C,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjC,sBAAA,mIAO3BI,KAAK,EAAI,CACT,OAAQA,KAAK,CAAC8B,MAAM,EAClB,IAAK,KAAK,CACR,oFAIF,IAAK,UAAU,CACb,oFAIF,IAAK,aAAa,CAChB,oFAIF,IAAK,gBAAgB,CACnB,oFAIF,IAAK,UAAU,CACb,oFAIF,IAAK,UAAU,CACb,oFAIF,QACE,iFAIJ,CACF,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAGhD,MAAM,CAACW,GAAG,CAAAsC,gBAAA,GAAAA,gBAAA,CAAApC,sBAAA,yCAG/B,CAED,KAAM,CAAAqC,UAAU,CAAGlD,MAAM,CAACW,GAAG,CAAAwC,gBAAA,GAAAA,gBAAA,CAAAtC,sBAAA,mHAM5B,CAED,KAAM,CAAAuC,UAAU,CAAGpD,MAAM,CAACqD,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAAzC,sBAAA,0PAEVI,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAC5CH,KAAK,EAAIA,KAAK,CAACsC,MAAM,CAAGtC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAAG,OAAO,CACjEN,KAAK,EAAIA,KAAK,CAACsC,MAAM,CAAG,OAAO,CAAGtC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACqC,QAAQ,CACrDvC,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAIrCL,KAAK,EAAIA,KAAK,CAACsC,MAAM,CAAGtC,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAAGN,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAOlG,CAED,KAAM,CAAAsB,WAAW,CAAGzD,MAAM,CAACW,GAAG,CAAA+C,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,gNACjBI,KAAK,EAAIA,KAAK,CAAC0C,MAAM,CAAG,MAAM,CAAG,MAAM,CAUnD,CAED,KAAM,CAAAC,YAAY,CAAG5D,MAAM,CAACW,GAAG,CAAAkD,iBAAA,GAAAA,iBAAA,CAAAhD,sBAAA,6GAM9B,CAED,KAAM,CAAAiD,UAAU,CAAG9D,MAAM,CAAC+D,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAnD,sBAAA,qDAG3B,CAED,KAAM,CAAAoD,YAAY,CAAGjE,MAAM,CAACW,GAAG,CAAAuD,iBAAA,GAAAA,iBAAA,CAAArD,sBAAA,6FAK9B,CAED,KAAM,CAAAsD,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGxE,QAAQ,CAAiB,EAAE,CAAC,CACtD,KAAM,CAACyE,MAAM,CAAEC,SAAS,CAAC,CAAG1E,QAAQ,CAAS,EAAE,CAAC,CAChD,KAAM,CAAC2E,OAAO,CAAEC,UAAU,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6E,YAAY,CAAEC,eAAe,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC+E,UAAU,CAAEC,aAAa,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiF,WAAW,CAAEC,cAAc,CAAC,CAAGlF,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACmF,UAAU,CAAEC,aAAa,CAAC,CAAGpF,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACqF,YAAY,CAAEC,eAAe,CAAC,CAAGtF,QAAQ,CAAsB,IAAI,CAAC,CAC3E,KAAM,CAACuF,aAAa,CAAEC,gBAAgB,CAAC,CAAGxF,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACyF,SAAS,CAAEC,YAAY,CAAC,CAAG1F,QAAQ,CAAC,aAAa,CAAC,CACzD,KAAM,CAAC2F,aAAa,CAAEC,gBAAgB,CAAC,CAAG5F,QAAQ,CAAiB,MAAM,CAAC,CAC1E,KAAM,CAAA6F,QAAQ,CAAG,EAAE,CACnB,KAAM,CAAAC,QAAQ,CAAG5F,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd8F,SAAS,CAAC,CAAC,CACXC,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACf,WAAW,CAAEJ,YAAY,CAAEY,SAAS,CAAEE,aAAa,CAAC,CAAC,CAEzD,KAAM,CAAAI,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFnB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAqB,QAAQ,CAAG,KAAM,CAAAzF,UAAU,CAAC0F,QAAQ,CACxCjB,WAAW,CACXY,QAAQ,CACRhB,YAAY,GAAK,KAAK,CAAGsB,SAAS,CAAGtB,YACvC,CAAC,CACDL,QAAQ,CAACyB,QAAQ,CAACG,IAAI,EAAI,EAAE,CAAC,CAC7BhB,aAAa,CAACiB,IAAI,CAACC,IAAI,CAAC,CAACL,QAAQ,CAACM,UAAU,EAAI,CAAC,EAAIV,QAAQ,CAAC,CAAC,CACjE,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C;AACAhC,QAAQ,CAAC,CACP,CACEkC,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,UAAU,CACxBC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,eAAe,CACzB3D,MAAM,CAAE,KAAK,CACb4D,WAAW,CAAE,sBAAsB,CACnCC,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,EAAE,CAClBC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACD,CACER,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,YAAY,CAC1BC,QAAQ,CAAE,WAAW,CACrB3D,MAAM,CAAE,UAAU,CAClB4D,WAAW,CAAE,sBAAsB,CACnCK,YAAY,CAAE,sBAAsB,CACpCJ,aAAa,CAAE,YAAY,CAC3BC,cAAc,CAAE,eAAe,CAC/BC,aAAa,CAAE,CAAC,CAChBC,iBAAiB,CAAE,CACrB,CAAC,CACF,CAAC,CACF9B,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,OAAS,CACRR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAoB,KAAK,CAAG,KAAM,CAAA5G,UAAU,CAAC6G,QAAQ,CAAC,CAAC,CACzC3C,SAAS,CAAC0C,KAAK,CAACE,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,IAAI,GAAK,OAAO,EAAID,IAAI,CAACE,QAAQ,CAAC,CAAC,CACzE,CAAE,MAAOjB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAkB,aAAa,CAAGnD,KAAK,CAAC+C,MAAM,CAACK,IAAI,EACrCA,IAAI,CAAChB,YAAY,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC,EAClED,IAAI,CAACf,YAAY,CAACiB,QAAQ,CAAC9C,UAAU,CAAC,EACtC4C,IAAI,CAACd,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC,EAC7DD,IAAI,CAACX,cAAc,EAAIW,IAAI,CAACX,cAAc,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAC7F,CAAC,CAED,KAAM,CAAAE,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAMnC,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC/E,CAAEiC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,OAAO,CAAEC,OAAO,CAAEA,CAAA,GAAMnC,QAAQ,CAAC,cAAc,CAAE,CAAC,CACvE,CAAEiC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,OAAO,CAAEtE,MAAM,CAAE,IAAK,CAAC,CAC5C,CAAEqE,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEC,OAAO,CAAEA,CAAA,GAAMnC,QAAQ,CAAC,gBAAgB,CAAE,CAAC,CAC3E,CAAEiC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAU,CAAEC,OAAO,CAAEA,CAAA,GAAMnC,QAAQ,CAAC,iBAAiB,CAAE,CAAC,CAC9E,CAED,KAAM,CAAAoC,UAAU,CAAIC,KAAa,EAAK,CACpC,GAAI1C,SAAS,GAAK0C,KAAK,CAAE,CACvBvC,gBAAgB,CAACD,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAAC,CAC5D,CAAC,IAAM,CACLD,YAAY,CAACyC,KAAK,CAAC,CACnBvC,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAwC,gBAAgB,CAAIT,IAAkB,EAAK,CAC/CrC,eAAe,CAACqC,IAAI,CAAC,CACrBnC,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,CAED,KAAM,CAAA6C,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAAChD,YAAY,EAAI,CAACE,aAAa,CAAE,OAErC,GAAI,CACF,KAAM,CAAA/E,UAAU,CAAC8H,UAAU,CAACjD,YAAY,CAACqB,MAAM,CAAE6B,QAAQ,CAAChD,aAAa,CAAC,CAAE,mBAAmB,CAAC,CAE9F;AACAf,QAAQ,CAACD,KAAK,EACZA,KAAK,CAACiE,GAAG,CAACb,IAAI,OAAAc,YAAA,CAAAC,aAAA,OACZ,CAAAf,IAAI,CAACjB,MAAM,GAAKrB,YAAY,CAACqB,MAAM,CAAAiC,aAAA,CAAAA,aAAA,IAE1BhB,IAAI,MACPzE,MAAM,CAAE,UAAU,CAClB8D,cAAc,CAAE,EAAAyB,YAAA,CAAAhE,MAAM,CAACmE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,MAAM,GAAKP,QAAQ,CAAChD,aAAa,CAAC,CAAC,UAAAkD,YAAA,iBAAtDA,YAAA,CAAwDM,SAAS,EAAG,GAAG,GAAAL,aAAA,CACxEjE,MAAM,CAACmE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,MAAM,GAAKP,QAAQ,CAAChD,aAAa,CAAC,CAAC,UAAAmD,aAAA,iBAAtDA,aAAA,CAAwDM,QAAQ,EAC/E7B,YAAY,CAAE,GAAI,CAAA8B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAExCvB,IAAI,EACV,CACF,CAAC,CAEDrC,eAAe,CAAC,IAAI,CAAC,CACrBE,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAE,MAAOgB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C2C,KAAK,CAAC,uBAAuB,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAJ,IAAI,CAACI,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIpB,KAAa,EAAK,CACrC,GAAI1C,SAAS,GAAK0C,KAAK,CAAE,MAAO,IAAI,CACpC,MAAO,CAAAxC,aAAa,GAAK,KAAK,CAAG,GAAG,CAAG,GAAG,CAC5C,CAAC,CAED,GAAIhB,OAAO,CAAE,CACX,mBACEjE,IAAA,CAACN,eAAe,EAACoJ,KAAK,CAAC,WAAW,CAAC1B,eAAe,CAAEA,eAAgB,CAAA2B,QAAA,cAClE/I,IAAA,CAACH,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,mBACEK,KAAA,CAACR,eAAe,EAACoJ,KAAK,CAAC,WAAW,CAAC1B,eAAe,CAAEA,eAAgB,CAAA2B,QAAA,eAClE7I,KAAA,CAACP,IAAI,EAAAoJ,QAAA,eACH7I,KAAA,QAAK8I,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eAC3G/I,IAAA,OAAIgJ,KAAK,CAAE,CAAEK,KAAK,CAAE,SAAU,CAAE,CAAAN,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrD/I,IAAA,CAACJ,MAAM,EAAC2H,OAAO,CAAEA,CAAA,GAAMnC,QAAQ,CAAC,oBAAoB,CAAE,CAAA2D,QAAA,CAAC,mBAEvD,CAAQ,CAAC,EACN,CAAC,cAEN7I,KAAA,CAACC,eAAe,EAAA4I,QAAA,eACd7I,KAAA,CAACK,YAAY,EAAC+I,KAAK,CAAEnF,YAAa,CAACoF,QAAQ,CAAGC,CAAC,EAAKpF,eAAe,CAACoF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAP,QAAA,eAClF/I,IAAA,WAAQsJ,KAAK,CAAC,KAAK,CAAAP,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvC/I,IAAA,WAAQsJ,KAAK,CAAC,KAAK,CAAAP,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChC/I,IAAA,WAAQsJ,KAAK,CAAC,UAAU,CAAAP,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1C/I,IAAA,WAAQsJ,KAAK,CAAC,aAAa,CAAAP,QAAA,CAAC,aAAW,CAAQ,CAAC,cAChD/I,IAAA,WAAQsJ,KAAK,CAAC,gBAAgB,CAAAP,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACtD/I,IAAA,WAAQsJ,KAAK,CAAC,UAAU,CAAAP,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1C/I,IAAA,WAAQsJ,KAAK,CAAC,UAAU,CAAAP,QAAA,CAAC,UAAQ,CAAQ,CAAC,EAC9B,CAAC,cAEf/I,IAAA,CAACiB,WAAW,EACVyI,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oDAAoD,CAChEL,KAAK,CAAEjF,UAAW,CAClBkF,QAAQ,CAAGC,CAAC,EAAKlF,aAAa,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,EACa,CAAC,cAElBtJ,IAAA,CAACoB,cAAc,EAAA2H,QAAA,cACb7I,KAAA,CAACoB,KAAK,EAAAyH,QAAA,eACJ/I,IAAA,UAAA+I,QAAA,cACE7I,KAAA,OAAA6I,QAAA,eACE7I,KAAA,CAACuB,WAAW,EAAC8F,OAAO,CAAEA,CAAA,GAAMC,UAAU,CAAC,QAAQ,CAAE,CAAAuB,QAAA,EAAC,KAC7C,CAACF,WAAW,CAAC,QAAQ,CAAC,EACd,CAAC,cACd3I,KAAA,CAACuB,WAAW,EAAC8F,OAAO,CAAEA,CAAA,GAAMC,UAAU,CAAC,cAAc,CAAE,CAAAuB,QAAA,EAAC,WAC7C,CAACF,WAAW,CAAC,cAAc,CAAC,EAC1B,CAAC,cACd7I,IAAA,CAACyB,WAAW,EAAAsH,QAAA,CAAC,QAAM,CAAa,CAAC,cACjC7I,KAAA,CAACuB,WAAW,EAAC8F,OAAO,CAAEA,CAAA,GAAMC,UAAU,CAAC,UAAU,CAAE,CAAAuB,QAAA,EAAC,YACxC,CAACF,WAAW,CAAC,UAAU,CAAC,EACvB,CAAC,cACd3I,KAAA,CAACuB,WAAW,EAAC8F,OAAO,CAAEA,CAAA,GAAMC,UAAU,CAAC,QAAQ,CAAE,CAAAuB,QAAA,EAAC,SACzC,CAACF,WAAW,CAAC,QAAQ,CAAC,EAClB,CAAC,cACd7I,IAAA,CAACyB,WAAW,EAAAsH,QAAA,CAAC,aAAW,CAAa,CAAC,cACtC7I,KAAA,CAACuB,WAAW,EAAC8F,OAAO,CAAEA,CAAA,GAAMC,UAAU,CAAC,aAAa,CAAE,CAAAuB,QAAA,EAAC,UAC7C,CAACF,WAAW,CAAC,aAAa,CAAC,EACxB,CAAC,cACd7I,IAAA,CAACyB,WAAW,EAAAsH,QAAA,CAAC,SAAO,CAAa,CAAC,EAChC,CAAC,CACA,CAAC,cACR/I,IAAA,UAAA+I,QAAA,CACG/B,aAAa,CAACc,GAAG,CAAEb,IAAI,eACtB/G,KAAA,CAACgC,QAAQ,EAAA6G,QAAA,eACP7I,KAAA,CAAC6B,SAAS,EAAAgH,QAAA,EAAC,GAAC,CAAC9B,IAAI,CAACjB,MAAM,EAAY,CAAC,cACrChG,IAAA,CAAC+B,SAAS,EAACiH,KAAK,CAAE,CAAEY,UAAU,CAAE,KAAM,CAAE,CAAAb,QAAA,CAAE9B,IAAI,CAAChB,YAAY,CAAY,CAAC,cACxEjG,IAAA,CAAC+B,SAAS,EAAAgH,QAAA,CAAE9B,IAAI,CAACf,YAAY,CAAY,CAAC,cAC1ClG,IAAA,CAAC+B,SAAS,EAAAgH,QAAA,CAAE9B,IAAI,CAACd,QAAQ,CAAY,CAAC,cACtCnG,IAAA,CAAC+B,SAAS,EAAAgH,QAAA,cACR/I,IAAA,CAACqC,WAAW,EAACG,MAAM,CAAEyE,IAAI,CAACzE,MAAO,CAAAuG,QAAA,CAC9B9B,IAAI,CAACzE,MAAM,CAACqH,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CACjC,CAAC,CACL,CAAC,cACZ9J,IAAA,CAAC+B,SAAS,EAAAgH,QAAA,CAAE9B,IAAI,CAACX,cAAc,EAAI,YAAY,CAAY,CAAC,cAC5DtG,IAAA,CAAC+B,SAAS,EAAAgH,QAAA,CAAEL,UAAU,CAACzB,IAAI,CAACb,WAAW,CAAC,CAAY,CAAC,cACrDpG,IAAA,CAAC+B,SAAS,EAAAgH,QAAA,cACR7I,KAAA,CAACuC,aAAa,EAAAsG,QAAA,eACZ/I,IAAA,CAACJ,MAAM,EACLmK,IAAI,CAAC,IAAI,CACTxC,OAAO,CAAEA,CAAA,GAAMnC,QAAQ,UAAA4E,MAAA,CAAU/C,IAAI,CAACjB,MAAM,CAAE,CAAE,CAAA+C,QAAA,CACjD,MAED,CAAQ,CAAC,CACR9B,IAAI,CAACzE,MAAM,GAAK,KAAK,eACpBxC,IAAA,CAACJ,MAAM,EACLmK,IAAI,CAAC,IAAI,CACTE,OAAO,CAAC,WAAW,CACnB1C,OAAO,CAAEA,CAAA,GAAMG,gBAAgB,CAACT,IAAI,CAAE,CAAA8B,QAAA,CACvC,QAED,CAAQ,CACT,EACY,CAAC,CACP,CAAC,GA9BC9B,IAAI,CAACjB,MA+BV,CACX,CAAC,CACG,CAAC,EACH,CAAC,CACM,CAAC,CAEhBgB,aAAa,CAACkD,MAAM,GAAK,CAAC,eACzBlK,IAAA,QAAKgJ,KAAK,CAAE,CAAEmB,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEf,KAAK,CAAE,MAAO,CAAE,CAAAN,QAAA,CACjE1E,UAAU,CAAG,sCAAsC,CAAG,iBAAiB,CACrE,CACN,cAGDnE,KAAA,CAACyC,UAAU,EAAAoG,QAAA,eACT/I,IAAA,CAAC6C,UAAU,EACT0E,OAAO,CAAEA,CAAA,GAAM/C,cAAc,CAAC,CAAC,CAAE,CACjC6F,QAAQ,CAAE9F,WAAW,GAAK,CAAE,CAAAwE,QAAA,CAC7B,OAED,CAAY,CAAC,cACb/I,IAAA,CAAC6C,UAAU,EACT0E,OAAO,CAAEA,CAAA,GAAM/C,cAAc,CAACD,WAAW,CAAG,CAAC,CAAE,CAC/C8F,QAAQ,CAAE9F,WAAW,GAAK,CAAE,CAAAwE,QAAA,CAC7B,UAED,CAAY,CAAC,CAEZuB,KAAK,CAACC,IAAI,CAAC,CAAEL,MAAM,CAAEvE,IAAI,CAAC6E,GAAG,CAAC,CAAC,CAAE/F,UAAU,CAAE,CAAC,CAAE,CAACgG,CAAC,CAAEC,CAAC,GAAK,CACzD,KAAM,CAAAC,IAAI,CAAGhF,IAAI,CAACiF,GAAG,CAAC,CAAC,CAAErG,WAAW,CAAG,CAAC,CAAC,CAAGmG,CAAC,CAC7C,GAAIC,IAAI,EAAIlG,UAAU,CAAE,CACtB,mBACEzE,IAAA,CAAC6C,UAAU,EAETG,MAAM,CAAE2H,IAAI,GAAKpG,WAAY,CAC7BgD,OAAO,CAAEA,CAAA,GAAM/C,cAAc,CAACmG,IAAI,CAAE,CAAA5B,QAAA,CAEnC4B,IAAI,EAJAA,IAKK,CAAC,CAEjB,CACA,MAAO,KAAI,CACb,CAAC,CAAC,cAEF3K,IAAA,CAAC6C,UAAU,EACT0E,OAAO,CAAEA,CAAA,GAAM/C,cAAc,CAACD,WAAW,CAAG,CAAC,CAAE,CAC/C8F,QAAQ,CAAE9F,WAAW,GAAKE,UAAW,CAAAsE,QAAA,CACtC,MAED,CAAY,CAAC,cACb/I,IAAA,CAAC6C,UAAU,EACT0E,OAAO,CAAEA,CAAA,GAAM/C,cAAc,CAACC,UAAU,CAAE,CAC1C4F,QAAQ,CAAE9F,WAAW,GAAKE,UAAW,CAAAsE,QAAA,CACtC,MAED,CAAY,CAAC,EACH,CAAC,EACT,CAAC,cAGP/I,IAAA,CAACkD,WAAW,EAACE,MAAM,CAAE,CAAC,CAACuB,YAAa,CAAAoE,QAAA,cAClC7I,KAAA,CAACmD,YAAY,EAAA0F,QAAA,eACX7I,KAAA,CAACqD,UAAU,EAAAwF,QAAA,EAAC,gBAAc,CAACpE,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsB,YAAY,EAAa,CAAC,cAEnE/F,KAAA,QAAK8I,KAAK,CAAE,CAAEI,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnC/I,IAAA,UAAOgJ,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEG,YAAY,CAAE,KAAK,CAAEQ,UAAU,CAAE,KAAM,CAAE,CAAAb,QAAA,CAAC,eAE5E,CAAO,CAAC,cACR7I,KAAA,CAACK,YAAY,EACX+I,KAAK,CAAEzE,aAAc,CACrB0E,QAAQ,CAAGC,CAAC,EAAK1E,gBAAgB,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDN,KAAK,CAAE,CAAE6B,KAAK,CAAE,MAAO,CAAE,CAAA9B,QAAA,eAEzB/I,IAAA,WAAQsJ,KAAK,CAAC,EAAE,CAAAP,QAAA,CAAC,oBAAkB,CAAQ,CAAC,CAC3ChF,MAAM,CAAC+D,GAAG,CAACgD,KAAK,eACf5K,KAAA,WAA2BoJ,KAAK,CAAEwB,KAAK,CAAC1C,MAAO,CAAAW,QAAA,EAC5C+B,KAAK,CAACzC,SAAS,CAAC,GAAC,CAACyC,KAAK,CAACxC,QAAQ,CAAC,IAAE,CAACwC,KAAK,CAACC,QAAQ,CAAC,GACtD,GAFaD,KAAK,CAAC1C,MAEX,CACT,CAAC,EACU,CAAC,EACZ,CAAC,cAENlI,KAAA,CAACwD,YAAY,EAAAqF,QAAA,eACX/I,IAAA,CAACJ,MAAM,EACLqK,OAAO,CAAC,SAAS,CACjB1C,OAAO,CAAEA,CAAA,GAAM,CACb3C,eAAe,CAAC,IAAI,CAAC,CACrBE,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAE,CAAAiE,QAAA,CACH,QAED,CAAQ,CAAC,cACT/I,IAAA,CAACJ,MAAM,EACL2H,OAAO,CAAEI,gBAAiB,CAC1B0C,QAAQ,CAAE,CAACxF,aAAc,CAAAkE,QAAA,CAC1B,aAED,CAAQ,CAAC,EACG,CAAC,EACH,CAAC,CACJ,CAAC,EACC,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAnF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}