{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Dashboard\\\\AdminDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c = StatsContainer;\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n_c2 = StatCard;\nconst StatIcon = styled.div`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ${props => props.theme.colors.white};\n  background: ${props => props.color};\n`;\n_c3 = StatIcon;\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n_c4 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n_c5 = StatLabel;\nconst ActionGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c6 = ActionGrid;\nconst ActionCard = styled(Card)`\n  text-align: center;\n  padding: 30px 20px;\n`;\n_c7 = ActionCard;\nconst ActionIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: 15px;\n`;\n_c8 = ActionIcon;\nconst ActionTitle = styled.h3`\n  color: ${props => props.theme.colors.primary};\n  margin-bottom: 10px;\n  font-size: 18px;\n`;\n_c9 = ActionTitle;\nconst ActionDescription = styled.p`\n  color: ${props => props.theme.colors.textMedium};\n  margin-bottom: 20px;\n  font-size: 14px;\n`;\n_c0 = ActionDescription;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c1 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c10 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c11 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c12 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c13 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.status) {\n    case 'new':\n      return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n    case 'assigned':\n      return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n    case 'in-progress':\n      return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n    case 'pending-review':\n      return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n    case 'approved':\n      return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n    case 'rejected':\n      return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c14 = StatusBadge;\nconst AdminDashboard = () => {\n  _s();\n  const [leads, setLeads] = useState([]);\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [leadsResponse, statsResponse] = await Promise.all([apiService.getLeads(1, 10), apiService.getDashboardStats()]);\n      setLeads(leadsResponse.data || []);\n      setStats(statsResponse);\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Mock data for demo\n      setLeads([{\n        leadId: 1,\n        customerName: 'John Doe',\n        mobileNumber: '9876543210',\n        loanType: 'Personal Loan',\n        status: 'new',\n        createdDate: '2024-01-15T10:30:00Z',\n        createdByName: 'Admin User',\n        documentCount: 0,\n        croppedImageCount: 0\n      }, {\n        leadId: 2,\n        customerName: 'Jane Smith',\n        mobileNumber: '9876543211',\n        loanType: 'Home Loan',\n        status: 'assigned',\n        createdDate: '2024-01-14T09:15:00Z',\n        createdByName: 'Admin User',\n        assignedToName: 'Agent Smith',\n        documentCount: 2,\n        croppedImageCount: 1\n      }]);\n      setStats({\n        totalLeads: 25,\n        newLeads: 5,\n        assignedLeads: 8,\n        inProgressLeads: 6,\n        pendingReviewLeads: 4,\n        approvedLeads: 2,\n        rejectedLeads: 0,\n        completedLeads: 2,\n        pendingLeads: 10\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    active: true\n  }, {\n    icon: '➕',\n    label: 'Create Lead',\n    onClick: () => navigate('/admin/create-lead')\n  }, {\n    icon: '👥',\n    label: 'Manage Users',\n    onClick: () => navigate('/admin/users')\n  }, {\n    icon: '📋',\n    label: 'All Leads',\n    onClick: () => navigate('/admin/leads')\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    onClick: () => navigate('/admin/reports')\n  }, {\n    icon: '⚙️',\n    label: 'Settings',\n    onClick: () => navigate('/admin/settings')\n  }];\n  const handleCreateLead = () => {\n    navigate('/admin/create-lead');\n  };\n  const handleManageUsers = () => {\n    navigate('/admin/users');\n  };\n  const handleViewReports = () => {\n    navigate('/admin/reports');\n  };\n  const handleLeadClick = leadId => {\n    navigate(`/lead/${leadId}`);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"Admin Dashboard\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"Admin Dashboard\",\n    navigationItems: navigationItems,\n    children: [/*#__PURE__*/_jsxDEV(StatsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #007E3A, #005a2a)\",\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.totalLeads) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Total Leads\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #e3f2fd, #0d47a1)\",\n          children: \"\\uD83C\\uDD95\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.newLeads) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"New Leads\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #fff8e1, #ff8f00)\",\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.inProgressLeads) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"In Progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #f3e5f5, #4a148c)\",\n          children: \"\\uD83D\\uDC41\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.pendingReviewLeads) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Pending Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #28a745, #1e7e34)\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.approvedLeads) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Approved\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"linear-gradient(135deg, #dc3545, #c82333)\",\n          children: \"\\u274C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: (stats === null || stats === void 0 ? void 0 : stats.rejectedLeads) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Rejected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ActionGrid, {\n      children: [/*#__PURE__*/_jsxDEV(ActionCard, {\n        children: [/*#__PURE__*/_jsxDEV(ActionIcon, {\n          children: \"\\u2795\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionTitle, {\n          children: \"Create New Lead\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionDescription, {\n          children: \"Add a new customer verification lead to the system\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateLead,\n          children: \"Create Lead\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionCard, {\n        children: [/*#__PURE__*/_jsxDEV(ActionIcon, {\n          children: \"\\uD83D\\uDC65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionTitle, {\n          children: \"Manage Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionDescription, {\n          children: \"Add, edit, or deactivate agents and supervisors\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleManageUsers,\n          children: \"Manage Users\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionCard, {\n        children: [/*#__PURE__*/_jsxDEV(ActionIcon, {\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionTitle, {\n          children: \"View Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionDescription, {\n          children: \"Generate and view system performance reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleViewReports,\n          children: \"View Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '20px',\n          color: '#007E3A'\n        },\n        children: \"Recent Leads\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Customer Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Mobile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Loan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Created Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Assigned Agent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: leads.map(lead => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.customerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.mobileNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.loanType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                  status: lead.status,\n                  children: lead.status.replace('-', ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(lead.createdDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: lead.assignedToName || 'Unassigned'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"sm\",\n                  onClick: () => handleLeadClick(lead.leadId),\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)]\n            }, lead.leadId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), leads.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#777'\n        },\n        children: \"No leads found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"HvE0YxRnciiAvEIk3kYy6VTVraQ=\", false, function () {\n  return [useNavigate];\n});\n_c15 = AdminDashboard;\nexport default AdminDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"StatsContainer\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"StatIcon\");\n$RefreshReg$(_c4, \"StatValue\");\n$RefreshReg$(_c5, \"StatLabel\");\n$RefreshReg$(_c6, \"ActionGrid\");\n$RefreshReg$(_c7, \"ActionCard\");\n$RefreshReg$(_c8, \"ActionIcon\");\n$RefreshReg$(_c9, \"ActionTitle\");\n$RefreshReg$(_c0, \"ActionDescription\");\n$RefreshReg$(_c1, \"TableContainer\");\n$RefreshReg$(_c10, \"Table\");\n$RefreshReg$(_c11, \"TableHeader\");\n$RefreshReg$(_c12, \"TableCell\");\n$RefreshReg$(_c13, \"TableRow\");\n$RefreshReg$(_c14, \"StatusBadge\");\n$RefreshReg$(_c15, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "apiService", "jsxDEV", "_jsxDEV", "StatsContainer", "div", "_c", "StatCard", "props", "theme", "transitions", "default", "shadows", "lg", "_c2", "StatIcon", "colors", "white", "color", "_c3", "StatValue", "textDark", "_c4", "StatLabel", "textLight", "_c5", "ActionGrid", "_c6", "ActionCard", "_c7", "ActionIcon", "_c8", "ActionTitle", "h3", "primary", "_c9", "ActionDescription", "p", "textMedium", "_c0", "TableContainer", "_c1", "Table", "table", "_c10", "TableHeader", "th", "lightGray", "offWhite", "_c11", "TableCell", "td", "_c12", "TableRow", "tr", "_c13", "StatusBadge", "span", "status", "_c14", "AdminDashboard", "_s", "leads", "setLeads", "stats", "setStats", "loading", "setLoading", "navigate", "loadDashboardData", "leadsResponse", "statsResponse", "Promise", "all", "getLeads", "getDashboardStats", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "createdByName", "documentCount", "croppedImageCount", "assignedToName", "totalLeads", "newLeads", "assignedLeads", "inProgressLeads", "pendingReviewLeads", "approvedLeads", "rejectedLeads", "completedLeads", "pendingLeads", "navigationItems", "icon", "label", "active", "onClick", "handleCreateLead", "handleManageUsers", "handleViewReports", "handleLeadClick", "formatDate", "dateString", "Date", "toLocaleDateString", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "map", "lead", "replace", "toUpperCase", "size", "length", "textAlign", "padding", "_c15", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Dashboard/AdminDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem, DashboardStats } from '../../services/apiService';\n\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: ${props => props.theme.transitions.default};\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n\nconst StatIcon = styled.div<{ color: string }>`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-size: 20px;\n  color: ${props => props.theme.colors.white};\n  background: ${props => props.color};\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n\nconst ActionGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst ActionCard = styled(Card)`\n  text-align: center;\n  padding: 30px 20px;\n`;\n\nconst ActionIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: 15px;\n`;\n\nconst ActionTitle = styled.h3`\n  color: ${props => props.theme.colors.primary};\n  margin-bottom: 10px;\n  font-size: 18px;\n`;\n\nconst ActionDescription = styled.p`\n  color: ${props => props.theme.colors.textMedium};\n  margin-bottom: 20px;\n  font-size: 14px;\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'new':\n        return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n      case 'assigned':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'in-progress':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'approved':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst AdminDashboard: React.FC = () => {\n  const [leads, setLeads] = useState<LeadListItem[]>([]);\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      const [leadsResponse, statsResponse] = await Promise.all([\n        apiService.getLeads(1, 10),\n        apiService.getDashboardStats(),\n      ]);\n\n      setLeads(leadsResponse.data || []);\n      setStats(statsResponse);\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Mock data for demo\n      setLeads([\n        {\n          leadId: 1,\n          customerName: 'John Doe',\n          mobileNumber: '9876543210',\n          loanType: 'Personal Loan',\n          status: 'new',\n          createdDate: '2024-01-15T10:30:00Z',\n          createdByName: 'Admin User',\n          documentCount: 0,\n          croppedImageCount: 0,\n        },\n        {\n          leadId: 2,\n          customerName: 'Jane Smith',\n          mobileNumber: '9876543211',\n          loanType: 'Home Loan',\n          status: 'assigned',\n          createdDate: '2024-01-14T09:15:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Agent Smith',\n          documentCount: 2,\n          croppedImageCount: 1,\n        },\n      ]);\n      setStats({\n        totalLeads: 25,\n        newLeads: 5,\n        assignedLeads: 8,\n        inProgressLeads: 6,\n        pendingReviewLeads: 4,\n        approvedLeads: 2,\n        rejectedLeads: 0,\n        completedLeads: 2,\n        pendingLeads: 10,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', active: true },\n    { icon: '➕', label: 'Create Lead', onClick: () => navigate('/admin/create-lead') },\n    { icon: '👥', label: 'Manage Users', onClick: () => navigate('/admin/users') },\n    { icon: '📋', label: 'All Leads', onClick: () => navigate('/admin/leads') },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/admin/reports') },\n    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },\n  ];\n\n  const handleCreateLead = () => {\n    navigate('/admin/create-lead');\n  };\n\n  const handleManageUsers = () => {\n    navigate('/admin/users');\n  };\n\n  const handleViewReports = () => {\n    navigate('/admin/reports');\n  };\n\n  const handleLeadClick = (leadId: number) => {\n    navigate(`/lead/${leadId}`);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Admin Dashboard\" navigationItems={navigationItems}>\n        <div>Loading...</div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Admin Dashboard\" navigationItems={navigationItems}>\n      {/* Stats Cards */}\n      <StatsContainer>\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #007E3A, #005a2a)\">📊</StatIcon>\n          <StatValue>{stats?.totalLeads || 0}</StatValue>\n          <StatLabel>Total Leads</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #e3f2fd, #0d47a1)\">🆕</StatIcon>\n          <StatValue>{stats?.newLeads || 0}</StatValue>\n          <StatLabel>New Leads</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #fff8e1, #ff8f00)\">⏳</StatIcon>\n          <StatValue>{stats?.inProgressLeads || 0}</StatValue>\n          <StatLabel>In Progress</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #f3e5f5, #4a148c)\">👁️</StatIcon>\n          <StatValue>{stats?.pendingReviewLeads || 0}</StatValue>\n          <StatLabel>Pending Review</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #28a745, #1e7e34)\">✅</StatIcon>\n          <StatValue>{stats?.approvedLeads || 0}</StatValue>\n          <StatLabel>Approved</StatLabel>\n        </StatCard>\n\n        <StatCard>\n          <StatIcon color=\"linear-gradient(135deg, #dc3545, #c82333)\">❌</StatIcon>\n          <StatValue>{stats?.rejectedLeads || 0}</StatValue>\n          <StatLabel>Rejected</StatLabel>\n        </StatCard>\n      </StatsContainer>\n\n      {/* Quick Actions */}\n      <ActionGrid>\n        <ActionCard>\n          <ActionIcon>➕</ActionIcon>\n          <ActionTitle>Create New Lead</ActionTitle>\n          <ActionDescription>\n            Add a new customer verification lead to the system\n          </ActionDescription>\n          <Button onClick={handleCreateLead}>Create Lead</Button>\n        </ActionCard>\n\n        <ActionCard>\n          <ActionIcon>👥</ActionIcon>\n          <ActionTitle>Manage Users</ActionTitle>\n          <ActionDescription>\n            Add, edit, or deactivate agents and supervisors\n          </ActionDescription>\n          <Button onClick={handleManageUsers}>Manage Users</Button>\n        </ActionCard>\n\n        <ActionCard>\n          <ActionIcon>📊</ActionIcon>\n          <ActionTitle>View Reports</ActionTitle>\n          <ActionDescription>\n            Generate and view system performance reports\n          </ActionDescription>\n          <Button onClick={handleViewReports}>View Reports</Button>\n        </ActionCard>\n      </ActionGrid>\n\n      {/* Recent Leads */}\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Recent Leads</h2>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer Name</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Created Date</TableHeader>\n                <TableHeader>Assigned Agent</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {leads.map((lead) => (\n                <TableRow key={lead.leadId}>\n                  <TableCell>{lead.customerName}</TableCell>\n                  <TableCell>{lead.mobileNumber}</TableCell>\n                  <TableCell>{lead.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={lead.status}>\n                      {lead.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>{formatDate(lead.createdDate)}</TableCell>\n                  <TableCell>\n                    {lead.assignedToName || 'Unassigned'}\n                  </TableCell>\n                  <TableCell>\n                    <Button\n                      size=\"sm\"\n                      onClick={() => handleLeadClick(lead.leadId)}\n                    >\n                      View Details\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {leads.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            No leads found.\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,QAAwB,2BAA2B;AACxE,SAASC,UAAU,QAAsC,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,cAAc,GAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,cAAc;AAOpB,MAAMG,QAAQ,GAAGV,MAAM,CAACE,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA,gBAAgBS,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,WAAW,CAACC,OAAO;AACxD;AACA;AACA;AACA,kBAAkBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,OAAO,CAACC,EAAE;AACjD;AACA,CAAC;AAACC,GAAA,GAXIP,QAAQ;AAad,MAAMQ,QAAQ,GAAGlB,MAAM,CAACQ,GAAsB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACC,KAAK;AAC5C,gBAAgBT,KAAK,IAAIA,KAAK,CAACU,KAAK;AACpC,CAAC;AAACC,GAAA,GAXIJ,QAAQ;AAad,MAAMK,SAAS,GAAGvB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACK,QAAQ;AAC/C,CAAC;AAACC,GAAA,GALIF,SAAS;AAOf,MAAMG,SAAS,GAAG1B,MAAM,CAACQ,GAAG;AAC5B;AACA,WAAWG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACQ,SAAS;AAChD;AACA,CAAC;AAACC,GAAA,GAJIF,SAAS;AAMf,MAAMG,UAAU,GAAG7B,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GALID,UAAU;AAOhB,MAAME,UAAU,GAAG/B,MAAM,CAACE,IAAI,CAAC;AAC/B;AACA;AACA,CAAC;AAAC8B,GAAA,GAHID,UAAU;AAKhB,MAAME,UAAU,GAAGjC,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA,CAAC;AAAC0B,GAAA,GAHID,UAAU;AAKhB,MAAME,WAAW,GAAGnC,MAAM,CAACoC,EAAE;AAC7B,WAAWzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACkB,OAAO;AAC9C;AACA;AACA,CAAC;AAACC,GAAA,GAJIH,WAAW;AAMjB,MAAMI,iBAAiB,GAAGvC,MAAM,CAACwC,CAAC;AAClC,WAAW7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACsB,UAAU;AACjD;AACA;AACA,CAAC;AAACC,GAAA,GAJIH,iBAAiB;AAMvB,MAAMI,cAAc,GAAG3C,MAAM,CAACQ,GAAG;AACjC;AACA,CAAC;AAACoC,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAG7C,MAAM,CAAC8C,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,IAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAGhD,MAAM,CAACiD,EAAE;AAC7B;AACA;AACA,6BAA6BtC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAAC+B,SAAS;AAClE,sBAAsBvC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACgC,QAAQ;AAC1D;AACA,WAAWxC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAACsB,UAAU;AACjD,CAAC;AAACW,IAAA,GAPIJ,WAAW;AASjB,MAAMK,SAAS,GAAGrD,MAAM,CAACsD,EAAE;AAC3B;AACA;AACA,6BAA6B3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAAC+B,SAAS;AAClE,CAAC;AAACK,IAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGxD,MAAM,CAACyD,EAAE;AAC1B;AACA,wBAAwB9C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,MAAM,CAAC+B,SAAS;AAC7D;AACA,CAAC;AAACQ,IAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAG3D,MAAM,CAAC4D,IAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIjD,KAAK,IAAI;EACT,QAAQA,KAAK,CAACkD,MAAM;IAClB,KAAK,KAAK;MACR,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,aAAa;MAChB,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,gBAAgB;MACnB,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,IAAA,GA9CIH,WAAW;AAgDjB,MAAMI,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrE,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAGvE,QAAQ,CAAwB,IAAI,CAAC;EAC/D,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM0E,QAAQ,GAAGxE,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd0E,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACG,aAAa,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACvDxE,UAAU,CAACyE,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAC1BzE,UAAU,CAAC0E,iBAAiB,CAAC,CAAC,CAC/B,CAAC;MAEFZ,QAAQ,CAACO,aAAa,CAACM,IAAI,IAAI,EAAE,CAAC;MAClCX,QAAQ,CAACM,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACAd,QAAQ,CAAC,CACP;QACEgB,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,UAAU;QACxBC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,eAAe;QACzBxB,MAAM,EAAE,KAAK;QACbyB,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE,YAAY;QAC3BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEP,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,YAAY;QAC1BC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,WAAW;QACrBxB,MAAM,EAAE,UAAU;QAClByB,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE,YAAY;QAC3BG,cAAc,EAAE,aAAa;QAC7BF,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,CACF,CAAC;MACFrB,QAAQ,CAAC;QACPuB,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,CAAC;QACXC,aAAa,EAAE,CAAC;QAChBC,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,CAAC;QACrBC,aAAa,EAAE,CAAC;QAChBC,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,MAAM,EAAE;EAAK,CAAC,EAChD;IAAEF,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,aAAa;IAAEE,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,oBAAoB;EAAE,CAAC,EAClF;IAAE8B,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,cAAc;IAAEE,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,cAAc;EAAE,CAAC,EAC9E;IAAE8B,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEE,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,cAAc;EAAE,CAAC,EAC3E;IAAE8B,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEE,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,gBAAgB;EAAE,CAAC,EAC3E;IAAE8B,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,UAAU;IAAEE,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,iBAAiB;EAAE,CAAC,CAC9E;EAED,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlC,QAAQ,CAAC,oBAAoB,CAAC;EAChC,CAAC;EAED,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnC,QAAQ,CAAC,cAAc,CAAC;EAC1B,CAAC;EAED,MAAMoC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpC,QAAQ,CAAC,gBAAgB,CAAC;EAC5B,CAAC;EAED,MAAMqC,eAAe,GAAI1B,MAAc,IAAK;IAC1CX,QAAQ,CAAC,SAASW,MAAM,EAAE,CAAC;EAC7B,CAAC;EAED,MAAM2B,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAI3C,OAAO,EAAE;IACX,oBACE/D,OAAA,CAACL,eAAe;MAACgH,KAAK,EAAC,iBAAiB;MAACb,eAAe,EAAEA,eAAgB;MAAAc,QAAA,eACxE5G,OAAA;QAAA4G,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEtB;EAEA,oBACEhH,OAAA,CAACL,eAAe;IAACgH,KAAK,EAAC,iBAAiB;IAACb,eAAe,EAAEA,eAAgB;IAAAc,QAAA,gBAExE5G,OAAA,CAACC,cAAc;MAAA2G,QAAA,gBACb5G,OAAA,CAACI,QAAQ;QAAAwG,QAAA,gBACP5G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA6F,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACzEhH,OAAA,CAACiB,SAAS;UAAA2F,QAAA,EAAE,CAAA/C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwB,UAAU,KAAI;QAAC;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC/ChH,OAAA,CAACoB,SAAS;UAAAwF,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAEXhH,OAAA,CAACI,QAAQ;QAAAwG,QAAA,gBACP5G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA6F,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACzEhH,OAAA,CAACiB,SAAS;UAAA2F,QAAA,EAAE,CAAA/C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,QAAQ,KAAI;QAAC;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC7ChH,OAAA,CAACoB,SAAS;UAAAwF,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEXhH,OAAA,CAACI,QAAQ;QAAAwG,QAAA,gBACP5G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA6F,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxEhH,OAAA,CAACiB,SAAS;UAAA2F,QAAA,EAAE,CAAA/C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2B,eAAe,KAAI;QAAC;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACpDhH,OAAA,CAACoB,SAAS;UAAAwF,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAEXhH,OAAA,CAACI,QAAQ;QAAAwG,QAAA,gBACP5G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA6F,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC1EhH,OAAA,CAACiB,SAAS;UAAA2F,QAAA,EAAE,CAAA/C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4B,kBAAkB,KAAI;QAAC;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvDhH,OAAA,CAACoB,SAAS;UAAAwF,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEXhH,OAAA,CAACI,QAAQ;QAAAwG,QAAA,gBACP5G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA6F,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxEhH,OAAA,CAACiB,SAAS;UAAA2F,QAAA,EAAE,CAAA/C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6B,aAAa,KAAI;QAAC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClDhH,OAAA,CAACoB,SAAS;UAAAwF,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAEXhH,OAAA,CAACI,QAAQ;QAAAwG,QAAA,gBACP5G,OAAA,CAACY,QAAQ;UAACG,KAAK,EAAC,2CAA2C;UAAA6F,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxEhH,OAAA,CAACiB,SAAS;UAAA2F,QAAA,EAAE,CAAA/C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8B,aAAa,KAAI;QAAC;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClDhH,OAAA,CAACoB,SAAS;UAAAwF,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGjBhH,OAAA,CAACuB,UAAU;MAAAqF,QAAA,gBACT5G,OAAA,CAACyB,UAAU;QAAAmF,QAAA,gBACT5G,OAAA,CAAC2B,UAAU;UAAAiF,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1BhH,OAAA,CAAC6B,WAAW;UAAA+E,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1ChH,OAAA,CAACiC,iBAAiB;UAAA2E,QAAA,EAAC;QAEnB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBhH,OAAA,CAACH,MAAM;UAACqG,OAAO,EAAEC,gBAAiB;UAAAS,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAEbhH,OAAA,CAACyB,UAAU;QAAAmF,QAAA,gBACT5G,OAAA,CAAC2B,UAAU;UAAAiF,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhH,OAAA,CAAC6B,WAAW;UAAA+E,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvChH,OAAA,CAACiC,iBAAiB;UAAA2E,QAAA,EAAC;QAEnB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBhH,OAAA,CAACH,MAAM;UAACqG,OAAO,EAAEE,iBAAkB;UAAAQ,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eAEbhH,OAAA,CAACyB,UAAU;QAAAmF,QAAA,gBACT5G,OAAA,CAAC2B,UAAU;UAAAiF,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC3BhH,OAAA,CAAC6B,WAAW;UAAA+E,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvChH,OAAA,CAACiC,iBAAiB;UAAA2E,QAAA,EAAC;QAEnB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBhH,OAAA,CAACH,MAAM;UAACqG,OAAO,EAAEG,iBAAkB;UAAAO,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGbhH,OAAA,CAACJ,IAAI;MAAAgH,QAAA,gBACH5G,OAAA;QAAIiH,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEnG,KAAK,EAAE;QAAU,CAAE;QAAA6F,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAExEhH,OAAA,CAACqC,cAAc;QAAAuE,QAAA,eACb5G,OAAA,CAACuC,KAAK;UAAAqE,QAAA,gBACJ5G,OAAA;YAAA4G,QAAA,eACE5G,OAAA;cAAA4G,QAAA,gBACE5G,OAAA,CAAC0C,WAAW;gBAAAkE,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxChH,OAAA,CAAC0C,WAAW;gBAAAkE,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjChH,OAAA,CAAC0C,WAAW;gBAAAkE,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpChH,OAAA,CAAC0C,WAAW;gBAAAkE,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjChH,OAAA,CAAC0C,WAAW;gBAAAkE,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvChH,OAAA,CAAC0C,WAAW;gBAAAkE,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzChH,OAAA,CAAC0C,WAAW;gBAAAkE,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhH,OAAA;YAAA4G,QAAA,EACGjD,KAAK,CAACwD,GAAG,CAAEC,IAAI,iBACdpH,OAAA,CAACkD,QAAQ;cAAA0D,QAAA,gBACP5G,OAAA,CAAC+C,SAAS;gBAAA6D,QAAA,EAAEQ,IAAI,CAACvC;cAAY;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1ChH,OAAA,CAAC+C,SAAS;gBAAA6D,QAAA,EAAEQ,IAAI,CAACtC;cAAY;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1ChH,OAAA,CAAC+C,SAAS;gBAAA6D,QAAA,EAAEQ,IAAI,CAACrC;cAAQ;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtChH,OAAA,CAAC+C,SAAS;gBAAA6D,QAAA,eACR5G,OAAA,CAACqD,WAAW;kBAACE,MAAM,EAAE6D,IAAI,CAAC7D,MAAO;kBAAAqD,QAAA,EAC9BQ,IAAI,CAAC7D,MAAM,CAAC8D,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACZhH,OAAA,CAAC+C,SAAS;gBAAA6D,QAAA,EAAEL,UAAU,CAACa,IAAI,CAACpC,WAAW;cAAC;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrDhH,OAAA,CAAC+C,SAAS;gBAAA6D,QAAA,EACPQ,IAAI,CAAChC,cAAc,IAAI;cAAY;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACZhH,OAAA,CAAC+C,SAAS;gBAAA6D,QAAA,eACR5G,OAAA,CAACH,MAAM;kBACL0H,IAAI,EAAC,IAAI;kBACTrB,OAAO,EAAEA,CAAA,KAAMI,eAAe,CAACc,IAAI,CAACxC,MAAM,CAAE;kBAAAgC,QAAA,EAC7C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GApBCI,IAAI,CAACxC,MAAM;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBhB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBrD,KAAK,CAAC6D,MAAM,KAAK,CAAC,iBACjBxH,OAAA;QAAKiH,KAAK,EAAE;UAAEQ,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAE3G,KAAK,EAAE;QAAO,CAAE;QAAA6F,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEtB,CAAC;AAACtD,EAAA,CAlOID,cAAwB;EAAA,QAIXhE,WAAW;AAAA;AAAAkI,IAAA,GAJxBlE,cAAwB;AAoO9B,eAAeA,cAAc;AAAC,IAAAtD,EAAA,EAAAQ,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAK,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAmE,IAAA;AAAAC,YAAA,CAAAzH,EAAA;AAAAyH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}