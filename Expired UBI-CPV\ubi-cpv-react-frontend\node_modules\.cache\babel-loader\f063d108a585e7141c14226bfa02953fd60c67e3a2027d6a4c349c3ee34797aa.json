{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Admin\\\\UserManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage } from '../../styles/GlobalStyles';\nimport { User } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n_c3 = Title;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c4 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c5 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c6 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c7 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c8 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ${props => props.active ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.active ? '#2e7d32' : '#c62828'};\n`;\n_c9 = StatusBadge;\nconst RoleBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.role) {\n    case 'Admin':\n      return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n    case 'Supervisor':\n      return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n    case 'Agent':\n      return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c0 = RoleBadge;\nconst Modal = styled.div`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n_c1 = Modal;\nconst ModalContent = styled.div`\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n`;\n_c10 = ModalContent;\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n_c11 = ModalHeader;\nconst ModalTitle = styled.h2`\n  color: ${props => props.theme.colors.primary};\n  margin: 0;\n`;\n_c12 = ModalTitle;\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ${props => props.theme.colors.textMedium};\n\n  &:hover {\n    color: ${props => props.theme.colors.textDark};\n  }\n`;\n_c13 = CloseButton;\nconst UserManagement = () => {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: 'Agent',\n    password: '',\n    confirmPassword: ''\n  });\n  const [errors, setErrors] = useState({});\n  useEffect(() => {\n    loadUsers();\n  }, []);\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      // Mock users data\n      const mockUsers = [{\n        userId: 1,\n        username: 'agent1',\n        firstName: 'John',\n        lastName: 'Agent',\n        email: '<EMAIL>',\n        role: 'Agent',\n        isActive: true,\n        createdDate: '2024-01-01T00:00:00Z',\n        lastLoginDate: '2024-01-16T08:30:00Z'\n      }, {\n        userId: 2,\n        username: 'supervisor1',\n        firstName: 'Jane',\n        lastName: 'Supervisor',\n        email: '<EMAIL>',\n        role: 'Supervisor',\n        isActive: true,\n        createdDate: '2024-01-01T00:00:00Z',\n        lastLoginDate: '2024-01-16T09:15:00Z'\n      }, {\n        userId: 3,\n        username: 'admin1',\n        firstName: 'Admin',\n        lastName: 'User',\n        email: '<EMAIL>',\n        role: 'Admin',\n        isActive: true,\n        createdDate: '2024-01-01T00:00:00Z',\n        lastLoginDate: '2024-01-16T10:00:00Z'\n      }, {\n        userId: 4,\n        username: 'agent2',\n        firstName: 'Bob',\n        lastName: 'Agent',\n        email: '<EMAIL>',\n        role: 'Agent',\n        isActive: false,\n        createdDate: '2024-01-05T00:00:00Z',\n        lastLoginDate: '2024-01-10T14:20:00Z'\n      }];\n      setUsers(mockUsers);\n    } catch (error) {\n      console.error('Error loading users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!editingUser) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 6) {\n        newErrors.password = 'Password must be at least 6 characters';\n      }\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    try {\n      if (editingUser) {\n        // Update existing user\n        const updatedUser = {\n          ...editingUser,\n          username: formData.username,\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          role: formData.role\n        };\n        setUsers(prev => prev.map(user => user.userId === editingUser.userId ? updatedUser : user));\n        alert('User updated successfully!');\n      } else {\n        // Create new user\n        const newUser = {\n          userId: Math.max(...users.map(u => u.userId)) + 1,\n          username: formData.username,\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          role: formData.role,\n          isActive: true,\n          createdDate: new Date().toISOString()\n        };\n        setUsers(prev => [...prev, newUser]);\n        alert('User created successfully!');\n      }\n      handleCloseModal();\n    } catch (error) {\n      console.error('Error saving user:', error);\n      alert('Failed to save user. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n  const handleEditUser = user => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      email: user.email,\n      role: user.role,\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n  const handleToggleUserStatus = userId => {\n    setUsers(prev => prev.map(user => user.userId === userId ? {\n      ...user,\n      isActive: !user.isActive\n    } : user));\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: ''\n    });\n    setErrors({});\n  };\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: handleBack,\n          children: \"\\u2190 Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          style: {\n            marginLeft: '20px'\n          },\n          children: \"User Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCreateUser,\n        children: \"+ Create User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Created Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Last Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: users.map(user => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(RoleBadge, {\n                  role: user.role,\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                  active: user.isActive,\n                  children: user.isActive ? 'Active' : 'Inactive'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(user.createdDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Never'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    onClick: () => handleEditUser(user),\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: user.isActive ? 'danger' : 'secondary',\n                    onClick: () => handleToggleUserStatus(user.userId),\n                    children: user.isActive ? 'Deactivate' : 'Activate'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)]\n            }, user.userId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), users.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#777'\n        },\n        children: \"No users found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n          children: [/*#__PURE__*/_jsxDEV(ModalTitle, {\n            children: editingUser ? 'Edit User' : 'Create New User'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CloseButton, {\n            onClick: handleCloseModal,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"username\",\n              children: \"Username *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"username\",\n              name: \"username\",\n              value: formData.username,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), errors.username && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"firstName\",\n              children: \"First Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"firstName\",\n              name: \"firstName\",\n              value: formData.firstName,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), errors.firstName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.firstName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 36\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"lastName\",\n              children: \"Last Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              id: \"lastName\",\n              name: \"lastName\",\n              value: formData.lastName,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this), errors.lastName && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.lastName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"email\",\n              children: \"Email *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"role\",\n              children: \"Role *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              id: \"role\",\n              name: \"role\",\n              value: formData.role,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Agent\",\n                children: \"Agent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Supervisor\",\n                children: \"Supervisor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Admin\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this), !editingUser && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"password\",\n                children: \"Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"confirmPassword\",\n                children: \"Confirm Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"password\",\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 46\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '10px',\n              justifyContent: 'flex-end',\n              marginTop: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: handleCloseModal,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: loading,\n              children: loading ? 'Saving...' : editingUser ? 'Update User' : 'Create User'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 402,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagement, \"T4Go8mY5KjC1acl112uSL6fXCtk=\", false, function () {\n  return [useNavigate];\n});\n_c14 = UserManagement;\nexport default UserManagement;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"TableContainer\");\n$RefreshReg$(_c5, \"Table\");\n$RefreshReg$(_c6, \"TableHeader\");\n$RefreshReg$(_c7, \"TableCell\");\n$RefreshReg$(_c8, \"TableRow\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"RoleBadge\");\n$RefreshReg$(_c1, \"Modal\");\n$RefreshReg$(_c10, \"ModalContent\");\n$RefreshReg$(_c11, \"ModalHeader\");\n$RefreshReg$(_c12, \"ModalTitle\");\n$RefreshReg$(_c13, \"CloseButton\");\n$RefreshReg$(_c14, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "Input", "Select", "FormGroup", "Label", "ErrorMessage", "User", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Container", "div", "_c", "Header", "props", "theme", "colors", "mediumGray", "_c2", "Title", "h1", "primary", "_c3", "TableContainer", "_c4", "Table", "table", "_c5", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c6", "TableCell", "td", "_c7", "TableRow", "tr", "_c8", "StatusBadge", "span", "active", "_c9", "RoleBadge", "role", "_c0", "Modal", "show", "_c1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "white", "borderRadius", "md", "_c10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c11", "ModalTitle", "h2", "_c12", "CloseButton", "button", "textDark", "_c13", "UserManagement", "_s", "navigate", "users", "setUsers", "showModal", "setShowModal", "editingUser", "setEditingUser", "loading", "setLoading", "formData", "setFormData", "username", "firstName", "lastName", "email", "password", "confirmPassword", "errors", "setErrors", "loadUsers", "mockUsers", "userId", "isActive", "createdDate", "lastLoginDate", "error", "console", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "trim", "test", "length", "Object", "keys", "handleSubmit", "preventDefault", "updatedUser", "map", "user", "alert", "newUser", "Math", "max", "u", "Date", "toISOString", "handleCloseModal", "handleCreateUser", "handleEditUser", "handleToggleUserStatus", "handleBack", "formatDate", "dateString", "toLocaleDateString", "children", "style", "display", "alignItems", "variant", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "gap", "size", "textAlign", "padding", "color", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "justifyContent", "marginTop", "disabled", "_c14", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/UserManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, User } from '../../services/apiService';\n\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ active: boolean }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: ${props => props.active ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.active ? '#2e7d32' : '#c62828'};\n`;\n\nconst RoleBadge = styled.span<{ role: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.role) {\n      case 'Admin':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'Supervisor':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'Agent':\n        return `\n          background-color: #e3f2fd;\n          color: #0d47a1;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst Modal = styled.div<{ show: boolean }>`\n  display: ${props => props.show ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 1000;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst ModalContent = styled.div`\n  background: ${props => props.theme.colors.white};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: 30px;\n  max-width: 500px;\n  width: 90%;\n  max-height: 90%;\n  overflow-y: auto;\n`;\n\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst ModalTitle = styled.h2`\n  color: ${props => props.theme.colors.primary};\n  margin: 0;\n`;\n\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: ${props => props.theme.colors.textMedium};\n\n  &:hover {\n    color: ${props => props.theme.colors.textDark};\n  }\n`;\n\ninterface User {\n  userId: number;\n  username: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  role: 'Agent' | 'Supervisor' | 'Admin';\n  isActive: boolean;\n  createdDate: string;\n  lastLoginDate?: string;\n}\n\nconst UserManagement: React.FC = () => {\n  const navigate = useNavigate();\n  const [users, setUsers] = useState<User[]>([]);\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    username: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: 'Agent' as 'Agent' | 'Supervisor' | 'Admin',\n    password: '',\n    confirmPassword: '',\n  });\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\n\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      // Mock users data\n      const mockUsers: User[] = [\n        {\n          userId: 1,\n          username: 'agent1',\n          firstName: 'John',\n          lastName: 'Agent',\n          email: '<EMAIL>',\n          role: 'Agent',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T08:30:00Z',\n        },\n        {\n          userId: 2,\n          username: 'supervisor1',\n          firstName: 'Jane',\n          lastName: 'Supervisor',\n          email: '<EMAIL>',\n          role: 'Supervisor',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T09:15:00Z',\n        },\n        {\n          userId: 3,\n          username: 'admin1',\n          firstName: 'Admin',\n          lastName: 'User',\n          email: '<EMAIL>',\n          role: 'Admin',\n          isActive: true,\n          createdDate: '2024-01-01T00:00:00Z',\n          lastLoginDate: '2024-01-16T10:00:00Z',\n        },\n        {\n          userId: 4,\n          username: 'agent2',\n          firstName: 'Bob',\n          lastName: 'Agent',\n          email: '<EMAIL>',\n          role: 'Agent',\n          isActive: false,\n          createdDate: '2024-01-05T00:00:00Z',\n          lastLoginDate: '2024-01-10T14:20:00Z',\n        },\n      ];\n\n      setUsers(mockUsers);\n    } catch (error) {\n      console.error('Error loading users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: { [key: string]: string } = {};\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!editingUser) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 6) {\n        newErrors.password = 'Password must be at least 6 characters';\n      }\n\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      if (editingUser) {\n        // Update existing user\n        const updatedUser: User = {\n          ...editingUser,\n          username: formData.username,\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          role: formData.role,\n        };\n\n        setUsers(prev => prev.map(user =>\n          user.userId === editingUser.userId ? updatedUser : user\n        ));\n\n        alert('User updated successfully!');\n      } else {\n        // Create new user\n        const newUser: User = {\n          userId: Math.max(...users.map(u => u.userId)) + 1,\n          username: formData.username,\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          role: formData.role,\n          isActive: true,\n          createdDate: new Date().toISOString(),\n        };\n\n        setUsers(prev => [...prev, newUser]);\n\n        alert('User created successfully!');\n      }\n\n      handleCloseModal();\n    } catch (error) {\n      console.error('Error saving user:', error);\n      alert('Failed to save user. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setEditingUser(user);\n    setFormData({\n      username: user.username,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      email: user.email,\n      role: user.role,\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n    setShowModal(true);\n  };\n\n  const handleToggleUserStatus = (userId: number) => {\n    setUsers(prev => prev.map(user =>\n      user.userId === userId ? { ...user, isActive: !user.isActive } : user\n    ));\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: 'Agent',\n      password: '',\n      confirmPassword: '',\n    });\n    setErrors({});\n  };\n\n  const handleBack = () => {\n    navigate('/admin/dashboard');\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  return (\n    <Container>\n      <Header>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <Button variant=\"outline\" onClick={handleBack}>\n            ← Back\n          </Button>\n          <Title style={{ marginLeft: '20px' }}>User Management</Title>\n        </div>\n        <Button onClick={handleCreateUser}>\n          + Create User\n        </Button>\n      </Header>\n\n      <Card>\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Name</TableHeader>\n                <TableHeader>Username</TableHeader>\n                <TableHeader>Email</TableHeader>\n                <TableHeader>Role</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Created Date</TableHeader>\n                <TableHeader>Last Login</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {users.map((user) => (\n                <TableRow key={user.userId}>\n                  <TableCell>{user.firstName} {user.lastName}</TableCell>\n                  <TableCell>{user.username}</TableCell>\n                  <TableCell>{user.email}</TableCell>\n                  <TableCell>\n                    <RoleBadge role={user.role}>{user.role}</RoleBadge>\n                  </TableCell>\n                  <TableCell>\n                    <StatusBadge active={user.isActive}>\n                      {user.isActive ? 'Active' : 'Inactive'}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>{formatDate(user.createdDate)}</TableCell>\n                  <TableCell>\n                    {user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Never'}\n                  </TableCell>\n                  <TableCell>\n                    <div style={{ display: 'flex', gap: '8px' }}>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleEditUser(user)}\n                      >\n                        Edit\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant={user.isActive ? 'danger' : 'secondary'}\n                        onClick={() => handleToggleUserStatus(user.userId)}\n                      >\n                        {user.isActive ? 'Deactivate' : 'Activate'}\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {users.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            No users found.\n          </div>\n        )}\n      </Card>\n\n      {/* Create/Edit User Modal */}\n      <Modal show={showModal}>\n        <ModalContent>\n          <ModalHeader>\n            <ModalTitle>\n              {editingUser ? 'Edit User' : 'Create New User'}\n            </ModalTitle>\n            <CloseButton onClick={handleCloseModal}>×</CloseButton>\n          </ModalHeader>\n\n          <form onSubmit={handleSubmit}>\n            <FormGroup>\n              <Label htmlFor=\"username\">Username *</Label>\n              <Input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                value={formData.username}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.username && <ErrorMessage>{errors.username}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"firstName\">First Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"firstName\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.firstName && <ErrorMessage>{errors.firstName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"lastName\">Last Name *</Label>\n              <Input\n                type=\"text\"\n                id=\"lastName\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.lastName && <ErrorMessage>{errors.lastName}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"email\">Email *</Label>\n              <Input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n              />\n              {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"role\">Role *</Label>\n              <Select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"Agent\">Agent</option>\n                <option value=\"Supervisor\">Supervisor</option>\n                <option value=\"Admin\">Admin</option>\n              </Select>\n            </FormGroup>\n\n            {!editingUser && (\n              <>\n                <FormGroup>\n                  <Label htmlFor=\"password\">Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.password && <ErrorMessage>{errors.password}</ErrorMessage>}\n                </FormGroup>\n\n                <FormGroup>\n                  <Label htmlFor=\"confirmPassword\">Confirm Password *</Label>\n                  <Input\n                    type=\"password\"\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    value={formData.confirmPassword}\n                    onChange={handleInputChange}\n                    required\n                  />\n                  {errors.confirmPassword && <ErrorMessage>{errors.confirmPassword}</ErrorMessage>}\n                </FormGroup>\n              </>\n            )}\n\n            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}>\n              <Button type=\"button\" variant=\"outline\" onClick={handleCloseModal}>\n                Cancel\n              </Button>\n              <Button type=\"submit\" disabled={loading}>\n                {loading ? 'Saving...' : (editingUser ? 'Update User' : 'Create User')}\n              </Button>\n            </div>\n          </form>\n        </ModalContent>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,QAAwB,2BAA2B;AACvH,SAAqBC,IAAI,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,SAAS,GAAGb,MAAM,CAACc,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,SAAS;AAMf,MAAMG,MAAM,GAAGhB,MAAM,CAACc,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACC,GAAA,GAPIL,MAAM;AASZ,MAAMM,KAAK,GAAGtB,MAAM,CAACuB,EAAE;AACvB;AACA;AACA,WAAWN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C,CAAC;AAACC,GAAA,GAJIH,KAAK;AAMX,MAAMI,cAAc,GAAG1B,MAAM,CAACc,GAAG;AACjC;AACA,CAAC;AAACa,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAG5B,MAAM,CAAC6B,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAG/B,MAAM,CAACgC,EAAE;AAC7B;AACA;AACA,6BAA6Bf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAClE,sBAAsBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACe,QAAQ;AAC1D;AACA,WAAWjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU;AACjD,CAAC;AAACC,GAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAGrC,MAAM,CAACsC,EAAE;AAC3B;AACA;AACA,6BAA6BrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAClE,CAAC;AAACM,GAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGxC,MAAM,CAACyC,EAAE;AAC1B;AACA,wBAAwBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACc,SAAS;AAC7D;AACA,CAAC;AAACS,GAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAG3C,MAAM,CAAC4C,IAAyB;AACpD;AACA;AACA;AACA;AACA;AACA,sBAAsB3B,KAAK,IAAIA,KAAK,CAAC4B,MAAM,GAAG,SAAS,GAAG,SAAS;AACnE,WAAW5B,KAAK,IAAIA,KAAK,CAAC4B,MAAM,GAAG,SAAS,GAAG,SAAS;AACxD,CAAC;AAACC,GAAA,GARIH,WAAW;AAUjB,MAAMI,SAAS,GAAG/C,MAAM,CAAC4C,IAAsB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,IAAI3B,KAAK,IAAI;EACT,QAAQA,KAAK,CAAC+B,IAAI;IAChB,KAAK,OAAO;MACV,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,YAAY;MACf,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,OAAO;MACV,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA/BIF,SAAS;AAiCf,MAAMG,KAAK,GAAGlD,MAAM,CAACc,GAAsB;AAC3C,aAAaG,KAAK,IAAIA,KAAK,CAACkC,IAAI,GAAG,MAAM,GAAG,MAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,KAAK;AAaX,MAAMG,YAAY,GAAGrD,MAAM,CAACc,GAAG;AAC/B,gBAAgBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACmC,KAAK;AACjD,mBAAmBrC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqC,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GARIJ,YAAY;AAUlB,MAAMK,WAAW,GAAG1D,MAAM,CAACc,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,6BAA6BG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AACnE,CAAC;AAACuC,IAAA,GAPID,WAAW;AASjB,MAAME,UAAU,GAAG5D,MAAM,CAAC6D,EAAE;AAC5B,WAAW5C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC9C;AACA,CAAC;AAACsC,IAAA,GAHIF,UAAU;AAKhB,MAAMG,WAAW,GAAG/D,MAAM,CAACgE,MAAM;AACjC;AACA;AACA;AACA;AACA,WAAW/C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,UAAU;AACjD;AACA;AACA,aAAalB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC8C,QAAQ;AACjD;AACA,CAAC;AAACC,IAAA,GAVIH,WAAW;AAwBjB,MAAMI,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGtE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAG1E,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC+E,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiF,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAAC;IACvCmF,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTnC,IAAI,EAAE,OAA2C;IACjDoC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1F,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd0F,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMY,SAAiB,GAAG,CACxB;QACEC,MAAM,EAAE,CAAC;QACTV,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,oBAAoB;QAC3BnC,IAAI,EAAE,OAAO;QACb2C,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC,EACD;QACEH,MAAM,EAAE,CAAC;QACTV,QAAQ,EAAE,aAAa;QACvBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,YAAY;QACtBC,KAAK,EAAE,yBAAyB;QAChCnC,IAAI,EAAE,YAAY;QAClB2C,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC,EACD;QACEH,MAAM,EAAE,CAAC;QACTV,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,eAAe;QACtBnC,IAAI,EAAE,OAAO;QACb2C,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC,EACD;QACEH,MAAM,EAAE,CAAC;QACTV,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,mBAAmB;QAC1BnC,IAAI,EAAE,OAAO;QACb2C,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC,CACF;MAEDtB,QAAQ,CAACkB,SAAS,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrB,WAAW,CAACsB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAIb,MAAM,CAACY,IAAI,CAAC,EAAE;MAChBX,SAAS,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAoC,GAAG,CAAC,CAAC;IAE/C,IAAI,CAACzB,QAAQ,CAACE,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACvB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACF,QAAQ,CAACG,SAAS,CAACuB,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAACtB,SAAS,GAAG,wBAAwB;IAChD;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACrB,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACJ,QAAQ,CAACK,KAAK,CAACqB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACpB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACsB,IAAI,CAAC3B,QAAQ,CAACK,KAAK,CAAC,EAAE;MAC/CoB,SAAS,CAACpB,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACT,WAAW,EAAE;MAChB,IAAI,CAACI,QAAQ,CAACM,QAAQ,EAAE;QACtBmB,SAAS,CAACnB,QAAQ,GAAG,sBAAsB;MAC7C,CAAC,MAAM,IAAIN,QAAQ,CAACM,QAAQ,CAACsB,MAAM,GAAG,CAAC,EAAE;QACvCH,SAAS,CAACnB,QAAQ,GAAG,wCAAwC;MAC/D;MAEA,IAAIN,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,eAAe,EAAE;QAClDkB,SAAS,CAAClB,eAAe,GAAG,wBAAwB;MACtD;IACF;IAEAE,SAAS,CAACgB,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOZ,CAAkB,IAAK;IACjDA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAzB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAIH,WAAW,EAAE;QACf;QACA,MAAMqC,WAAiB,GAAG;UACxB,GAAGrC,WAAW;UACdM,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;UAC3BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;UAC7BC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;UAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK;UACrBnC,IAAI,EAAE8B,QAAQ,CAAC9B;QACjB,CAAC;QAEDuB,QAAQ,CAAC8B,IAAI,IAAIA,IAAI,CAACW,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACvB,MAAM,KAAKhB,WAAW,CAACgB,MAAM,GAAGqB,WAAW,GAAGE,IACrD,CAAC,CAAC;QAEFC,KAAK,CAAC,4BAA4B,CAAC;MACrC,CAAC,MAAM;QACL;QACA,MAAMC,OAAa,GAAG;UACpBzB,MAAM,EAAE0B,IAAI,CAACC,GAAG,CAAC,GAAG/C,KAAK,CAAC0C,GAAG,CAACM,CAAC,IAAIA,CAAC,CAAC5B,MAAM,CAAC,CAAC,GAAG,CAAC;UACjDV,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;UAC3BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;UAC7BC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;UAC3BC,KAAK,EAAEL,QAAQ,CAACK,KAAK;UACrBnC,IAAI,EAAE8B,QAAQ,CAAC9B,IAAI;UACnB2C,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE,IAAI2B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACtC,CAAC;QAEDjD,QAAQ,CAAC8B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEc,OAAO,CAAC,CAAC;QAEpCD,KAAK,CAAC,4BAA4B,CAAC;MACrC;MAEAO,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CoB,KAAK,CAAC,wCAAwC,CAAC;IACjD,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/C,cAAc,CAAC,IAAI,CAAC;IACpBI,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTnC,IAAI,EAAE,OAAO;MACboC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkD,cAAc,GAAIV,IAAU,IAAK;IACrCtC,cAAc,CAACsC,IAAI,CAAC;IACpBlC,WAAW,CAAC;MACVC,QAAQ,EAAEiC,IAAI,CAACjC,QAAQ;MACvBC,SAAS,EAAEgC,IAAI,CAAChC,SAAS;MACzBC,QAAQ,EAAE+B,IAAI,CAAC/B,QAAQ;MACvBC,KAAK,EAAE8B,IAAI,CAAC9B,KAAK;MACjBnC,IAAI,EAAEiE,IAAI,CAACjE,IAAI;MACfoC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmD,sBAAsB,GAAIlC,MAAc,IAAK;IACjDnB,QAAQ,CAAC8B,IAAI,IAAIA,IAAI,CAACW,GAAG,CAACC,IAAI,IAC5BA,IAAI,CAACvB,MAAM,KAAKA,MAAM,GAAG;MAAE,GAAGuB,IAAI;MAAEtB,QAAQ,EAAE,CAACsB,IAAI,CAACtB;IAAS,CAAC,GAAGsB,IACnE,CAAC,CAAC;EACJ,CAAC;EAED,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhD,YAAY,CAAC,KAAK,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;IACpBI,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTnC,IAAI,EAAE,OAAO;MACboC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;EAED,MAAMsC,UAAU,GAAGA,CAAA,KAAM;IACvBxD,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,MAAMyD,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIR,IAAI,CAACQ,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,oBACEtH,OAAA,CAACG,SAAS;IAAAoH,QAAA,gBACRvH,OAAA,CAACM,MAAM;MAAAiH,QAAA,gBACLvH,OAAA;QAAKwH,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACpDvH,OAAA,CAACR,MAAM;UAACmI,OAAO,EAAC,SAAS;UAACC,OAAO,EAAET,UAAW;UAAAI,QAAA,EAAC;QAE/C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA,CAACY,KAAK;UAAC4G,KAAK,EAAE;YAAES,UAAU,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNhI,OAAA,CAACR,MAAM;QAACoI,OAAO,EAAEZ,gBAAiB;QAAAO,QAAA,EAAC;MAEnC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEThI,OAAA,CAACT,IAAI;MAAAgI,QAAA,gBACHvH,OAAA,CAACgB,cAAc;QAAAuG,QAAA,eACbvH,OAAA,CAACkB,KAAK;UAAAqG,QAAA,gBACJvH,OAAA;YAAAuH,QAAA,eACEvH,OAAA;cAAAuH,QAAA,gBACEvH,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnChI,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChChI,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjChI,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvChI,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrChI,OAAA,CAACqB,WAAW;gBAAAkG,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhI,OAAA;YAAAuH,QAAA,EACG3D,KAAK,CAAC0C,GAAG,CAAEC,IAAI,iBACdvG,OAAA,CAAC8B,QAAQ;cAAAyF,QAAA,gBACPvH,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,GAAEhB,IAAI,CAAChC,SAAS,EAAC,GAAC,EAACgC,IAAI,CAAC/B,QAAQ;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvDhI,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,EAAEhB,IAAI,CAACjC;cAAQ;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtChI,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,EAAEhB,IAAI,CAAC9B;cAAK;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnChI,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,eACRvH,OAAA,CAACqC,SAAS;kBAACC,IAAI,EAAEiE,IAAI,CAACjE,IAAK;kBAAAiF,QAAA,EAAEhB,IAAI,CAACjE;gBAAI;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACZhI,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,eACRvH,OAAA,CAACiC,WAAW;kBAACE,MAAM,EAAEoE,IAAI,CAACtB,QAAS;kBAAAsC,QAAA,EAChChB,IAAI,CAACtB,QAAQ,GAAG,QAAQ,GAAG;gBAAU;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACZhI,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,EAAEH,UAAU,CAACb,IAAI,CAACrB,WAAW;cAAC;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrDhI,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,EACPhB,IAAI,CAACpB,aAAa,GAAGiC,UAAU,CAACb,IAAI,CAACpB,aAAa,CAAC,GAAG;cAAO;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACZhI,OAAA,CAAC2B,SAAS;gBAAA4F,QAAA,eACRvH,OAAA;kBAAKwH,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAES,GAAG,EAAE;kBAAM,CAAE;kBAAAX,QAAA,gBAC1CvH,OAAA,CAACR,MAAM;oBACL2I,IAAI,EAAC,IAAI;oBACTP,OAAO,EAAEA,CAAA,KAAMX,cAAc,CAACV,IAAI,CAAE;oBAAAgB,QAAA,EACrC;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACThI,OAAA,CAACR,MAAM;oBACL2I,IAAI,EAAC,IAAI;oBACTR,OAAO,EAAEpB,IAAI,CAACtB,QAAQ,GAAG,QAAQ,GAAG,WAAY;oBAChD2C,OAAO,EAAEA,CAAA,KAAMV,sBAAsB,CAACX,IAAI,CAACvB,MAAM,CAAE;oBAAAuC,QAAA,EAElDhB,IAAI,CAACtB,QAAQ,GAAG,YAAY,GAAG;kBAAU;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GAhCCzB,IAAI,CAACvB,MAAM;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiChB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBpE,KAAK,CAACoC,MAAM,KAAK,CAAC,iBACjBhG,OAAA;QAAKwH,KAAK,EAAE;UAAEY,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,EAAC;MAErE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPhI,OAAA,CAACwC,KAAK;MAACC,IAAI,EAAEqB,SAAU;MAAAyD,QAAA,eACrBvH,OAAA,CAAC2C,YAAY;QAAA4E,QAAA,gBACXvH,OAAA,CAACgD,WAAW;UAAAuE,QAAA,gBACVvH,OAAA,CAACkD,UAAU;YAAAqE,QAAA,EACRvD,WAAW,GAAG,WAAW,GAAG;UAAiB;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACbhI,OAAA,CAACqD,WAAW;YAACuE,OAAO,EAAEb,gBAAiB;YAAAQ,QAAA,EAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAEdhI,OAAA;UAAMuI,QAAQ,EAAEpC,YAAa;UAAAoB,QAAA,gBAC3BvH,OAAA,CAACL,SAAS;YAAA4H,QAAA,gBACRvH,OAAA,CAACJ,KAAK;cAAC4I,OAAO,EAAC,UAAU;cAAAjB,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5ChI,OAAA,CAACP,KAAK;cACJgJ,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACblD,IAAI,EAAC,UAAU;cACfC,KAAK,EAAErB,QAAQ,CAACE,QAAS;cACzBqE,QAAQ,EAAErD,iBAAkB;cAC5BsD,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDpD,MAAM,CAACN,QAAQ,iBAAItE,OAAA,CAACH,YAAY;cAAA0H,QAAA,EAAE3C,MAAM,CAACN;YAAQ;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAEZhI,OAAA,CAACL,SAAS;YAAA4H,QAAA,gBACRvH,OAAA,CAACJ,KAAK;cAAC4I,OAAO,EAAC,WAAW;cAAAjB,QAAA,EAAC;YAAY;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/ChI,OAAA,CAACP,KAAK;cACJgJ,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,WAAW;cACdlD,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAErB,QAAQ,CAACG,SAAU;cAC1BoE,QAAQ,EAAErD,iBAAkB;cAC5BsD,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDpD,MAAM,CAACL,SAAS,iBAAIvE,OAAA,CAACH,YAAY;cAAA0H,QAAA,EAAE3C,MAAM,CAACL;YAAS;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEZhI,OAAA,CAACL,SAAS;YAAA4H,QAAA,gBACRvH,OAAA,CAACJ,KAAK;cAAC4I,OAAO,EAAC,UAAU;cAAAjB,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7ChI,OAAA,CAACP,KAAK;cACJgJ,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACblD,IAAI,EAAC,UAAU;cACfC,KAAK,EAAErB,QAAQ,CAACI,QAAS;cACzBmE,QAAQ,EAAErD,iBAAkB;cAC5BsD,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDpD,MAAM,CAACJ,QAAQ,iBAAIxE,OAAA,CAACH,YAAY;cAAA0H,QAAA,EAAE3C,MAAM,CAACJ;YAAQ;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAEZhI,OAAA,CAACL,SAAS;YAAA4H,QAAA,gBACRvH,OAAA,CAACJ,KAAK;cAAC4I,OAAO,EAAC,OAAO;cAAAjB,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtChI,OAAA,CAACP,KAAK;cACJgJ,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACVlD,IAAI,EAAC,OAAO;cACZC,KAAK,EAAErB,QAAQ,CAACK,KAAM;cACtBkE,QAAQ,EAAErD,iBAAkB;cAC5BsD,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACDpD,MAAM,CAACH,KAAK,iBAAIzE,OAAA,CAACH,YAAY;cAAA0H,QAAA,EAAE3C,MAAM,CAACH;YAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEZhI,OAAA,CAACL,SAAS;YAAA4H,QAAA,gBACRvH,OAAA,CAACJ,KAAK;cAAC4I,OAAO,EAAC,MAAM;cAAAjB,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpChI,OAAA,CAACN,MAAM;cACLgJ,EAAE,EAAC,MAAM;cACTlD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAErB,QAAQ,CAAC9B,IAAK;cACrBqG,QAAQ,EAAErD,iBAAkB;cAC5BsD,QAAQ;cAAArB,QAAA,gBAERvH,OAAA;gBAAQyF,KAAK,EAAC,OAAO;gBAAA8B,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpChI,OAAA;gBAAQyF,KAAK,EAAC,YAAY;gBAAA8B,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9ChI,OAAA;gBAAQyF,KAAK,EAAC,OAAO;gBAAA8B,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAEX,CAAChE,WAAW,iBACXhE,OAAA,CAAAE,SAAA;YAAAqH,QAAA,gBACEvH,OAAA,CAACL,SAAS;cAAA4H,QAAA,gBACRvH,OAAA,CAACJ,KAAK;gBAAC4I,OAAO,EAAC,UAAU;gBAAAjB,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5ChI,OAAA,CAACP,KAAK;gBACJgJ,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,UAAU;gBACblD,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAErB,QAAQ,CAACM,QAAS;gBACzBiE,QAAQ,EAAErD,iBAAkB;gBAC5BsD,QAAQ;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDpD,MAAM,CAACF,QAAQ,iBAAI1E,OAAA,CAACH,YAAY;gBAAA0H,QAAA,EAAE3C,MAAM,CAACF;cAAQ;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eAEZhI,OAAA,CAACL,SAAS;cAAA4H,QAAA,gBACRvH,OAAA,CAACJ,KAAK;gBAAC4I,OAAO,EAAC,iBAAiB;gBAAAjB,QAAA,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DhI,OAAA,CAACP,KAAK;gBACJgJ,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,iBAAiB;gBACpBlD,IAAI,EAAC,iBAAiB;gBACtBC,KAAK,EAAErB,QAAQ,CAACO,eAAgB;gBAChCgE,QAAQ,EAAErD,iBAAkB;gBAC5BsD,QAAQ;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDpD,MAAM,CAACD,eAAe,iBAAI3E,OAAA,CAACH,YAAY;gBAAA0H,QAAA,EAAE3C,MAAM,CAACD;cAAe;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA,eACZ,CACH,eAEDhI,OAAA;YAAKwH,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE,MAAM;cAAEW,cAAc,EAAE,UAAU;cAAEC,SAAS,EAAE;YAAO,CAAE;YAAAvB,QAAA,gBAC1FvH,OAAA,CAACR,MAAM;cAACiJ,IAAI,EAAC,QAAQ;cAACd,OAAO,EAAC,SAAS;cAACC,OAAO,EAAEb,gBAAiB;cAAAQ,QAAA,EAAC;YAEnE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThI,OAAA,CAACR,MAAM;cAACiJ,IAAI,EAAC,QAAQ;cAACM,QAAQ,EAAE7E,OAAQ;cAAAqD,QAAA,EACrCrD,OAAO,GAAG,WAAW,GAAIF,WAAW,GAAG,aAAa,GAAG;YAAc;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACtE,EAAA,CArbID,cAAwB;EAAA,QACXpE,WAAW;AAAA;AAAA2J,IAAA,GADxBvF,cAAwB;AAub9B,eAAeA,cAAc;AAAC,IAAApD,EAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAwF,IAAA;AAAAC,YAAA,CAAA5I,EAAA;AAAA4I,YAAA,CAAAtI,GAAA;AAAAsI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}