{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13;import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import styled from'styled-components';import DashboardLayout from'../Layout/DashboardLayout';import{Card,Button,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReportsContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  display: grid;\\n  gap: 20px;\\n\"])));const StatsGrid=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\"])));const StatCard=styled(Card)(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #007E3A, #005a2a);\\n  color: white;\\n\"])));const StatValue=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-size: 28px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n\"])));const StatLabel=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  font-size: 14px;\\n  opacity: 0.9;\\n  font-weight: 500;\\n\"])));const ChartContainer=styled.div(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n\\n  @media (max-width: 768px) {\\n    grid-template-columns: 1fr;\\n  }\\n\"])));const ChartCard=styled(Card)(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  padding: 20px;\\n\"])));const ChartTitle=styled.h3(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  margin-bottom: 20px;\\n  color: #007E3A;\\n  text-align: center;\\n\"])));const FilterContainer=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n\"])));const FilterSelect=styled.select(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  padding: 8px 12px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  background: white;\\n  \\n  &:focus {\\n    border-color: \",\";\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.colors.primary);const TeamPerformanceTable=styled.table(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  border-collapse: collapse;\\n\"])));const TableHeader=styled.th(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n  background-color: \",\";\\n  font-weight: 600;\\n  color: \",\";\\n\"])),props=>props.theme.colors.lightGray,props=>props.theme.colors.offWhite,props=>props.theme.colors.textMedium);const TableCell=styled.td(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  padding: 12px 15px;\\n  text-align: left;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.lightGray);const TableRow=styled.tr(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  &:hover {\\n    background-color: \",\";\\n  }\\n\"])),props=>props.theme.colors.lightGray);const PerformanceBar=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  height: 15px;\\n  background-color: #f0f0f0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  \\n  &::after {\\n    content: '';\\n    display: block;\\n    width: \",\"%;\\n    height: 100%;\\n    background-color: \",\";\\n    transition: width 0.3s ease;\\n  }\\n\"])),props=>props.percentage,props=>props.color);const SupervisorReports=()=>{const[loading,setLoading]=useState(true);const[timeFilter,setTimeFilter]=useState('month');const[teamStats,setTeamStats]=useState({totalAgents:0,totalTasks:0,completedTasks:0,pendingReviews:0,averageTime:0,teamEfficiency:0});const[agentPerformance,setAgentPerformance]=useState([]);const navigate=useNavigate();useEffect(()=>{loadReportsData();},[timeFilter]);const loadReportsData=async()=>{try{setLoading(true);const[dashboardStats]=await Promise.all([apiService.getSupervisorDashboardStats()]);setTeamStats({totalAgents:5,// Mock data\ntotalTasks:dashboardStats.totalLeads||0,completedTasks:dashboardStats.completedLeads||0,pendingReviews:dashboardStats.pendingReview||0,averageTime:2.8,// Mock data\nteamEfficiency:82// Mock data\n});// Mock agent performance data\nsetAgentPerformance([{agentName:'John Agent',tasksAssigned:15,tasksCompleted:12,approvalRate:90,avgTime:2.5,efficiency:85},{agentName:'Jane Agent',tasksAssigned:18,tasksCompleted:16,approvalRate:95,avgTime:2.2,efficiency:92},{agentName:'Bob Agent',tasksAssigned:12,tasksCompleted:10,approvalRate:80,avgTime:3.1,efficiency:75}]);}catch(error){console.error('Error loading reports data:',error);// Use mock data on error\nsetTeamStats({totalAgents:5,totalTasks:45,completedTasks:38,pendingReviews:7,averageTime:2.8,teamEfficiency:82});}finally{setLoading(false);}};const navigationItems=[{icon:'🏠',label:'Dashboard',onClick:()=>navigate('/supervisor/dashboard')},{icon:'👁️',label:'Review Queue',onClick:()=>navigate('/supervisor/review')},{icon:'📊',label:'Reports',active:true},{icon:'👥',label:'Team',onClick:()=>navigate('/supervisor/team')}];const getCompletionRate=()=>{return teamStats.totalTasks>0?Math.round(teamStats.completedTasks/teamStats.totalTasks*100):0;};if(loading){return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Team Reports\",navigationItems:navigationItems,children:/*#__PURE__*/_jsx(LoadingSpinner,{})});}return/*#__PURE__*/_jsx(DashboardLayout,{title:\"Team Reports\",navigationItems:navigationItems,children:/*#__PURE__*/_jsxs(ReportsContainer,{children:[/*#__PURE__*/_jsxs(FilterContainer,{children:[/*#__PURE__*/_jsxs(FilterSelect,{value:timeFilter,onChange:e=>setTimeFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"week\",children:\"This Week\"}),/*#__PURE__*/_jsx(\"option\",{value:\"month\",children:\"This Month\"}),/*#__PURE__*/_jsx(\"option\",{value:\"quarter\",children:\"This Quarter\"}),/*#__PURE__*/_jsx(\"option\",{value:\"year\",children:\"This Year\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>window.print(),children:\"\\uD83D\\uDCC4 Export Report\"})]}),/*#__PURE__*/_jsxs(StatsGrid,{children:[/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:teamStats.totalAgents}),/*#__PURE__*/_jsx(StatLabel,{children:\"Team Members\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:teamStats.totalTasks}),/*#__PURE__*/_jsx(StatLabel,{children:\"Total Tasks\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:teamStats.completedTasks}),/*#__PURE__*/_jsx(StatLabel,{children:\"Completed\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:teamStats.pendingReviews}),/*#__PURE__*/_jsx(StatLabel,{children:\"Pending Review\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsx(StatValue,{children:teamStats.averageTime}),/*#__PURE__*/_jsx(StatLabel,{children:\"Avg. Days\"})]}),/*#__PURE__*/_jsxs(StatCard,{children:[/*#__PURE__*/_jsxs(StatValue,{children:[teamStats.teamEfficiency,\"%\"]}),/*#__PURE__*/_jsx(StatLabel,{children:\"Team Efficiency\"})]})]}),/*#__PURE__*/_jsxs(ChartContainer,{children:[/*#__PURE__*/_jsxs(ChartCard,{children:[/*#__PURE__*/_jsx(ChartTitle,{children:\"Team Performance Overview\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'20px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"Completion Rate\"}),/*#__PURE__*/_jsxs(\"span\",{children:[getCompletionRate(),\"%\"]})]}),/*#__PURE__*/_jsx(PerformanceBar,{percentage:getCompletionRate(),color:\"#2e7d32\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'20px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"Team Efficiency\"}),/*#__PURE__*/_jsxs(\"span\",{children:[teamStats.teamEfficiency,\"%\"]})]}),/*#__PURE__*/_jsx(PerformanceBar,{percentage:teamStats.teamEfficiency,color:\"#007E3A\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"Quality Score\"}),/*#__PURE__*/_jsx(\"span\",{children:\"88%\"})]}),/*#__PURE__*/_jsx(PerformanceBar,{percentage:88,color:\"#FFD100\"})]})]}),/*#__PURE__*/_jsxs(ChartCard,{children:[/*#__PURE__*/_jsx(ChartTitle,{children:\"Task Distribution\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'15px',textAlign:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#2e7d32'},children:teamStats.completedTasks}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:\"Completed\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#4a148c'},children:teamStats.pendingReviews}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:\"Pending\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#ff8f00'},children:teamStats.totalTasks-teamStats.completedTasks-teamStats.pendingReviews}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:\"In Progress\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'24px',fontWeight:'bold',color:'#007E3A'},children:teamStats.totalTasks}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:\"Total\"})]})]})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Agent Performance\"}),/*#__PURE__*/_jsx(\"div\",{style:{overflowX:'auto'},children:/*#__PURE__*/_jsxs(TeamPerformanceTable,{children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(TableHeader,{children:\"Agent Name\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Tasks Assigned\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Completed\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Completion Rate\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Approval Rate\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Avg. Time\"}),/*#__PURE__*/_jsx(TableHeader,{children:\"Efficiency\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:agentPerformance.map((agent,index)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{style:{fontWeight:'500'},children:agent.agentName}),/*#__PURE__*/_jsx(TableCell,{children:agent.tasksAssigned}),/*#__PURE__*/_jsx(TableCell,{children:agent.tasksCompleted}),/*#__PURE__*/_jsxs(TableCell,{children:[Math.round(agent.tasksCompleted/agent.tasksAssigned*100),\"%\"]}),/*#__PURE__*/_jsxs(TableCell,{children:[agent.approvalRate,\"%\"]}),/*#__PURE__*/_jsxs(TableCell,{children:[agent.avgTime,\" days\"]}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'10px'},children:[/*#__PURE__*/_jsxs(\"span\",{children:[agent.efficiency,\"%\"]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,minWidth:'60px'},children:/*#__PURE__*/_jsx(PerformanceBar,{percentage:agent.efficiency,color:agent.efficiency>=85?'#2e7d32':agent.efficiency>=70?'#ff8f00':'#c62828'})})]})})]},index))})]})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Key Insights\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(250px, 1fr))',gap:'20px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{style:{color:'#2e7d32',marginBottom:'10px'},children:\"\\uD83C\\uDFAF Top Performer\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Jane Agent leads with 95% approval rate and 92% efficiency\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{style:{color:'#ff8f00',marginBottom:'10px'},children:\"\\u26A0\\uFE0F Needs Attention\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Bob Agent requires support to improve efficiency from 75% to team average\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{style:{color:'#007E3A',marginBottom:'10px'},children:\"\\uD83D\\uDCC8 Team Trend\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Overall team performance improved by 8% compared to last month\"})]})]})]})]})});};export default SupervisorReports;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "ReportsContainer", "div", "_templateObject", "_taggedTemplateLiteral", "StatsGrid", "_templateObject2", "StatCard", "_templateObject3", "StatValue", "_templateObject4", "StatLabel", "_templateObject5", "ChartContainer", "_templateObject6", "ChartCard", "_templateObject7", "ChartTitle", "h3", "_templateObject8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_templateObject9", "FilterSelect", "select", "_templateObject0", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "TeamPerformanceTable", "table", "_templateObject1", "TableHeader", "th", "_templateObject10", "lightGray", "offWhite", "textMedium", "TableCell", "td", "_templateObject11", "TableRow", "tr", "_templateObject12", "PerformanceBar", "_templateObject13", "percentage", "color", "SupervisorReports", "loading", "setLoading", "timeFilter", "setTimeFilter", "teamStats", "setTeamStats", "totalAgents", "totalTasks", "completedTasks", "pendingReviews", "averageTime", "teamEfficiency", "agentPerformance", "setAgentPerformance", "navigate", "loadReportsData", "dashboardStats", "Promise", "all", "getSupervisorDashboardStats", "totalLeads", "completedLeads", "pendingReview", "<PERSON><PERSON><PERSON>", "tasksAssigned", "tasksCompleted", "approvalRate", "avgTime", "efficiency", "error", "console", "navigationItems", "icon", "label", "onClick", "active", "getCompletionRate", "Math", "round", "title", "children", "value", "onChange", "e", "target", "variant", "window", "print", "style", "marginBottom", "display", "justifyContent", "gridTemplateColumns", "gap", "textAlign", "fontSize", "fontWeight", "overflowX", "map", "agent", "index", "alignItems", "flex", "min<PERSON><PERSON><PERSON>"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Supervisor/SupervisorReports.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\n\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n\nconst StatValue = styled.div`\n  font-size: 28px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TeamPerformanceTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst PerformanceBar = styled.div<{ percentage: number; color: string }>`\n  width: 100%;\n  height: 15px;\n  background-color: #f0f0f0;\n  border-radius: 8px;\n  overflow: hidden;\n  \n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.percentage}%;\n    height: 100%;\n    background-color: ${props => props.color};\n    transition: width 0.3s ease;\n  }\n`;\n\nconst SupervisorReports: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('month');\n  const [teamStats, setTeamStats] = useState({\n    totalAgents: 0,\n    totalTasks: 0,\n    completedTasks: 0,\n    pendingReviews: 0,\n    averageTime: 0,\n    teamEfficiency: 0,\n  });\n  const [agentPerformance, setAgentPerformance] = useState<any[]>([]);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n      \n      const [dashboardStats] = await Promise.all([\n        apiService.getSupervisorDashboardStats(),\n      ]);\n\n      setTeamStats({\n        totalAgents: 5, // Mock data\n        totalTasks: dashboardStats.totalLeads || 0,\n        completedTasks: dashboardStats.completedLeads || 0,\n        pendingReviews: dashboardStats.pendingReview || 0,\n        averageTime: 2.8, // Mock data\n        teamEfficiency: 82, // Mock data\n      });\n\n      // Mock agent performance data\n      setAgentPerformance([\n        {\n          agentName: 'John Agent',\n          tasksAssigned: 15,\n          tasksCompleted: 12,\n          approvalRate: 90,\n          avgTime: 2.5,\n          efficiency: 85,\n        },\n        {\n          agentName: 'Jane Agent',\n          tasksAssigned: 18,\n          tasksCompleted: 16,\n          approvalRate: 95,\n          avgTime: 2.2,\n          efficiency: 92,\n        },\n        {\n          agentName: 'Bob Agent',\n          tasksAssigned: 12,\n          tasksCompleted: 10,\n          approvalRate: 80,\n          avgTime: 3.1,\n          efficiency: 75,\n        },\n      ]);\n\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data on error\n      setTeamStats({\n        totalAgents: 5,\n        totalTasks: 45,\n        completedTasks: 38,\n        pendingReviews: 7,\n        averageTime: 2.8,\n        teamEfficiency: 82,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/supervisor/dashboard') },\n    { icon: '👁️', label: 'Review Queue', onClick: () => navigate('/supervisor/review') },\n    { icon: '📊', label: 'Reports', active: true },\n    { icon: '👥', label: 'Team', onClick: () => navigate('/supervisor/team') },\n  ];\n\n  const getCompletionRate = () => {\n    return teamStats.totalTasks > 0 ? Math.round((teamStats.completedTasks / teamStats.totalTasks) * 100) : 0;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Team Reports\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Team Reports\" navigationItems={navigationItems}>\n      <ReportsContainer>\n        <FilterContainer>\n          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </FilterSelect>\n          \n          <Button variant=\"outline\" onClick={() => window.print()}>\n            📄 Export Report\n          </Button>\n        </FilterContainer>\n\n        {/* Team Overview Stats */}\n        <StatsGrid>\n          <StatCard>\n            <StatValue>{teamStats.totalAgents}</StatValue>\n            <StatLabel>Team Members</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.totalTasks}</StatValue>\n            <StatLabel>Total Tasks</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.completedTasks}</StatValue>\n            <StatLabel>Completed</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.pendingReviews}</StatValue>\n            <StatLabel>Pending Review</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.averageTime}</StatValue>\n            <StatLabel>Avg. Days</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{teamStats.teamEfficiency}%</StatValue>\n            <StatLabel>Team Efficiency</StatLabel>\n          </StatCard>\n        </StatsGrid>\n\n        {/* Team Performance Charts */}\n        <ChartContainer>\n          <ChartCard>\n            <ChartTitle>Team Performance Overview</ChartTitle>\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>\n                <span>Completion Rate</span>\n                <span>{getCompletionRate()}%</span>\n              </div>\n              <PerformanceBar percentage={getCompletionRate()} color=\"#2e7d32\" />\n            </div>\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>\n                <span>Team Efficiency</span>\n                <span>{teamStats.teamEfficiency}%</span>\n              </div>\n              <PerformanceBar percentage={teamStats.teamEfficiency} color=\"#007E3A\" />\n            </div>\n            <div>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>\n                <span>Quality Score</span>\n                <span>88%</span>\n              </div>\n              <PerformanceBar percentage={88} color=\"#FFD100\" />\n            </div>\n          </ChartCard>\n\n          <ChartCard>\n            <ChartTitle>Task Distribution</ChartTitle>\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', textAlign: 'center' }}>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#2e7d32' }}>\n                  {teamStats.completedTasks}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Completed</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#4a148c' }}>\n                  {teamStats.pendingReviews}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Pending</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff8f00' }}>\n                  {teamStats.totalTasks - teamStats.completedTasks - teamStats.pendingReviews}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>In Progress</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#007E3A' }}>\n                  {teamStats.totalTasks}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Total</div>\n              </div>\n            </div>\n          </ChartCard>\n        </ChartContainer>\n\n        {/* Agent Performance Table */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Agent Performance</h3>\n          <div style={{ overflowX: 'auto' }}>\n            <TeamPerformanceTable>\n              <thead>\n                <tr>\n                  <TableHeader>Agent Name</TableHeader>\n                  <TableHeader>Tasks Assigned</TableHeader>\n                  <TableHeader>Completed</TableHeader>\n                  <TableHeader>Completion Rate</TableHeader>\n                  <TableHeader>Approval Rate</TableHeader>\n                  <TableHeader>Avg. Time</TableHeader>\n                  <TableHeader>Efficiency</TableHeader>\n                </tr>\n              </thead>\n              <tbody>\n                {agentPerformance.map((agent, index) => (\n                  <TableRow key={index}>\n                    <TableCell style={{ fontWeight: '500' }}>{agent.agentName}</TableCell>\n                    <TableCell>{agent.tasksAssigned}</TableCell>\n                    <TableCell>{agent.tasksCompleted}</TableCell>\n                    <TableCell>\n                      {Math.round((agent.tasksCompleted / agent.tasksAssigned) * 100)}%\n                    </TableCell>\n                    <TableCell>{agent.approvalRate}%</TableCell>\n                    <TableCell>{agent.avgTime} days</TableCell>\n                    <TableCell>\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>\n                        <span>{agent.efficiency}%</span>\n                        <div style={{ flex: 1, minWidth: '60px' }}>\n                          <PerformanceBar \n                            percentage={agent.efficiency} \n                            color={agent.efficiency >= 85 ? '#2e7d32' : agent.efficiency >= 70 ? '#ff8f00' : '#c62828'} \n                          />\n                        </div>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </tbody>\n            </TeamPerformanceTable>\n          </div>\n        </Card>\n\n        {/* Summary Insights */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Key Insights</h3>\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>\n            <div>\n              <h4 style={{ color: '#2e7d32', marginBottom: '10px' }}>🎯 Top Performer</h4>\n              <p>Jane Agent leads with 95% approval rate and 92% efficiency</p>\n            </div>\n            <div>\n              <h4 style={{ color: '#ff8f00', marginBottom: '10px' }}>⚠️ Needs Attention</h4>\n              <p>Bob Agent requires support to improve efficiency from 75% to team average</p>\n            </div>\n            <div>\n              <h4 style={{ color: '#007E3A', marginBottom: '10px' }}>📈 Team Trend</h4>\n              <p>Overall team performance improved by 8% compared to last month</p>\n            </div>\n          </div>\n        </Card>\n      </ReportsContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default SupervisorReports;\n"], "mappings": "sbAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,2BAA2B,CACxE,OAASC,UAAU,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,KAAM,CAAAC,gBAAgB,CAAGV,MAAM,CAACW,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,0CAGlC,CAED,KAAM,CAAAC,SAAS,CAAGd,MAAM,CAACW,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,kIAK3B,CAED,KAAM,CAAAG,QAAQ,CAAGhB,MAAM,CAACE,IAAI,CAAC,CAAAe,gBAAA,GAAAA,gBAAA,CAAAJ,sBAAA,mMAQ5B,CAED,KAAM,CAAAK,SAAS,CAAGlB,MAAM,CAACW,GAAG,CAAAQ,gBAAA,GAAAA,gBAAA,CAAAN,sBAAA,0EAI3B,CAED,KAAM,CAAAO,SAAS,CAAGpB,MAAM,CAACW,GAAG,CAAAU,gBAAA,GAAAA,gBAAA,CAAAR,sBAAA,oEAI3B,CAED,KAAM,CAAAS,cAAc,CAAGtB,MAAM,CAACW,GAAG,CAAAY,gBAAA,GAAAA,gBAAA,CAAAV,sBAAA,4KAShC,CAED,KAAM,CAAAW,SAAS,CAAGxB,MAAM,CAACE,IAAI,CAAC,CAAAuB,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,4BAE7B,CAED,KAAM,CAAAa,UAAU,CAAG1B,MAAM,CAAC2B,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,4EAI3B,CAED,KAAM,CAAAgB,eAAe,CAAG7B,MAAM,CAACW,GAAG,CAAAmB,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,sFAKjC,CAED,KAAM,CAAAkB,YAAY,CAAG/B,MAAM,CAACgC,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,6LAEZqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE,CAKnCL,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO,CAGtD,CAED,KAAM,CAAAC,oBAAoB,CAAGzC,MAAM,CAAC0C,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAA9B,sBAAA,wDAGxC,CAED,KAAM,CAAA+B,WAAW,CAAG5C,MAAM,CAAC6C,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAjC,sBAAA,qJAGAqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS,CAC5Cb,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,QAAQ,CAE/Cd,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,UAAU,CAChD,CAED,KAAM,CAAAC,SAAS,CAAGlD,MAAM,CAACmD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAvC,sBAAA,uFAGEqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS,CACjE,CAED,KAAM,CAAAM,QAAQ,CAAGrD,MAAM,CAACsD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA1C,sBAAA,wDAEFqB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS,CAE5D,CAED,KAAM,CAAAS,cAAc,CAAGxD,MAAM,CAACW,GAAG,CAAA8C,iBAAA,GAAAA,iBAAA,CAAA5C,sBAAA,sRAUpBqB,KAAK,EAAIA,KAAK,CAACwB,UAAU,CAEdxB,KAAK,EAAIA,KAAK,CAACyB,KAAK,CAG3C,CAED,KAAM,CAAAC,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGjE,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkE,UAAU,CAAEC,aAAa,CAAC,CAAGnE,QAAQ,CAAC,OAAO,CAAC,CACrD,KAAM,CAACoE,SAAS,CAAEC,YAAY,CAAC,CAAGrE,QAAQ,CAAC,CACzCsE,WAAW,CAAE,CAAC,CACdC,UAAU,CAAE,CAAC,CACbC,cAAc,CAAE,CAAC,CACjBC,cAAc,CAAE,CAAC,CACjBC,WAAW,CAAE,CAAC,CACdC,cAAc,CAAE,CAClB,CAAC,CAAC,CACF,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7E,QAAQ,CAAQ,EAAE,CAAC,CACnE,KAAM,CAAA8E,QAAQ,CAAG5E,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd8E,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAACb,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAa,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFd,UAAU,CAAC,IAAI,CAAC,CAEhB,KAAM,CAACe,cAAc,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACzC1E,UAAU,CAAC2E,2BAA2B,CAAC,CAAC,CACzC,CAAC,CAEFd,YAAY,CAAC,CACXC,WAAW,CAAE,CAAC,CAAE;AAChBC,UAAU,CAAES,cAAc,CAACI,UAAU,EAAI,CAAC,CAC1CZ,cAAc,CAAEQ,cAAc,CAACK,cAAc,EAAI,CAAC,CAClDZ,cAAc,CAAEO,cAAc,CAACM,aAAa,EAAI,CAAC,CACjDZ,WAAW,CAAE,GAAG,CAAE;AAClBC,cAAc,CAAE,EAAI;AACtB,CAAC,CAAC,CAEF;AACAE,mBAAmB,CAAC,CAClB,CACEU,SAAS,CAAE,YAAY,CACvBC,aAAa,CAAE,EAAE,CACjBC,cAAc,CAAE,EAAE,CAClBC,YAAY,CAAE,EAAE,CAChBC,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,EACd,CAAC,CACD,CACEL,SAAS,CAAE,YAAY,CACvBC,aAAa,CAAE,EAAE,CACjBC,cAAc,CAAE,EAAE,CAClBC,YAAY,CAAE,EAAE,CAChBC,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,EACd,CAAC,CACD,CACEL,SAAS,CAAE,WAAW,CACtBC,aAAa,CAAE,EAAE,CACjBC,cAAc,CAAE,EAAE,CAClBC,YAAY,CAAE,EAAE,CAChBC,OAAO,CAAE,GAAG,CACZC,UAAU,CAAE,EACd,CAAC,CACF,CAAC,CAEJ,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD;AACAxB,YAAY,CAAC,CACXC,WAAW,CAAE,CAAC,CACdC,UAAU,CAAE,EAAE,CACdC,cAAc,CAAE,EAAE,CAClBC,cAAc,CAAE,CAAC,CACjBC,WAAW,CAAE,GAAG,CAChBC,cAAc,CAAE,EAClB,CAAC,CAAC,CACJ,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA8B,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,WAAW,CAAEC,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,uBAAuB,CAAE,CAAC,CACpF,CAAEkB,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,cAAc,CAAEC,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,oBAAoB,CAAE,CAAC,CACrF,CAAEkB,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,SAAS,CAAEE,MAAM,CAAE,IAAK,CAAC,CAC9C,CAAEH,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,MAAM,CAAEC,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,kBAAkB,CAAE,CAAC,CAC3E,CAED,KAAM,CAAAsB,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,MAAO,CAAAhC,SAAS,CAACG,UAAU,CAAG,CAAC,CAAG8B,IAAI,CAACC,KAAK,CAAElC,SAAS,CAACI,cAAc,CAAGJ,SAAS,CAACG,UAAU,CAAI,GAAG,CAAC,CAAG,CAAC,CAC3G,CAAC,CAED,GAAIP,OAAO,CAAE,CACX,mBACEtD,IAAA,CAACN,eAAe,EAACmG,KAAK,CAAC,cAAc,CAACR,eAAe,CAAEA,eAAgB,CAAAS,QAAA,cACrE9F,IAAA,CAACH,cAAc,GAAE,CAAC,CACH,CAAC,CAEtB,CAEA,mBACEG,IAAA,CAACN,eAAe,EAACmG,KAAK,CAAC,cAAc,CAACR,eAAe,CAAEA,eAAgB,CAAAS,QAAA,cACrE5F,KAAA,CAACC,gBAAgB,EAAA2F,QAAA,eACf5F,KAAA,CAACoB,eAAe,EAAAwE,QAAA,eACd5F,KAAA,CAACsB,YAAY,EAACuE,KAAK,CAAEvC,UAAW,CAACwC,QAAQ,CAAGC,CAAC,EAAKxC,aAAa,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAD,QAAA,eAC9E9F,IAAA,WAAQ+F,KAAK,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAQ,CAAC,cACvC9F,IAAA,WAAQ+F,KAAK,CAAC,OAAO,CAAAD,QAAA,CAAC,YAAU,CAAQ,CAAC,cACzC9F,IAAA,WAAQ+F,KAAK,CAAC,SAAS,CAAAD,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC7C9F,IAAA,WAAQ+F,KAAK,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAQ,CAAC,EAC3B,CAAC,cAEf9F,IAAA,CAACJ,MAAM,EAACuG,OAAO,CAAC,SAAS,CAACX,OAAO,CAAEA,CAAA,GAAMY,MAAM,CAACC,KAAK,CAAC,CAAE,CAAAP,QAAA,CAAC,4BAEzD,CAAQ,CAAC,EACM,CAAC,cAGlB5F,KAAA,CAACK,SAAS,EAAAuF,QAAA,eACR5F,KAAA,CAACO,QAAQ,EAAAqF,QAAA,eACP9F,IAAA,CAACW,SAAS,EAAAmF,QAAA,CAAEpC,SAAS,CAACE,WAAW,CAAY,CAAC,cAC9C5D,IAAA,CAACa,SAAS,EAAAiF,QAAA,CAAC,cAAY,CAAW,CAAC,EAC3B,CAAC,cACX5F,KAAA,CAACO,QAAQ,EAAAqF,QAAA,eACP9F,IAAA,CAACW,SAAS,EAAAmF,QAAA,CAAEpC,SAAS,CAACG,UAAU,CAAY,CAAC,cAC7C7D,IAAA,CAACa,SAAS,EAAAiF,QAAA,CAAC,aAAW,CAAW,CAAC,EAC1B,CAAC,cACX5F,KAAA,CAACO,QAAQ,EAAAqF,QAAA,eACP9F,IAAA,CAACW,SAAS,EAAAmF,QAAA,CAAEpC,SAAS,CAACI,cAAc,CAAY,CAAC,cACjD9D,IAAA,CAACa,SAAS,EAAAiF,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,cACX5F,KAAA,CAACO,QAAQ,EAAAqF,QAAA,eACP9F,IAAA,CAACW,SAAS,EAAAmF,QAAA,CAAEpC,SAAS,CAACK,cAAc,CAAY,CAAC,cACjD/D,IAAA,CAACa,SAAS,EAAAiF,QAAA,CAAC,gBAAc,CAAW,CAAC,EAC7B,CAAC,cACX5F,KAAA,CAACO,QAAQ,EAAAqF,QAAA,eACP9F,IAAA,CAACW,SAAS,EAAAmF,QAAA,CAAEpC,SAAS,CAACM,WAAW,CAAY,CAAC,cAC9ChE,IAAA,CAACa,SAAS,EAAAiF,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,cACX5F,KAAA,CAACO,QAAQ,EAAAqF,QAAA,eACP5F,KAAA,CAACS,SAAS,EAAAmF,QAAA,EAAEpC,SAAS,CAACO,cAAc,CAAC,GAAC,EAAW,CAAC,cAClDjE,IAAA,CAACa,SAAS,EAAAiF,QAAA,CAAC,iBAAe,CAAW,CAAC,EAC9B,CAAC,EACF,CAAC,cAGZ5F,KAAA,CAACa,cAAc,EAAA+E,QAAA,eACb5F,KAAA,CAACe,SAAS,EAAA6E,QAAA,eACR9F,IAAA,CAACmB,UAAU,EAAA2E,QAAA,CAAC,2BAAyB,CAAY,CAAC,cAClD5F,KAAA,QAAKoG,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAT,QAAA,eACnC5F,KAAA,QAAKoG,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAT,QAAA,eACpF9F,IAAA,SAAA8F,QAAA,CAAM,iBAAe,CAAM,CAAC,cAC5B5F,KAAA,SAAA4F,QAAA,EAAOJ,iBAAiB,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,EAChC,CAAC,cACN1F,IAAA,CAACiD,cAAc,EAACE,UAAU,CAAEuC,iBAAiB,CAAC,CAAE,CAACtC,KAAK,CAAC,SAAS,CAAE,CAAC,EAChE,CAAC,cACNlD,KAAA,QAAKoG,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAT,QAAA,eACnC5F,KAAA,QAAKoG,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAT,QAAA,eACpF9F,IAAA,SAAA8F,QAAA,CAAM,iBAAe,CAAM,CAAC,cAC5B5F,KAAA,SAAA4F,QAAA,EAAOpC,SAAS,CAACO,cAAc,CAAC,GAAC,EAAM,CAAC,EACrC,CAAC,cACNjE,IAAA,CAACiD,cAAc,EAACE,UAAU,CAAEO,SAAS,CAACO,cAAe,CAACb,KAAK,CAAC,SAAS,CAAE,CAAC,EACrE,CAAC,cACNlD,KAAA,QAAA4F,QAAA,eACE5F,KAAA,QAAKoG,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAT,QAAA,eACpF9F,IAAA,SAAA8F,QAAA,CAAM,eAAa,CAAM,CAAC,cAC1B9F,IAAA,SAAA8F,QAAA,CAAM,KAAG,CAAM,CAAC,EACb,CAAC,cACN9F,IAAA,CAACiD,cAAc,EAACE,UAAU,CAAE,EAAG,CAACC,KAAK,CAAC,SAAS,CAAE,CAAC,EAC/C,CAAC,EACG,CAAC,cAEZlD,KAAA,CAACe,SAAS,EAAA6E,QAAA,eACR9F,IAAA,CAACmB,UAAU,EAAA2E,QAAA,CAAC,mBAAiB,CAAY,CAAC,cAC1C5F,KAAA,QAAKoG,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEE,mBAAmB,CAAE,SAAS,CAAEC,GAAG,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAd,QAAA,eAChG5F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAE1D,KAAK,CAAE,SAAU,CAAE,CAAA0C,QAAA,CACpEpC,SAAS,CAACI,cAAc,CACtB,CAAC,cACN9D,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEzD,KAAK,CAAE,MAAO,CAAE,CAAA0C,QAAA,CAAC,WAAS,CAAK,CAAC,EAC7D,CAAC,cACN5F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAE1D,KAAK,CAAE,SAAU,CAAE,CAAA0C,QAAA,CACpEpC,SAAS,CAACK,cAAc,CACtB,CAAC,cACN/D,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEzD,KAAK,CAAE,MAAO,CAAE,CAAA0C,QAAA,CAAC,SAAO,CAAK,CAAC,EAC3D,CAAC,cACN5F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAE1D,KAAK,CAAE,SAAU,CAAE,CAAA0C,QAAA,CACpEpC,SAAS,CAACG,UAAU,CAAGH,SAAS,CAACI,cAAc,CAAGJ,SAAS,CAACK,cAAc,CACxE,CAAC,cACN/D,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEzD,KAAK,CAAE,MAAO,CAAE,CAAA0C,QAAA,CAAC,aAAW,CAAK,CAAC,EAC/D,CAAC,cACN5F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAE1D,KAAK,CAAE,SAAU,CAAE,CAAA0C,QAAA,CACpEpC,SAAS,CAACG,UAAU,CAClB,CAAC,cACN7D,IAAA,QAAKsG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEzD,KAAK,CAAE,MAAO,CAAE,CAAA0C,QAAA,CAAC,OAAK,CAAK,CAAC,EACzD,CAAC,EACH,CAAC,EACG,CAAC,EACE,CAAC,cAGjB5F,KAAA,CAACP,IAAI,EAAAmG,QAAA,eACH9F,IAAA,OAAIsG,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEnD,KAAK,CAAE,SAAU,CAAE,CAAA0C,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC7E9F,IAAA,QAAKsG,KAAK,CAAE,CAAES,SAAS,CAAE,MAAO,CAAE,CAAAjB,QAAA,cAChC5F,KAAA,CAACgC,oBAAoB,EAAA4D,QAAA,eACnB9F,IAAA,UAAA8F,QAAA,cACE5F,KAAA,OAAA4F,QAAA,eACE9F,IAAA,CAACqC,WAAW,EAAAyD,QAAA,CAAC,YAAU,CAAa,CAAC,cACrC9F,IAAA,CAACqC,WAAW,EAAAyD,QAAA,CAAC,gBAAc,CAAa,CAAC,cACzC9F,IAAA,CAACqC,WAAW,EAAAyD,QAAA,CAAC,WAAS,CAAa,CAAC,cACpC9F,IAAA,CAACqC,WAAW,EAAAyD,QAAA,CAAC,iBAAe,CAAa,CAAC,cAC1C9F,IAAA,CAACqC,WAAW,EAAAyD,QAAA,CAAC,eAAa,CAAa,CAAC,cACxC9F,IAAA,CAACqC,WAAW,EAAAyD,QAAA,CAAC,WAAS,CAAa,CAAC,cACpC9F,IAAA,CAACqC,WAAW,EAAAyD,QAAA,CAAC,YAAU,CAAa,CAAC,EACnC,CAAC,CACA,CAAC,cACR9F,IAAA,UAAA8F,QAAA,CACG5B,gBAAgB,CAAC8C,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBACjChH,KAAA,CAAC4C,QAAQ,EAAAgD,QAAA,eACP9F,IAAA,CAAC2C,SAAS,EAAC2D,KAAK,CAAE,CAAEQ,UAAU,CAAE,KAAM,CAAE,CAAAhB,QAAA,CAAEmB,KAAK,CAACpC,SAAS,CAAY,CAAC,cACtE7E,IAAA,CAAC2C,SAAS,EAAAmD,QAAA,CAAEmB,KAAK,CAACnC,aAAa,CAAY,CAAC,cAC5C9E,IAAA,CAAC2C,SAAS,EAAAmD,QAAA,CAAEmB,KAAK,CAAClC,cAAc,CAAY,CAAC,cAC7C7E,KAAA,CAACyC,SAAS,EAAAmD,QAAA,EACPH,IAAI,CAACC,KAAK,CAAEqB,KAAK,CAAClC,cAAc,CAAGkC,KAAK,CAACnC,aAAa,CAAI,GAAG,CAAC,CAAC,GAClE,EAAW,CAAC,cACZ5E,KAAA,CAACyC,SAAS,EAAAmD,QAAA,EAAEmB,KAAK,CAACjC,YAAY,CAAC,GAAC,EAAW,CAAC,cAC5C9E,KAAA,CAACyC,SAAS,EAAAmD,QAAA,EAAEmB,KAAK,CAAChC,OAAO,CAAC,OAAK,EAAW,CAAC,cAC3CjF,IAAA,CAAC2C,SAAS,EAAAmD,QAAA,cACR5F,KAAA,QAAKoG,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEW,UAAU,CAAE,QAAQ,CAAER,GAAG,CAAE,MAAO,CAAE,CAAAb,QAAA,eACjE5F,KAAA,SAAA4F,QAAA,EAAOmB,KAAK,CAAC/B,UAAU,CAAC,GAAC,EAAM,CAAC,cAChClF,IAAA,QAAKsG,KAAK,CAAE,CAAEc,IAAI,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAvB,QAAA,cACxC9F,IAAA,CAACiD,cAAc,EACbE,UAAU,CAAE8D,KAAK,CAAC/B,UAAW,CAC7B9B,KAAK,CAAE6D,KAAK,CAAC/B,UAAU,EAAI,EAAE,CAAG,SAAS,CAAG+B,KAAK,CAAC/B,UAAU,EAAI,EAAE,CAAG,SAAS,CAAG,SAAU,CAC5F,CAAC,CACC,CAAC,EACH,CAAC,CACG,CAAC,GAnBCgC,KAoBL,CACX,CAAC,CACG,CAAC,EACY,CAAC,CACpB,CAAC,EACF,CAAC,cAGPhH,KAAA,CAACP,IAAI,EAAAmG,QAAA,eACH9F,IAAA,OAAIsG,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEnD,KAAK,CAAE,SAAU,CAAE,CAAA0C,QAAA,CAAC,cAAY,CAAI,CAAC,cACxE5F,KAAA,QAAKoG,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEE,mBAAmB,CAAE,sCAAsC,CAAEC,GAAG,CAAE,MAAO,CAAE,CAAAb,QAAA,eACxG5F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,OAAIsG,KAAK,CAAE,CAAElD,KAAK,CAAE,SAAS,CAAEmD,YAAY,CAAE,MAAO,CAAE,CAAAT,QAAA,CAAC,4BAAgB,CAAI,CAAC,cAC5E9F,IAAA,MAAA8F,QAAA,CAAG,4DAA0D,CAAG,CAAC,EAC9D,CAAC,cACN5F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,OAAIsG,KAAK,CAAE,CAAElD,KAAK,CAAE,SAAS,CAAEmD,YAAY,CAAE,MAAO,CAAE,CAAAT,QAAA,CAAC,8BAAkB,CAAI,CAAC,cAC9E9F,IAAA,MAAA8F,QAAA,CAAG,2EAAyE,CAAG,CAAC,EAC7E,CAAC,cACN5F,KAAA,QAAA4F,QAAA,eACE9F,IAAA,OAAIsG,KAAK,CAAE,CAAElD,KAAK,CAAE,SAAS,CAAEmD,YAAY,CAAE,MAAO,CAAE,CAAAT,QAAA,CAAC,yBAAa,CAAI,CAAC,cACzE9F,IAAA,MAAA8F,QAAA,CAAG,gEAA8D,CAAG,CAAC,EAClE,CAAC,EACH,CAAC,EACF,CAAC,EACS,CAAC,CACJ,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAzC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}