{"ast": null, "code": "import _objectSpread from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _taggedTemplateLiteral from\"D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7;import React,{useState}from'react';import{useParams,useNavigate}from'react-router-dom';import styled from'styled-components';import{Card,Button,Input,Select,FormGroup,Label,LoadingSpinner}from'../../styles/GlobalStyles';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Container=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 20px;\\n\"])));const Header=styled.div(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid \",\";\\n\"])),props=>props.theme.colors.mediumGray);const Title=styled.h1(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: \",\";\\n  margin-left: 20px;\\n\"])),props=>props.theme.colors.primary);const FormGrid=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n\"])));const CheckboxGroup=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 15px;\\n\"])));const Checkbox=styled.input(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  width: 18px;\\n  height: 18px;\\n\"])));const TextArea=styled.textarea(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: 1px solid \",\";\\n  border-radius: \",\";\\n  font-size: 14px;\\n  font-family: inherit;\\n  resize: vertical;\\n  min-height: 100px;\\n  transition: \",\";\\n\\n  &:focus {\\n    border-color: \",\";\\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\\n    outline: none;\\n  }\\n\"])),props=>props.theme.colors.mediumGray,props=>props.theme.borderRadius.sm,props=>props.theme.transitions.default,props=>props.theme.colors.primary);const VerificationForm=()=>{const{id}=useParams();const navigate=useNavigate();const[loading,setLoading]=useState(false);const[formData,setFormData]=useState({agentName:'',agentContact:'',addressConfirmed:false,personMet:'',relationship:'',officeAddress:'',officeState:'',officeDistrict:'',officePincode:'',landmark:'',companyType:'',businessNature:'',establishmentYear:'',employeesCount:'',grossSalary:'',netSalary:'',proofType:'',verificationDate:'',additionalNotes:''});const handleInputChange=e=>{const{name,value,type}=e.target;if(type==='checkbox'){const checked=e.target.checked;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:checked}));}else{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));}};const handleSubmit=async e=>{e.preventDefault();if(!id)return;setLoading(true);try{const verificationData={agentName:formData.agentName,agentContact:formData.agentContact,addressConfirmed:formData.addressConfirmed?'Yes':'No',personMet:formData.personMet,relationship:formData.relationship,officeAddress:formData.officeAddress,officeState:formData.officeState,officeDistrict:formData.officeDistrict,officePincode:formData.officePincode,landmark:formData.landmark,companyType:formData.companyType,businessNature:formData.businessNature,establishmentYear:parseInt(formData.establishmentYear),employeesCount:formData.employeesCount,grossSalary:parseFloat(formData.grossSalary),netSalary:parseFloat(formData.netSalary),proofType:formData.proofType,verificationDate:formData.verificationDate,additionalNotes:formData.additionalNotes};await apiService.saveVerificationData(parseInt(id),verificationData);alert('Verification data saved successfully!');navigate(\"/lead/\".concat(id));}catch(error){console.error('Error saving verification data:',error);alert('Failed to save verification data');}finally{setLoading(false);}};const handleBack=()=>{navigate(\"/lead/\".concat(id));};return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:handleBack,children:\"\\u2190 Back\"}),/*#__PURE__*/_jsxs(Title,{children:[\"Verification Form - Lead #\",id]})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(FormGrid,{children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Agent Information\"}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"agentName\",children:\"Agent Name\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"agentName\",name:\"agentName\",value:formData.agentName,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"agentContact\",children:\"Agent Contact\"}),/*#__PURE__*/_jsx(Input,{type:\"tel\",id:\"agentContact\",name:\"agentContact\",value:formData.agentContact,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"verificationDate\",children:\"Verification Date\"}),/*#__PURE__*/_jsx(Input,{type:\"date\",id:\"verificationDate\",name:\"verificationDate\",value:formData.verificationDate,onChange:handleInputChange,required:true})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Verification Details\"}),/*#__PURE__*/_jsxs(CheckboxGroup,{children:[/*#__PURE__*/_jsx(Checkbox,{type:\"checkbox\",id:\"addressConfirmed\",name:\"addressConfirmed\",checked:formData.addressConfirmed,onChange:handleInputChange}),/*#__PURE__*/_jsx(Label,{htmlFor:\"addressConfirmed\",children:\"Address Confirmed\"})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"personMet\",children:\"Person Met\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"personMet\",name:\"personMet\",value:formData.personMet,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"relationship\",children:\"Relationship\"}),/*#__PURE__*/_jsxs(Select,{id:\"relationship\",name:\"relationship\",value:formData.relationship,onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Relationship\"}),/*#__PURE__*/_jsx(\"option\",{value:\"self\",children:\"Self\"}),/*#__PURE__*/_jsx(\"option\",{value:\"spouse\",children:\"Spouse\"}),/*#__PURE__*/_jsx(\"option\",{value:\"parent\",children:\"Parent\"}),/*#__PURE__*/_jsx(\"option\",{value:\"sibling\",children:\"Sibling\"}),/*#__PURE__*/_jsx(\"option\",{value:\"other\",children:\"Other\"})]})]})]})]}),/*#__PURE__*/_jsxs(Card,{style:{marginTop:'20px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Office Information\"}),/*#__PURE__*/_jsxs(FormGrid,{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"officeAddress\",children:\"Office Address\"}),/*#__PURE__*/_jsx(TextArea,{id:\"officeAddress\",name:\"officeAddress\",value:formData.officeAddress,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"officeState\",children:\"State\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"officeState\",name:\"officeState\",value:formData.officeState,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"officeDistrict\",children:\"District\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"officeDistrict\",name:\"officeDistrict\",value:formData.officeDistrict,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"officePincode\",children:\"Pincode\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"officePincode\",name:\"officePincode\",value:formData.officePincode,onChange:handleInputChange,required:true})]})]})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"landmark\",children:\"Landmark\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"landmark\",name:\"landmark\",value:formData.landmark,onChange:handleInputChange})]})]}),/*#__PURE__*/_jsxs(Card,{style:{marginTop:'20px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Company Information\"}),/*#__PURE__*/_jsxs(FormGrid,{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"companyType\",children:\"Company Type\"}),/*#__PURE__*/_jsxs(Select,{id:\"companyType\",name:\"companyType\",value:formData.companyType,onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Company Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"private\",children:\"Private Limited\"}),/*#__PURE__*/_jsx(\"option\",{value:\"public\",children:\"Public Limited\"}),/*#__PURE__*/_jsx(\"option\",{value:\"partnership\",children:\"Partnership\"}),/*#__PURE__*/_jsx(\"option\",{value:\"proprietorship\",children:\"Proprietorship\"}),/*#__PURE__*/_jsx(\"option\",{value:\"government\",children:\"Government\"})]})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"businessNature\",children:\"Business Nature\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",id:\"businessNature\",name:\"businessNature\",value:formData.businessNature,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"establishmentYear\",children:\"Establishment Year\"}),/*#__PURE__*/_jsx(Input,{type:\"number\",id:\"establishmentYear\",name:\"establishmentYear\",value:formData.establishmentYear,onChange:handleInputChange,min:\"1900\",max:new Date().getFullYear(),required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"employeesCount\",children:\"Number of Employees\"}),/*#__PURE__*/_jsx(Input,{type:\"number\",id:\"employeesCount\",name:\"employeesCount\",value:formData.employeesCount,onChange:handleInputChange,min:\"1\",required:true})]})]})]}),/*#__PURE__*/_jsxs(Card,{style:{marginTop:'20px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#007E3A'},children:\"Salary Information\"}),/*#__PURE__*/_jsxs(FormGrid,{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"grossSalary\",children:\"Gross Salary (\\u20B9)\"}),/*#__PURE__*/_jsx(Input,{type:\"number\",id:\"grossSalary\",name:\"grossSalary\",value:formData.grossSalary,onChange:handleInputChange,min:\"0\",required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"netSalary\",children:\"Net Salary (\\u20B9)\"}),/*#__PURE__*/_jsx(Input,{type:\"number\",id:\"netSalary\",name:\"netSalary\",value:formData.netSalary,onChange:handleInputChange,min:\"0\",required:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"proofType\",children:\"Proof Type\"}),/*#__PURE__*/_jsxs(Select,{id:\"proofType\",name:\"proofType\",value:formData.proofType,onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Proof Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"salary-slip\",children:\"Salary Slip\"}),/*#__PURE__*/_jsx(\"option\",{value:\"bank-statement\",children:\"Bank Statement\"}),/*#__PURE__*/_jsx(\"option\",{value:\"appointment-letter\",children:\"Appointment Letter\"}),/*#__PURE__*/_jsx(\"option\",{value:\"id-card\",children:\"ID Card\"})]})]})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(Label,{htmlFor:\"additionalNotes\",children:\"Additional Notes\"}),/*#__PURE__*/_jsx(TextArea,{id:\"additionalNotes\",name:\"additionalNotes\",value:formData.additionalNotes,onChange:handleInputChange,placeholder:\"Any additional observations or notes...\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'30px',display:'flex',gap:'10px',justifyContent:'flex-end'},children:[/*#__PURE__*/_jsx(Button,{type:\"button\",variant:\"outline\",onClick:handleBack,disabled:loading,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(LoadingSpinner,{}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:'8px'},children:\"Saving...\"})]}):'Save Verification Data'})]})]})]});};export default VerificationForm;", "map": {"version": 3, "names": ["React", "useState", "useParams", "useNavigate", "styled", "Card", "<PERSON><PERSON>", "Input", "Select", "FormGroup", "Label", "LoadingSpinner", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Container", "div", "_templateObject", "_taggedTemplateLiteral", "Header", "_templateObject2", "props", "theme", "colors", "mediumGray", "Title", "h1", "_templateObject3", "primary", "FormGrid", "_templateObject4", "CheckboxGroup", "_templateObject5", "Checkbox", "input", "_templateObject6", "TextArea", "textarea", "_templateObject7", "borderRadius", "sm", "transitions", "default", "VerificationForm", "id", "navigate", "loading", "setLoading", "formData", "setFormData", "<PERSON><PERSON><PERSON>", "agentContact", "addressConfirmed", "personMet", "relationship", "officeAddress", "officeState", "officeDistrict", "officePincode", "landmark", "companyType", "businessNature", "establishmentYear", "employeesCount", "grossSalary", "netSalary", "proofType", "verificationDate", "additionalNotes", "handleInputChange", "e", "name", "value", "type", "target", "checked", "prev", "_objectSpread", "handleSubmit", "preventDefault", "verificationData", "parseInt", "parseFloat", "saveVerificationData", "alert", "concat", "error", "console", "handleBack", "children", "variant", "onClick", "onSubmit", "style", "marginBottom", "color", "htmlFor", "onChange", "required", "marginTop", "min", "max", "Date", "getFullYear", "placeholder", "display", "gap", "justifyContent", "disabled", "marginLeft"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Verification/VerificationForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Card, Button, Input, Select, FormGroup, Label, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, VerificationData } from '../../services/apiService';\n\nconst Container = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};\n`;\n\nconst Title = styled.h1`\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.theme.colors.primary};\n  margin-left: 20px;\n`;\n\nconst FormGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n`;\n\nconst CheckboxGroup = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 15px;\n`;\n\nconst Checkbox = styled.input`\n  width: 18px;\n  height: 18px;\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  padding: 12px 16px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  font-family: inherit;\n  resize: vertical;\n  min-height: 100px;\n  transition: ${props => props.theme.transitions.default};\n\n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n    outline: none;\n  }\n`;\n\nconst VerificationForm: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n\n  const [formData, setFormData] = useState({\n    agentName: '',\n    agentContact: '',\n    addressConfirmed: false,\n    personMet: '',\n    relationship: '',\n    officeAddress: '',\n    officeState: '',\n    officeDistrict: '',\n    officePincode: '',\n    landmark: '',\n    companyType: '',\n    businessNature: '',\n    establishmentYear: '',\n    employeesCount: '',\n    grossSalary: '',\n    netSalary: '',\n    proofType: '',\n    verificationDate: '',\n    additionalNotes: '',\n  });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target;\n\n    if (type === 'checkbox') {\n      const checked = (e.target as HTMLInputElement).checked;\n      setFormData(prev => ({ ...prev, [name]: checked }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!id) return;\n\n    setLoading(true);\n\n    try {\n      const verificationData: VerificationData = {\n        agentName: formData.agentName,\n        agentContact: formData.agentContact,\n        addressConfirmed: formData.addressConfirmed ? 'Yes' : 'No',\n        personMet: formData.personMet,\n        relationship: formData.relationship,\n        officeAddress: formData.officeAddress,\n        officeState: formData.officeState,\n        officeDistrict: formData.officeDistrict,\n        officePincode: formData.officePincode,\n        landmark: formData.landmark,\n        companyType: formData.companyType,\n        businessNature: formData.businessNature,\n        establishmentYear: parseInt(formData.establishmentYear),\n        employeesCount: formData.employeesCount,\n        grossSalary: parseFloat(formData.grossSalary),\n        netSalary: parseFloat(formData.netSalary),\n        proofType: formData.proofType,\n        verificationDate: formData.verificationDate,\n        additionalNotes: formData.additionalNotes,\n      };\n\n      await apiService.saveVerificationData(parseInt(id), verificationData);\n\n      alert('Verification data saved successfully!');\n      navigate(`/lead/${id}`);\n    } catch (error) {\n      console.error('Error saving verification data:', error);\n      alert('Failed to save verification data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate(`/lead/${id}`);\n  };\n\n  return (\n    <Container>\n      <Header>\n        <Button variant=\"outline\" onClick={handleBack}>\n          ← Back\n        </Button>\n        <Title>Verification Form - Lead #{id}</Title>\n      </Header>\n\n      <form onSubmit={handleSubmit}>\n        <FormGrid>\n          <Card>\n            <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Agent Information</h3>\n\n            <FormGroup>\n              <Label htmlFor=\"agentName\">Agent Name</Label>\n              <Input\n                type=\"text\"\n                id=\"agentName\"\n                name=\"agentName\"\n                value={formData.agentName}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"agentContact\">Agent Contact</Label>\n              <Input\n                type=\"tel\"\n                id=\"agentContact\"\n                name=\"agentContact\"\n                value={formData.agentContact}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"verificationDate\">Verification Date</Label>\n              <Input\n                type=\"date\"\n                id=\"verificationDate\"\n                name=\"verificationDate\"\n                value={formData.verificationDate}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n          </Card>\n\n          <Card>\n            <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Verification Details</h3>\n\n            <CheckboxGroup>\n              <Checkbox\n                type=\"checkbox\"\n                id=\"addressConfirmed\"\n                name=\"addressConfirmed\"\n                checked={formData.addressConfirmed}\n                onChange={handleInputChange}\n              />\n              <Label htmlFor=\"addressConfirmed\">Address Confirmed</Label>\n            </CheckboxGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"personMet\">Person Met</Label>\n              <Input\n                type=\"text\"\n                id=\"personMet\"\n                name=\"personMet\"\n                value={formData.personMet}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"relationship\">Relationship</Label>\n              <Select\n                id=\"relationship\"\n                name=\"relationship\"\n                value={formData.relationship}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Select Relationship</option>\n                <option value=\"self\">Self</option>\n                <option value=\"spouse\">Spouse</option>\n                <option value=\"parent\">Parent</option>\n                <option value=\"sibling\">Sibling</option>\n                <option value=\"other\">Other</option>\n              </Select>\n            </FormGroup>\n          </Card>\n        </FormGrid>\n\n        <Card style={{ marginTop: '20px' }}>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Office Information</h3>\n\n          <FormGrid>\n            <FormGroup>\n              <Label htmlFor=\"officeAddress\">Office Address</Label>\n              <TextArea\n                id=\"officeAddress\"\n                name=\"officeAddress\"\n                value={formData.officeAddress}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n\n            <div>\n              <FormGroup>\n                <Label htmlFor=\"officeState\">State</Label>\n                <Input\n                  type=\"text\"\n                  id=\"officeState\"\n                  name=\"officeState\"\n                  value={formData.officeState}\n                  onChange={handleInputChange}\n                  required\n                />\n              </FormGroup>\n\n              <FormGroup>\n                <Label htmlFor=\"officeDistrict\">District</Label>\n                <Input\n                  type=\"text\"\n                  id=\"officeDistrict\"\n                  name=\"officeDistrict\"\n                  value={formData.officeDistrict}\n                  onChange={handleInputChange}\n                  required\n                />\n              </FormGroup>\n\n              <FormGroup>\n                <Label htmlFor=\"officePincode\">Pincode</Label>\n                <Input\n                  type=\"text\"\n                  id=\"officePincode\"\n                  name=\"officePincode\"\n                  value={formData.officePincode}\n                  onChange={handleInputChange}\n                  required\n                />\n              </FormGroup>\n            </div>\n          </FormGrid>\n\n          <FormGroup>\n            <Label htmlFor=\"landmark\">Landmark</Label>\n            <Input\n              type=\"text\"\n              id=\"landmark\"\n              name=\"landmark\"\n              value={formData.landmark}\n              onChange={handleInputChange}\n            />\n          </FormGroup>\n        </Card>\n\n        <Card style={{ marginTop: '20px' }}>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Company Information</h3>\n\n          <FormGrid>\n            <FormGroup>\n              <Label htmlFor=\"companyType\">Company Type</Label>\n              <Select\n                id=\"companyType\"\n                name=\"companyType\"\n                value={formData.companyType}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Select Company Type</option>\n                <option value=\"private\">Private Limited</option>\n                <option value=\"public\">Public Limited</option>\n                <option value=\"partnership\">Partnership</option>\n                <option value=\"proprietorship\">Proprietorship</option>\n                <option value=\"government\">Government</option>\n              </Select>\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"businessNature\">Business Nature</Label>\n              <Input\n                type=\"text\"\n                id=\"businessNature\"\n                name=\"businessNature\"\n                value={formData.businessNature}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"establishmentYear\">Establishment Year</Label>\n              <Input\n                type=\"number\"\n                id=\"establishmentYear\"\n                name=\"establishmentYear\"\n                value={formData.establishmentYear}\n                onChange={handleInputChange}\n                min=\"1900\"\n                max={new Date().getFullYear()}\n                required\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"employeesCount\">Number of Employees</Label>\n              <Input\n                type=\"number\"\n                id=\"employeesCount\"\n                name=\"employeesCount\"\n                value={formData.employeesCount}\n                onChange={handleInputChange}\n                min=\"1\"\n                required\n              />\n            </FormGroup>\n          </FormGrid>\n        </Card>\n\n        <Card style={{ marginTop: '20px' }}>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Salary Information</h3>\n\n          <FormGrid>\n            <FormGroup>\n              <Label htmlFor=\"grossSalary\">Gross Salary (₹)</Label>\n              <Input\n                type=\"number\"\n                id=\"grossSalary\"\n                name=\"grossSalary\"\n                value={formData.grossSalary}\n                onChange={handleInputChange}\n                min=\"0\"\n                required\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"netSalary\">Net Salary (₹)</Label>\n              <Input\n                type=\"number\"\n                id=\"netSalary\"\n                name=\"netSalary\"\n                value={formData.netSalary}\n                onChange={handleInputChange}\n                min=\"0\"\n                required\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor=\"proofType\">Proof Type</Label>\n              <Select\n                id=\"proofType\"\n                name=\"proofType\"\n                value={formData.proofType}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Select Proof Type</option>\n                <option value=\"salary-slip\">Salary Slip</option>\n                <option value=\"bank-statement\">Bank Statement</option>\n                <option value=\"appointment-letter\">Appointment Letter</option>\n                <option value=\"id-card\">ID Card</option>\n              </Select>\n            </FormGroup>\n          </FormGrid>\n\n          <FormGroup>\n            <Label htmlFor=\"additionalNotes\">Additional Notes</Label>\n            <TextArea\n              id=\"additionalNotes\"\n              name=\"additionalNotes\"\n              value={formData.additionalNotes}\n              onChange={handleInputChange}\n              placeholder=\"Any additional observations or notes...\"\n            />\n          </FormGroup>\n        </Card>\n\n        <div style={{ marginTop: '30px', display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>\n          <Button type=\"button\" variant=\"outline\" onClick={handleBack} disabled={loading}>\n            Cancel\n          </Button>\n          <Button type=\"submit\" disabled={loading}>\n            {loading ? (\n              <>\n                <LoadingSpinner />\n                <span style={{ marginLeft: '8px' }}>Saving...</span>\n              </>\n            ) : (\n              'Save Verification Data'\n            )}\n          </Button>\n        </div>\n      </form>\n    </Container>\n  );\n};\n\nexport default VerificationForm;\n"], "mappings": "ycAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,MAAM,CAAEC,SAAS,CAAEC,KAAK,CAAEC,cAAc,KAAQ,2BAA2B,CACzG,OAASC,UAAU,KAA0B,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzE,KAAM,CAAAC,SAAS,CAAGf,MAAM,CAACgB,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,oEAI3B,CAED,KAAM,CAAAC,MAAM,CAAGnB,MAAM,CAACgB,GAAG,CAAAI,gBAAA,GAAAA,gBAAA,CAAAF,sBAAA,sIAKIG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CAClE,CAED,KAAM,CAAAC,KAAK,CAAGzB,MAAM,CAAC0B,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,wFAGZG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAE7C,CAED,KAAM,CAAAC,QAAQ,CAAG7B,MAAM,CAACgB,GAAG,CAAAc,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,0GAI1B,CAED,KAAM,CAAAa,aAAa,CAAG/B,MAAM,CAACgB,GAAG,CAAAgB,gBAAA,GAAAA,gBAAA,CAAAd,sBAAA,0FAK/B,CAED,KAAM,CAAAe,QAAQ,CAAGjC,MAAM,CAACkC,KAAK,CAAAC,gBAAA,GAAAA,gBAAA,CAAAjB,sBAAA,2CAG5B,CAED,KAAM,CAAAkB,QAAQ,CAAGpC,MAAM,CAACqC,QAAQ,CAAAC,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,gUAGVG,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU,CACzCH,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACiB,YAAY,CAACC,EAAE,CAKvCnB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACmB,WAAW,CAACC,OAAO,CAGpCrB,KAAK,EAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO,CAItD,CAED,KAAM,CAAAe,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAAEC,EAAG,CAAC,CAAG9C,SAAS,CAAiB,CAAC,CAC1C,KAAM,CAAA+C,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC+C,OAAO,CAAEC,UAAU,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAACmD,QAAQ,CAAEC,WAAW,CAAC,CAAGpD,QAAQ,CAAC,CACvCqD,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBC,gBAAgB,CAAE,KAAK,CACvBC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,EAAE,CACrBC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,gBAAgB,CAAE,EAAE,CACpBC,eAAe,CAAE,EACnB,CAAC,CAAC,CAEF,KAAM,CAAAC,iBAAiB,CAAIC,CAAgF,EAAK,CAC9G,KAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGH,CAAC,CAACI,MAAM,CAEtC,GAAID,IAAI,GAAK,UAAU,CAAE,CACvB,KAAM,CAAAE,OAAO,CAAIL,CAAC,CAACI,MAAM,CAAsBC,OAAO,CACtD1B,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAGI,OAAO,EAAG,CAAC,CACrD,CAAC,IAAM,CACL1B,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAGC,KAAK,EAAG,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAM,YAAY,CAAG,KAAO,CAAAR,CAAkB,EAAK,CACjDA,CAAC,CAACS,cAAc,CAAC,CAAC,CAElB,GAAI,CAACnC,EAAE,CAAE,OAETG,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAiC,gBAAkC,CAAG,CACzC9B,SAAS,CAAEF,QAAQ,CAACE,SAAS,CAC7BC,YAAY,CAAEH,QAAQ,CAACG,YAAY,CACnCC,gBAAgB,CAAEJ,QAAQ,CAACI,gBAAgB,CAAG,KAAK,CAAG,IAAI,CAC1DC,SAAS,CAAEL,QAAQ,CAACK,SAAS,CAC7BC,YAAY,CAAEN,QAAQ,CAACM,YAAY,CACnCC,aAAa,CAAEP,QAAQ,CAACO,aAAa,CACrCC,WAAW,CAAER,QAAQ,CAACQ,WAAW,CACjCC,cAAc,CAAET,QAAQ,CAACS,cAAc,CACvCC,aAAa,CAAEV,QAAQ,CAACU,aAAa,CACrCC,QAAQ,CAAEX,QAAQ,CAACW,QAAQ,CAC3BC,WAAW,CAAEZ,QAAQ,CAACY,WAAW,CACjCC,cAAc,CAAEb,QAAQ,CAACa,cAAc,CACvCC,iBAAiB,CAAEmB,QAAQ,CAACjC,QAAQ,CAACc,iBAAiB,CAAC,CACvDC,cAAc,CAAEf,QAAQ,CAACe,cAAc,CACvCC,WAAW,CAAEkB,UAAU,CAAClC,QAAQ,CAACgB,WAAW,CAAC,CAC7CC,SAAS,CAAEiB,UAAU,CAAClC,QAAQ,CAACiB,SAAS,CAAC,CACzCC,SAAS,CAAElB,QAAQ,CAACkB,SAAS,CAC7BC,gBAAgB,CAAEnB,QAAQ,CAACmB,gBAAgB,CAC3CC,eAAe,CAAEpB,QAAQ,CAACoB,eAC5B,CAAC,CAED,KAAM,CAAA5D,UAAU,CAAC2E,oBAAoB,CAACF,QAAQ,CAACrC,EAAE,CAAC,CAAEoC,gBAAgB,CAAC,CAErEI,KAAK,CAAC,uCAAuC,CAAC,CAC9CvC,QAAQ,UAAAwC,MAAA,CAAUzC,EAAE,CAAE,CAAC,CACzB,CAAE,MAAO0C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDF,KAAK,CAAC,kCAAkC,CAAC,CAC3C,CAAC,OAAS,CACRrC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAyC,UAAU,CAAGA,CAAA,GAAM,CACvB3C,QAAQ,UAAAwC,MAAA,CAAUzC,EAAE,CAAE,CAAC,CACzB,CAAC,CAED,mBACEhC,KAAA,CAACG,SAAS,EAAA0E,QAAA,eACR7E,KAAA,CAACO,MAAM,EAAAsE,QAAA,eACL/E,IAAA,CAACR,MAAM,EAACwF,OAAO,CAAC,SAAS,CAACC,OAAO,CAAEH,UAAW,CAAAC,QAAA,CAAC,aAE/C,CAAQ,CAAC,cACT7E,KAAA,CAACa,KAAK,EAAAgE,QAAA,EAAC,4BAA0B,CAAC7C,EAAE,EAAQ,CAAC,EACvC,CAAC,cAEThC,KAAA,SAAMgF,QAAQ,CAAEd,YAAa,CAAAW,QAAA,eAC3B7E,KAAA,CAACiB,QAAQ,EAAA4D,QAAA,eACP7E,KAAA,CAACX,IAAI,EAAAwF,QAAA,eACH/E,IAAA,OAAImF,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAN,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAE7E7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,WAAW,CAAAP,QAAA,CAAC,YAAU,CAAO,CAAC,cAC7C/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,WAAW,CACd2B,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACE,SAAU,CAC1B+C,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,cAAc,CAAAP,QAAA,CAAC,eAAa,CAAO,CAAC,cACnD/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,KAAK,CACV7B,EAAE,CAAC,cAAc,CACjB2B,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAExB,QAAQ,CAACG,YAAa,CAC7B8C,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,kBAAkB,CAAAP,QAAA,CAAC,mBAAiB,CAAO,CAAC,cAC3D/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,kBAAkB,CACrB2B,IAAI,CAAC,kBAAkB,CACvBC,KAAK,CAAExB,QAAQ,CAACmB,gBAAiB,CACjC8B,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,EACR,CAAC,cAEPtF,KAAA,CAACX,IAAI,EAAAwF,QAAA,eACH/E,IAAA,OAAImF,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAN,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAEhF7E,KAAA,CAACmB,aAAa,EAAA0D,QAAA,eACZ/E,IAAA,CAACuB,QAAQ,EACPwC,IAAI,CAAC,UAAU,CACf7B,EAAE,CAAC,kBAAkB,CACrB2B,IAAI,CAAC,kBAAkB,CACvBI,OAAO,CAAE3B,QAAQ,CAACI,gBAAiB,CACnC6C,QAAQ,CAAE5B,iBAAkB,CAC7B,CAAC,cACF3D,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,kBAAkB,CAAAP,QAAA,CAAC,mBAAiB,CAAO,CAAC,EAC9C,CAAC,cAEhB7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,WAAW,CAAAP,QAAA,CAAC,YAAU,CAAO,CAAC,cAC7C/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,WAAW,CACd2B,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACK,SAAU,CAC1B4C,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,cAAc,CAAAP,QAAA,CAAC,cAAY,CAAO,CAAC,cAClD7E,KAAA,CAACR,MAAM,EACLwC,EAAE,CAAC,cAAc,CACjB2B,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAExB,QAAQ,CAACM,YAAa,CAC7B2C,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MAAAT,QAAA,eAER/E,IAAA,WAAQ8D,KAAK,CAAC,EAAE,CAAAiB,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cAC7C/E,IAAA,WAAQ8D,KAAK,CAAC,MAAM,CAAAiB,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC/E,IAAA,WAAQ8D,KAAK,CAAC,QAAQ,CAAAiB,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC/E,IAAA,WAAQ8D,KAAK,CAAC,QAAQ,CAAAiB,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC/E,IAAA,WAAQ8D,KAAK,CAAC,SAAS,CAAAiB,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC/E,IAAA,WAAQ8D,KAAK,CAAC,OAAO,CAAAiB,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC9B,CAAC,EACA,CAAC,EACR,CAAC,EACC,CAAC,cAEX7E,KAAA,CAACX,IAAI,EAAC4F,KAAK,CAAE,CAAEM,SAAS,CAAE,MAAO,CAAE,CAAAV,QAAA,eACjC/E,IAAA,OAAImF,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAN,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAE9E7E,KAAA,CAACiB,QAAQ,EAAA4D,QAAA,eACP7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,eAAe,CAAAP,QAAA,CAAC,gBAAc,CAAO,CAAC,cACrD/E,IAAA,CAAC0B,QAAQ,EACPQ,EAAE,CAAC,eAAe,CAClB2B,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAExB,QAAQ,CAACO,aAAc,CAC9B0C,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,QAAA6E,QAAA,eACE7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,aAAa,CAAAP,QAAA,CAAC,OAAK,CAAO,CAAC,cAC1C/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,aAAa,CAChB2B,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAExB,QAAQ,CAACQ,WAAY,CAC5ByC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,gBAAgB,CAAAP,QAAA,CAAC,UAAQ,CAAO,CAAC,cAChD/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,gBAAgB,CACnB2B,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAExB,QAAQ,CAACS,cAAe,CAC/BwC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,eAAe,CAAAP,QAAA,CAAC,SAAO,CAAO,CAAC,cAC9C/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,eAAe,CAClB2B,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAExB,QAAQ,CAACU,aAAc,CAC9BuC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,EACT,CAAC,EACE,CAAC,cAEXtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,UAAU,CAAAP,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1C/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,UAAU,CACb2B,IAAI,CAAC,UAAU,CACfC,KAAK,CAAExB,QAAQ,CAACW,QAAS,CACzBsC,QAAQ,CAAE5B,iBAAkB,CAC7B,CAAC,EACO,CAAC,EACR,CAAC,cAEPzD,KAAA,CAACX,IAAI,EAAC4F,KAAK,CAAE,CAAEM,SAAS,CAAE,MAAO,CAAE,CAAAV,QAAA,eACjC/E,IAAA,OAAImF,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAN,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAE/E7E,KAAA,CAACiB,QAAQ,EAAA4D,QAAA,eACP7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,aAAa,CAAAP,QAAA,CAAC,cAAY,CAAO,CAAC,cACjD7E,KAAA,CAACR,MAAM,EACLwC,EAAE,CAAC,aAAa,CAChB2B,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAExB,QAAQ,CAACY,WAAY,CAC5BqC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MAAAT,QAAA,eAER/E,IAAA,WAAQ8D,KAAK,CAAC,EAAE,CAAAiB,QAAA,CAAC,qBAAmB,CAAQ,CAAC,cAC7C/E,IAAA,WAAQ8D,KAAK,CAAC,SAAS,CAAAiB,QAAA,CAAC,iBAAe,CAAQ,CAAC,cAChD/E,IAAA,WAAQ8D,KAAK,CAAC,QAAQ,CAAAiB,QAAA,CAAC,gBAAc,CAAQ,CAAC,cAC9C/E,IAAA,WAAQ8D,KAAK,CAAC,aAAa,CAAAiB,QAAA,CAAC,aAAW,CAAQ,CAAC,cAChD/E,IAAA,WAAQ8D,KAAK,CAAC,gBAAgB,CAAAiB,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACtD/E,IAAA,WAAQ8D,KAAK,CAAC,YAAY,CAAAiB,QAAA,CAAC,YAAU,CAAQ,CAAC,EACxC,CAAC,EACA,CAAC,cAEZ7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,gBAAgB,CAAAP,QAAA,CAAC,iBAAe,CAAO,CAAC,cACvD/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,MAAM,CACX7B,EAAE,CAAC,gBAAgB,CACnB2B,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAExB,QAAQ,CAACa,cAAe,CAC/BoC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,mBAAmB,CAAAP,QAAA,CAAC,oBAAkB,CAAO,CAAC,cAC7D/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,QAAQ,CACb7B,EAAE,CAAC,mBAAmB,CACtB2B,IAAI,CAAC,mBAAmB,CACxBC,KAAK,CAAExB,QAAQ,CAACc,iBAAkB,CAClCmC,QAAQ,CAAE5B,iBAAkB,CAC5B+B,GAAG,CAAC,MAAM,CACVC,GAAG,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAE,CAC9BL,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,gBAAgB,CAAAP,QAAA,CAAC,qBAAmB,CAAO,CAAC,cAC3D/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,QAAQ,CACb7B,EAAE,CAAC,gBAAgB,CACnB2B,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAExB,QAAQ,CAACe,cAAe,CAC/BkC,QAAQ,CAAE5B,iBAAkB,CAC5B+B,GAAG,CAAC,GAAG,CACPF,QAAQ,MACT,CAAC,EACO,CAAC,EACJ,CAAC,EACP,CAAC,cAEPtF,KAAA,CAACX,IAAI,EAAC4F,KAAK,CAAE,CAAEM,SAAS,CAAE,MAAO,CAAE,CAAAV,QAAA,eACjC/E,IAAA,OAAImF,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAN,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAE9E7E,KAAA,CAACiB,QAAQ,EAAA4D,QAAA,eACP7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,aAAa,CAAAP,QAAA,CAAC,uBAAgB,CAAO,CAAC,cACrD/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,QAAQ,CACb7B,EAAE,CAAC,aAAa,CAChB2B,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAExB,QAAQ,CAACgB,WAAY,CAC5BiC,QAAQ,CAAE5B,iBAAkB,CAC5B+B,GAAG,CAAC,GAAG,CACPF,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,WAAW,CAAAP,QAAA,CAAC,qBAAc,CAAO,CAAC,cACjD/E,IAAA,CAACP,KAAK,EACJsE,IAAI,CAAC,QAAQ,CACb7B,EAAE,CAAC,WAAW,CACd2B,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACiB,SAAU,CAC1BgC,QAAQ,CAAE5B,iBAAkB,CAC5B+B,GAAG,CAAC,GAAG,CACPF,QAAQ,MACT,CAAC,EACO,CAAC,cAEZtF,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,WAAW,CAAAP,QAAA,CAAC,YAAU,CAAO,CAAC,cAC7C7E,KAAA,CAACR,MAAM,EACLwC,EAAE,CAAC,WAAW,CACd2B,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACkB,SAAU,CAC1B+B,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,MAAAT,QAAA,eAER/E,IAAA,WAAQ8D,KAAK,CAAC,EAAE,CAAAiB,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cAC3C/E,IAAA,WAAQ8D,KAAK,CAAC,aAAa,CAAAiB,QAAA,CAAC,aAAW,CAAQ,CAAC,cAChD/E,IAAA,WAAQ8D,KAAK,CAAC,gBAAgB,CAAAiB,QAAA,CAAC,gBAAc,CAAQ,CAAC,cACtD/E,IAAA,WAAQ8D,KAAK,CAAC,oBAAoB,CAAAiB,QAAA,CAAC,oBAAkB,CAAQ,CAAC,cAC9D/E,IAAA,WAAQ8D,KAAK,CAAC,SAAS,CAAAiB,QAAA,CAAC,SAAO,CAAQ,CAAC,EAClC,CAAC,EACA,CAAC,EACJ,CAAC,cAEX7E,KAAA,CAACP,SAAS,EAAAoF,QAAA,eACR/E,IAAA,CAACJ,KAAK,EAAC0F,OAAO,CAAC,iBAAiB,CAAAP,QAAA,CAAC,kBAAgB,CAAO,CAAC,cACzD/E,IAAA,CAAC0B,QAAQ,EACPQ,EAAE,CAAC,iBAAiB,CACpB2B,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAExB,QAAQ,CAACoB,eAAgB,CAChC6B,QAAQ,CAAE5B,iBAAkB,CAC5BmC,WAAW,CAAC,yCAAyC,CACtD,CAAC,EACO,CAAC,EACR,CAAC,cAEP5F,KAAA,QAAKiF,KAAK,CAAE,CAAEM,SAAS,CAAE,MAAM,CAAEM,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAW,CAAE,CAAAlB,QAAA,eAC1F/E,IAAA,CAACR,MAAM,EAACuE,IAAI,CAAC,QAAQ,CAACiB,OAAO,CAAC,SAAS,CAACC,OAAO,CAAEH,UAAW,CAACoB,QAAQ,CAAE9D,OAAQ,CAAA2C,QAAA,CAAC,QAEhF,CAAQ,CAAC,cACT/E,IAAA,CAACR,MAAM,EAACuE,IAAI,CAAC,QAAQ,CAACmC,QAAQ,CAAE9D,OAAQ,CAAA2C,QAAA,CACrC3C,OAAO,cACNlC,KAAA,CAAAE,SAAA,EAAA2E,QAAA,eACE/E,IAAA,CAACH,cAAc,GAAE,CAAC,cAClBG,IAAA,SAAMmF,KAAK,CAAE,CAAEgB,UAAU,CAAE,KAAM,CAAE,CAAApB,QAAA,CAAC,WAAS,CAAM,CAAC,EACpD,CAAC,CAEH,wBACD,CACK,CAAC,EACN,CAAC,EACF,CAAC,EACE,CAAC,CAEhB,CAAC,CAED,cAAe,CAAA9C,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}