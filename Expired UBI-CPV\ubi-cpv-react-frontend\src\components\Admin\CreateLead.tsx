import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Card, Button, Input, Select, FormGroup, Label, ErrorMessage } from '../../styles/GlobalStyles';
import { apiService, CreateLeadRequest } from '../../services/apiService';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};
`;

const Title = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  margin-left: 20px;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const AddressSection = styled.div`
  margin-top: 20px;
`;

const AddressCard = styled(Card)`
  margin-bottom: 15px;
  position: relative;
`;

const RemoveButton = styled(Button)`
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  font-size: 12px;
`;

const AddAddressButton = styled(Button)`
  margin-top: 10px;
`;

interface Address {
  type: string;
  address: string;
  pincode: string;
  state: string;
  district: string;
  landmark?: string;
}

const CreateLead: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const [formData, setFormData] = useState({
    customerName: '',
    mobileNumber: '',
    loanType: '',
  });

  const [addresses, setAddresses] = useState<Address[]>([
    {
      type: 'Residential',
      address: '',
      pincode: '',
      state: '',
      district: '',
      landmark: '',
    }
  ]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleAddressChange = (index: number, field: string, value: string) => {
    setAddresses(prev => prev.map((addr, i) =>
      i === index ? { ...addr, [field]: value } : addr
    ));
  };

  const addAddress = () => {
    setAddresses(prev => [...prev, {
      type: 'Residential',
      address: '',
      pincode: '',
      state: '',
      district: '',
      landmark: '',
    }]);
  };

  const removeAddress = (index: number) => {
    if (addresses.length > 1) {
      setAddresses(prev => prev.filter((_, i) => i !== index));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.mobileNumber.trim()) {
      newErrors.mobileNumber = 'Mobile number is required';
    } else if (!/^\d{10}$/.test(formData.mobileNumber)) {
      newErrors.mobileNumber = 'Mobile number must be 10 digits';
    }

    if (!formData.loanType) {
      newErrors.loanType = 'Loan type is required';
    }

    // Validate addresses
    addresses.forEach((addr, index) => {
      if (!addr.address.trim()) {
        newErrors[`address_${index}`] = 'Address is required';
      }
      if (!addr.pincode.trim()) {
        newErrors[`pincode_${index}`] = 'Pincode is required';
      } else if (!/^\d{6}$/.test(addr.pincode)) {
        newErrors[`pincode_${index}`] = 'Pincode must be 6 digits';
      }
      if (!addr.state.trim()) {
        newErrors[`state_${index}`] = 'State is required';
      }
      if (!addr.district.trim()) {
        newErrors[`district_${index}`] = 'District is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const leadData: CreateLeadRequest = {
        customerName: formData.customerName,
        mobileNumber: formData.mobileNumber,
        loanType: formData.loanType,
        addresses: addresses,
      };

      await apiService.createLead(leadData);

      alert('Lead created successfully!');
      navigate('/admin/dashboard');
    } catch (error) {
      console.error('Error creating lead:', error);
      alert('Failed to create lead. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/admin/dashboard');
  };

  return (
    <Container>
      <Header>
        <Button variant="outline" onClick={handleBack}>
          ← Back
        </Button>
        <Title>Create New Lead</Title>
      </Header>

      <form onSubmit={handleSubmit}>
        <Card>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Customer Information</h3>

          <FormGrid>
            <FormGroup>
              <Label htmlFor="customerName">Customer Name *</Label>
              <Input
                type="text"
                id="customerName"
                name="customerName"
                value={formData.customerName}
                onChange={handleInputChange}
                placeholder="Enter customer full name"
                required
              />
              {errors.customerName && <ErrorMessage>{errors.customerName}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="mobileNumber">Mobile Number *</Label>
              <Input
                type="tel"
                id="mobileNumber"
                name="mobileNumber"
                value={formData.mobileNumber}
                onChange={handleInputChange}
                placeholder="Enter 10-digit mobile number"
                maxLength={10}
                required
              />
              {errors.mobileNumber && <ErrorMessage>{errors.mobileNumber}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="loanType">Loan Type *</Label>
              <Select
                id="loanType"
                name="loanType"
                value={formData.loanType}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Loan Type</option>
                <option value="Personal Loan">Personal Loan</option>
                <option value="Home Loan">Home Loan</option>
                <option value="Car Loan">Car Loan</option>
                <option value="Business Loan">Business Loan</option>
                <option value="Education Loan">Education Loan</option>
                <option value="Gold Loan">Gold Loan</option>
              </Select>
              {errors.loanType && <ErrorMessage>{errors.loanType}</ErrorMessage>}
            </FormGroup>
          </FormGrid>
        </Card>

        <AddressSection>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Address Information</h3>

          {addresses.map((address, index) => (
            <AddressCard key={index}>
              {addresses.length > 1 && (
                <RemoveButton
                  type="button"
                  variant="danger"
                  size="sm"
                  onClick={() => removeAddress(index)}
                >
                  Remove
                </RemoveButton>
              )}

              <h4 style={{ marginBottom: '15px', color: '#555' }}>
                Address {index + 1}
              </h4>

              <FormGrid>
                <FormGroup>
                  <Label>Address Type</Label>
                  <Select
                    value={address.type}
                    onChange={(e) => handleAddressChange(index, 'type', e.target.value)}
                  >
                    <option value="Residential">Residential</option>
                    <option value="Office">Office</option>
                    <option value="Business">Business</option>
                  </Select>
                </FormGroup>

                <FormGroup>
                  <Label>State *</Label>
                  <Input
                    type="text"
                    value={address.state}
                    onChange={(e) => handleAddressChange(index, 'state', e.target.value)}
                    placeholder="Enter state"
                    required
                  />
                  {errors[`state_${index}`] && <ErrorMessage>{errors[`state_${index}`]}</ErrorMessage>}
                </FormGroup>

                <FormGroup>
                  <Label>District *</Label>
                  <Input
                    type="text"
                    value={address.district}
                    onChange={(e) => handleAddressChange(index, 'district', e.target.value)}
                    placeholder="Enter district"
                    required
                  />
                  {errors[`district_${index}`] && <ErrorMessage>{errors[`district_${index}`]}</ErrorMessage>}
                </FormGroup>

                <FormGroup>
                  <Label>Pincode *</Label>
                  <Input
                    type="text"
                    value={address.pincode}
                    onChange={(e) => handleAddressChange(index, 'pincode', e.target.value)}
                    placeholder="Enter 6-digit pincode"
                    maxLength={6}
                    required
                  />
                  {errors[`pincode_${index}`] && <ErrorMessage>{errors[`pincode_${index}`]}</ErrorMessage>}
                </FormGroup>
              </FormGrid>

              <FormGroup>
                <Label>Full Address *</Label>
                <Input
                  type="text"
                  value={address.address}
                  onChange={(e) => handleAddressChange(index, 'address', e.target.value)}
                  placeholder="Enter complete address"
                  required
                />
                {errors[`address_${index}`] && <ErrorMessage>{errors[`address_${index}`]}</ErrorMessage>}
              </FormGroup>

              <FormGroup>
                <Label>Landmark</Label>
                <Input
                  type="text"
                  value={address.landmark}
                  onChange={(e) => handleAddressChange(index, 'landmark', e.target.value)}
                  placeholder="Enter nearby landmark (optional)"
                />
              </FormGroup>
            </AddressCard>
          ))}

          <AddAddressButton
            type="button"
            variant="outline"
            onClick={addAddress}
          >
            + Add Another Address
          </AddAddressButton>
        </AddressSection>

        <div style={{ marginTop: '30px', display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
          <Button type="button" variant="outline" onClick={handleBack}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Creating...' : 'Create Lead'}
          </Button>
        </div>
      </form>
    </Container>
  );
};

export default CreateLead;
