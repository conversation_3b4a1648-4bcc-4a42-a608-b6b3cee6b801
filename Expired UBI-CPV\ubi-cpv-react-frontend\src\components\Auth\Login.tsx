import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { Button, Input, FormGroup, Label, ErrorMessage, LoadingSpinner, Card, Container } from '../../styles/GlobalStyles';

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.theme.spacing.lg};
  background: linear-gradient(135deg,
    ${props => props.theme.colors.backgroundSecondary} 0%,
    ${props => props.theme.colors.backgroundTertiary} 50%,
    ${props => props.theme.colors.backgroundSecondary} 100%
  );
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, ${props => props.theme.colors.primary}20 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    50% { transform: translate(-50%, -50%) rotate(180deg); }
  }
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 420px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeIn 0.8s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const LogoContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: ${props => props.theme.spacing.lg};
  position: relative;
`;

const Logo = styled.div`
  width: 140px;
  height: 70px;
  position: relative;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
`;

const LogoULeft = styled.div`
  width: 60px;
  height: 60px;
  background: ${props => props.theme.colors.primaryGradient};
  border-radius: 30px 30px 0 0;
  position: absolute;
  left: 10px;
  transform: rotate(180deg);
  box-shadow: ${props => props.theme.shadows.md};
  animation: pulse 2s ease-in-out infinite alternate;

  @keyframes pulse {
    0% { transform: rotate(180deg) scale(1); }
    100% { transform: rotate(180deg) scale(1.05); }
  }
`;

const LogoURight = styled.div`
  width: 60px;
  height: 60px;
  background: ${props => props.theme.colors.secondaryGradient};
  border-radius: 30px 30px 0 0;
  position: absolute;
  right: 10px;
  box-shadow: ${props => props.theme.shadows.md};
  animation: pulse 2s ease-in-out infinite alternate 0.5s;
`;

const Title = styled.h1`
  background: ${props => props.theme.colors.primaryGradient};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: ${props => props.theme.typography.fontSize['2xl']};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  margin-bottom: ${props => props.theme.spacing.xs};
  letter-spacing: -0.025em;
  line-height: ${props => props.theme.typography.lineHeight.tight};
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  font-size: ${props => props.theme.typography.fontSize.base};
  margin-bottom: ${props => props.theme.spacing.sm};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const RoleSelector = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: ${props => props.theme.spacing.xs};
  margin-bottom: ${props => props.theme.spacing.lg};
  padding: ${props => props.theme.spacing.xs};
  background: ${props => props.theme.colors.backgroundTertiary};
  border-radius: ${props => props.theme.borderRadius.xl};
  border: 1px solid ${props => props.theme.colors.border};
`;

const RoleOption = styled.div<{ active: boolean }>`
  text-align: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.lg};
  cursor: pointer;
  transition: ${props => props.theme.transitions.spring};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  font-size: ${props => props.theme.typography.fontSize.sm};
  position: relative;
  overflow: hidden;

  ${props => props.active ? `
    background: ${props.theme.colors.primaryGradient};
    color: ${props.theme.colors.white};
    box-shadow: ${props.theme.shadows.md};
    transform: scale(1.02);
  ` : `
    background: transparent;
    color: ${props.theme.colors.textSecondary};

    &:hover {
      background: ${props.theme.colors.white};
      color: ${props.theme.colors.textPrimary};
      transform: translateY(-1px);
      box-shadow: ${props.theme.shadows.sm};
    }
  `}

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: ${props => props.theme.transitions.default};
  }

  &:hover::before {
    left: 100%;
  }
`;

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [selectedRole, setSelectedRole] = useState<'Agent' | 'Supervisor' | 'Admin'>('Agent');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { login, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Redirect based on user role
      switch (user.role) {
        case 'Agent':
          navigate('/agent/dashboard');
          break;
        case 'Supervisor':
          navigate('/supervisor/dashboard');
          break;
        case 'Admin':
          navigate('/admin/dashboard');
          break;
        default:
          navigate('/');
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }

    setIsSubmitting(true);

    try {
      await login(username, password, selectedRole);
    } catch (err: any) {
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <LoginContainer>
      <LoginCard>
        <Header>
          <LogoContainer>
            <Logo>
              <LogoULeft />
              <LogoURight />
            </Logo>
          </LogoContainer>
          <Title>Union Bank of India</Title>
          <Subtitle>Office Verification System</Subtitle>
        </Header>

        <form onSubmit={handleSubmit}>
          <RoleSelector>
            <RoleOption
              active={selectedRole === 'Agent'}
              onClick={() => setSelectedRole('Agent')}
            >
              Agent
            </RoleOption>
            <RoleOption
              active={selectedRole === 'Supervisor'}
              onClick={() => setSelectedRole('Supervisor')}
            >
              Supervisor
            </RoleOption>
            <RoleOption
              active={selectedRole === 'Admin'}
              onClick={() => setSelectedRole('Admin')}
            >
              Admin
            </RoleOption>
          </RoleSelector>

          <FormGroup>
            <Label htmlFor="username">Username</Label>
            <Input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter your username"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">Password</Label>
            <Input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
            />
            {error && <ErrorMessage>{error}</ErrorMessage>}
          </FormGroup>

          <Button
            type="submit"
            variant="primary"
            fullWidth
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner />
                <span style={{ marginLeft: '8px' }}>Logging in...</span>
              </>
            ) : (
              'Login'
            )}
          </Button>
        </form>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
