import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { Button, Input, FormGroup, Label, ErrorMessage, LoadingSpinner } from '../../styles/GlobalStyles';

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

const LoginCard = styled.div`
  width: 100%;
  max-width: 400px;
  background: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.borderRadius.md};
  box-shadow: ${props => props.theme.shadows.md};
  padding: 30px;
  transition: ${props => props.theme.transitions.default};
  border-top: 4px solid ${props => props.theme.colors.secondary};

  &:hover {
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const LogoContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
`;

const Logo = styled.div`
  width: 120px;
  height: 60px;
  position: relative;
`;

const LogoULeft = styled.div`
  width: 50px;
  height: 50px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 25px 25px 0 0;
  position: absolute;
  left: 15px;
  transform: rotate(180deg);
  box-shadow: ${props => props.theme.shadows.sm};
`;

const LogoURight = styled.div`
  width: 50px;
  height: 50px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 25px 25px 0 0;
  position: absolute;
  right: 15px;
  box-shadow: ${props => props.theme.shadows.sm};
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.secondary};
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
  letter-spacing: 0.5px;
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.textLight};
  font-size: 14px;
  margin-bottom: 5px;
  font-weight: 500;
`;

const RoleSelector = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
`;

const RoleOption = styled.div<{ active: boolean }>`
  flex: 1;
  text-align: center;
  padding: 10px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  background-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.offWhite};
  color: ${props => props.active ? props.theme.colors.white : props.theme.colors.textDark};
  border-color: ${props => props.active ? props.theme.colors.secondary : props.theme.colors.mediumGray};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};
  font-weight: 500;
  font-size: 13px;

  &:first-child {
    border-radius: ${props => props.theme.borderRadius.sm} 0 0 ${props => props.theme.borderRadius.sm};
  }

  &:last-child {
    border-radius: 0 ${props => props.theme.borderRadius.sm} ${props => props.theme.borderRadius.sm} 0;
  }

  &:hover {
    background-color: ${props => props.active ? props.theme.colors.secondaryDark : props.theme.colors.lightGray};
  }
`;

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [selectedRole, setSelectedRole] = useState<'Agent' | 'Supervisor' | 'Admin'>('Agent');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { login, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Redirect based on user role
      switch (user.role) {
        case 'Agent':
          navigate('/agent/dashboard');
          break;
        case 'Supervisor':
          navigate('/supervisor/dashboard');
          break;
        case 'Admin':
          navigate('/admin/dashboard');
          break;
        default:
          navigate('/');
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!username || !password) {
      setError('Please enter both username and password');
      return;
    }

    setIsSubmitting(true);

    try {
      await login(username, password, selectedRole);
    } catch (err: any) {
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <LoginContainer>
      <LoginCard>
        <Header>
          <LogoContainer>
            <Logo>
              <LogoULeft />
              <LogoURight />
            </Logo>
          </LogoContainer>
          <Title>Union Bank of India</Title>
          <Subtitle>Office Verification System</Subtitle>
        </Header>

        <form onSubmit={handleSubmit}>
          <RoleSelector>
            <RoleOption
              active={selectedRole === 'Agent'}
              onClick={() => setSelectedRole('Agent')}
            >
              Agent
            </RoleOption>
            <RoleOption
              active={selectedRole === 'Supervisor'}
              onClick={() => setSelectedRole('Supervisor')}
            >
              Supervisor
            </RoleOption>
            <RoleOption
              active={selectedRole === 'Admin'}
              onClick={() => setSelectedRole('Admin')}
            >
              Admin
            </RoleOption>
          </RoleSelector>

          <FormGroup>
            <Label htmlFor="username">Username</Label>
            <Input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter your username"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">Password</Label>
            <Input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
            />
            {error && <ErrorMessage>{error}</ErrorMessage>}
          </FormGroup>

          <Button
            type="submit"
            variant="primary"
            fullWidth
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner />
                <span style={{ marginLeft: '8px' }}>Logging in...</span>
              </>
            ) : (
              'Login'
            )}
          </Button>
        </form>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
