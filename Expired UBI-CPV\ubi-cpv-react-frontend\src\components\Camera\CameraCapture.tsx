import React, { useRef, useState, useEffect } from 'react';
import styled from 'styled-components';
import { Button } from '../../styles/GlobalStyles';

const CameraContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.borderRadius.md};
  max-width: 600px;
  margin: 0 auto;
`;

const VideoContainer = styled.div`
  position: relative;
  border-radius: ${props => props.theme.borderRadius.md};
  overflow: hidden;
  box-shadow: ${props => props.theme.shadows.md};
`;

const Video = styled.video`
  width: 100%;
  max-width: 500px;
  height: auto;
  display: block;
`;

const Canvas = styled.canvas`
  display: none;
`;

const PreviewImage = styled.img`
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: ${props => props.theme.borderRadius.md};
  box-shadow: ${props => props.theme.shadows.md};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  text-align: center;
  padding: 10px;
  background: #ffebee;
  border-radius: ${props => props.theme.borderRadius.sm};
  border: 1px solid ${props => props.theme.colors.error};
`;

const Instructions = styled.div`
  text-align: center;
  color: ${props => props.theme.colors.textMedium};
  font-size: 14px;
  max-width: 400px;
`;

interface CameraCaptureProps {
  onCapture: (blob: Blob, filename: string) => void;
  onCancel: () => void;
  documentType: string;
}

const CameraCapture: React.FC<CameraCaptureProps> = ({
  onCapture,
  onCancel,
  documentType,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    startCamera();
    
    return () => {
      stopCamera();
    };
  }, []);

  const startCamera = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'environment' // Use back camera on mobile
        },
        audio: false
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsStreaming(true);
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      setError('Unable to access camera. Please check permissions and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsStreaming(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (blob) {
        const imageUrl = URL.createObjectURL(blob);
        setCapturedImage(imageUrl);
        stopCamera();
      }
    }, 'image/jpeg', 0.8);
  };

  const retakePhoto = () => {
    setCapturedImage(null);
    startCamera();
  };

  const confirmCapture = () => {
    if (!capturedImage || !canvasRef.current) return;

    canvasRef.current.toBlob((blob) => {
      if (blob) {
        const filename = `${documentType.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}.jpg`;
        onCapture(blob, filename);
      }
    }, 'image/jpeg', 0.8);
  };

  const handleCancel = () => {
    stopCamera();
    onCancel();
  };

  if (error) {
    return (
      <CameraContainer>
        <ErrorMessage>{error}</ErrorMessage>
        <ButtonGroup>
          <Button onClick={startCamera}>Try Again</Button>
          <Button variant="outline" onClick={handleCancel}>Cancel</Button>
        </ButtonGroup>
      </CameraContainer>
    );
  }

  return (
    <CameraContainer>
      <h3 style={{ color: '#007E3A', marginBottom: '10px' }}>
        Capture {documentType}
      </h3>
      
      <Instructions>
        Position the document clearly in the frame and ensure good lighting for best results.
      </Instructions>

      {isLoading && (
        <div>Loading camera...</div>
      )}

      {capturedImage ? (
        <>
          <PreviewImage src={capturedImage} alt="Captured document" />
          <ButtonGroup>
            <Button onClick={confirmCapture}>Use This Photo</Button>
            <Button variant="outline" onClick={retakePhoto}>Retake</Button>
            <Button variant="outline" onClick={handleCancel}>Cancel</Button>
          </ButtonGroup>
        </>
      ) : (
        <>
          <VideoContainer>
            <Video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              style={{ display: isStreaming ? 'block' : 'none' }}
            />
          </VideoContainer>
          
          <Canvas ref={canvasRef} />
          
          {isStreaming && (
            <ButtonGroup>
              <Button onClick={capturePhoto}>📷 Capture Photo</Button>
              <Button variant="outline" onClick={handleCancel}>Cancel</Button>
            </ButtonGroup>
          )}
        </>
      )}
    </CameraContainer>
  );
};

export default CameraCapture;
