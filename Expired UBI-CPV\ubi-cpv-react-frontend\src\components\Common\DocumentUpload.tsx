import React, { useState, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON>, <PERSON>ading<PERSON><PERSON><PERSON>, Badge } from '../../styles/GlobalStyles';
import { DocumentUpload, UploadProgress } from '../../services/apiService';

const UploadContainer = styled.div`
  border: 2px dashed ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.xl};
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
  transition: ${props => props.theme.transitions.default};
  background: ${props => props.theme.colors.backgroundSecondary};

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background: ${props => props.theme.colors.backgroundTertiary};
  }

  &.drag-over {
    border-color: ${props => props.theme.colors.primary};
    background: ${props => props.theme.colors.primaryLight}20;
  }
`;

const UploadIcon = styled.div`
  font-size: 48px;
  color: ${props => props.theme.colors.textTertiary};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const UploadText = styled.div`
  color: ${props => props.theme.colors.textSecondary};
  font-size: ${props => props.theme.typography.fontSize.lg};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const UploadSubtext = styled.div`
  color: ${props => props.theme.colors.textTertiary};
  font-size: ${props => props.theme.typography.fontSize.sm};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const HiddenInput = styled.input`
  display: none;
`;

const DocumentList = styled.div`
  margin-top: ${props => props.theme.spacing.lg};
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const DocumentItem = styled.div<{ status: string }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  background: ${props => props.theme.colors.white};
  transition: ${props => props.theme.transitions.default};

  ${props => props.status === 'error' && `
    border-color: ${props.theme.colors.error};
    background: ${props.theme.colors.errorLight};
  `}

  ${props => props.status === 'completed' && `
    border-color: ${props.theme.colors.success};
    background: ${props.theme.colors.successLight};
  `}
`;

const DocumentInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  flex: 1;
`;

const DocumentIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.theme.colors.primaryGradient};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.white};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const DocumentDetails = styled.div`
  flex: 1;
`;

const DocumentName = styled.div`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: 2px;
`;

const DocumentMeta = styled.div`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.textTertiary};
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 4px;
  background: ${props => props.theme.colors.backgroundTertiary};
  border-radius: ${props => props.theme.borderRadius.full};
  overflow: hidden;
  margin-top: ${props => props.theme.spacing.xs};
`;

const ProgressFill = styled.div<{ percentage: number }>`
  height: 100%;
  background: ${props => props.theme.colors.primaryGradient};
  width: ${props => props.percentage}%;
  transition: width 0.3s ease;
`;

const DocumentActions = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  font-size: ${props => props.theme.typography.fontSize.sm};
  margin-top: ${props => props.theme.spacing.xs};
`;

interface DocumentUploadComponentProps {
  documents: DocumentUpload[];
  onDocumentsChange: (documents: DocumentUpload[]) => void;
  acceptedTypes?: string[];
  maxFileSize?: number; // in MB
  maxFiles?: number;
  required?: boolean;
}

const DocumentUploadComponent: React.FC<DocumentUploadComponentProps> = ({
  documents,
  onDocumentsChange,
  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],
  maxFileSize = 10, // 10MB default
  maxFiles = 10,
  required = false,
}) => {
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedTypes.includes(fileExtension)) {
      return `File type not supported. Accepted types: ${acceptedTypes.join(', ')}`;
    }

    return null;
  };

  const handleFiles = useCallback((files: FileList) => {
    const newDocuments: DocumentUpload[] = [];

    for (let i = 0; i < files.length && documents.length + newDocuments.length < maxFiles; i++) {
      const file = files[i];
      const error = validateFile(file);

      const documentUpload: DocumentUpload = {
        file,
        documentType: getDocumentTypeFromFileName(file.name),
        documentTypeId: 1, // Default, should be set based on actual document type
        category: 'General',
        isRequired: required,
        status: error ? 'error' : 'pending',
        ...(error && { error }),
      };

      newDocuments.push(documentUpload);
    }

    onDocumentsChange([...documents, ...newDocuments]);
  }, [documents, onDocumentsChange, maxFiles, maxFileSize, acceptedTypes, required]);

  const getDocumentTypeFromFileName = (fileName: string): string => {
    const name = fileName.toLowerCase();
    if (name.includes('id') || name.includes('identity')) return 'ID Proof';
    if (name.includes('address') || name.includes('utility')) return 'Address Proof';
    if (name.includes('income') || name.includes('salary')) return 'Income Proof';
    if (name.includes('bank') || name.includes('statement')) return 'Bank Statement';
    return 'Other Document';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const removeDocument = (index: number) => {
    const updatedDocuments = documents.filter((_, i) => i !== index);
    onDocumentsChange(updatedDocuments);
  };

  const retryUpload = (index: number) => {
    const updatedDocuments = [...documents];
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      status: 'pending',
      error: undefined,
    };
    onDocumentsChange(updatedDocuments);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return '📄';
      case 'jpg':
      case 'jpeg':
      case 'png': return '🖼️';
      case 'doc':
      case 'docx': return '📝';
      default: return '📎';
    }
  };

  return (
    <div>
      <UploadContainer
        className={dragOver ? 'drag-over' : ''}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <UploadIcon>📁</UploadIcon>
        <UploadText>
          Drag and drop files here, or click to select
        </UploadText>
        <UploadSubtext>
          Supported formats: {acceptedTypes.join(', ')} • Max size: {maxFileSize}MB each
        </UploadSubtext>
        <Button variant="outline" type="button">
          Choose Files
        </Button>

        <HiddenInput
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
        />
      </UploadContainer>

      {documents.length > 0 && (
        <DocumentList>
          {documents.map((doc, index) => (
            <DocumentItem key={index} status={doc.status}>
              <DocumentInfo>
                <DocumentIcon>
                  {doc.status === 'uploading' ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    getFileIcon(doc.file.name)
                  )}
                </DocumentIcon>
                <DocumentDetails>
                  <DocumentName>{doc.file.name}</DocumentName>
                  <DocumentMeta>
                    {formatFileSize(doc.file.size)} • {doc.documentType}
                  </DocumentMeta>
                  {doc.status === 'uploading' && doc.uploadProgress && (
                    <ProgressBar>
                      <ProgressFill percentage={doc.uploadProgress.percentage} />
                    </ProgressBar>
                  )}
                  {doc.error && <ErrorMessage>{doc.error}</ErrorMessage>}
                </DocumentDetails>
              </DocumentInfo>

              <DocumentActions>
                <Badge
                  variant={
                    doc.status === 'completed' ? 'success' :
                    doc.status === 'error' ? 'error' :
                    doc.status === 'uploading' ? 'info' : 'secondary'
                  }
                  size="sm"
                >
                  {doc.status === 'uploading' ? 'Uploading...' :
                   doc.status === 'completed' ? 'Uploaded' :
                   doc.status === 'error' ? 'Failed' : 'Ready'}
                </Badge>

                {doc.status === 'error' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => retryUpload(index)}
                  >
                    Retry
                  </Button>
                )}

                <Button
                  size="sm"
                  variant="danger"
                  onClick={() => removeDocument(index)}
                >
                  Remove
                </Button>
              </DocumentActions>
            </DocumentItem>
          ))}
        </DocumentList>
      )}
    </div>
  );
};

export default DocumentUploadComponent;
