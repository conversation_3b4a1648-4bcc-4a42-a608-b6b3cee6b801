import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, LeadListItem, DashboardStats } from '../../services/apiService';

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const StatCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: ${props => props.theme.transitions.default};

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const StatIcon = styled.div<{ color: string }>`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
  color: ${props => props.theme.colors.white};
  background: ${props => props.color};
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: ${props => props.theme.colors.textDark};
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textLight};
  font-weight: 500;
`;

const ActionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const ActionCard = styled(Card)`
  text-align: center;
  padding: 30px 20px;
`;

const ActionIcon = styled.div`
  font-size: 48px;
  margin-bottom: 15px;
`;

const ActionTitle = styled.h3`
  color: ${props => props.theme.colors.primary};
  margin-bottom: 10px;
  font-size: 18px;
`;

const ActionDescription = styled.p`
  color: ${props => props.theme.colors.textMedium};
  margin-bottom: 20px;
  font-size: 14px;
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'new':
        return `
          background-color: #e3f2fd;
          color: #0d47a1;
        `;
      case 'assigned':
        return `
          background-color: #fff3e0;
          color: #e65100;
        `;
      case 'in-progress':
        return `
          background-color: #fff8e1;
          color: #ff8f00;
        `;
      case 'pending-review':
        return `
          background-color: #f3e5f5;
          color: #4a148c;
        `;
      case 'approved':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      case 'rejected':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const AdminDashboard: React.FC = () => {
  const [leads, setLeads] = useState<LeadListItem[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [leadsResponse, statsResponse] = await Promise.all([
        apiService.getLeads(1, 10),
        apiService.getDashboardStats(),
      ]);

      setLeads(leadsResponse.data || []);
      setStats(statsResponse);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Mock data for demo
      setLeads([
        {
          leadId: 1,
          customerName: 'John Doe',
          mobileNumber: '9876543210',
          loanType: 'Personal Loan',
          status: 'new',
          createdDate: '2024-01-15T10:30:00Z',
          createdByName: 'Admin User',
          documentCount: 0,
          croppedImageCount: 0,
        },
        {
          leadId: 2,
          customerName: 'Jane Smith',
          mobileNumber: '9876543211',
          loanType: 'Home Loan',
          status: 'assigned',
          createdDate: '2024-01-14T09:15:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Agent Smith',
          documentCount: 2,
          croppedImageCount: 1,
        },
      ]);
      setStats({
        totalLeads: 25,
        newLeads: 5,
        assignedLeads: 8,
        inProgressLeads: 6,
        pendingReviewLeads: 4,
        approvedLeads: 2,
        rejectedLeads: 0,
        completedLeads: 2,
        pendingLeads: 10,
      });
    } finally {
      setLoading(false);
    }
  };

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', active: true },
    { icon: '➕', label: 'Create Lead', onClick: () => navigate('/admin/create-lead') },
    { icon: '👥', label: 'Manage Users', onClick: () => navigate('/admin/users') },
    { icon: '📋', label: 'All Leads', onClick: () => navigate('/admin/leads') },
    { icon: '📊', label: 'Reports', onClick: () => navigate('/admin/reports') },
    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },
  ];

  const handleCreateLead = () => {
    navigate('/admin/create-lead');
  };

  const handleManageUsers = () => {
    navigate('/admin/users');
  };

  const handleViewReports = () => {
    navigate('/admin/reports');
  };

  const handleLeadClick = (leadId: number) => {
    navigate(`/lead/${leadId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <DashboardLayout title="Admin Dashboard" navigationItems={navigationItems}>
        <div>Loading...</div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Admin Dashboard" navigationItems={navigationItems}>
      {/* Stats Cards */}
      <StatsContainer>
        <StatCard>
          <StatIcon color="linear-gradient(135deg, #007E3A, #005a2a)">📊</StatIcon>
          <StatValue>{stats?.totalLeads || 0}</StatValue>
          <StatLabel>Total Leads</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #e3f2fd, #0d47a1)">🆕</StatIcon>
          <StatValue>{stats?.newLeads || 0}</StatValue>
          <StatLabel>New Leads</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #fff8e1, #ff8f00)">⏳</StatIcon>
          <StatValue>{stats?.inProgressLeads || 0}</StatValue>
          <StatLabel>In Progress</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #f3e5f5, #4a148c)">👁️</StatIcon>
          <StatValue>{stats?.pendingReviewLeads || 0}</StatValue>
          <StatLabel>Pending Review</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #28a745, #1e7e34)">✅</StatIcon>
          <StatValue>{stats?.approvedLeads || 0}</StatValue>
          <StatLabel>Approved</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #dc3545, #c82333)">❌</StatIcon>
          <StatValue>{stats?.rejectedLeads || 0}</StatValue>
          <StatLabel>Rejected</StatLabel>
        </StatCard>
      </StatsContainer>

      {/* Quick Actions */}
      <ActionGrid>
        <ActionCard>
          <ActionIcon>➕</ActionIcon>
          <ActionTitle>Create New Lead</ActionTitle>
          <ActionDescription>
            Add a new customer verification lead to the system
          </ActionDescription>
          <Button onClick={handleCreateLead}>Create Lead</Button>
        </ActionCard>

        <ActionCard>
          <ActionIcon>👥</ActionIcon>
          <ActionTitle>Manage Users</ActionTitle>
          <ActionDescription>
            Add, edit, or deactivate agents and supervisors
          </ActionDescription>
          <Button onClick={handleManageUsers}>Manage Users</Button>
        </ActionCard>

        <ActionCard>
          <ActionIcon>📊</ActionIcon>
          <ActionTitle>View Reports</ActionTitle>
          <ActionDescription>
            Generate and view system performance reports
          </ActionDescription>
          <Button onClick={handleViewReports}>View Reports</Button>
        </ActionCard>
      </ActionGrid>

      {/* Recent Leads */}
      <Card>
        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Recent Leads</h2>

        <TableContainer>
          <Table>
            <thead>
              <tr>
                <TableHeader>Customer Name</TableHeader>
                <TableHeader>Mobile</TableHeader>
                <TableHeader>Loan Type</TableHeader>
                <TableHeader>Status</TableHeader>
                <TableHeader>Created Date</TableHeader>
                <TableHeader>Assigned Agent</TableHeader>
                <TableHeader>Actions</TableHeader>
              </tr>
            </thead>
            <tbody>
              {leads.map((lead) => (
                <TableRow key={lead.leadId}>
                  <TableCell>{lead.customerName}</TableCell>
                  <TableCell>{lead.mobileNumber}</TableCell>
                  <TableCell>{lead.loanType}</TableCell>
                  <TableCell>
                    <StatusBadge status={lead.status}>
                      {lead.status.replace('-', ' ').toUpperCase()}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>{formatDate(lead.createdDate)}</TableCell>
                  <TableCell>
                    {lead.assignedToName || 'Unassigned'}
                  </TableCell>
                  <TableCell>
                    <Button
                      size="sm"
                      onClick={() => handleLeadClick(lead.leadId)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {leads.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>
            No leads found.
          </div>
        )}
      </Card>
    </DashboardLayout>
  );
};

export default AdminDashboard;
