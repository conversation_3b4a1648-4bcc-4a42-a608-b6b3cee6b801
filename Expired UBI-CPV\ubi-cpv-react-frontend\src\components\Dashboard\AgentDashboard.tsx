import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, Button } from '../../styles/GlobalStyles';
import { apiService, LeadListItem, AgentDashboardStats } from '../../services/apiService';

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const StatCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: ${props => props.theme.transitions.default};

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const StatIcon = styled.div<{ color: string }>`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
  color: ${props => props.theme.colors.white};
  background: ${props => props.color};
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: ${props => props.theme.colors.textDark};
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textLight};
  font-weight: 500;
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'new':
        return `
          background-color: #e3f2fd;
          color: #0d47a1;
        `;
      case 'assigned':
        return `
          background-color: #fff3e0;
          color: #e65100;
        `;
      case 'in-progress':
        return `
          background-color: #fff8e1;
          color: #ff8f00;
        `;
      case 'pending-review':
        return `
          background-color: #f3e5f5;
          color: #4a148c;
        `;
      case 'completed':
      case 'approved':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      case 'rejected':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 10px 15px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  margin-bottom: 20px;
  transition: ${props => props.theme.transitions.default};

  &:focus {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
    outline: none;
  }
`;

const AgentDashboard: React.FC = () => {
  const [leads, setLeads] = useState<LeadListItem[]>([]);
  const [stats, setStats] = useState<AgentDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [leadsResponse, statsResponse] = await Promise.all([
        apiService.getLeads(1, 50, 'assigned'),
        apiService.getAgentDashboardStats(),
      ]);

      setLeads(leadsResponse.data || []);
      setStats(statsResponse);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // For demo purposes, use mock data
      setLeads([
        {
          leadId: 1,
          customerName: 'John Doe',
          mobileNumber: '9876543210',
          loanType: 'Personal Loan',
          status: 'assigned',
          createdDate: '2024-01-15T10:30:00Z',
          assignedDate: '2024-01-15T11:00:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Current Agent',
          documentCount: 0,
          croppedImageCount: 0,
        },
        {
          leadId: 2,
          customerName: 'Jane Smith',
          mobileNumber: '9876543211',
          loanType: 'Home Loan',
          status: 'in-progress',
          createdDate: '2024-01-14T09:15:00Z',
          assignedDate: '2024-01-14T10:00:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Current Agent',
          documentCount: 2,
          croppedImageCount: 1,
        },
      ]);
      setStats({
        pendingLeads: 1,
        inProgressLeads: 2,
        completedLeads: 0,
        rejectedLeads: 0,
        totalAssigned: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredLeads = leads.filter(lead =>
    lead.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.mobileNumber.includes(searchTerm) ||
    lead.loanType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', active: true },
    { icon: '📋', label: 'My Tasks', onClick: () => navigate('/agent/tasks') },
    { icon: '✅', label: 'Completed', onClick: () => navigate('/agent/completed') },
    { icon: '📊', label: 'Reports', onClick: () => navigate('/agent/reports') },
  ];

  const handleLeadClick = (leadId: number) => {
    navigate(`/lead/${leadId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <DashboardLayout title="Agent Dashboard" navigationItems={navigationItems}>
        <div>Loading...</div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Agent Dashboard" navigationItems={navigationItems}>
      {/* Stats Cards */}
      <StatsContainer>
        <StatCard>
          <StatIcon color="linear-gradient(135deg, #FFD100, #e6bc00)">📋</StatIcon>
          <StatValue>{stats?.pendingLeads || 0}</StatValue>
          <StatLabel>Pending Tasks</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #007E3A, #005a2a)">⏱️</StatIcon>
          <StatValue>{stats?.inProgressLeads || 0}</StatValue>
          <StatLabel>In Progress</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #28a745, #1e7e34)">✅</StatIcon>
          <StatValue>{stats?.completedLeads || 0}</StatValue>
          <StatLabel>Completed</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #dc3545, #c82333)">❌</StatIcon>
          <StatValue>{stats?.rejectedLeads || 0}</StatValue>
          <StatLabel>Rejected</StatLabel>
        </StatCard>
      </StatsContainer>

      {/* Leads Table */}
      <Card>
        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>My Assigned Leads</h2>

        <SearchInput
          type="text"
          placeholder="Search leads by name, mobile, or loan type..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />

        <TableContainer>
          <Table>
            <thead>
              <tr>
                <TableHeader>Customer Name</TableHeader>
                <TableHeader>Mobile</TableHeader>
                <TableHeader>Loan Type</TableHeader>
                <TableHeader>Status</TableHeader>
                <TableHeader>Assigned Date</TableHeader>
                <TableHeader>Actions</TableHeader>
              </tr>
            </thead>
            <tbody>
              {filteredLeads.map((lead) => (
                <TableRow key={lead.leadId}>
                  <TableCell>{lead.customerName}</TableCell>
                  <TableCell>{lead.mobileNumber}</TableCell>
                  <TableCell>{lead.loanType}</TableCell>
                  <TableCell>
                    <StatusBadge status={lead.status}>
                      {lead.status.replace('-', ' ').toUpperCase()}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>
                    {lead.assignedDate ? formatDate(lead.assignedDate) : '-'}
                  </TableCell>
                  <TableCell>
                    <Button
                      size="sm"
                      onClick={() => handleLeadClick(lead.leadId)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {filteredLeads.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>
            {searchTerm ? 'No leads found matching your search.' : 'No leads assigned yet.'}
          </div>
        )}
      </Card>
    </DashboardLayout>
  );
};

export default AgentDashboard;
