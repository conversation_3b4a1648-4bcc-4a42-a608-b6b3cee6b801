import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, LeadListItem, SupervisorDashboardStats } from '../../services/apiService';

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const StatCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: ${props => props.theme.transitions.default};

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const StatIcon = styled.div<{ color: string }>`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
  color: ${props => props.theme.colors.white};
  background: ${props => props.color};
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: ${props => props.theme.colors.textDark};
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textLight};
  font-weight: 500;
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'pending-review':
        return `
          background-color: #f3e5f5;
          color: #4a148c;
        `;
      case 'approved':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      case 'rejected':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background-color: ${props => props.theme.colors.white};
`;

const SupervisorDashboard: React.FC = () => {
  const [leads, setLeads] = useState<LeadListItem[]>([]);
  const [stats, setStats] = useState<SupervisorDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('pending-review');
  const navigate = useNavigate();

  useEffect(() => {
    loadDashboardData();
  }, [statusFilter]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [leadsResponse, statsResponse] = await Promise.all([
        apiService.getLeads(1, 50, statusFilter || undefined),
        apiService.getSupervisorDashboardStats(),
      ]);

      setLeads(leadsResponse.data || []);
      setStats(statsResponse);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Mock data for demo
      setLeads([
        {
          leadId: 1,
          customerName: 'John Doe',
          mobileNumber: '9876543210',
          loanType: 'Personal Loan',
          status: 'pending-review',
          createdDate: '2024-01-15T10:30:00Z',
          assignedToName: 'Agent Smith',
          createdByName: 'Admin User',
          documentCount: 3,
          croppedImageCount: 2,
        },
        {
          leadId: 2,
          customerName: 'Jane Smith',
          mobileNumber: '9876543211',
          loanType: 'Home Loan',
          status: 'pending-review',
          createdDate: '2024-01-14T09:15:00Z',
          assignedToName: 'Agent Johnson',
          createdByName: 'Admin User',
          documentCount: 5,
          croppedImageCount: 3,
        },
      ]);
      setStats({
        pendingReviews: 2,
        approvedToday: 1,
        rejectedToday: 0,
        totalReviewed: 10,
        approvalRate: 85.5,
        agentPerformance: [
          {
            agentId: 1,
            agentName: 'Agent Smith',
            assignedCount: 5,
            completedCount: 4,
            approvedCount: 3,
            rejectedCount: 1,
            completionRate: 80,
            approvalRate: 75,
          },
        ],
        totalLeads: 15,
        completedLeads: 10,
      });
    } finally {
      setLoading(false);
    }
  };

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', active: true },
    { icon: '👁️', label: 'Review Queue', onClick: () => navigate('/supervisor/review') },
    { icon: '✅', label: 'Approved', onClick: () => navigate('/supervisor/approved') },
    { icon: '❌', label: 'Rejected', onClick: () => navigate('/supervisor/rejected') },
    { icon: '📊', label: 'Reports', onClick: () => navigate('/supervisor/reports') },
  ];

  const handleLeadClick = (leadId: number) => {
    navigate(`/lead/${leadId}`);
  };

  const handleReviewLead = (leadId: number) => {
    navigate(`/supervisor/review/${leadId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <DashboardLayout title="Supervisor Dashboard" navigationItems={navigationItems}>
        <div>Loading...</div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Supervisor Dashboard" navigationItems={navigationItems}>
      {/* Stats Cards */}
      <StatsContainer>
        <StatCard>
          <StatIcon color="linear-gradient(135deg, #f3e5f5, #4a148c)">⏳</StatIcon>
          <StatValue>{stats?.pendingReviews || 0}</StatValue>
          <StatLabel>Pending Review</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #28a745, #1e7e34)">✅</StatIcon>
          <StatValue>{stats?.approvedToday || 0}</StatValue>
          <StatLabel>Approved Today</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #dc3545, #c82333)">❌</StatIcon>
          <StatValue>{stats?.rejectedToday || 0}</StatValue>
          <StatLabel>Rejected Today</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon color="linear-gradient(135deg, #007E3A, #005a2a)">📊</StatIcon>
          <StatValue>{stats?.approvalRate ? `${stats.approvalRate.toFixed(1)}%` : '0%'}</StatValue>
          <StatLabel>Approval Rate</StatLabel>
        </StatCard>
      </StatsContainer>

      {/* Review Queue */}
      <Card>
        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Review Queue</h2>

        <FilterContainer>
          <FilterSelect
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="pending-review">Pending Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="">All Statuses</option>
          </FilterSelect>
        </FilterContainer>

        <TableContainer>
          <Table>
            <thead>
              <tr>
                <TableHeader>Customer Name</TableHeader>
                <TableHeader>Mobile</TableHeader>
                <TableHeader>Loan Type</TableHeader>
                <TableHeader>Status</TableHeader>
                <TableHeader>Submitted Date</TableHeader>
                <TableHeader>Agent</TableHeader>
                <TableHeader>Actions</TableHeader>
              </tr>
            </thead>
            <tbody>
              {leads.map((lead) => (
                <TableRow key={lead.leadId}>
                  <TableCell>{lead.customerName}</TableCell>
                  <TableCell>{lead.mobileNumber}</TableCell>
                  <TableCell>{lead.loanType}</TableCell>
                  <TableCell>
                    <StatusBadge status={lead.status}>
                      {lead.status.replace('-', ' ').toUpperCase()}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>
                    {lead.createdDate ? formatDate(lead.createdDate) : '-'}
                  </TableCell>
                  <TableCell>
                    {lead.assignedToName || 'Unassigned'}
                  </TableCell>
                  <TableCell>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <Button
                        size="sm"
                        onClick={() => handleLeadClick(lead.leadId)}
                      >
                        View
                      </Button>
                      {lead.status === 'pending-review' && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleReviewLead(lead.leadId)}
                        >
                          Review
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {leads.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>
            No leads found for the selected status.
          </div>
        )}
      </Card>
    </DashboardLayout>
  );
};

export default SupervisorDashboard;
