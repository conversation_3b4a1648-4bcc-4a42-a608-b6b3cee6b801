import React, { useState } from 'react';
import styled from 'styled-components';
import {
  Con<PERSON>er,
  Card,
  Button,
  Input,
  FormGroup,
  Label,
  Badge,
  Avatar,
  Divider,
  Loading<PERSON><PERSON>ner,
  TooltipWrapper,
  Tooltip,
} from '../../styles/GlobalStyles';

const ShowcaseContainer = styled.div`
  min-height: 100vh;
  padding: ${props => props.theme.spacing.xl};
  background: linear-gradient(135deg, 
    ${props => props.theme.colors.backgroundSecondary} 0%, 
    ${props => props.theme.colors.backgroundTertiary} 100%
  );
`;

const Title = styled.h1`
  font-size: ${props => props.theme.typography.fontSize['4xl']};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  background: ${props => props.theme.colors.primaryGradient};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: ${props => props.theme.spacing['2xl']};
`;

const Section = styled.div`
  margin-bottom: ${props => props.theme.spacing['2xl']};
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.typography.fontSize['2xl']};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.lg};
`;

const FlexContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};
  align-items: center;
`;

const ModernUIShowcase: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const handleLoadingDemo = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 3000);
  };

  return (
    <ShowcaseContainer>
      <Container maxWidth="xl">
        <Title>Modern UI/UX Showcase</Title>
        
        {/* Cards Section */}
        <Section>
          <SectionTitle>Modern Cards</SectionTitle>
          <Grid>
            <Card variant="default">
              <h3>Default Card</h3>
              <p>This is a default card with modern styling, subtle shadows, and smooth hover effects.</p>
            </Card>
            
            <Card variant="glass">
              <h3>Glass Card</h3>
              <p>Glassmorphism effect with backdrop blur and transparency for a modern look.</p>
            </Card>
            
            <Card variant="neumorphism">
              <h3>Neumorphism Card</h3>
              <p>Soft, extruded design that appears to emerge from the background.</p>
            </Card>
          </Grid>
        </Section>

        {/* Buttons Section */}
        <Section>
          <SectionTitle>Modern Buttons</SectionTitle>
          <Card>
            <FlexContainer>
              <Button variant="primary">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="success">Success</Button>
              <Button variant="danger">Danger</Button>
            </FlexContainer>
            
            <Divider spacing="lg" />
            
            <FlexContainer>
              <Button size="xs">Extra Small</Button>
              <Button size="sm">Small</Button>
              <Button size="md">Medium</Button>
              <Button size="lg">Large</Button>
              <Button size="xl">Extra Large</Button>
            </FlexContainer>
            
            <Divider spacing="lg" />
            
            <FlexContainer>
              <Button rounded>Rounded</Button>
              <Button loading={loading} onClick={handleLoadingDemo}>
                {loading ? 'Loading...' : 'Click for Loading'}
              </Button>
              <Button disabled>Disabled</Button>
            </FlexContainer>
          </Card>
        </Section>

        {/* Form Elements */}
        <Section>
          <SectionTitle>Modern Form Elements</SectionTitle>
          <Grid>
            <Card>
              <FormGroup>
                <Label required>Default Input</Label>
                <Input placeholder="Enter your text..." />
              </FormGroup>
              
              <FormGroup>
                <Label>Filled Input</Label>
                <Input variant="filled" placeholder="Filled variant..." />
              </FormGroup>
              
              <FormGroup>
                <Label>Flushed Input</Label>
                <Input variant="flushed" placeholder="Flushed variant..." />
              </FormGroup>
            </Card>
            
            <Card>
              <FormGroup>
                <Label>Small Input</Label>
                <Input size="sm" placeholder="Small size..." />
              </FormGroup>
              
              <FormGroup>
                <Label>Medium Input</Label>
                <Input size="md" placeholder="Medium size..." />
              </FormGroup>
              
              <FormGroup>
                <Label>Large Input</Label>
                <Input size="lg" placeholder="Large size..." />
              </FormGroup>
            </Card>
          </Grid>
        </Section>

        {/* Badges and Avatars */}
        <Section>
          <SectionTitle>Badges & Avatars</SectionTitle>
          <Card>
            <h4>Badges</h4>
            <FlexContainer>
              <Badge variant="primary">Primary</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
              <Badge variant="error">Error</Badge>
              <Badge variant="info">Info</Badge>
            </FlexContainer>
            
            <Divider spacing="lg" />
            
            <h4>Avatars</h4>
            <FlexContainer>
              <Avatar size="xs">XS</Avatar>
              <Avatar size="sm">SM</Avatar>
              <Avatar size="md">MD</Avatar>
              <Avatar size="lg">LG</Avatar>
              <Avatar size="xl">XL</Avatar>
            </FlexContainer>
          </Card>
        </Section>

        {/* Interactive Elements */}
        <Section>
          <SectionTitle>Interactive Elements</SectionTitle>
          <Card>
            <FlexContainer>
              <TooltipWrapper>
                <Button variant="outline">Hover for Tooltip</Button>
                <Tooltip position="top">This is a tooltip!</Tooltip>
              </TooltipWrapper>
              
              <LoadingSpinner size="sm" />
              <LoadingSpinner size="md" />
              <LoadingSpinner size="lg" />
            </FlexContainer>
          </Card>
        </Section>
      </Container>
    </ShowcaseContainer>
  );
};

export default ModernUIShowcase;
