import React, { useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import CameraCapture from '../Camera/CameraCapture';
import { apiService } from '../../services/apiService';

const Container = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};
`;

const Title = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  margin-left: 20px;
`;

const DocumentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const DocumentCard = styled(Card)`
  text-align: center;
  padding: 30px 20px;
  border: 2px dashed ${props => props.theme.colors.mediumGray};
  transition: ${props => props.theme.transitions.default};

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.offWhite};
  }
`;

const DocumentIcon = styled.div`
  font-size: 48px;
  margin-bottom: 15px;
  color: ${props => props.theme.colors.primary};
`;

const DocumentTitle = styled.h3`
  color: ${props => props.theme.colors.primary};
  margin-bottom: 10px;
  font-size: 18px;
`;

const DocumentDescription = styled.p`
  color: ${props => props.theme.colors.textMedium};
  margin-bottom: 20px;
  font-size: 14px;
`;

const UploadArea = styled.div<{ isDragOver: boolean }>`
  border: 2px dashed ${props => props.isDragOver ? props.theme.colors.primary : props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: 40px 20px;
  text-align: center;
  background-color: ${props => props.isDragOver ? props.theme.colors.offWhite : 'transparent'};
  transition: ${props => props.theme.transitions.default};
  cursor: pointer;
  margin-bottom: 15px;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.offWhite};
  }
`;

const UploadIcon = styled.div`
  font-size: 32px;
  margin-bottom: 10px;
  color: ${props => props.theme.colors.primary};
`;

const UploadText = styled.div`
  color: ${props => props.theme.colors.textMedium};
  font-size: 14px;
`;

const FileInput = styled.input`
  display: none;
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 200px;
  border-radius: ${props => props.theme.borderRadius.sm};
  margin-bottom: 10px;
`;

const FileName = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textDark};
  margin-bottom: 10px;
  word-break: break-all;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
`;

const CameraModal = styled.div<{ show: boolean }>`
  display: ${props => props.show ? 'flex' : 'none'};
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  align-items: center;
  justify-content: center;
`;



interface DocumentType {
  id: string;
  title: string;
  description: string;
  icon: string;
  required: boolean;
}

const documentTypes: DocumentType[] = [
  {
    id: 'id-proof',
    title: 'ID Proof',
    description: 'Aadhaar Card, PAN Card, Passport, or Driving License',
    icon: '🆔',
    required: true,
  },
  {
    id: 'address-proof',
    title: 'Address Proof',
    description: 'Utility Bill, Bank Statement, or Rental Agreement',
    icon: '🏠',
    required: true,
  },
  {
    id: 'income-proof',
    title: 'Income Proof',
    description: 'Salary Slip, Bank Statement, or ITR',
    icon: '💰',
    required: true,
  },
  {
    id: 'office-photo',
    title: 'Office Photo',
    description: 'Photo of the office premises',
    icon: '🏢',
    required: false,
  },
];

const DocumentUpload: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [uploadedFiles, setUploadedFiles] = useState<{ [key: string]: File }>({});
  const [dragOver, setDragOver] = useState<{ [key: string]: boolean }>({});
  const [showCamera, setShowCamera] = useState(false);
  const [currentDocType, setCurrentDocType] = useState<string>('');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: boolean }>({});
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  const handleFileSelect = (docType: string, file: File) => {
    setUploadedFiles(prev => ({ ...prev, [docType]: file }));
  };

  const handleFileInputChange = (docType: string, e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(docType, file);
    }
  };

  const handleDragOver = (docType: string, e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(prev => ({ ...prev, [docType]: true }));
  };

  const handleDragLeave = (docType: string) => {
    setDragOver(prev => ({ ...prev, [docType]: false }));
  };

  const handleDrop = (docType: string, e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(prev => ({ ...prev, [docType]: false }));

    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(docType, file);
    }
  };

  const handleUploadClick = (docType: string) => {
    fileInputRefs.current[docType]?.click();
  };

  const handleCameraCapture = (docType: string) => {
    setCurrentDocType(docType);
    setShowCamera(true);
  };

  const handleRemoveFile = (docType: string) => {
    setUploadedFiles(prev => {
      const newFiles = { ...prev };
      delete newFiles[docType];
      return newFiles;
    });
  };

  const handleSubmit = async () => {
    if (!id) return;

    const leadId = parseInt(id);
    const filesToUpload = Object.entries(uploadedFiles);

    if (filesToUpload.length === 0) {
      alert('Please select at least one document to upload.');
      return;
    }

    setUploading(true);

    try {
      // Upload each file individually
      for (const [docType, file] of filesToUpload) {
        setUploadProgress(prev => ({ ...prev, [docType]: true }));

        await apiService.uploadDocument(leadId, docType, file);

        setUploadProgress(prev => ({ ...prev, [docType]: false }));
      }

      alert('All documents uploaded successfully!');
      navigate(`/lead/${id}`);
    } catch (error) {
      console.error('Error uploading documents:', error);
      alert('Failed to upload some documents. Please try again.');
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  };

  const handleBack = () => {
    navigate(`/lead/${id}`);
  };

  const isImageFile = (file: File) => {
    return file.type.startsWith('image/');
  };

  const getFilePreview = (file: File) => {
    if (isImageFile(file)) {
      return URL.createObjectURL(file);
    }
    return null;
  };

  return (
    <Container>
      <Header>
        <Button variant="outline" onClick={handleBack}>
          ← Back
        </Button>
        <Title>Document Upload - Lead #{id}</Title>
      </Header>

      <DocumentGrid>
        {documentTypes.map((docType) => {
          const uploadedFile = uploadedFiles[docType.id];
          const isDragOver = dragOver[docType.id] || false;

          return (
            <DocumentCard key={docType.id}>
              <DocumentIcon>{docType.icon}</DocumentIcon>
              <DocumentTitle>
                {docType.title}
                {docType.required && <span style={{ color: 'red' }}> *</span>}
              </DocumentTitle>
              <DocumentDescription>{docType.description}</DocumentDescription>

              {uploadedFile ? (
                <div>
                  {isImageFile(uploadedFile) && (
                    <PreviewImage
                      src={getFilePreview(uploadedFile) || ''}
                      alt="Preview"
                    />
                  )}
                  <FileName>{uploadedFile.name}</FileName>
                  {uploadProgress[docType.id] ? (
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                      <LoadingSpinner />
                      <span>Uploading...</span>
                    </div>
                  ) : (
                    <ButtonGroup>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleRemoveFile(docType.id)}
                        disabled={uploading}
                      >
                        Remove
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleUploadClick(docType.id)}
                        disabled={uploading}
                      >
                        Replace
                      </Button>
                    </ButtonGroup>
                  )}
                </div>
              ) : (
                <div>
                  <UploadArea
                    isDragOver={isDragOver}
                    onClick={() => handleUploadClick(docType.id)}
                    onDragOver={(e) => handleDragOver(docType.id, e)}
                    onDragLeave={() => handleDragLeave(docType.id)}
                    onDrop={(e) => handleDrop(docType.id, e)}
                  >
                    <UploadIcon>📁</UploadIcon>
                    <UploadText>
                      Click to upload or drag and drop
                    </UploadText>
                  </UploadArea>

                  <ButtonGroup>
                    <Button
                      size="sm"
                      onClick={() => handleUploadClick(docType.id)}
                    >
                      Choose File
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => handleCameraCapture(docType.id)}
                    >
                      📷 Camera
                    </Button>
                  </ButtonGroup>
                </div>
              )}

              <FileInput
                ref={(el) => {
                  fileInputRefs.current[docType.id] = el;
                }}
                type="file"
                accept="image/*,.pdf"
                onChange={(e) => handleFileInputChange(docType.id, e)}
              />
            </DocumentCard>
          );
        })}
      </DocumentGrid>

      <div style={{ marginTop: '30px', textAlign: 'center' }}>
        <Button onClick={handleSubmit} size="lg" disabled={uploading}>
          {uploading ? (
            <>
              <LoadingSpinner />
              <span style={{ marginLeft: '8px' }}>Uploading...</span>
            </>
          ) : (
            'Upload All Documents'
          )}
        </Button>
      </div>

      {/* Camera Modal */}
      <CameraModal show={showCamera}>
        <CameraCapture
          documentType={documentTypes.find(dt => dt.id === currentDocType)?.title || 'Document'}
          onCapture={(blob, filename) => {
            const file = new File([blob], filename, { type: 'image/jpeg' });
            handleFileSelect(currentDocType, file);
            setShowCamera(false);
          }}
          onCancel={() => setShowCamera(false)}
        />
      </CameraModal>
    </Container>
  );
};

export default DocumentUpload;
