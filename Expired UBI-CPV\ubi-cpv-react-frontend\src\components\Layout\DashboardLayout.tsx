import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';

const DashboardContainer = styled.div`
  display: flex;
  min-height: 100vh;
`;

const Sidebar = styled.div<{ collapsed: boolean }>`
  width: ${props => props.collapsed ? '70px' : '250px'};
  background-color: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.white};
  padding: 20px;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  transition: ${props => props.theme.transitions.default};
  z-index: 1000;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    width: ${props => props.collapsed ? '70px' : '250px'};
    padding: 15px 10px;
  }
`;

const LogoContainer = styled.div<{ collapsed: boolean }>`
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};
`;

const Logo = styled.div`
  width: 40px;
  height: 40px;
  position: relative;
  margin-right: 10px;
`;

const LogoULeft = styled.div`
  width: 20px;
  height: 20px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 10px 10px 0 0;
  position: absolute;
  left: 5px;
  transform: rotate(180deg);
`;

const LogoURight = styled.div`
  width: 20px;
  height: 20px;
  background-color: ${props => props.theme.colors.secondary};
  border-radius: 10px 10px 0 0;
  position: absolute;
  right: 5px;
`;

const LogoText = styled.div<{ collapsed: boolean }>`
  font-size: 18px;
  font-weight: 600;
  display: ${props => props.collapsed ? 'none' : 'block'};
`;

const NavItem = styled.div<{ active?: boolean }>`
  padding: 12px 15px;
  border-radius: ${props => props.theme.borderRadius.sm};
  margin-bottom: 5px;
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};
  display: flex;
  align-items: center;
  background-color: ${props => props.active ? 'rgba(255, 255, 255, 0.2)' : 'transparent'};

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const NavIcon = styled.div<{ collapsed: boolean }>`
  margin-right: ${props => props.collapsed ? '0' : '10px'};
  width: 20px;
  text-align: center;
  font-size: 16px;
`;

const NavText = styled.div<{ collapsed: boolean }>`
  display: ${props => props.collapsed ? 'none' : 'block'};
`;

const UserInfo = styled.div<{ collapsed: boolean }>`
  margin-top: auto;
  padding: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: ${props => props.theme.colors.primary};
  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-weight: 600;
  color: ${props => props.theme.colors.textDark};
`;

const UserDetails = styled.div<{ collapsed: boolean }>`
  display: ${props => props.collapsed ? 'none' : 'block'};
`;

const UserName = styled.div`
  font-size: 14px;
  font-weight: 500;
`;

const UserRole = styled.div`
  font-size: 12px;
  opacity: 0.8;
`;

const MainContent = styled.div<{ sidebarCollapsed: boolean }>`
  flex: 1;
  margin-left: ${props => props.sidebarCollapsed ? '70px' : '250px'};
  padding: 20px;
  transition: ${props => props.theme.transitions.default};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};
`;

const PageTitle = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
`;

const HeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const CollapseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.white};
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

interface DashboardLayoutProps {
  children: React.ReactNode;
  title: string;
  headerActions?: React.ReactNode;
  navigationItems: Array<{
    icon: string;
    label: string;
    active?: boolean;
    onClick?: () => void;
  }>;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title,
  headerActions,
  navigationItems,
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user, logout } = useAuth();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <DashboardContainer>
      <Sidebar collapsed={sidebarCollapsed}>
        <LogoContainer collapsed={sidebarCollapsed}>
          <Logo>
            <LogoULeft />
            <LogoURight />
          </Logo>
          <LogoText collapsed={sidebarCollapsed}>UBI Verify</LogoText>
        </LogoContainer>

        <CollapseButton onClick={toggleSidebar}>
          {sidebarCollapsed ? '☰' : '←'}
        </CollapseButton>

        {navigationItems.map((item, index) => (
          <NavItem key={index} active={item.active} onClick={item.onClick}>
            <NavIcon collapsed={sidebarCollapsed}>{item.icon}</NavIcon>
            <NavText collapsed={sidebarCollapsed}>{item.label}</NavText>
          </NavItem>
        ))}

        <NavItem onClick={logout}>
          <NavIcon collapsed={sidebarCollapsed}>🚪</NavIcon>
          <NavText collapsed={sidebarCollapsed}>Logout</NavText>
        </NavItem>

        <UserInfo collapsed={sidebarCollapsed}>
          <UserAvatar>
            {user?.firstName?.charAt(0) || 'U'}
          </UserAvatar>
          <UserDetails collapsed={sidebarCollapsed}>
            <UserName>{user?.firstName || 'User'}</UserName>
            <UserRole>{user?.role || 'Role'}</UserRole>
          </UserDetails>
        </UserInfo>
      </Sidebar>

      <MainContent sidebarCollapsed={sidebarCollapsed}>
        <PageHeader>
          <PageTitle>{title}</PageTitle>
          <HeaderActions>
            {headerActions}
            <span>{new Date().toLocaleDateString()}</span>
          </HeaderActions>
        </PageHeader>
        {children}
      </MainContent>
    </DashboardContainer>
  );
};

export default DashboardLayout;
