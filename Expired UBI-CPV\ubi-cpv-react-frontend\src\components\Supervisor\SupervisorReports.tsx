import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService } from '../../services/apiService';

const ReportsContainer = styled.div`
  display: grid;
  gap: 20px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const StatCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #007E3A, #005a2a);
  color: white;
`;

const StatValue = styled.div`
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
`;

const ChartContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled(Card)`
  padding: 20px;
`;

const ChartTitle = styled.h3`
  margin-bottom: 20px;
  color: #007E3A;
  text-align: center;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background: white;

  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const TeamPerformanceTable = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const PerformanceBar = styled.div<{ percentage: number; color: string }>`
  width: 100%;
  height: 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    width: ${props => props.percentage}%;
    height: 100%;
    background-color: ${props => props.color};
    transition: width 0.3s ease;
  }
`;

const SupervisorReports: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState('month');
  const [teamStats, setTeamStats] = useState({
    totalAgents: 0,
    totalTasks: 0,
    completedTasks: 0,
    pendingReviews: 0,
    averageTime: 0,
    teamEfficiency: 0,
  });
  const [agentPerformance, setAgentPerformance] = useState<any[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    loadReportsData();
  }, [timeFilter]);

  const loadReportsData = async () => {
    try {
      setLoading(true);

      const [dashboardStats] = await Promise.all([
        apiService.getSupervisorDashboardStats(),
      ]);

      setTeamStats({
        totalAgents: 5, // Mock data
        totalTasks: dashboardStats.totalLeads || 0,
        completedTasks: dashboardStats.completedLeads || 0,
        pendingReviews: dashboardStats.pendingReviews || 0,
        averageTime: 2.8, // Mock data
        teamEfficiency: 82, // Mock data
      });

      // Mock agent performance data
      setAgentPerformance([
        {
          agentName: 'John Agent',
          tasksAssigned: 15,
          tasksCompleted: 12,
          approvalRate: 90,
          avgTime: 2.5,
          efficiency: 85,
        },
        {
          agentName: 'Jane Agent',
          tasksAssigned: 18,
          tasksCompleted: 16,
          approvalRate: 95,
          avgTime: 2.2,
          efficiency: 92,
        },
        {
          agentName: 'Bob Agent',
          tasksAssigned: 12,
          tasksCompleted: 10,
          approvalRate: 80,
          avgTime: 3.1,
          efficiency: 75,
        },
      ]);

    } catch (error) {
      console.error('Error loading reports data:', error);
      // Use mock data on error
      setTeamStats({
        totalAgents: 5,
        totalTasks: 45,
        completedTasks: 38,
        pendingReviews: 7,
        averageTime: 2.8,
        teamEfficiency: 82,
      });
    } finally {
      setLoading(false);
    }
  };

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/supervisor/dashboard') },
    { icon: '👁️', label: 'Review Queue', onClick: () => navigate('/supervisor/review') },
    { icon: '📊', label: 'Reports', active: true },
    { icon: '👥', label: 'Team', onClick: () => navigate('/supervisor/team') },
  ];

  const getCompletionRate = () => {
    return teamStats.totalTasks > 0 ? Math.round((teamStats.completedTasks / teamStats.totalTasks) * 100) : 0;
  };

  if (loading) {
    return (
      <DashboardLayout title="Team Reports" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Team Reports" navigationItems={navigationItems}>
      <ReportsContainer>
        <FilterContainer>
          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </FilterSelect>

          <Button variant="outline" onClick={() => window.print()}>
            📄 Export Report
          </Button>
        </FilterContainer>

        {/* Team Overview Stats */}
        <StatsGrid>
          <StatCard>
            <StatValue>{teamStats.totalAgents}</StatValue>
            <StatLabel>Team Members</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{teamStats.totalTasks}</StatValue>
            <StatLabel>Total Tasks</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{teamStats.completedTasks}</StatValue>
            <StatLabel>Completed</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{teamStats.pendingReviews}</StatValue>
            <StatLabel>Pending Review</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{teamStats.averageTime}</StatValue>
            <StatLabel>Avg. Days</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{teamStats.teamEfficiency}%</StatValue>
            <StatLabel>Team Efficiency</StatLabel>
          </StatCard>
        </StatsGrid>

        {/* Team Performance Charts */}
        <ChartContainer>
          <ChartCard>
            <ChartTitle>Team Performance Overview</ChartTitle>
            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>Completion Rate</span>
                <span>{getCompletionRate()}%</span>
              </div>
              <PerformanceBar percentage={getCompletionRate()} color="#2e7d32" />
            </div>
            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>Team Efficiency</span>
                <span>{teamStats.teamEfficiency}%</span>
              </div>
              <PerformanceBar percentage={teamStats.teamEfficiency} color="#007E3A" />
            </div>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>Quality Score</span>
                <span>88%</span>
              </div>
              <PerformanceBar percentage={88} color="#FFD100" />
            </div>
          </ChartCard>

          <ChartCard>
            <ChartTitle>Task Distribution</ChartTitle>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', textAlign: 'center' }}>
              <div>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#2e7d32' }}>
                  {teamStats.completedTasks}
                </div>
                <div style={{ fontSize: '14px', color: '#666' }}>Completed</div>
              </div>
              <div>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#4a148c' }}>
                  {teamStats.pendingReviews}
                </div>
                <div style={{ fontSize: '14px', color: '#666' }}>Pending</div>
              </div>
              <div>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff8f00' }}>
                  {teamStats.totalTasks - teamStats.completedTasks - teamStats.pendingReviews}
                </div>
                <div style={{ fontSize: '14px', color: '#666' }}>In Progress</div>
              </div>
              <div>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#007E3A' }}>
                  {teamStats.totalTasks}
                </div>
                <div style={{ fontSize: '14px', color: '#666' }}>Total</div>
              </div>
            </div>
          </ChartCard>
        </ChartContainer>

        {/* Agent Performance Table */}
        <Card>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Agent Performance</h3>
          <div style={{ overflowX: 'auto' }}>
            <TeamPerformanceTable>
              <thead>
                <tr>
                  <TableHeader>Agent Name</TableHeader>
                  <TableHeader>Tasks Assigned</TableHeader>
                  <TableHeader>Completed</TableHeader>
                  <TableHeader>Completion Rate</TableHeader>
                  <TableHeader>Approval Rate</TableHeader>
                  <TableHeader>Avg. Time</TableHeader>
                  <TableHeader>Efficiency</TableHeader>
                </tr>
              </thead>
              <tbody>
                {agentPerformance.map((agent, index) => (
                  <TableRow key={index}>
                    <TableCell style={{ fontWeight: '500' }}>{agent.agentName}</TableCell>
                    <TableCell>{agent.tasksAssigned}</TableCell>
                    <TableCell>{agent.tasksCompleted}</TableCell>
                    <TableCell>
                      {Math.round((agent.tasksCompleted / agent.tasksAssigned) * 100)}%
                    </TableCell>
                    <TableCell>{agent.approvalRate}%</TableCell>
                    <TableCell>{agent.avgTime} days</TableCell>
                    <TableCell>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                        <span>{agent.efficiency}%</span>
                        <div style={{ flex: 1, minWidth: '60px' }}>
                          <PerformanceBar
                            percentage={agent.efficiency}
                            color={agent.efficiency >= 85 ? '#2e7d32' : agent.efficiency >= 70 ? '#ff8f00' : '#c62828'}
                          />
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </tbody>
            </TeamPerformanceTable>
          </div>
        </Card>

        {/* Summary Insights */}
        <Card>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Key Insights</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
            <div>
              <h4 style={{ color: '#2e7d32', marginBottom: '10px' }}>🎯 Top Performer</h4>
              <p>Jane Agent leads with 95% approval rate and 92% efficiency</p>
            </div>
            <div>
              <h4 style={{ color: '#ff8f00', marginBottom: '10px' }}>⚠️ Needs Attention</h4>
              <p>Bob Agent requires support to improve efficiency from 75% to team average</p>
            </div>
            <div>
              <h4 style={{ color: '#007E3A', marginBottom: '10px' }}>📈 Team Trend</h4>
              <p>Overall team performance improved by 8% compared to last month</p>
            </div>
          </div>
        </Card>
      </ReportsContainer>
    </DashboardLayout>
  );
};

export default SupervisorReports;
