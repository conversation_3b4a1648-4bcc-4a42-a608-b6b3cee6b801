import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Card, Button, Input, Select, FormGroup, Label, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, VerificationData } from '../../services/apiService';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};
`;

const Title = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
  margin-left: 20px;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 12px 16px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: ${props => props.theme.transitions.default};

  &:focus {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
    outline: none;
  }
`;

const VerificationForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    agentName: '',
    agentContact: '',
    addressConfirmed: false,
    personMet: '',
    relationship: '',
    officeAddress: '',
    officeState: '',
    officeDistrict: '',
    officePincode: '',
    landmark: '',
    companyType: '',
    businessNature: '',
    establishmentYear: '',
    employeesCount: '',
    grossSalary: '',
    netSalary: '',
    proofType: '',
    verificationDate: '',
    additionalNotes: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!id) return;

    setLoading(true);

    try {
      const verificationData: VerificationData = {
        agentName: formData.agentName,
        agentContact: formData.agentContact,
        addressConfirmed: formData.addressConfirmed ? 'Yes' : 'No',
        personMet: formData.personMet,
        relationship: formData.relationship,
        officeAddress: formData.officeAddress,
        officeState: formData.officeState,
        officeDistrict: formData.officeDistrict,
        officePincode: formData.officePincode,
        landmark: formData.landmark,
        companyType: formData.companyType,
        businessNature: formData.businessNature,
        establishmentYear: parseInt(formData.establishmentYear),
        employeesCount: formData.employeesCount,
        grossSalary: parseFloat(formData.grossSalary),
        netSalary: parseFloat(formData.netSalary),
        proofType: formData.proofType,
        verificationDate: formData.verificationDate,
        additionalNotes: formData.additionalNotes,
      };

      await apiService.saveVerificationData(parseInt(id), verificationData);

      alert('Verification data saved successfully!');
      navigate(`/lead/${id}`);
    } catch (error) {
      console.error('Error saving verification data:', error);
      alert('Failed to save verification data');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate(`/lead/${id}`);
  };

  return (
    <Container>
      <Header>
        <Button variant="outline" onClick={handleBack}>
          ← Back
        </Button>
        <Title>Verification Form - Lead #{id}</Title>
      </Header>

      <form onSubmit={handleSubmit}>
        <FormGrid>
          <Card>
            <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Agent Information</h3>

            <FormGroup>
              <Label htmlFor="agentName">Agent Name</Label>
              <Input
                type="text"
                id="agentName"
                name="agentName"
                value={formData.agentName}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="agentContact">Agent Contact</Label>
              <Input
                type="tel"
                id="agentContact"
                name="agentContact"
                value={formData.agentContact}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="verificationDate">Verification Date</Label>
              <Input
                type="date"
                id="verificationDate"
                name="verificationDate"
                value={formData.verificationDate}
                onChange={handleInputChange}
                required
              />
            </FormGroup>
          </Card>

          <Card>
            <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Verification Details</h3>

            <CheckboxGroup>
              <Checkbox
                type="checkbox"
                id="addressConfirmed"
                name="addressConfirmed"
                checked={formData.addressConfirmed}
                onChange={handleInputChange}
              />
              <Label htmlFor="addressConfirmed">Address Confirmed</Label>
            </CheckboxGroup>

            <FormGroup>
              <Label htmlFor="personMet">Person Met</Label>
              <Input
                type="text"
                id="personMet"
                name="personMet"
                value={formData.personMet}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="relationship">Relationship</Label>
              <Select
                id="relationship"
                name="relationship"
                value={formData.relationship}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Relationship</option>
                <option value="self">Self</option>
                <option value="spouse">Spouse</option>
                <option value="parent">Parent</option>
                <option value="sibling">Sibling</option>
                <option value="other">Other</option>
              </Select>
            </FormGroup>
          </Card>
        </FormGrid>

        <Card style={{ marginTop: '20px' }}>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Office Information</h3>

          <FormGrid>
            <FormGroup>
              <Label htmlFor="officeAddress">Office Address</Label>
              <TextArea
                id="officeAddress"
                name="officeAddress"
                value={formData.officeAddress}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <div>
              <FormGroup>
                <Label htmlFor="officeState">State</Label>
                <Input
                  type="text"
                  id="officeState"
                  name="officeState"
                  value={formData.officeState}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="officeDistrict">District</Label>
                <Input
                  type="text"
                  id="officeDistrict"
                  name="officeDistrict"
                  value={formData.officeDistrict}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="officePincode">Pincode</Label>
                <Input
                  type="text"
                  id="officePincode"
                  name="officePincode"
                  value={formData.officePincode}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>
            </div>
          </FormGrid>

          <FormGroup>
            <Label htmlFor="landmark">Landmark</Label>
            <Input
              type="text"
              id="landmark"
              name="landmark"
              value={formData.landmark}
              onChange={handleInputChange}
            />
          </FormGroup>
        </Card>

        <Card style={{ marginTop: '20px' }}>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Company Information</h3>

          <FormGrid>
            <FormGroup>
              <Label htmlFor="companyType">Company Type</Label>
              <Select
                id="companyType"
                name="companyType"
                value={formData.companyType}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Company Type</option>
                <option value="private">Private Limited</option>
                <option value="public">Public Limited</option>
                <option value="partnership">Partnership</option>
                <option value="proprietorship">Proprietorship</option>
                <option value="government">Government</option>
              </Select>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="businessNature">Business Nature</Label>
              <Input
                type="text"
                id="businessNature"
                name="businessNature"
                value={formData.businessNature}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="establishmentYear">Establishment Year</Label>
              <Input
                type="number"
                id="establishmentYear"
                name="establishmentYear"
                value={formData.establishmentYear}
                onChange={handleInputChange}
                min="1900"
                max={new Date().getFullYear()}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="employeesCount">Number of Employees</Label>
              <Input
                type="number"
                id="employeesCount"
                name="employeesCount"
                value={formData.employeesCount}
                onChange={handleInputChange}
                min="1"
                required
              />
            </FormGroup>
          </FormGrid>
        </Card>

        <Card style={{ marginTop: '20px' }}>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Salary Information</h3>

          <FormGrid>
            <FormGroup>
              <Label htmlFor="grossSalary">Gross Salary (₹)</Label>
              <Input
                type="number"
                id="grossSalary"
                name="grossSalary"
                value={formData.grossSalary}
                onChange={handleInputChange}
                min="0"
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="netSalary">Net Salary (₹)</Label>
              <Input
                type="number"
                id="netSalary"
                name="netSalary"
                value={formData.netSalary}
                onChange={handleInputChange}
                min="0"
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="proofType">Proof Type</Label>
              <Select
                id="proofType"
                name="proofType"
                value={formData.proofType}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Proof Type</option>
                <option value="salary-slip">Salary Slip</option>
                <option value="bank-statement">Bank Statement</option>
                <option value="appointment-letter">Appointment Letter</option>
                <option value="id-card">ID Card</option>
              </Select>
            </FormGroup>
          </FormGrid>

          <FormGroup>
            <Label htmlFor="additionalNotes">Additional Notes</Label>
            <TextArea
              id="additionalNotes"
              name="additionalNotes"
              value={formData.additionalNotes}
              onChange={handleInputChange}
              placeholder="Any additional observations or notes..."
            />
          </FormGroup>
        </Card>

        <div style={{ marginTop: '30px', display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
          <Button type="button" variant="outline" onClick={handleBack} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <LoadingSpinner />
                <span style={{ marginLeft: '8px' }}>Saving...</span>
              </>
            ) : (
              'Save Verification Data'
            )}
          </Button>
        </div>
      </form>
    </Container>
  );
};

export default VerificationForm;
