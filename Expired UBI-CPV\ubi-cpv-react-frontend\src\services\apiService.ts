import axios, { AxiosInstance } from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:59358/api';

// Types matching the API DTOs
export interface User {
  userId: number;
  username: string;
  email: string;
  role: 'Agent' | 'Supervisor' | 'Admin';
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  isActive: boolean;
  createdDate: string;
  lastLoginDate?: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  refreshToken?: string;
  user?: User;
  expiresAt?: string;
}

export interface Lead {
  leadId: number;
  customerName: string;
  mobileNumber: string;
  loanType: string;
  status: string;
  createdDate: string;
  assignedDate?: string;
  startedDate?: string;
  submittedDate?: string;
  reviewedDate?: string;
  approvedDate?: string;
  rejectedDate?: string;
  rejectionReason?: string;
  reviewComments?: string;
  creator?: User;
  assignedAgent?: User;
  reviewer?: User;
  addresses: Address[];
  statusHistory: StatusHistory[];
  documents: Document[];
  croppedImages: CroppedImage[];
  verificationData?: VerificationData;
}

export interface LeadListItem {
  leadId: number;
  customerName: string;
  mobileNumber: string;
  loanType: string;
  status: string;
  createdDate: string;
  assignedDate?: string;
  submittedDate?: string;
  createdByName?: string;
  assignedToName?: string;
  reviewedByName?: string;
  documentCount: number;
  croppedImageCount: number;
}

export interface PagedResult<T> {
  data: T[];
  totalRecords: number;
  totalCount: number; // Alias for totalRecords for backward compatibility
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface Address {
  addressId: number;
  addressType: string;
  address: string;
  pincode: string;
  state: string;
  district: string;
  landmark?: string;
  createdDate: string;
}

export interface StatusHistory {
  historyId: number;
  status: string;
  timestamp: string;
  comments?: string;
  updatedByUser?: User;
}

export interface Document {
  documentId: number;
  leadId: number;
  documentTypeName: string;
  fileName: string;
  originalFileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedDate: string;
  uploadedByName?: string;
  isActive: boolean;
}

export interface CroppedImage {
  imageId: number;
  leadId: number;
  fileName: string;
  originalFileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  cropData?: string;
  pageNumber?: number;
  createdDate: string;
  createdByName?: string;
}

export interface VerificationData {
  verificationId?: number;
  leadId?: number;
  agentName: string;
  agentContact: string;
  addressConfirmed?: string;
  personMet?: string;
  relationship?: string;
  officeAddress?: string;
  officeState?: string;
  officeDistrict?: string;
  officePincode?: string;
  landmark?: string;
  companyType?: string;
  businessNature?: string;
  establishmentYear?: number;
  employeesCount?: string;
  grossSalary?: number;
  netSalary?: number;
  proofType?: string;
  verificationDate?: string;
  additionalNotes?: string;
}

export interface DashboardStats {
  totalLeads: number;
  newLeads: number;
  assignedLeads: number;
  inProgressLeads: number;
  pendingReviewLeads: number;
  approvedLeads: number;
  rejectedLeads: number;
  completedLeads: number;
  pendingLeads: number;
}

export interface AgentDashboardStats {
  pendingLeads: number;
  inProgressLeads: number;
  completedLeads: number;
  rejectedLeads: number;
  totalAssigned: number;
}

export interface SupervisorDashboardStats {
  pendingReviews: number;
  approvedToday: number;
  rejectedToday: number;
  totalReviewed: number;
  approvalRate: number;
  agentPerformance: AgentPerformance[];
  totalLeads: number;
  completedLeads: number;
}

export interface AgentPerformance {
  agentId: number;
  agentName: string;
  assignedCount: number;
  completedCount: number;
  approvedCount: number;
  rejectedCount: number;
  completionRate: number;
  approvalRate: number;
}

export interface CreateLeadRequest {
  customerName: string;
  mobileNumber: string;
  loanType: string;
  addresses: {
    type: string;
    address: string;
    pincode: string;
    state: string;
    district: string;
    landmark?: string;
  }[];
}

export interface DocumentType {
  documentTypeId: number;
  typeName: string;
  description?: string;
  isActive: boolean;
}

export interface FileUploadResult {
  success: boolean;
  message: string;
  fileName?: string;
  filePath?: string;
  fileSize: number;
  documentId?: number;
  imageId?: number;
}

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;

    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling and token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          const refreshToken = localStorage.getItem('refreshToken');
          const token = localStorage.getItem('authToken');

          if (refreshToken && token) {
            try {
              const response = await axios.post(`${this.baseURL}/auth/refresh-token`, {
                token,
                refreshToken
              });

              if (response.data.success) {
                localStorage.setItem('authToken', response.data.token);
                localStorage.setItem('refreshToken', response.data.refreshToken);
                localStorage.setItem('user', JSON.stringify(response.data.user));

                // Retry original request with new token
                originalRequest.headers.Authorization = `Bearer ${response.data.token}`;
                return this.api(originalRequest);
              }
            } catch (refreshError) {
              console.error('Token refresh failed:', refreshError);
            }
          }

          // If refresh fails, logout user
          this.logoutUser();
        }

        return Promise.reject(error);
      }
    );
  }

  private logoutUser() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    window.location.href = '/login';
  }

  // Authentication
  async login(username: string, password: string, role: string): Promise<LoginResponse> {
    try {
      const response = await this.api.post('/auth/login', {
        username,
        password,
        role,
      });
      return response.data;
    } catch (error: any) {
      console.error('Login API error:', error);
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      this.logoutUser();
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      const response = await this.api.get('/auth/me');
      return response.data;
    } catch (error: any) {
      console.error('Get current user API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to get user information');
    }
  }

  // Leads
  async getLeads(pageNumber = 1, pageSize = 10, status?: string, assignedTo?: number): Promise<PagedResult<LeadListItem>> {
    try {
      const params = new URLSearchParams({
        pageNumber: pageNumber.toString(),
        pageSize: pageSize.toString(),
      });

      if (status) params.append('status', status);
      if (assignedTo) params.append('assignedTo', assignedTo.toString());

      const response = await this.api.get(`/leads?${params.toString()}`);
      const data = response.data;

      // Ensure totalCount is available for backward compatibility
      return {
        ...data,
        totalCount: data.totalRecords || data.totalCount || 0
      };
    } catch (error: any) {
      console.error('Get leads API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch leads');
    }
  }

  async getLead(id: number): Promise<Lead> {
    try {
      const response = await this.api.get(`/leads/${id}`);
      return response.data;
    } catch (error: any) {
      console.error('Get lead API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch lead');
    }
  }

  async createLead(leadData: CreateLeadRequest): Promise<Lead> {
    try {
      const response = await this.api.post('/leads', leadData);
      return response.data;
    } catch (error: any) {
      console.error('Create lead API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to create lead');
    }
  }

  async updateLeadStatus(id: number, status: string, comments?: string, rejectionReason?: string): Promise<void> {
    try {
      await this.api.put(`/leads/${id}/status`, {
        status,
        comments,
        rejectionReason,
      });
    } catch (error: any) {
      console.error('Update lead status API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to update lead status');
    }
  }

  async assignLead(id: number, agentId: number, comments?: string): Promise<void> {
    try {
      await this.api.put(`/leads/${id}/assign`, {
        agentId,
        comments,
      });
    } catch (error: any) {
      console.error('Assign lead API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to assign lead');
    }
  }

  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const response = await this.api.get('/leads/dashboard-stats');
      return response.data;
    } catch (error: any) {
      console.error('Get dashboard stats API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard statistics');
    }
  }

  // Verification
  async getAgentDashboardStats(): Promise<AgentDashboardStats> {
    try {
      const response = await this.api.get('/verification/agent/dashboard-stats');
      return response.data;
    } catch (error: any) {
      console.error('Get agent dashboard stats API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch agent dashboard statistics');
    }
  }

  async getSupervisorDashboardStats(): Promise<SupervisorDashboardStats> {
    try {
      const response = await this.api.get('/verification/supervisor/dashboard-stats');
      return response.data;
    } catch (error: any) {
      console.error('Get supervisor dashboard stats API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch supervisor dashboard statistics');
    }
  }

  async saveVerificationData(leadId: number, verificationData: VerificationData): Promise<VerificationData> {
    try {
      const response = await this.api.post(`/verification/leads/${leadId}`, verificationData);
      return response.data;
    } catch (error: any) {
      console.error('Save verification data API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to save verification data');
    }
  }

  async updateVerificationData(leadId: number, verificationData: VerificationData): Promise<VerificationData> {
    try {
      const response = await this.api.put(`/verification/leads/${leadId}`, verificationData);
      return response.data;
    } catch (error: any) {
      console.error('Update verification data API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to update verification data');
    }
  }

  async getVerificationData(leadId: number): Promise<VerificationData> {
    try {
      const response = await this.api.get(`/verification/leads/${leadId}`);
      return response.data;
    } catch (error: any) {
      console.error('Get verification data API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch verification data');
    }
  }

  // Documents
  async getDocumentTypes(): Promise<DocumentType[]> {
    try {
      const response = await this.api.get('/documents/types');
      return response.data;
    } catch (error: any) {
      console.error('Get document types API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch document types');
    }
  }

  async uploadDocument(leadId: number, documentType: string, file: File): Promise<FileUploadResult> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentTypeId', '1'); // Default document type ID
      formData.append('documentCategory', documentType);

      const response = await this.api.post(`/documents/leads/${leadId}/verification-documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Upload document API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to upload document');
    }
  }

  async getVerificationDocuments(leadId: number): Promise<Document[]> {
    try {
      const response = await this.api.get(`/documents/leads/${leadId}/verification-documents`);
      return response.data;
    } catch (error: any) {
      console.error('Get verification documents API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch verification documents');
    }
  }

  // Users (for Admin)
  async getUsers(): Promise<User[]> {
    try {
      const response = await this.api.get('/users');
      return response.data;
    } catch (error: any) {
      console.error('Get users API error:', error);
      // Return mock data for now since UsersController doesn't exist yet
      return [
        {
          userId: 1,
          username: 'agent1',
          firstName: 'John',
          lastName: 'Agent',
          email: '<EMAIL>',
          role: 'Agent',
          isActive: true,
          createdDate: '2024-01-01T00:00:00Z',
          lastLoginDate: '2024-01-16T08:30:00Z',
        },
        {
          userId: 2,
          username: 'supervisor1',
          firstName: 'Jane',
          lastName: 'Supervisor',
          email: '<EMAIL>',
          role: 'Supervisor',
          isActive: true,
          createdDate: '2024-01-01T00:00:00Z',
          lastLoginDate: '2024-01-16T09:15:00Z',
        },
        {
          userId: 3,
          username: 'admin1',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          role: 'Admin',
          isActive: true,
          createdDate: '2024-01-01T00:00:00Z',
          lastLoginDate: '2024-01-16T10:00:00Z',
        },
      ];
    }
  }

  async createUser(userData: any): Promise<User> {
    try {
      const response = await this.api.post('/users', userData);
      return response.data;
    } catch (error: any) {
      console.error('Create user API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to create user');
    }
  }

  async updateUser(userId: number, userData: any): Promise<User> {
    try {
      const response = await this.api.put(`/users/${userId}`, userData);
      return response.data;
    } catch (error: any) {
      console.error('Update user API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to update user');
    }
  }

  async toggleUserStatus(userId: number): Promise<void> {
    try {
      await this.api.put(`/users/${userId}/toggle-status`);
    } catch (error: any) {
      console.error('Toggle user status API error:', error);
      throw new Error(error.response?.data?.message || 'Failed to toggle user status');
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
