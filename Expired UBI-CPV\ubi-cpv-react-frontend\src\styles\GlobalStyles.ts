import styled, { createGlobalStyle } from 'styled-components';

export const theme = {
  colors: {
    primary: '#007E3A',
    primaryDark: '#005a2a',
    secondary: '#FFD100',
    secondaryDark: '#e6bc00',
    white: '#FFFFFF',
    offWhite: '#f9f9f9',
    lightGray: '#F5F5F5',
    mediumGray: '#e0e0e0',
    textDark: '#333333',
    textMedium: '#555555',
    textLight: '#777777',
    error: '#dc3545',
    success: '#28a745',
    warning: '#ffc107',
    info: '#17a2b8',
  },
  shadows: {
    sm: '0 2px 8px rgba(0, 0, 0, 0.05)',
    md: '0 4px 12px rgba(0, 0, 0, 0.08)',
    lg: '0 8px 24px rgba(0, 0, 0, 0.12)',
  },
  borderRadius: {
    sm: '6px',
    md: '12px',
    lg: '20px',
  },
  transitions: {
    default: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
  },
  breakpoints: {
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
  },
};

export const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    color: ${theme.colors.textDark};
    min-height: 100vh;
    line-height: 1.6;
  }

  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }

  button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    outline: none;
  }

  input, textarea, select {
    font-family: inherit;
    outline: none;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  ul, ol {
    list-style: none;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
`;

// Common styled components
export const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
`;

export const Card = styled.div`
  background: ${theme.colors.white};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.md};
  padding: 20px;
  margin-bottom: 20px;
  transition: ${theme.transitions.default};

  &:hover {
    box-shadow: ${theme.shadows.lg};
  }
`;

export const Button = styled.button<{
  variant?: 'primary' | 'secondary' | 'outline' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: ${props => {
    switch (props.size) {
      case 'sm': return '8px 16px';
      case 'lg': return '14px 28px';
      default: return '10px 20px';
    }
  }};
  border-radius: ${theme.borderRadius.sm};
  font-size: ${props => {
    switch (props.size) {
      case 'sm': return '12px';
      case 'lg': return '16px';
      default: return '14px';
    }
  }};
  font-weight: 600;
  transition: ${theme.transitions.default};
  text-align: center;
  letter-spacing: 0.5px;
  box-shadow: ${theme.shadows.sm};
  width: ${props => props.fullWidth ? '100%' : 'auto'};

  ${props => {
    switch (props.variant) {
      case 'secondary':
        return `
          background: linear-gradient(135deg, ${theme.colors.secondary}, ${theme.colors.secondaryDark});
          color: ${theme.colors.textDark};
          &:hover {
            background: linear-gradient(135deg, ${theme.colors.secondaryDark}, ${theme.colors.secondary});
            transform: translateY(-2px);
            box-shadow: ${theme.shadows.md};
          }
        `;
      case 'outline':
        return `
          background: transparent;
          color: ${theme.colors.primary};
          border: 1px solid ${theme.colors.primary};
          &:hover {
            background: ${theme.colors.primary};
            color: ${theme.colors.white};
          }
        `;
      case 'danger':
        return `
          background: ${theme.colors.error};
          color: ${theme.colors.white};
          &:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: ${theme.shadows.md};
          }
        `;
      default:
        return `
          background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryDark});
          color: ${theme.colors.white};
          &:hover {
            background: linear-gradient(135deg, ${theme.colors.primaryDark}, ${theme.colors.primary});
            transform: translateY(-2px);
            box-shadow: ${theme.shadows.md};
          }
        `;
    }
  }}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
`;

export const Input = styled.input`
  width: 100%;
  padding: 12px 16px;
  border: 1px solid ${theme.colors.mediumGray};
  border-radius: ${theme.borderRadius.sm};
  font-size: 14px;
  transition: ${theme.transitions.default};
  background-color: ${theme.colors.offWhite};
  color: ${theme.colors.textDark};
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);

  &:focus {
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
    background-color: ${theme.colors.white};
  }

  &::placeholder {
    color: ${theme.colors.textLight};
  }
`;

export const Select = styled.select`
  width: 100%;
  padding: 12px 16px;
  border: 1px solid ${theme.colors.mediumGray};
  border-radius: ${theme.borderRadius.sm};
  font-size: 14px;
  transition: ${theme.transitions.default};
  background-color: ${theme.colors.offWhite};
  color: ${theme.colors.textDark};
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);

  &:focus {
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
    background-color: ${theme.colors.white};
  }
`;

export const Label = styled.label`
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: ${theme.colors.textMedium};
  font-size: 14px;
  transition: ${theme.transitions.default};
`;

export const FormGroup = styled.div`
  margin-bottom: 20px;

  &:focus-within ${Label} {
    color: ${theme.colors.primary};
  }
`;

export const ErrorMessage = styled.div`
  color: ${theme.colors.error};
  font-size: 12px;
  margin-top: 5px;
`;

export const LoadingSpinner = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
