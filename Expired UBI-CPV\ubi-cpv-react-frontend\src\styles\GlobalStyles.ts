import styled, { createGlobalStyle } from 'styled-components';

export const theme = {
  colors: {
    // Primary Brand Colors
    primary: '#007E3A',
    primaryDark: '#005a2a',
    primaryLight: '#4CAF50',
    primaryGradient: 'linear-gradient(135deg, #007E3A 0%, #4CAF50 100%)',

    // Secondary Colors
    secondary: '#FFD100',
    secondaryDark: '#e6bc00',
    secondaryLight: '#FFF176',
    secondaryGradient: 'linear-gradient(135deg, #FFD100 0%, #FFF176 100%)',

    // Neutral Colors
    white: '#FFFFFF',
    offWhite: '#FAFAFA',
    lightGray: '#F5F7FA',
    mediumGray: '#E2E8F0',
    darkGray: '#64748B',

    // Text Colors
    textPrimary: '#1E293B',
    textSecondary: '#475569',
    textTertiary: '#94A3B8',
    textInverse: '#FFFFFF',

    // Status Colors
    error: '#EF4444',
    errorLight: '#FEF2F2',
    success: '#10B981',
    successLight: '#F0FDF4',
    warning: '#F59E0B',
    warningLight: '#FFFBEB',
    info: '#3B82F6',
    infoLight: '#EFF6FF',

    // Background Colors
    background: '#FFFFFF',
    backgroundSecondary: '#F8FAFC',
    backgroundTertiary: '#F1F5F9',

    // Border Colors
    border: '#E2E8F0',
    borderLight: '#F1F5F9',
    borderDark: '#CBD5E1',

    // Glass Effect Colors
    glass: 'rgba(255, 255, 255, 0.25)',
    glassDark: 'rgba(0, 0, 0, 0.1)',

    // Backward Compatibility Aliases
    textDark: '#1E293B', // alias for textPrimary
    textMedium: '#475569', // alias for textSecondary
    textLight: '#94A3B8', // alias for textTertiary
  },
  shadows: {
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    glass: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
    neumorphism: '20px 20px 60px #d1d9e6, -20px -20px 60px #ffffff',
  },
  borderRadius: {
    none: '0',
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    '2xl': '24px',
    '3xl': '32px',
    full: '9999px',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    '2xl': '48px',
    '3xl': '64px',
    '4xl': '96px',
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      mono: ['JetBrains Mono', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
    },
    fontSize: {
      xs: '12px',
      sm: '14px',
      base: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
      '3xl': '30px',
      '4xl': '36px',
      '5xl': '48px',
    },
    fontWeight: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75',
    },
  },
  transitions: {
    fast: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
    default: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    slow: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
    spring: 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  breakpoints: {
    xs: '320px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
    // Backward Compatibility Aliases
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
  },
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060,
  },
};

export const GlobalStyles = createGlobalStyle`
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  *::before,
  *::after {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }

  body {
    font-family: ${theme.typography.fontFamily.sans.join(', ')};
    font-size: ${theme.typography.fontSize.base};
    font-weight: ${theme.typography.fontWeight.normal};
    line-height: ${theme.typography.lineHeight.normal};
    color: ${theme.colors.textPrimary};
    background: linear-gradient(135deg, ${theme.colors.backgroundSecondary} 0%, ${theme.colors.backgroundTertiary} 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${theme.colors.backgroundSecondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${theme.colors.mediumGray};
    border-radius: ${theme.borderRadius.full};
    transition: ${theme.transitions.default};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${theme.colors.darkGray};
  }

  /* Focus styles */
  :focus-visible {
    outline: 2px solid ${theme.colors.primary};
    outline-offset: 2px;
  }

  /* Selection styles */
  ::selection {
    background: ${theme.colors.primary};
    color: ${theme.colors.white};
  }

  code {
    font-family: ${theme.typography.fontFamily.mono.join(', ')};
    font-size: 0.875em;
    background: ${theme.colors.backgroundTertiary};
    padding: 0.125rem 0.25rem;
    border-radius: ${theme.borderRadius.sm};
  }

  button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    outline: none;
    background: none;
    transition: ${theme.transitions.default};
  }

  button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  input, textarea, select {
    font-family: inherit;
    outline: none;
    transition: ${theme.transitions.default};
  }

  a {
    text-decoration: none;
    color: inherit;
    transition: ${theme.transitions.default};
  }

  ul, ol {
    list-style: none;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  /* Utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .glass-effect {
    background: ${theme.colors.glass};
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .neumorphism {
    background: ${theme.colors.backgroundSecondary};
    box-shadow: ${theme.shadows.neumorphism};
  }

  /* Animation keyframes */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      transform: translate3d(0, -30px, 0);
    }
    70% {
      transform: translate3d(0, -15px, 0);
    }
    90% {
      transform: translate3d(0, -4px, 0);
    }
  }

  /* Responsive design helpers */
  @media (max-width: ${theme.breakpoints.sm}) {
    html {
      font-size: 14px;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
`;

// Common styled components
export const Container = styled.div<{
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}>`
  max-width: ${props => {
    switch (props.maxWidth) {
      case 'sm': return '640px';
      case 'md': return '768px';
      case 'lg': return '1024px';
      case 'xl': return '1280px';
      case '2xl': return '1536px';
      case 'full': return '100%';
      default: return '1200px';
    }
  }};
  margin: 0 auto;
  padding: ${props => {
    switch (props.padding) {
      case 'none': return '0';
      case 'sm': return `0 ${theme.spacing.md}`;
      case 'lg': return `0 ${theme.spacing.xl}`;
      case 'md':
      default: return `0 ${theme.spacing.lg}`;
    }
  }};
  width: 100%;
`;

export const Card = styled.div<{
  variant?: 'default' | 'glass' | 'neumorphism' | 'elevated';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
}>`
  background: ${props => {
    switch (props.variant) {
      case 'glass':
        return theme.colors.glass;
      case 'neumorphism':
        return theme.colors.backgroundSecondary;
      case 'elevated':
        return theme.colors.white;
      default:
        return theme.colors.white;
    }
  }};

  ${props => props.variant === 'glass' && `
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  `}

  ${props => props.variant === 'neumorphism' && `
    box-shadow: ${theme.shadows.neumorphism};
  `}

  ${props => props.variant !== 'neumorphism' && `
    box-shadow: ${theme.shadows.md};
  `}

  border-radius: ${theme.borderRadius.xl};
  padding: ${props => {
    switch (props.padding) {
      case 'sm': return theme.spacing.md;
      case 'lg': return theme.spacing.xl;
      case 'xl': return theme.spacing['2xl'];
      case 'md':
      default: return theme.spacing.lg;
    }
  }};
  margin-bottom: ${theme.spacing.lg};
  transition: ${theme.transitions.default};
  border: 1px solid ${theme.colors.border};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, ${theme.colors.primary}, transparent);
    opacity: 0;
    transition: ${theme.transitions.default};
  }

  ${props => props.hover !== false && `
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${theme.shadows.xl};
      border-color: ${theme.colors.primary};

      &::before {
        opacity: 1;
      }
    }
  `}

  @media (max-width: ${theme.breakpoints.sm}) {
    padding: ${theme.spacing.md};
    margin-bottom: ${theme.spacing.md};
  }
`;

export const Button = styled.button<{
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  loading?: boolean;
  rounded?: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.sm};
  position: relative;
  overflow: hidden;

  padding: ${props => {
    switch (props.size) {
      case 'xs': return `${theme.spacing.xs} ${theme.spacing.sm}`;
      case 'sm': return `${theme.spacing.sm} ${theme.spacing.md}`;
      case 'lg': return `${theme.spacing.md} ${theme.spacing.xl}`;
      case 'xl': return `${theme.spacing.lg} ${theme.spacing['2xl']}`;
      case 'md':
      default: return `${theme.spacing.sm} ${theme.spacing.lg}`;
    }
  }};

  border-radius: ${props => props.rounded ? theme.borderRadius.full : theme.borderRadius.lg};

  font-family: ${theme.typography.fontFamily.sans.join(', ')};
  font-size: ${props => {
    switch (props.size) {
      case 'xs': return theme.typography.fontSize.xs;
      case 'sm': return theme.typography.fontSize.sm;
      case 'lg': return theme.typography.fontSize.lg;
      case 'xl': return theme.typography.fontSize.xl;
      case 'md':
      default: return theme.typography.fontSize.base;
    }
  }};
  font-weight: ${theme.typography.fontWeight.medium};
  line-height: ${theme.typography.lineHeight.tight};

  transition: ${theme.transitions.default};
  text-align: center;
  letter-spacing: 0.025em;
  width: ${props => props.fullWidth ? '100%' : 'auto'};
  cursor: pointer;
  border: 1px solid transparent;

  /* Ripple effect */
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  &:active::before {
    width: 300px;
    height: 300px;
  }

  ${props => {
    switch (props.variant) {
      case 'secondary':
        return `
          background: ${theme.colors.secondaryGradient};
          color: ${theme.colors.textPrimary};
          box-shadow: ${theme.shadows.sm};

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: ${theme.shadows.lg};
            filter: brightness(1.05);
          }

          &:active:not(:disabled) {
            transform: translateY(0);
            box-shadow: ${theme.shadows.md};
          }
        `;
      case 'outline':
        return `
          background: transparent;
          color: ${theme.colors.primary};
          border-color: ${theme.colors.primary};

          &:hover:not(:disabled) {
            background: ${theme.colors.primary};
            color: ${theme.colors.white};
            transform: translateY(-1px);
            box-shadow: ${theme.shadows.md};
          }
        `;
      case 'ghost':
        return `
          background: transparent;
          color: ${theme.colors.textSecondary};

          &:hover:not(:disabled) {
            background: ${theme.colors.backgroundTertiary};
            color: ${theme.colors.textPrimary};
          }
        `;
      case 'danger':
        return `
          background: linear-gradient(135deg, ${theme.colors.error} 0%, #dc2626 100%);
          color: ${theme.colors.white};
          box-shadow: ${theme.shadows.sm};

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: ${theme.shadows.lg};
            filter: brightness(1.1);
          }
        `;
      case 'success':
        return `
          background: linear-gradient(135deg, ${theme.colors.success} 0%, #059669 100%);
          color: ${theme.colors.white};
          box-shadow: ${theme.shadows.sm};

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: ${theme.shadows.lg};
            filter: brightness(1.1);
          }
        `;
      case 'primary':
      default:
        return `
          background: ${theme.colors.primaryGradient};
          color: ${theme.colors.white};
          box-shadow: ${theme.shadows.sm};

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: ${theme.shadows.lg};
            filter: brightness(1.1);
          }

          &:active:not(:disabled) {
            transform: translateY(0);
            box-shadow: ${theme.shadows.md};
          }
        `;
    }
  }}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    filter: none !important;
  }

  ${props => props.loading && `
    cursor: wait;

    &::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  `}

  @media (max-width: ${theme.breakpoints.sm}) {
    padding: ${props => {
      switch (props.size) {
        case 'xs': return `${theme.spacing.xs} ${theme.spacing.sm}`;
        case 'sm': return `${theme.spacing.sm} ${theme.spacing.md}`;
        case 'lg': return `${theme.spacing.sm} ${theme.spacing.lg}`;
        case 'xl': return `${theme.spacing.md} ${theme.spacing.xl}`;
        case 'md':
        default: return `${theme.spacing.sm} ${theme.spacing.md}`;
      }
    }};
  }
`;

export const Input = styled.input<{
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'flushed';
  error?: boolean;
}>`
  width: 100%;
  padding: ${props => {
    switch (props.size) {
      case 'sm': return `${theme.spacing.sm} ${theme.spacing.md}`;
      case 'lg': return `${theme.spacing.md} ${theme.spacing.lg}`;
      case 'md':
      default: return `${theme.spacing.sm} ${theme.spacing.md}`;
    }
  }};

  border: 1px solid ${props => props.error ? theme.colors.error : theme.colors.border};
  border-radius: ${props => {
    switch (props.variant) {
      case 'flushed': return '0';
      default: return theme.borderRadius.lg;
    }
  }};

  ${props => props.variant === 'flushed' && `
    border-left: none;
    border-right: none;
    border-top: none;
    border-radius: 0;
    padding-left: 0;
    padding-right: 0;
  `}

  font-family: ${theme.typography.fontFamily.sans.join(', ')};
  font-size: ${props => {
    switch (props.size) {
      case 'sm': return theme.typography.fontSize.sm;
      case 'lg': return theme.typography.fontSize.lg;
      case 'md':
      default: return theme.typography.fontSize.base;
    }
  }};
  font-weight: ${theme.typography.fontWeight.normal};
  line-height: ${theme.typography.lineHeight.normal};

  transition: ${theme.transitions.default};
  background-color: ${props => {
    switch (props.variant) {
      case 'filled': return theme.colors.backgroundTertiary;
      default: return theme.colors.white;
    }
  }};
  color: ${theme.colors.textPrimary};

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
    background-color: ${theme.colors.white};
  }

  &:hover:not(:focus) {
    border-color: ${theme.colors.darkGray};
  }

  &::placeholder {
    color: ${theme.colors.textTertiary};
    font-weight: ${theme.typography.fontWeight.normal};
  }

  &:disabled {
    background-color: ${theme.colors.backgroundTertiary};
    color: ${theme.colors.textTertiary};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

export const Textarea = styled.textarea<{
  size?: 'sm' | 'md' | 'lg';
  error?: boolean;
}>`
  width: 100%;
  min-height: 120px;
  padding: ${props => {
    switch (props.size) {
      case 'sm': return `${theme.spacing.sm} ${theme.spacing.md}`;
      case 'lg': return `${theme.spacing.md} ${theme.spacing.lg}`;
      case 'md':
      default: return `${theme.spacing.sm} ${theme.spacing.md}`;
    }
  }};

  border: 1px solid ${props => props.error ? theme.colors.error : theme.colors.border};
  border-radius: ${theme.borderRadius.lg};

  font-family: ${theme.typography.fontFamily.sans.join(', ')};
  font-size: ${theme.typography.fontSize.base};
  font-weight: ${theme.typography.fontWeight.normal};
  line-height: ${theme.typography.lineHeight.normal};

  transition: ${theme.transitions.default};
  background-color: ${theme.colors.white};
  color: ${theme.colors.textPrimary};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
  }

  &:hover:not(:focus) {
    border-color: ${theme.colors.darkGray};
  }

  &::placeholder {
    color: ${theme.colors.textTertiary};
  }

  &:disabled {
    background-color: ${theme.colors.backgroundTertiary};
    color: ${theme.colors.textTertiary};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

export const Select = styled.select<{
  size?: 'sm' | 'md' | 'lg';
  error?: boolean;
}>`
  width: 100%;
  padding: ${props => {
    switch (props.size) {
      case 'sm': return `${theme.spacing.sm} ${theme.spacing.md}`;
      case 'lg': return `${theme.spacing.md} ${theme.spacing.lg}`;
      case 'md':
      default: return `${theme.spacing.sm} ${theme.spacing.md}`;
    }
  }};

  border: 1px solid ${props => props.error ? theme.colors.error : theme.colors.border};
  border-radius: ${theme.borderRadius.lg};

  font-family: ${theme.typography.fontFamily.sans.join(', ')};
  font-size: ${theme.typography.fontSize.base};
  font-weight: ${theme.typography.fontWeight.normal};

  transition: ${theme.transitions.default};
  background-color: ${theme.colors.white};
  color: ${theme.colors.textPrimary};
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
  }

  &:hover:not(:focus) {
    border-color: ${theme.colors.darkGray};
  }

  &:disabled {
    background-color: ${theme.colors.backgroundTertiary};
    color: ${theme.colors.textTertiary};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

export const Label = styled.label<{
  size?: 'sm' | 'md' | 'lg';
  required?: boolean;
}>`
  display: block;
  margin-bottom: ${theme.spacing.xs};
  font-family: ${theme.typography.fontFamily.sans.join(', ')};
  font-size: ${props => {
    switch (props.size) {
      case 'sm': return theme.typography.fontSize.sm;
      case 'lg': return theme.typography.fontSize.lg;
      case 'md':
      default: return theme.typography.fontSize.base;
    }
  }};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.textSecondary};
  transition: ${theme.transitions.default};

  ${props => props.required && `
    &::after {
      content: ' *';
      color: ${theme.colors.error};
    }
  `}
`;

export const FormGroup = styled.div<{
  spacing?: 'sm' | 'md' | 'lg';
}>`
  margin-bottom: ${props => {
    switch (props.spacing) {
      case 'sm': return theme.spacing.md;
      case 'lg': return theme.spacing.xl;
      case 'md':
      default: return theme.spacing.lg;
    }
  }};

  &:focus-within ${Label} {
    color: ${theme.colors.primary};
  }
`;

export const ErrorMessage = styled.div<{
  size?: 'sm' | 'md';
}>`
  color: ${theme.colors.error};
  font-size: ${props => props.size === 'sm' ? theme.typography.fontSize.xs : theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.normal};
  margin-top: ${theme.spacing.xs};
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};

  &::before {
    content: '⚠';
    font-size: ${theme.typography.fontSize.sm};
  }
`;

export const HelperText = styled.div<{
  size?: 'sm' | 'md';
}>`
  color: ${theme.colors.textTertiary};
  font-size: ${props => props.size === 'sm' ? theme.typography.fontSize.xs : theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.normal};
  margin-top: ${theme.spacing.xs};
`;

export const LoadingSpinner = styled.div<{
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}>`
  display: inline-block;
  width: ${props => {
    switch (props.size) {
      case 'sm': return '16px';
      case 'lg': return '32px';
      case 'md':
      default: return '24px';
    }
  }};
  height: ${props => {
    switch (props.size) {
      case 'sm': return '16px';
      case 'lg': return '32px';
      case 'md':
      default: return '24px';
    }
  }};
  border: 2px solid ${theme.colors.backgroundTertiary};
  border-top: 2px solid ${props => props.color || theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;
`;

// Modern UI Components
export const Badge = styled.span<{
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: ${props => {
    switch (props.size) {
      case 'sm': return `${theme.spacing.xs} ${theme.spacing.sm}`;
      case 'lg': return `${theme.spacing.sm} ${theme.spacing.md}`;
      case 'md':
      default: return `2px ${theme.spacing.sm}`;
    }
  }};

  font-size: ${props => {
    switch (props.size) {
      case 'sm': return theme.typography.fontSize.xs;
      case 'lg': return theme.typography.fontSize.base;
      case 'md':
      default: return theme.typography.fontSize.sm;
    }
  }};
  font-weight: ${theme.typography.fontWeight.medium};
  line-height: 1;

  border-radius: ${theme.borderRadius.full};
  text-transform: uppercase;
  letter-spacing: 0.05em;

  ${props => {
    switch (props.variant) {
      case 'secondary':
        return `
          background: ${theme.colors.secondaryGradient};
          color: ${theme.colors.textPrimary};
        `;
      case 'success':
        return `
          background: ${theme.colors.successLight};
          color: ${theme.colors.success};
        `;
      case 'warning':
        return `
          background: ${theme.colors.warningLight};
          color: ${theme.colors.warning};
        `;
      case 'error':
        return `
          background: ${theme.colors.errorLight};
          color: ${theme.colors.error};
        `;
      case 'info':
        return `
          background: ${theme.colors.infoLight};
          color: ${theme.colors.info};
        `;
      case 'primary':
      default:
        return `
          background: ${theme.colors.primary};
          color: ${theme.colors.white};
        `;
    }
  }}
`;

export const Avatar = styled.div<{
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  src?: string;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;

  width: ${props => {
    switch (props.size) {
      case 'xs': return '24px';
      case 'sm': return '32px';
      case 'lg': return '56px';
      case 'xl': return '72px';
      case 'md':
      default: return '40px';
    }
  }};
  height: ${props => {
    switch (props.size) {
      case 'xs': return '24px';
      case 'sm': return '32px';
      case 'lg': return '56px';
      case 'xl': return '72px';
      case 'md':
      default: return '40px';
    }
  }};

  border-radius: ${theme.borderRadius.full};
  background: ${props => props.src ? `url(${props.src})` : theme.colors.primaryGradient};
  background-size: cover;
  background-position: center;

  font-size: ${props => {
    switch (props.size) {
      case 'xs': return theme.typography.fontSize.xs;
      case 'sm': return theme.typography.fontSize.sm;
      case 'lg': return theme.typography.fontSize.xl;
      case 'xl': return theme.typography.fontSize['2xl'];
      case 'md':
      default: return theme.typography.fontSize.base;
    }
  }};
  font-weight: ${theme.typography.fontWeight.semibold};
  color: ${theme.colors.white};
  text-transform: uppercase;

  box-shadow: ${theme.shadows.md};
  transition: ${theme.transitions.default};

  &:hover {
    transform: scale(1.05);
    box-shadow: ${theme.shadows.lg};
  }
`;

export const Divider = styled.hr<{
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'sm' | 'md' | 'lg';
}>`
  border: none;
  background: linear-gradient(90deg, transparent, ${theme.colors.border}, transparent);

  ${props => props.orientation === 'vertical' ? `
    width: 1px;
    height: 100%;
    margin: 0 ${props.spacing === 'sm' ? theme.spacing.sm : props.spacing === 'lg' ? theme.spacing.lg : theme.spacing.md};
  ` : `
    height: 1px;
    width: 100%;
    margin: ${props.spacing === 'sm' ? theme.spacing.sm : props.spacing === 'lg' ? theme.spacing.lg : theme.spacing.md} 0;
  `}
`;

export const Tooltip = styled.div<{
  position?: 'top' | 'bottom' | 'left' | 'right';
}>`
  position: absolute;
  z-index: ${theme.zIndex.tooltip};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  background: ${theme.colors.textPrimary};
  color: ${theme.colors.white};
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.lg};
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: ${theme.transitions.default};

  ${props => {
    switch (props.position) {
      case 'top':
        return `
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%) translateY(-8px);

          &::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: ${theme.colors.textPrimary};
          }
        `;
      case 'bottom':
        return `
          top: 100%;
          left: 50%;
          transform: translateX(-50%) translateY(8px);

          &::after {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-bottom-color: ${theme.colors.textPrimary};
          }
        `;
      case 'left':
        return `
          right: 100%;
          top: 50%;
          transform: translateY(-50%) translateX(-8px);

          &::after {
            content: '';
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            border: 4px solid transparent;
            border-left-color: ${theme.colors.textPrimary};
          }
        `;
      case 'right':
      default:
        return `
          left: 100%;
          top: 50%;
          transform: translateY(-50%) translateX(8px);

          &::after {
            content: '';
            position: absolute;
            right: 100%;
            top: 50%;
            transform: translateY(-50%);
            border: 4px solid transparent;
            border-right-color: ${theme.colors.textPrimary};
          }
        `;
    }
  }}
`;

export const TooltipWrapper = styled.div`
  position: relative;
  display: inline-block;

  &:hover ${Tooltip} {
    opacity: 1;
    visibility: visible;
  }
`;
