import 'styled-components';

declare module 'styled-components' {
  export interface DefaultTheme {
    colors: {
      // Primary Brand Colors
      primary: string;
      primaryDark: string;
      primaryLight: string;
      primaryGradient: string;

      // Secondary Colors
      secondary: string;
      secondaryDark: string;
      secondaryLight: string;
      secondaryGradient: string;

      // Neutral Colors
      white: string;
      offWhite: string;
      lightGray: string;
      mediumGray: string;
      darkGray: string;

      // Text Colors
      textPrimary: string;
      textSecondary: string;
      textTertiary: string;
      textInverse: string;

      // Status Colors
      error: string;
      errorLight: string;
      success: string;
      successLight: string;
      warning: string;
      warningLight: string;
      info: string;
      infoLight: string;

      // Background Colors
      background: string;
      backgroundSecondary: string;
      backgroundTertiary: string;

      // Border Colors
      border: string;
      borderLight: string;
      borderDark: string;

      // Glass Effect Colors
      glass: string;
      glassDark: string;

      // Backward Compatibility Aliases
      textDark: string;
      textMedium: string;
      textLight: string;
    };
    shadows: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
      inner: string;
      glass: string;
      neumorphism: string;
    };
    borderRadius: {
      none: string;
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      full: string;
    };
    spacing: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
    };
    typography: {
      fontFamily: {
        sans: string[];
        mono: string[];
      };
      fontSize: {
        xs: string;
        sm: string;
        base: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
        '4xl': string;
        '5xl': string;
      };
      fontWeight: {
        light: string;
        normal: string;
        medium: string;
        semibold: string;
        bold: string;
        extrabold: string;
      };
      lineHeight: {
        tight: string;
        normal: string;
        relaxed: string;
      };
    };
    transitions: {
      fast: string;
      default: string;
      slow: string;
      spring: string;
    };
    breakpoints: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
      // Backward Compatibility Aliases
      mobile: string;
      tablet: string;
      desktop: string;
    };
    zIndex: {
      dropdown: number;
      sticky: number;
      fixed: number;
      modal: number;
      popover: number;
      tooltip: number;
    };
  }
}
