import 'styled-components';

declare module 'styled-components' {
  export interface DefaultTheme {
    colors: {
      primary: string;
      primaryDark: string;
      secondary: string;
      secondaryDark: string;
      white: string;
      offWhite: string;
      lightGray: string;
      mediumGray: string;
      textDark: string;
      textMedium: string;
      textLight: string;
      error: string;
      success: string;
      warning: string;
      info: string;
    };
    shadows: {
      sm: string;
      md: string;
      lg: string;
    };
    borderRadius: {
      sm: string;
      md: string;
      lg: string;
    };
    transitions: {
      default: string;
    };
    breakpoints: {
      mobile: string;
      tablet: string;
      desktop: string;
    };
  }
}
